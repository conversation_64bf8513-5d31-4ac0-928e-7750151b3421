import { ProjectInput, GeneratedPrompt, Technology } from './types';
import { getProjectTemplate } from './templates';

export class PromptGenerator {
  
  static generatePrompt(input: ProjectInput): GeneratedPrompt {
    const template = getProjectTemplate(input.projectType);
    
    // Build technology stack description
    const technologyStack = this.buildTechnologyStackDescription(input.technologies);
    
    // Build features list
    const features = this.buildFeaturesDescription(input.features || []);
    
    // Generate project description with enhanced details
    const projectDescription = this.enhanceProjectDescription(input.projectIdea, input.complexity);
    
    // Replace template variables
    const content = template.promptTemplate
      .replace(/{projectType}/g, template.name)
      .replace(/{projectName}/g, input.projectName)
      .replace(/{projectIdea}/g, input.projectIdea)
      .replace(/{projectDescription}/g, projectDescription)
      .replace(/{platform}/g, input.platform)
      .replace(/{technologyStack}/g, technologyStack)
      .replace(/{complexity}/g, input.complexity)
      .replace(/{features}/g, features)
      .replace(/{additionalRequirements}/g, input.additionalRequirements || 'None specified');

    // Add Augment-specific enhancements
    const enhancedContent = this.addAugmentOptimizations(content, input);

    return {
      title: `Build ${input.projectName} - ${template.name}`,
      content: enhancedContent,
      sections: this.extractSections(enhancedContent)
    };
  }

  private static buildTechnologyStackDescription(technologies: Technology[]): string {
    if (technologies.length === 0) {
      return 'Please suggest an appropriate technology stack for this project';
    }

    const groupedTech = technologies.reduce((acc, tech) => {
      if (!acc[tech.category]) {
        acc[tech.category] = [];
      }
      acc[tech.category].push(tech);
      return acc;
    }, {} as Record<string, Technology[]>);

    const descriptions = Object.entries(groupedTech).map(([category, techs]) => {
      const techNames = techs.map(t => t.name).join(', ');
      return `- ${category.charAt(0).toUpperCase() + category.slice(1)}: ${techNames}`;
    });

    return descriptions.join('\n');
  }

  private static buildFeaturesDescription(features: string[]): string {
    if (features.length === 0) {
      return 'Core functionality as described in the project concept';
    }
    
    return features.map(feature => `- ${feature}`).join('\n');
  }

  private static enhanceProjectDescription(idea: string, complexity: string): string {
    const complexityEnhancements = {
      simple: 'Focus on core functionality with a clean, minimal design. Prioritize ease of use and quick implementation.',
      intermediate: 'Include additional features that enhance user experience. Implement proper error handling, validation, and basic optimization.',
      advanced: 'Build a comprehensive solution with advanced features, scalability considerations, performance optimization, and enterprise-level architecture.'
    };

    return `${idea}\n\n**Complexity Considerations:**\n${complexityEnhancements[complexity as keyof typeof complexityEnhancements]}`;
  }

  private static addAugmentOptimizations(content: string, input: ProjectInput): string {
    const augmentInstructions = `

**🚀 AUGMENT AGENT OPTIMIZATION INSTRUCTIONS:**

**Context-Aware Development:**
- Before making any code changes, use the codebase-retrieval tool to understand the current project structure
- Ask for detailed information about existing components, utilities, and patterns before implementing new features
- Respect existing code conventions and architectural decisions

**Task Management:**
- Break down this project into manageable tasks using the task management tools
- Create subtasks for each major component (setup, database, API, frontend, testing, deployment)
- Update task status as you progress through the implementation
- Use task dependencies to ensure proper development order

**Best Practices for ${input.projectType}:**
- Follow industry best practices for ${input.platform} development
- Implement proper error handling and user feedback mechanisms
- Ensure code is well-documented and follows consistent patterns
- Add comprehensive testing at unit, integration, and end-to-end levels

**Quality Assurance:**
- Suggest code reviews at major milestones
- Recommend performance optimizations specific to the chosen technology stack
- Implement security best practices relevant to the project type
- Plan for scalability and maintainability from the start

**Development Workflow:**
1. Start by analyzing the requirements and creating a detailed task breakdown
2. Set up the development environment and project structure
3. Implement core functionality incrementally
4. Add comprehensive testing throughout development
5. Optimize and refine based on testing results
6. Prepare for deployment with proper configuration

**Communication:**
- Provide regular progress updates and explain technical decisions
- Ask for clarification when requirements are ambiguous
- Suggest improvements and alternative approaches when appropriate
- Document any assumptions made during development

Please acknowledge this request and start by creating a detailed project plan with specific, actionable tasks.`;

    return content + augmentInstructions;
  }

  private static extractSections(content: string): GeneratedPrompt['sections'] {
    return {
      projectDescription: this.extractSection(content, 'Core Functionality:'),
      technicalRequirements: this.extractSection(content, 'Technical Specifications:'),
      technologyStack: this.extractSection(content, 'Technology Stack:'),
      implementationApproach: this.extractSection(content, 'Implementation Approach:'),
      testingRequirements: this.extractSection(content, 'Testing & Quality Assurance:'),
      augmentSpecificInstructions: this.extractSection(content, 'AUGMENT AGENT OPTIMIZATION INSTRUCTIONS:')
    };
  }

  private static extractSection(content: string, sectionHeader: string): string {
    const startIndex = content.indexOf(sectionHeader);
    if (startIndex === -1) return '';
    
    const nextSectionIndex = content.indexOf('\n\n**', startIndex + sectionHeader.length);
    const endIndex = nextSectionIndex === -1 ? content.length : nextSectionIndex;
    
    return content.substring(startIndex, endIndex).trim();
  }
}
