import React, { useState } from 'react';
import { INDUSTRY_TEMPLATES, IndustryTemplate } from '@/lib/templates';
import { ProjectInput } from '@/lib/types';

interface AdvancedTemplateBrowserProps {
  isOpen: boolean;
  onClose: () => void;
  onLoadTemplate: (projectInput: Partial<ProjectInput>) => void;
}

export default function AdvancedTemplateBrowser({
  isOpen,
  onClose,
  onLoadTemplate
}: AdvancedTemplateBrowserProps) {
  const [selectedIndustry, setSelectedIndustry] = useState<string>('all');
  const [selectedComplexity, setSelectedComplexity] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<IndustryTemplate | null>(null);

  const industries = Array.from(new Set(INDUSTRY_TEMPLATES.map(t => t.industry)));
  const complexities = ['basic', 'intermediate', 'advanced'];

  const filteredIndustryTemplates = INDUSTRY_TEMPLATES.filter(template => {
    const matchesIndustry = selectedIndustry === 'all' || template.industry === selectedIndustry;
    const matchesComplexity = selectedComplexity === 'all' || template.complexity === selectedComplexity;
    const matchesSearch = searchQuery === '' || 
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.industry.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesIndustry && matchesComplexity && matchesSearch;
  });

  const handleLoadIndustryTemplate = (template: IndustryTemplate) => {
    const projectInput: Partial<ProjectInput> = {
      projectType: template.projectType,
      projectName: template.name,
      projectIdea: `${template.description}\n\nBusiness Goals:\n${template.businessGoals.map(goal => `• ${goal}`).join('\n')}\n\nTarget Audience: ${template.targetAudience}\n\nKey Features:\n${template.features.map(feature => `• ${feature}`).join('\n')}`,
      platform: template.platform,
      complexity: template.complexity,
      technologies: template.technologies.slice(0, 4).map(tech => ({
        category: 'frontend' as const,
        name: tech,
        description: `${tech} for ${template.industry.toLowerCase()} solutions`
      })),
      additionalRequirements: `Timeline: ${template.timeline}\nBudget: ${template.budget}\n\nSuccess Metrics:\n${template.successMetrics.map(metric => `• ${metric}`).join('\n')}\n\nRisk Considerations:\n${template.risks.map(risk => `• ${risk}`).join('\n')}`
    };
    onLoadTemplate(projectInput);
    onClose();
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'basic': return 'bg-green-600 text-green-200';
      case 'intermediate': return 'bg-yellow-600 text-yellow-200';
      case 'advanced': return 'bg-red-600 text-red-200';
      default: return 'bg-gray-600 text-gray-200';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-2 sm:p-4">
      <div className="bg-black border border-white/20 rounded-xl shadow-2xl p-4 sm:p-6 max-w-7xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-hidden" style={{
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8)'
      }}>
        <div className="flex justify-between items-center mb-4 sm:mb-6">
          <h3 className="text-lg sm:text-xl font-semibold text-white">Industry Templates</h3>
          <button
            onClick={onClose}
            className="bg-gray-800 text-white px-3 py-1 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm flex-shrink-0"
          >
            Close
          </button>
        </div>

        {/* Industry Templates Header */}
        <div className="mb-6">
          <h3 className="text-xl font-semibold text-white mb-2">Industry Templates</h3>
          <p className="text-gray-400 text-sm">Choose from comprehensive industry-specific project templates with detailed specifications and best practices.</p>
        </div>

        {/* Industry Templates Content */}
            {/* Filters */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-4 sm:mb-6">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search templates..."
                className="px-3 py-2 rounded-md bg-gray-900 text-white placeholder-gray-400 border border-white/10 focus:border-white/30 focus:outline-none text-sm sm:text-base"
              />
              <select
                value={selectedIndustry}
                onChange={(e) => setSelectedIndustry(e.target.value)}
                className="px-3 py-2 rounded-md bg-gray-900 text-white border border-white/10 focus:border-white/30 focus:outline-none text-sm sm:text-base"
              >
                <option value="all">All Industries</option>
                {industries.map(industry => (
                  <option key={industry} value={industry}>{industry}</option>
                ))}
              </select>
              <select
                value={selectedComplexity}
                onChange={(e) => setSelectedComplexity(e.target.value)}
                className="px-3 py-2 rounded-md bg-gray-900 text-white border border-white/10 focus:border-white/30 focus:outline-none text-sm sm:text-base sm:col-span-2 lg:col-span-1"
              >
                <option value="all">All Complexity Levels</option>
                {complexities.map(complexity => (
                  <option key={complexity} value={complexity} className="capitalize">{complexity}</option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 max-h-[60vh] sm:max-h-[65vh] overflow-y-auto">
              {/* Templates List */}
              <div className="space-y-3 sm:space-y-4">
                {filteredIndustryTemplates.map((template) => (
                  <div
                    key={template.id}
                    className={`bg-gray-900 border border-white/10 rounded-lg p-3 sm:p-4 transition-all duration-300 cursor-pointer ${
                      selectedTemplate?.id === template.id
                        ? 'ring-2 ring-white bg-gray-800'
                        : 'hover:bg-gray-800'
                    }`}
                    onClick={() => setSelectedTemplate(template)}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="text-white font-medium text-sm sm:text-base pr-2 flex-1 min-w-0">{template.name}</h4>
                      <span className={`px-2 py-1 rounded text-xs capitalize flex-shrink-0 ${getComplexityColor(template.complexity)}`}>
                        {template.complexity}
                      </span>
                    </div>
                    <p className="text-gray-300 text-xs sm:text-sm mb-2">{template.industry}</p>
                    <p className="text-gray-400 text-xs sm:text-sm mb-3 line-clamp-2">{template.description}</p>
                    <div className="flex justify-between items-center text-xs text-gray-500">
                      <span className="truncate pr-2">{template.timeline}</span>
                      <span className="truncate">{template.budget}</span>
                    </div>
                  </div>
                ))}
                {filteredIndustryTemplates.length === 0 && (
                  <div className="text-center text-gray-400 py-12">
                    <div className="text-4xl mb-4">🔍</div>
                    <h4 className="text-lg font-semibold mb-2">No Templates Found</h4>
                    <p className="text-sm">Try adjusting your filters or search query</p>
                  </div>
                )}
              </div>

              {/* Template Details */}
              <div className="bg-gray-900 border border-white/10 rounded-lg p-3 sm:p-4">
                {selectedTemplate ? (
                  <div className="space-y-3 sm:space-y-4">
                    <div>
                      <h4 className="text-white font-medium text-base sm:text-lg mb-2">{selectedTemplate.name}</h4>
                      <p className="text-gray-300 text-xs sm:text-sm mb-2">{selectedTemplate.industry}</p>
                      <p className="text-gray-400 text-xs sm:text-sm">{selectedTemplate.description}</p>
                    </div>

                    <div>
                      <h5 className="text-white font-medium mb-2">Key Features</h5>
                      <div className="space-y-1">
                        {selectedTemplate.features.map((feature, index) => (
                          <div key={index} className="text-gray-400 text-sm">• {feature}</div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h5 className="text-white font-medium mb-2">Technologies</h5>
                      <div className="flex flex-wrap gap-1">
                        {selectedTemplate.technologies.map((tech, index) => (
                          <span key={index} className="bg-gray-700 text-gray-200 text-xs px-2 py-1 rounded">
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h5 className="text-white font-medium mb-2">Business Goals</h5>
                      <div className="space-y-1">
                        {selectedTemplate.businessGoals.map((goal, index) => (
                          <div key={index} className="text-gray-400 text-sm">• {goal}</div>
                        ))}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-400">Timeline:</span>
                        <div className="text-white">{selectedTemplate.timeline}</div>
                      </div>
                      <div>
                        <span className="text-gray-400">Budget:</span>
                        <div className="text-white">{selectedTemplate.budget}</div>
                      </div>
                    </div>

                    <button
                      onClick={() => handleLoadIndustryTemplate(selectedTemplate)}
                      className="w-full bg-white text-black py-2 sm:py-3 rounded-md hover:bg-gray-200 transition-all duration-300 font-medium text-sm sm:text-base"
                    >
                      Use This Template
                    </button>
                  </div>
                ) : (
                  <div className="text-center text-gray-400 py-8 sm:py-12">
                    <div className="text-3xl sm:text-4xl mb-3 sm:mb-4">📋</div>
                    <h4 className="text-base sm:text-lg font-semibold mb-2">Select a Template</h4>
                    <p className="text-xs sm:text-sm">Choose a template from the list to see detailed information</p>
                  </div>
                )}
              </div>
            </div>
      </div>
    </div>
  );
}
