import React, { useState, useEffect } from 'react';
import { analytics, UsageStats } from '@/lib/analytics';

interface AnalyticsDashboardProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function AnalyticsDashboard({ isOpen, onClose }: AnalyticsDashboardProps) {
  const [stats, setStats] = useState<UsageStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (isOpen) {
      setLoading(true);
      // Simulate loading delay for better UX
      setTimeout(() => {
        setStats(analytics.getUsageStats());
        setLoading(false);
      }, 500);
    }
  }, [isOpen]);

  const handleExportData = () => {
    const data = analytics.exportData();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `prompt-generator-analytics-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleClearData = () => {
    if (confirm('Are you sure you want to clear all analytics data? This action cannot be undone.')) {
      analytics.clearData();
      setStats(analytics.getUsageStats());
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-black/20 backdrop-blur-xl rounded-xl shadow-2xl p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto" style={{
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
      }}>
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-white">Analytics Dashboard</h3>
          <button
            onClick={onClose}
            className="bg-black/10 text-white px-3 py-1 rounded-md hover:bg-black/20 transition-all duration-300 text-sm"
          >
            Close
          </button>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            <span className="ml-3 text-white">Loading analytics...</span>
          </div>
        ) : stats ? (
          <div className="space-y-6">
            {/* Overview Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-black/10 rounded-lg p-4">
                <div className="text-2xl font-bold text-white">{stats.totalPrompts}</div>
                <div className="text-sm text-gray-400">Total Prompts</div>
              </div>
              <div className="bg-black/10 rounded-lg p-4">
                <div className="text-2xl font-bold text-white">{stats.totalSessions}</div>
                <div className="text-sm text-gray-400">Sessions</div>
              </div>
              <div className="bg-black/10 rounded-lg p-4">
                <div className="text-2xl font-bold text-white">{stats.averagePromptsPerSession.toFixed(1)}</div>
                <div className="text-sm text-gray-400">Avg per Session</div>
              </div>
              <div className="bg-black/10 rounded-lg p-4">
                <div className="text-2xl font-bold text-white">{(stats.errorRate * 100).toFixed(1)}%</div>
                <div className="text-sm text-gray-400">Error Rate</div>
              </div>
            </div>

            {/* Usage Breakdown */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Project Types */}
              <div className="bg-black/10 rounded-lg p-4">
                <h4 className="text-lg font-semibold text-white mb-3">Most Used Project Types</h4>
                <div className="space-y-2">
                  {Object.entries(stats.mostUsedProjectTypes)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 5)
                    .map(([type, count]) => (
                      <div key={type} className="flex justify-between items-center">
                        <span className="text-gray-300 capitalize">{type.replace('-', ' ')}</span>
                        <div className="flex items-center space-x-2">
                          <div className="bg-blue-500/20 h-2 rounded-full" style={{
                            width: `${Math.max(20, (count / Math.max(...Object.values(stats.mostUsedProjectTypes))) * 100)}px`
                          }}></div>
                          <span className="text-white text-sm">{count}</span>
                        </div>
                      </div>
                    ))}
                </div>
              </div>

              {/* Platforms */}
              <div className="bg-black/10 rounded-lg p-4">
                <h4 className="text-lg font-semibold text-white mb-3">Target Platforms</h4>
                <div className="space-y-2">
                  {Object.entries(stats.mostUsedPlatforms)
                    .sort(([,a], [,b]) => b - a)
                    .map(([platform, count]) => (
                      <div key={platform} className="flex justify-between items-center">
                        <span className="text-gray-300 capitalize">{platform}</span>
                        <div className="flex items-center space-x-2">
                          <div className="bg-green-500/20 h-2 rounded-full" style={{
                            width: `${Math.max(20, (count / Math.max(...Object.values(stats.mostUsedPlatforms))) * 100)}px`
                          }}></div>
                          <span className="text-white text-sm">{count}</span>
                        </div>
                      </div>
                    ))}
                </div>
              </div>

              {/* Complexity Levels */}
              <div className="bg-black/10 rounded-lg p-4">
                <h4 className="text-lg font-semibold text-white mb-3">Complexity Distribution</h4>
                <div className="space-y-2">
                  {Object.entries(stats.mostUsedComplexity)
                    .sort(([,a], [,b]) => b - a)
                    .map(([complexity, count]) => (
                      <div key={complexity} className="flex justify-between items-center">
                        <span className="text-gray-300 capitalize">{complexity}</span>
                        <div className="flex items-center space-x-2">
                          <div className="bg-purple-500/20 h-2 rounded-full" style={{
                            width: `${Math.max(20, (count / Math.max(...Object.values(stats.mostUsedComplexity))) * 100)}px`
                          }}></div>
                          <span className="text-white text-sm">{count}</span>
                        </div>
                      </div>
                    ))}
                </div>
              </div>

              {/* AI Models */}
              <div className="bg-black/10 rounded-lg p-4">
                <h4 className="text-lg font-semibold text-white mb-3">AI Models Used</h4>
                <div className="space-y-2">
                  {Object.entries(stats.mostUsedModels)
                    .sort(([,a], [,b]) => b - a)
                    .map(([model, count]) => (
                      <div key={model} className="flex justify-between items-center">
                        <span className="text-gray-300 text-sm">{model.split('/').pop()?.split(':')[0] || model}</span>
                        <div className="flex items-center space-x-2">
                          <div className="bg-yellow-500/20 h-2 rounded-full" style={{
                            width: `${Math.max(20, (count / Math.max(...Object.values(stats.mostUsedModels))) * 100)}px`
                          }}></div>
                          <span className="text-white text-sm">{count}</span>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </div>

            {/* Additional Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-black/10 rounded-lg p-4">
                <div className="text-lg font-bold text-white">{stats.totalTokensUsed.toLocaleString()}</div>
                <div className="text-sm text-gray-400">Total Tokens Used</div>
              </div>
              <div className="bg-black/10 rounded-lg p-4">
                <div className="text-lg font-bold text-white">{Math.round(stats.averagePromptLength)}</div>
                <div className="text-sm text-gray-400">Avg Input Length</div>
              </div>
              <div className="bg-black/10 rounded-lg p-4">
                <div className="text-lg font-bold text-white">{stats.lastUpdated.toLocaleDateString()}</div>
                <div className="text-sm text-gray-400">Last Updated</div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between pt-4 border-t border-white/10">
              <button
                onClick={handleClearData}
                className="bg-red-500/20 text-red-400 px-4 py-2 rounded-md hover:bg-red-500/30 transition-all duration-300 text-sm"
              >
                Clear All Data
              </button>
              <button
                onClick={handleExportData}
                className="bg-blue-500/20 text-blue-400 px-4 py-2 rounded-md hover:bg-blue-500/30 transition-all duration-300 text-sm"
              >
                Export Data
              </button>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400">No analytics data available</div>
          </div>
        )}
      </div>
    </div>
  );
}
