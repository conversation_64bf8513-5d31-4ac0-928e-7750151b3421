import { ProjectTemplate, ProjectType, Platform, Complexity } from './types';

export interface IndustryTemplate {
  id: string;
  name: string;
  industry: string;
  description: string;
  projectType: ProjectType;
  platform: Platform;
  complexity: Complexity;
  technologies: string[];
  features: string[];
  businessGoals: string[];
  targetAudience: string;
  timeline: string;
  budget: string;
  successMetrics: string[];
  risks: string[];
  template: string;
}

// Import unified templates
import { UNIFIED_INDUSTRY_TEMPLATES } from './unified-templates';

export const INDUSTRY_TEMPLATES: IndustryTemplate[] = UNIFIED_INDUSTRY_TEMPLATES;

export const PROJECT_TEMPLATES: Record<ProjectType, ProjectTemplate> = {
  'web-application': {
    type: 'web-application',
    name: 'Web Application',
    description: 'Modern web applications with frontend and backend components',
    defaultTechnologies: [],
    commonFeatures: [
      'User authentication and authorization',
      'Responsive design for all devices',
      'Database integration',
      'API endpoints',
      'Form handling and validation',
      'Search functionality',
      'Real-time updates',
      'File upload/download',
      'Email notifications',
      'Admin dashboard'
    ],
    promptTemplate: `I want to build a comprehensive {projectType} called "{projectName}" with the following concept: {projectIdea}

**Project Requirements:**

**Core Functionality:**
{projectDescription}

**Technical Specifications:**
- Platform: {platform}
- Technology Stack: {technologyStack}
- Complexity Level: {complexity}
- Key Features: {features}

**Implementation Approach:**
Please help me build this project step by step with the following approach:

1. **Project Setup & Architecture:**
   - Initialize the project with the specified technology stack
   - Set up the development environment and folder structure
   - Configure essential tools (linting, formatting, testing)
   - Design the overall system architecture

2. **Database Design:**
   - Design and implement the database schema
   - Set up database connections and ORM/query layer
   - Create necessary migrations and seed data

3. **Backend Development:**
   - Implement core API endpoints
   - Set up authentication and authorization
   - Add input validation and error handling
   - Implement business logic and data processing

4. **Frontend Development:**
   - Create responsive UI components
   - Implement user interfaces for all features
   - Add client-side routing and state management
   - Integrate with backend APIs

5. **Testing & Quality Assurance:**
   - Write comprehensive unit tests for all components
   - Implement integration tests for API endpoints
   - Add end-to-end tests for critical user flows
   - Set up automated testing pipeline

6. **Deployment & DevOps:**
   - Configure production environment
   - Set up CI/CD pipeline
   - Implement monitoring and logging
   - Deploy to production platform

**Augment Agent Specific Instructions:**
- Please use your codebase context engine to understand the project structure as we build
- Break down complex tasks into manageable subtasks using your task management capabilities
- For each major component, first analyze the codebase context before making changes
- Suggest and implement best practices for the chosen technology stack
- Provide code reviews and optimization suggestions throughout development
- Help debug any issues that arise during implementation

**Additional Requirements:**
{additionalRequirements}

Please start by analyzing this request and creating a detailed implementation plan with specific tasks. Then begin with the project setup and architecture phase.`
  },

  'mobile-application': {
    type: 'mobile-application',
    name: 'Mobile Application',
    description: 'Cross-platform or native mobile applications',
    defaultTechnologies: [],
    commonFeatures: [
      'User onboarding and authentication',
      'Push notifications',
      'Offline functionality',
      'Camera and photo integration',
      'Location services',
      'Social sharing',
      'In-app purchases',
      'Biometric authentication',
      'Dark/light theme support',
      'App store optimization'
    ],
    promptTemplate: `I want to develop a {projectType} called "{projectName}" with this concept: {projectIdea}

**Mobile App Requirements:**

**Core Functionality:**
{projectDescription}

**Technical Specifications:**
- Target Platform: {platform}
- Technology Stack: {technologyStack}
- Complexity Level: {complexity}
- Key Features: {features}

**Mobile-Specific Implementation Plan:**

1. **Project Setup & Configuration:**
   - Initialize mobile project with chosen framework
   - Configure development environment (iOS/Android SDKs)
   - Set up device testing and simulators
   - Configure app icons, splash screens, and metadata

2. **UI/UX Development:**
   - Design mobile-first user interface
   - Implement navigation patterns (tabs, stack, drawer)
   - Create responsive layouts for different screen sizes
   - Add platform-specific design elements

3. **Core Features Implementation:**
   - Implement user authentication and session management
   - Add data persistence and offline capabilities
   - Integrate device APIs (camera, location, sensors)
   - Implement push notification system

4. **Backend Integration:**
   - Set up API communication layer
   - Implement data synchronization
   - Add error handling and retry logic
   - Configure analytics and crash reporting

5. **Testing & Optimization:**
   - Test on multiple devices and OS versions
   - Optimize performance and memory usage
   - Implement automated testing for critical flows
   - Conduct user acceptance testing

6. **Deployment Preparation:**
   - Configure app store metadata and assets
   - Set up code signing and certificates
   - Prepare for app store submission
   - Plan release and update strategy

**Augment Agent Mobile Development Guidelines:**
- Use codebase context to understand mobile project structure
- Consider platform-specific best practices and guidelines
- Implement proper state management for mobile apps
- Focus on performance optimization and battery efficiency
- Ensure accessibility compliance for mobile interfaces
- Plan for different device capabilities and limitations

**Additional Requirements:**
{additionalRequirements}

Please create a detailed mobile development plan and start with the project setup phase.`
  },

  'api-backend': {
    type: 'api-backend',
    name: 'API/Backend Service',
    description: 'RESTful APIs and backend services',
    defaultTechnologies: [],
    commonFeatures: [
      'RESTful API endpoints',
      'Authentication and authorization',
      'Database operations (CRUD)',
      'Input validation and sanitization',
      'Error handling and logging',
      'Rate limiting and throttling',
      'API documentation',
      'Caching mechanisms',
      'Background job processing',
      'Monitoring and health checks'
    ],
    promptTemplate: `I need to build a robust {projectType} called "{projectName}" for: {projectIdea}

**Backend Service Requirements:**

**Core Functionality:**
{projectDescription}

**Technical Specifications:**
- Platform: {platform}
- Technology Stack: {technologyStack}
- Complexity Level: {complexity}
- Key Features: {features}

**Backend Development Roadmap:**

1. **API Architecture & Setup:**
   - Design RESTful API structure and endpoints
   - Set up project with chosen backend framework
   - Configure development environment and tools
   - Implement basic server setup and middleware

2. **Database Layer:**
   - Design normalized database schema
   - Set up database connections and pooling
   - Implement data models and relationships
   - Create migration scripts and seed data

3. **Authentication & Security:**
   - Implement user authentication system
   - Set up authorization and role-based access
   - Add input validation and sanitization
   - Configure security headers and CORS

4. **Core API Development:**
   - Implement CRUD operations for all entities
   - Add business logic and data processing
   - Create comprehensive error handling
   - Implement logging and monitoring

5. **Performance & Scalability:**
   - Add caching layers (Redis, in-memory)
   - Implement rate limiting and throttling
   - Optimize database queries and indexing
   - Set up background job processing

6. **Documentation & Testing:**
   - Generate comprehensive API documentation
   - Write unit tests for all endpoints
   - Implement integration testing
   - Add load testing and performance benchmarks

7. **Deployment & DevOps:**
   - Configure production environment
   - Set up CI/CD pipeline
   - Implement monitoring and alerting
   - Deploy to cloud infrastructure

**Augment Agent Backend Best Practices:**
- Use codebase context to understand existing API patterns
- Implement proper error handling and status codes
- Follow RESTful conventions and API design principles
- Ensure database queries are optimized and secure
- Add comprehensive logging for debugging and monitoring
- Plan for horizontal scaling and load distribution

**Additional Requirements:**
{additionalRequirements}

Please analyze this backend project and create a detailed implementation plan with specific development tasks.`
  },

  'desktop-application': {
    type: 'desktop-application',
    name: 'Desktop Application',
    description: 'Cross-platform desktop applications',
    defaultTechnologies: [],
    commonFeatures: [
      'Native OS integration',
      'File system access',
      'System tray/menu bar integration',
      'Auto-updater functionality',
      'Offline functionality',
      'Multi-window support',
      'Keyboard shortcuts',
      'Drag and drop support',
      'System notifications',
      'Cross-platform compatibility'
    ],
    promptTemplate: `I want to create a {projectType} called "{projectName}" with this concept: {projectIdea}

**Desktop Application Requirements:**

**Core Functionality:**
{projectDescription}

**Technical Specifications:**
- Target Platform: {platform}
- Technology Stack: {technologyStack}
- Complexity Level: {complexity}
- Key Features: {features}

**Desktop Development Plan:**

1. **Application Setup & Architecture:**
   - Initialize desktop project with chosen framework
   - Configure build tools and development environment
   - Set up application structure and main window
   - Configure app metadata and icons

2. **UI/UX Development:**
   - Design native-feeling user interface
   - Implement responsive layouts for different screen sizes
   - Add platform-specific UI elements and behaviors
   - Create consistent theming and styling

3. **Core Features Implementation:**
   - Implement main application functionality
   - Add file system integration and data persistence
   - Create menu systems and keyboard shortcuts
   - Implement inter-window communication

4. **System Integration:**
   - Add OS-specific features and integrations
   - Implement system tray/menu bar functionality
   - Configure auto-updater and installation
   - Add system notifications and alerts

5. **Testing & Optimization:**
   - Test on multiple operating systems
   - Optimize performance and memory usage
   - Implement automated testing for core features
   - Test installation and update processes

6. **Distribution & Deployment:**
   - Configure code signing and certificates
   - Set up build pipeline for multiple platforms
   - Prepare distribution packages
   - Plan release and update strategy

**Augment Agent Desktop Guidelines:**
- Focus on native OS integration and user experience
- Implement proper state management for desktop apps
- Consider platform-specific design guidelines
- Plan for offline functionality and data synchronization
- Ensure proper resource management and performance

**Additional Requirements:**
{additionalRequirements}

Please create a detailed desktop development plan and start with the application setup.`
  },

  'data-analysis': {
    type: 'data-analysis',
    name: 'Data Analysis Tool',
    description: 'Data processing and analysis applications',
    defaultTechnologies: [],
    commonFeatures: [
      'Data import/export (CSV, JSON, Excel)',
      'Data cleaning and preprocessing',
      'Statistical analysis and calculations',
      'Data visualization and charts',
      'Interactive dashboards',
      'Report generation',
      'Data filtering and querying',
      'Machine learning integration',
      'Real-time data processing',
      'Automated data pipelines'
    ],
    promptTemplate: `I need to build a {projectType} called "{projectName}" for: {projectIdea}

**Data Analysis Requirements:**

**Core Functionality:**
{projectDescription}

**Technical Specifications:**
- Platform: {platform}
- Technology Stack: {technologyStack}
- Complexity Level: {complexity}
- Key Features: {features}

**Data Analysis Development Roadmap:**

1. **Project Setup & Data Architecture:**
   - Set up development environment with data science tools
   - Design data storage and processing architecture
   - Configure data pipeline and workflow tools
   - Set up version control for data and code

2. **Data Ingestion & Processing:**
   - Implement data import from various sources
   - Create data cleaning and preprocessing pipelines
   - Add data validation and quality checks
   - Set up automated data processing workflows

3. **Analysis & Computation:**
   - Implement statistical analysis functions
   - Add data transformation and aggregation
   - Create custom analysis algorithms
   - Integrate machine learning capabilities

4. **Visualization & Reporting:**
   - Build interactive data visualizations
   - Create customizable dashboards
   - Implement report generation features
   - Add export functionality for results

5. **User Interface & Experience:**
   - Design intuitive data exploration interface
   - Add interactive filtering and querying
   - Implement real-time data updates
   - Create user-friendly analysis workflows

6. **Performance & Scalability:**
   - Optimize data processing performance
   - Implement caching and data indexing
   - Add support for large datasets
   - Configure distributed processing if needed

**Augment Agent Data Science Best Practices:**
- Use appropriate data structures and algorithms
- Implement proper error handling for data operations
- Add comprehensive logging and monitoring
- Follow data science workflow best practices
- Ensure reproducible analysis and results
- Plan for data security and privacy compliance

**Additional Requirements:**
{additionalRequirements}

Please analyze this data project and create a detailed implementation plan.`
  },

  'machine-learning': {
    type: 'machine-learning',
    name: 'Machine Learning Project',
    description: 'AI/ML applications and models',
    defaultTechnologies: [],
    commonFeatures: [
      'Data preprocessing and cleaning',
      'Model training and evaluation',
      'Feature engineering',
      'Model deployment',
      'Performance monitoring',
      'A/B testing framework',
      'Data pipeline automation',
      'Model versioning',
      'Inference API',
      'Real-time predictions'
    ],
    promptTemplate: `I want to build a machine learning project called "{projectName}" with the following concept: {projectIdea}

**Project Requirements:**
{projectDescription}

**Technical Specifications:**
- Platform: {platform}
- Technology Stack: {technologyStack}
- Complexity Level: {complexity}
- Key Features: {features}

**Implementation Approach:**
1. **Data Pipeline Setup:**
   - Design data collection and preprocessing pipeline
   - Implement data validation and quality checks
   - Set up feature engineering workflows
   - Create data versioning system

2. **Model Development:**
   - Implement model training pipeline
   - Set up experiment tracking and model versioning
   - Create model evaluation and validation framework
   - Implement hyperparameter tuning

3. **Model Deployment:**
   - Create model serving infrastructure
   - Implement inference API endpoints
   - Set up monitoring and alerting
   - Plan for model updates and rollbacks

**Additional Requirements:**
{additionalRequirements}

Please create a comprehensive ML project plan.`
  },

  'devops-infrastructure': {
    type: 'devops-infrastructure',
    name: 'DevOps Infrastructure',
    description: 'Infrastructure automation and deployment systems',
    defaultTechnologies: [],
    commonFeatures: [
      'CI/CD pipelines',
      'Infrastructure as Code',
      'Container orchestration',
      'Monitoring and alerting',
      'Log aggregation',
      'Security scanning',
      'Backup and disaster recovery',
      'Auto-scaling',
      'Load balancing',
      'Service mesh'
    ],
    promptTemplate: `I want to build a DevOps infrastructure project called "{projectName}" with the following concept: {projectIdea}

**Project Requirements:**
{projectDescription}

**Technical Specifications:**
- Platform: {platform}
- Technology Stack: {technologyStack}
- Complexity Level: {complexity}
- Key Features: {features}

**Implementation Approach:**
1. **Infrastructure Setup:**
   - Design cloud architecture
   - Implement Infrastructure as Code
   - Set up networking and security
   - Configure monitoring and logging

2. **CI/CD Pipeline:**
   - Create automated build and test pipelines
   - Implement deployment automation
   - Set up environment management
   - Configure rollback mechanisms

3. **Operations:**
   - Implement monitoring and alerting
   - Set up log aggregation and analysis
   - Create backup and disaster recovery
   - Plan for scaling and optimization

**Additional Requirements:**
{additionalRequirements}

Please create a detailed DevOps infrastructure plan.`
  },

  'chrome-extension': {
    type: 'chrome-extension',
    name: 'Chrome Extension',
    description: 'Browser extensions for Chrome and Chromium browsers',
    defaultTechnologies: [],
    commonFeatures: [
      'Content script injection',
      'Background service worker',
      'Popup interface',
      'Options page',
      'Context menus',
      'Browser API integration',
      'Local storage',
      'Cross-origin requests',
      'Notifications',
      'Tab management'
    ],
    promptTemplate: `I want to build a Chrome extension called "{projectName}" with the following concept: {projectIdea}

**Project Requirements:**
{projectDescription}

**Technical Specifications:**
- Platform: {platform}
- Technology Stack: {technologyStack}
- Complexity Level: {complexity}
- Key Features: {features}

**Implementation Approach:**
1. **Extension Setup:**
   - Create manifest.json configuration
   - Set up project structure
   - Configure permissions and APIs
   - Implement background service worker

2. **User Interface:**
   - Design and implement popup interface
   - Create options/settings page
   - Implement content scripts
   - Add context menu integration

3. **Functionality:**
   - Implement core extension features
   - Add browser API integrations
   - Set up data storage and sync
   - Create user preferences system

**Additional Requirements:**
{additionalRequirements}

Please create a detailed Chrome extension development plan.`
  },

  'cli-tool': {
    type: 'cli-tool',
    name: 'CLI Tool',
    description: 'Command-line interface applications and utilities',
    defaultTechnologies: [],
    commonFeatures: [
      'Command parsing',
      'Interactive prompts',
      'File system operations',
      'Configuration management',
      'Progress indicators',
      'Error handling',
      'Help documentation',
      'Plugin system',
      'Auto-completion',
      'Logging and debugging'
    ],
    promptTemplate: `I want to build a CLI tool called "{projectName}" with the following concept: {projectIdea}

**Project Requirements:**
{projectDescription}

**Technical Specifications:**
- Platform: {platform}
- Technology Stack: {technologyStack}
- Complexity Level: {complexity}
- Key Features: {features}

**Implementation Approach:**
1. **CLI Framework Setup:**
   - Set up command parsing and routing
   - Implement help system and documentation
   - Create configuration management
   - Add input validation and error handling

2. **Core Functionality:**
   - Implement main command features
   - Add interactive prompts and menus
   - Create file system operations
   - Implement progress indicators

3. **User Experience:**
   - Add auto-completion support
   - Implement logging and debugging
   - Create installation and update system
   - Add plugin/extension support

**Additional Requirements:**
{additionalRequirements}

Please create a detailed CLI tool development plan.`
  },

  'library-package': {
    type: 'library-package',
    name: 'Library/Package',
    description: 'Reusable libraries and packages for developers',
    defaultTechnologies: [],
    commonFeatures: [
      'Clean API design',
      'Comprehensive documentation',
      'Unit testing',
      'Type definitions',
      'Build system',
      'Package publishing',
      'Version management',
      'Examples and demos',
      'Performance optimization',
      'Cross-platform support'
    ],
    promptTemplate: `I want to build a library/package called "{projectName}" with the following concept: {projectIdea}

**Project Requirements:**
{projectDescription}

**Technical Specifications:**
- Platform: {platform}
- Technology Stack: {technologyStack}
- Complexity Level: {complexity}
- Key Features: {features}

**Implementation Approach:**
1. **Library Architecture:**
   - Design clean and intuitive API
   - Set up modular code structure
   - Implement core functionality
   - Add type definitions and interfaces

2. **Development Workflow:**
   - Set up build and bundling system
   - Implement comprehensive testing
   - Create documentation and examples
   - Add linting and code quality tools

3. **Distribution:**
   - Configure package publishing
   - Set up version management
   - Create installation guides
   - Implement usage examples and demos

**Additional Requirements:**
{additionalRequirements}

Please create a detailed library development plan.`
  }
};

// Add more templates for other project types
export const getProjectTemplate = (projectType: ProjectType): ProjectTemplate => {
  return PROJECT_TEMPLATES[projectType] || PROJECT_TEMPLATES['web-application'];
};
