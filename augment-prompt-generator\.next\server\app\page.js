(()=>{var e={};e.id=974,e.ids=[974],e.modules={440:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>n});var a=i(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},554:(e,t)=>{"use strict";function i(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return i}})},660:(e,t)=>{"use strict";function i(e){let t=5381;for(let i=0;i<e.length;i++)t=(t<<5)+t+e.charCodeAt(i)|0;return t>>>0}function a(e){return i(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{djb2Hash:function(){return i},hexHash:function(){return a}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},939:()=>{},1135:()=>{},1204:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>s});var a=i(7413),n=i(7463),r=i(5840);function s(){return(0,a.jsx)(n.default,{children:(0,a.jsx)(r.default,{})})}},1437:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return n},extractInterceptionRouteInformation:function(){return s},isInterceptionRouteAppPath:function(){return r}});let a=i(4722),n=["(..)(..)","(.)","(..)","(...)"];function r(e){return void 0!==e.split("/").find(e=>n.find(t=>e.startsWith(t)))}function s(e){let t,i,r;for(let a of e.split("/"))if(i=n.find(e=>a.startsWith(e))){[t,r]=e.split(i,2);break}if(!t||!i||!r)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,a.normalizeAppPath)(t),i){case"(.)":r="/"===t?"/"+r:t+"/"+r;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});r=t.split("/").slice(0,-1).concat(r).join("/");break;case"(...)":r="/"+r;break;case"(..)(..)":let s=t.split("/");if(s.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});r=s.slice(0,-2).concat(r).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:r}}},1455:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6444,23)),Promise.resolve().then(i.t.bind(i,6042,23)),Promise.resolve().then(i.t.bind(i,8170,23)),Promise.resolve().then(i.t.bind(i,9477,23)),Promise.resolve().then(i.t.bind(i,9345,23)),Promise.resolve().then(i.t.bind(i,2089,23)),Promise.resolve().then(i.t.bind(i,6577,23)),Promise.resolve().then(i.t.bind(i,1307,23))},1658:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{fillMetadataSegment:function(){return p},normalizeMetadataPageToRoute:function(){return h},normalizeMetadataRoute:function(){return u}});let a=i(8304),n=function(e){return e&&e.__esModule?e:{default:e}}(i(8671)),r=i(6341),s=i(4396),o=i(660),l=i(4722),c=i(2958),d=i(5499);function m(e){let t=n.default.dirname(e);if(e.endsWith("/sitemap"))return"";let i="";return t.split("/").some(e=>(0,d.isGroupSegment)(e)||(0,d.isParallelRouteSegment)(e))&&(i=(0,o.djb2Hash)(t).toString(36).slice(0,6)),i}function p(e,t,i){let a=(0,l.normalizeAppPath)(e),o=(0,s.getNamedRouteRegex)(a,{prefixRouteKeys:!1}),d=(0,r.interpolateDynamicPath)(a,t,o),{name:p,ext:u}=n.default.parse(i),h=m(n.default.posix.join(e,p)),g=h?`-${h}`:"";return(0,c.normalizePathSep)(n.default.join(d,`${p}${g}${u}`))}function u(e){if(!(0,a.isMetadataPage)(e))return e;let t=e,i="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":i=m(e),!t.endsWith("/route")){let{dir:e,name:a,ext:r}=n.default.parse(t);t=n.default.posix.join(e,`${a}${i?`-${i}`:""}${r}`,"route")}return t}function h(e,t){let i=e.endsWith("/route"),a=i?e.slice(0,-6):e,n=a.endsWith("/sitemap")?".xml":"";return(t?`${a}/[__metadata_id__]`:`${a}${n}`)+(i?"/route":"")}},1968:(e,t)=>{"use strict";function i(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return i}})},2e3:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>s.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=i(5239),n=i(8088),r=i(8170),s=i.n(r),o=i(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);i.d(t,l);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,1204)),"C:\\Users\\<USER>\\Documents\\augment-projects\\test\\augment-prompt-generator\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,4431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\test\\augment-prompt-generator\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\test\\augment-prompt-generator\\src\\app\\page.tsx"],m={require:i,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2437:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return n}});let a=i(5362);function n(e,t){let i=[],n=(0,a.pathToRegexp)(e,i,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),r=(0,a.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(n.source),n.flags):n,i);return(e,a)=>{if("string"!=typeof e)return!1;let n=r(e);if(!n)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of i)"number"==typeof e.name&&delete n.params[e.name];return{...a,...n.params}}}},2770:(e,t,i)=>{Promise.resolve().then(i.bind(i,6821)),Promise.resolve().then(i.bind(i,4790))},2785:(e,t)=>{"use strict";function i(e){let t={};for(let[i,a]of e.entries()){let e=t[i];void 0===e?t[i]=a:Array.isArray(e)?e.push(a):t[i]=[e,a]}return t}function a(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function n(e){let t=new URLSearchParams;for(let[i,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(i,a(e));else t.set(i,a(n));return t}function r(e){for(var t=arguments.length,i=Array(t>1?t-1:0),a=1;a<t;a++)i[a-1]=arguments[a];for(let t of i){for(let i of t.keys())e.delete(i);for(let[i,a]of t.entries())e.append(i,a)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{assign:function(){return r},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return n}})},2958:(e,t)=>{"use strict";function i(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return i}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return n}});let i=/[|\\{}()[\]^$+*?.-]/,a=/[|\\{}()[\]^$+*?.-]/g;function n(e){return i.test(e)?e.replace(a,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3736:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return n}}),i(4827);let a=i(2785);function n(e,t,i){void 0===i&&(i=!0);let n=new URL("http://n"),r=t?new URL(t,n):e.startsWith(".")?new URL("http://n"):n,{pathname:s,searchParams:o,search:l,hash:c,href:d,origin:m}=new URL(e,r);if(m!==n.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:s,query:i?(0,a.searchParamsToUrlQuery)(o):void 0,search:l,hash:c,href:d.slice(m.length)}}},3873:e=>{"use strict";e.exports=require("path")},4396:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{getNamedMiddlewareRegex:function(){return g},getNamedRouteRegex:function(){return h},getRouteRegex:function(){return m},parseParameter:function(){return l}});let a=i(6143),n=i(1437),r=i(3293),s=i(2887),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(o);return t?c(t[2]):c(e)}function c(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let i=e.startsWith("...");return i&&(e=e.slice(3)),{key:e,repeat:i,optional:t}}function d(e,t,i){let a={},l=1,d=[];for(let m of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=n.INTERCEPTION_ROUTE_MARKERS.find(e=>m.startsWith(e)),s=m.match(o);if(e&&s&&s[2]){let{key:t,optional:i,repeat:n}=c(s[2]);a[t]={pos:l++,repeat:n,optional:i},d.push("/"+(0,r.escapeStringRegexp)(e)+"([^/]+?)")}else if(s&&s[2]){let{key:e,repeat:t,optional:n}=c(s[2]);a[e]={pos:l++,repeat:t,optional:n},i&&s[1]&&d.push("/"+(0,r.escapeStringRegexp)(s[1]));let o=t?n?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";i&&s[1]&&(o=o.substring(1)),d.push(o)}else d.push("/"+(0,r.escapeStringRegexp)(m));t&&s&&s[3]&&d.push((0,r.escapeStringRegexp)(s[3]))}return{parameterizedRoute:d.join(""),groups:a}}function m(e,t){let{includeSuffix:i=!1,includePrefix:a=!1,excludeOptionalTrailingSlash:n=!1}=void 0===t?{}:t,{parameterizedRoute:r,groups:s}=d(e,i,a),o=r;return n||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:s}}function p(e){let t,{interceptionMarker:i,getSafeRouteKey:a,segment:n,routeKeys:s,keyPrefix:o,backreferenceDuplicateKeys:l}=e,{key:d,optional:m,repeat:p}=c(n),u=d.replace(/\W/g,"");o&&(u=""+o+u);let h=!1;(0===u.length||u.length>30)&&(h=!0),isNaN(parseInt(u.slice(0,1)))||(h=!0),h&&(u=a());let g=u in s;o?s[u]=""+o+d:s[u]=d;let f=i?(0,r.escapeStringRegexp)(i):"";return t=g&&l?"\\k<"+u+">":p?"(?<"+u+">.+?)":"(?<"+u+">[^/]+?)",m?"(?:/"+f+t+")?":"/"+f+t}function u(e,t,i,l,c){let d,m=(d=0,()=>{let e="",t=++d;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),u={},h=[];for(let d of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=n.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)),s=d.match(o);if(e&&s&&s[2])h.push(p({getSafeRouteKey:m,interceptionMarker:s[1],segment:s[2],routeKeys:u,keyPrefix:t?a.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:c}));else if(s&&s[2]){l&&s[1]&&h.push("/"+(0,r.escapeStringRegexp)(s[1]));let e=p({getSafeRouteKey:m,segment:s[2],routeKeys:u,keyPrefix:t?a.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:c});l&&s[1]&&(e=e.substring(1)),h.push(e)}else h.push("/"+(0,r.escapeStringRegexp)(d));i&&s&&s[3]&&h.push((0,r.escapeStringRegexp)(s[3]))}return{namedParameterizedRoute:h.join(""),routeKeys:u}}function h(e,t){var i,a,n;let r=u(e,t.prefixRouteKeys,null!=(i=t.includeSuffix)&&i,null!=(a=t.includePrefix)&&a,null!=(n=t.backreferenceDuplicateKeys)&&n),s=r.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(s+="(?:/)?"),{...m(e,t),namedRegex:"^"+s+"$",routeKeys:r.routeKeys}}function g(e,t){let{parameterizedRoute:i}=d(e,!1,!1),{catchAll:a=!0}=t;if("/"===i)return{namedRegex:"^/"+(a?".*":"")+"$"};let{namedParameterizedRoute:n}=u(e,!1,!1,!1,!1);return{namedRegex:"^"+n+(a?"(?:(/.*)?)":"")+"$"}}},4431:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>c,metadata:()=>l});var a=i(7413),n=i(2376),r=i.n(n),s=i(8726),o=i.n(s);i(1135);let l={title:"AI Prompt Generator for Augment Agent - Complete User Guide",description:"Professional AI-powered prompt generation with DeepSeek V3 for Augment Agent. Comprehensive documentation and user guide included. 100% AI generated, zero templates, maximum intelligence."};function c({children:e}){return(0,a.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,a.jsx)("body",{className:`${r().variable} ${o().variable} antialiased`,suppressHydrationWarning:!0,children:e})})}},4484:(e,t,i)=>{Promise.resolve().then(i.bind(i,7463)),Promise.resolve().then(i.bind(i,5840))},4722:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{normalizeAppPath:function(){return r},normalizeRscURL:function(){return s}});let a=i(5531),n=i(5499);function r(e){return(0,a.ensureLeadingSlash)(e.split("/").reduce((e,t,i,a)=>!t||(0,n.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&i===a.length-1?e:e+"/"+t,""))}function s(e){return e.replace(/\.rsc($|\?)/,"$1")}},4777:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return o}});let a=i(687),n=i(1215),r=i(9294),s=i(1968);function o(e){let{moduleIds:t}=e,i=r.workAsyncStorage.getStore();if(void 0===i)return null;let o=[];if(i.reactLoadableManifest&&t){let e=i.reactLoadableManifest;for(let i of t){if(!e[i])continue;let t=e[i].files;o.push(...t)}}return 0===o.length?null:(0,a.jsx)(a.Fragment,{children:o.map(e=>{let t=i.assetPrefix+"/_next/"+(0,s.encodeURIPath)(e);return e.endsWith(".css")?(0,a.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,n.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},4790:(e,t,i)=>{"use strict";let a;i.d(t,{default:()=>od});var n,r,s=i(687),o=i(3210);let l={frontend:[{category:"frontend",name:"React",description:"Popular JavaScript library for building user interfaces"},{category:"frontend",name:"Next.js",description:"React framework with SSR and static generation"},{category:"frontend",name:"Vue.js",description:"Progressive JavaScript framework"},{category:"frontend",name:"Angular",description:"TypeScript-based web application framework"},{category:"frontend",name:"Svelte",description:"Compile-time optimized framework"},{category:"frontend",name:"Vanilla JavaScript",description:"Pure JavaScript without frameworks"},{category:"frontend",name:"TypeScript",description:"Typed superset of JavaScript"}],backend:[{category:"backend",name:"Node.js",description:"JavaScript runtime for server-side development"},{category:"backend",name:"Express.js",description:"Fast, unopinionated web framework for Node.js"},{category:"backend",name:"Python/Django",description:"High-level Python web framework"},{category:"backend",name:"Python/Flask",description:"Lightweight Python web framework"},{category:"backend",name:"Python/FastAPI",description:"Modern, fast Python API framework"},{category:"backend",name:"Go",description:"Efficient, compiled programming language"},{category:"backend",name:"Rust",description:"Systems programming language focused on safety"},{category:"backend",name:"Java/Spring Boot",description:"Enterprise Java framework"},{category:"backend",name:"C#/.NET",description:"Microsoft's cross-platform framework"}],database:[{category:"database",name:"PostgreSQL",description:"Advanced open-source relational database"},{category:"database",name:"MongoDB",description:"NoSQL document database"},{category:"database",name:"SQLite",description:"Lightweight embedded database"},{category:"database",name:"MySQL",description:"Popular open-source relational database"},{category:"database",name:"Redis",description:"In-memory data structure store"},{category:"database",name:"Supabase",description:"Open-source Firebase alternative"},{category:"database",name:"Firebase",description:"Google's mobile and web development platform"}],styling:[{category:"styling",name:"Tailwind CSS",description:"Utility-first CSS framework"},{category:"styling",name:"CSS Modules",description:"Localized CSS"},{category:"styling",name:"Styled Components",description:"CSS-in-JS library"},{category:"styling",name:"SCSS/Sass",description:"CSS preprocessor"},{category:"styling",name:"Material-UI",description:"React components implementing Material Design"},{category:"styling",name:"Chakra UI",description:"Simple, modular and accessible component library"}],"mobile-framework":[{category:"mobile-framework",name:"React Native",description:"Cross-platform mobile development with React"},{category:"mobile-framework",name:"Flutter",description:"Google's UI toolkit for mobile, web, and desktop"},{category:"mobile-framework",name:"Swift",description:"Native iOS development language"},{category:"mobile-framework",name:"Kotlin",description:"Modern programming language for Android"},{category:"mobile-framework",name:"Expo",description:"Platform for universal React applications"}],"desktop-framework":[{category:"desktop-framework",name:"Electron",description:"Build desktop apps with web technologies"},{category:"desktop-framework",name:"Tauri",description:"Rust-based desktop app framework"},{category:"desktop-framework",name:"Qt",description:"Cross-platform application framework"},{category:"desktop-framework",name:"WPF",description:"Windows Presentation Foundation"},{category:"desktop-framework",name:"JavaFX",description:"Java platform for desktop applications"}],deployment:[{category:"deployment",name:"Vercel",description:"Platform for frontend frameworks and static sites"},{category:"deployment",name:"Netlify",description:"Platform for modern web projects"},{category:"deployment",name:"AWS",description:"Amazon Web Services cloud platform"},{category:"deployment",name:"Google Cloud",description:"Google's cloud computing services"},{category:"deployment",name:"Docker",description:"Containerization platform"},{category:"deployment",name:"Kubernetes",description:"Container orchestration platform"}],testing:[{category:"testing",name:"Jest",description:"JavaScript testing framework"},{category:"testing",name:"Vitest",description:"Fast unit test framework"},{category:"testing",name:"Cypress",description:"End-to-end testing framework"},{category:"testing",name:"Playwright",description:"Cross-browser automation library"},{category:"testing",name:"React Testing Library",description:"Testing utilities for React components"}],authentication:[{category:"authentication",name:"Auth0",description:"Identity platform for developers"},{category:"authentication",name:"Firebase Auth",description:"Google's authentication service"},{category:"authentication",name:"NextAuth.js",description:"Authentication for Next.js"},{category:"authentication",name:"Supabase Auth",description:"Open-source authentication"},{category:"authentication",name:"JWT",description:"JSON Web Tokens for stateless authentication"}],"state-management":[{category:"state-management",name:"Redux Toolkit",description:"Modern Redux with simplified API"},{category:"state-management",name:"Zustand",description:"Lightweight state management solution"},{category:"state-management",name:"Recoil",description:"Experimental state management for React"},{category:"state-management",name:"MobX",description:"Reactive state management through observables"},{category:"state-management",name:"Context API",description:"React's built-in state management"},{category:"state-management",name:"Valtio",description:"Proxy-based state management"}],"api-tools":[{category:"api-tools",name:"GraphQL",description:"Query language for APIs"},{category:"api-tools",name:"Apollo Client",description:"Comprehensive GraphQL client"},{category:"api-tools",name:"React Query",description:"Data fetching and caching library"},{category:"api-tools",name:"SWR",description:"Data fetching with caching and revalidation"},{category:"api-tools",name:"Axios",description:"Promise-based HTTP client"},{category:"api-tools",name:"Fetch API",description:"Native browser API for HTTP requests"},{category:"api-tools",name:"tRPC",description:"End-to-end typesafe APIs"}],monitoring:[{category:"monitoring",name:"Sentry",description:"Error tracking and performance monitoring"},{category:"monitoring",name:"LogRocket",description:"Session replay and logging"},{category:"monitoring",name:"New Relic",description:"Application performance monitoring"},{category:"monitoring",name:"Datadog",description:"Infrastructure and application monitoring"},{category:"monitoring",name:"Prometheus",description:"Open-source monitoring and alerting"},{category:"monitoring",name:"Grafana",description:"Analytics and monitoring dashboards"}],"ci-cd":[{category:"ci-cd",name:"GitHub Actions",description:"CI/CD platform integrated with GitHub"},{category:"ci-cd",name:"GitLab CI",description:"Built-in CI/CD for GitLab"},{category:"ci-cd",name:"Jenkins",description:"Open-source automation server"},{category:"ci-cd",name:"CircleCI",description:"Cloud-based CI/CD platform"},{category:"ci-cd",name:"Azure DevOps",description:"Microsoft's DevOps platform"},{category:"ci-cd",name:"Travis CI",description:"Hosted CI service for GitHub projects"}],"version-control":[{category:"version-control",name:"Git",description:"Distributed version control system"},{category:"version-control",name:"GitHub",description:"Git hosting with collaboration features"},{category:"version-control",name:"GitLab",description:"DevOps platform with Git repository"},{category:"version-control",name:"Bitbucket",description:"Git solution for teams"},{category:"version-control",name:"Azure Repos",description:"Git repositories in Azure DevOps"}],"package-managers":[{category:"package-managers",name:"npm",description:"Node.js package manager"},{category:"package-managers",name:"Yarn",description:"Fast, reliable package manager"},{category:"package-managers",name:"pnpm",description:"Efficient package manager with shared dependencies"},{category:"package-managers",name:"Bun",description:"Fast all-in-one JavaScript runtime and package manager"}],"build-tools":[{category:"build-tools",name:"Webpack",description:"Module bundler for JavaScript applications"},{category:"build-tools",name:"Vite",description:"Fast build tool for modern web projects"},{category:"build-tools",name:"Rollup",description:"Module bundler for JavaScript libraries"},{category:"build-tools",name:"Parcel",description:"Zero-configuration build tool"},{category:"build-tools",name:"esbuild",description:"Extremely fast JavaScript bundler"},{category:"build-tools",name:"Turbopack",description:"Incremental bundler optimized for JavaScript and TypeScript"}],"code-quality":[{category:"code-quality",name:"ESLint",description:"JavaScript linting utility"},{category:"code-quality",name:"Prettier",description:"Code formatter"},{category:"code-quality",name:"Husky",description:"Git hooks for code quality"},{category:"code-quality",name:"lint-staged",description:"Run linters on staged files"},{category:"code-quality",name:"SonarQube",description:"Code quality and security analysis"},{category:"code-quality",name:"CodeClimate",description:"Automated code review and quality analytics"}],documentation:[{category:"documentation",name:"Storybook",description:"Tool for building UI components in isolation"},{category:"documentation",name:"Docusaurus",description:"Documentation website generator"},{category:"documentation",name:"GitBook",description:"Documentation platform"},{category:"documentation",name:"Notion",description:"All-in-one workspace for documentation"},{category:"documentation",name:"Confluence",description:"Team collaboration and documentation"},{category:"documentation",name:"JSDoc",description:"API documentation generator for JavaScript"}]},c={"deepseek-chat":{id:"deepseek/deepseek-chat-v3-0324:free",name:"DeepSeek V3",provider:"DeepSeek",description:"Advanced reasoning and code generation with free access",maxTokens:8192,costPer1kTokens:0,strengths:["Code generation","Technical documentation","Complex reasoning","Mathematical thinking"],bestFor:["Development projects","Technical specifications","API documentation","Algorithm design"]},"claude-sonnet":{id:"anthropic/claude-3-5-sonnet",name:"Claude Sonnet 4",provider:"Anthropic",description:"Excellent for creative and analytical tasks with structured thinking",maxTokens:8192,costPer1kTokens:3,strengths:["Creative writing","Analysis","Structured thinking","User experience design"],bestFor:["Content creation","Business planning","User experience","Complex project planning"]},"gpt-4o":{id:"openai/gpt-4o",name:"ChatGPT (GPT-4o)",provider:"OpenAI",description:"Versatile multimodal AI with strong general capabilities",maxTokens:8192,costPer1kTokens:5,strengths:["General intelligence","Multimodal","Versatile","Creative solutions"],bestFor:["General projects","Multimodal apps","Versatile solutions","Creative development"]},"gemini-pro":{id:"google/gemini-pro",name:"Google Gemini Pro",provider:"Google",description:"Large context window and multimodal capabilities",maxTokens:32768,costPer1kTokens:2.5,strengths:["Large context","Multimodal","Data analysis","Research synthesis"],bestFor:["Large documents","Data projects","Research tools","Complex analysis"]},"cursor-small":{id:"cursor/cursor-small",name:"Cursor AI",provider:"Cursor",description:"IDE-integrated AI assistant for development workflows",maxTokens:4096,costPer1kTokens:1,strengths:["IDE integration","Code completion","Workflow optimization","Real-time assistance"],bestFor:["IDE workflows","Code completion","Development assistance","Project setup"]},"perplexity-small":{id:"perplexity/llama-3.1-sonar-small-128k-online",name:"Perplexity AI",provider:"Perplexity",description:"Research-focused AI with real-time web access",maxTokens:8192,costPer1kTokens:1.5,strengths:["Research","Real-time data","Information synthesis","Fact-checking"],bestFor:["Research projects","Data gathering","Market analysis","Technical research"]},"cohere-command":{id:"cohere/command-r-plus",name:"Cohere Command R+",provider:"Cohere",description:"Enterprise-focused AI with strong reasoning capabilities",maxTokens:8192,costPer1kTokens:2,strengths:["Enterprise solutions","Business logic","Structured output","Professional writing"],bestFor:["Enterprise projects","Business applications","Professional documentation","Workflow automation"]},"huggingface-mixtral":{id:"mistralai/mixtral-8x7b-instruct",name:"Mixtral 8x7B (HuggingFace)",provider:"Hugging Face",description:"Open-source mixture of experts model",maxTokens:8192,costPer1kTokens:.5,strengths:["Open source","Cost effective","Multilingual","Code generation"],bestFor:["Open source projects","Cost-sensitive applications","Multilingual support","Experimentation"]}};class d{constructor(e="",t="https://openrouter.ai/api/v1"){this.apiKey=e,this.baseUrl=t}async generatePrompt(e,t){if(!this.apiKey)return this.generatePromptFallback(e,t);let i=this.buildSystemPrompt(t),a=this.buildUserPrompt(e,t);try{let e=await fetch(`${this.baseUrl}/chat/completions`,{method:"POST",headers:{Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json","HTTP-Referer":window.location.origin,"X-Title":"AI Prompt Generator"},body:JSON.stringify({model:t.model,messages:[{role:"system",content:i},{role:"user",content:a}],temperature:.7,max_tokens:2048,top_p:.9,frequency_penalty:.1,presence_penalty:.1})});if(!e.ok)throw Error(`AI API Error: ${e.status} ${e.statusText}`);let n=await e.json(),r=n.choices[0]?.message?.content||"",s=n.usage?.total_tokens||0,o=c[t.model]||c["deepseek-chat"],l=s/1e3*o.costPer1kTokens;return{prompt:r,metadata:{model:t.model,tokensUsed:s,cost:l,optimizationLevel:t.optimizationLevel,timestamp:new Date().toISOString()}}}catch(i){return console.error("AI Service Error:",i),this.generatePromptFallback(e,t)}}async generatePromptFallback(e,t){try{let i=await fetch("/api/generate-prompt",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!i.ok)throw Error("Failed to generate AI prompt");let a=await i.json(),n=a.metadata?.tokensUsed||0,r=c[t.model]||c["deepseek-chat"],s=n/1e3*r.costPer1kTokens;return{prompt:a.prompt,metadata:{model:t.model,tokensUsed:n,cost:s,optimizationLevel:t.optimizationLevel,timestamp:new Date().toISOString()}}}catch(t){console.error("Fallback API Error:",t);let e=t instanceof Error?t.message:"Unknown error occurred";throw Error(`Failed to generate prompt: ${e}`)}}buildSystemPrompt(e){let t=this.getAIToolName(e.model),i=this.getAICapabilities(e.model),a=`You are an expert AI prompt engineer specializing in creating optimized prompts for ${t}, a powerful AI coding assistant.

Your task is to transform user project descriptions into highly effective, detailed prompts that leverage ${t}'s capabilities:
${i.map(e=>`- ${e}`).join("\n")}`;return`${a}

OPTIMIZATION LEVEL: ${e.optimizationLevel.toUpperCase()}
${({basic:"Create a clear, structured prompt with essential project details.",enhanced:"Create a comprehensive prompt with detailed specifications, best practices, and implementation guidance.",expert:"Create an expert-level prompt with advanced architectural considerations, performance optimization, security requirements, and scalability planning."})[e.optimizationLevel]}

TARGET AUDIENCE: ${e.targetAudience.toUpperCase()}
${({developer:"Focus on technical implementation details, code architecture, and development best practices.",business:"Emphasize business value, user experience, and project outcomes.",technical:"Include technical specifications, system requirements, and integration details.",general:"Balance technical and business considerations for a general audience."})[e.targetAudience]}

${e.includeExamples?"Include relevant examples and code snippets where appropriate.":""}
${e.includeConstraints?"Specify technical constraints, limitations, and requirements.":""}
${e.includeMetrics?"Include success metrics, KPIs, and measurable outcomes.":""}

Generate a prompt that will help ${this.getAIToolName(e.model)} create exceptional results for this project.`}getAIToolName(e){return e.includes("deepseek")?"DeepSeek AI":e.includes("claude")?"Claude Sonnet 4":e.includes("gpt")||e.includes("openai")?"ChatGPT":e.includes("gemini")?"Google Gemini":e.includes("cursor")?"Cursor AI":e.includes("perplexity")?"Perplexity AI":e.includes("cohere")?"Cohere Command":"AI Assistant"}getAICapabilities(e){let t=["Advanced code generation and analysis","Multi-language programming support","Best practices and design patterns","Code optimization and refactoring"];return e.includes("deepseek")?[...t,"Deep reasoning and problem-solving","Mathematical and algorithmic thinking","Technical documentation generation"]:e.includes("claude")?[...t,"Structured thinking and analysis","Creative problem-solving approaches","Comprehensive project planning","User experience considerations"]:e.includes("gpt")||e.includes("openai")?[...t,"Versatile general intelligence","Creative and innovative solutions","Integration with development tools","Comprehensive documentation"]:e.includes("gemini")?[...t,"Large context understanding","Multimodal capabilities","Data analysis and insights","Research and information synthesis"]:e.includes("cursor")?[...t,"IDE integration and workflow optimization","Real-time code suggestions","Project-aware assistance","Development environment setup"]:t}buildUserPrompt(e,t){let i=this.getAIToolName(t.model);return`Please create an optimized prompt for ${i} based on this project:

PROJECT NAME: ${e.projectName}
PROJECT TYPE: ${e.projectType}
TARGET PLATFORM: ${e.platform}
COMPLEXITY: ${e.complexity}

PROJECT DESCRIPTION:
${e.projectIdea}

SELECTED TECHNOLOGIES:
${e.technologies.map(e=>`- ${e.name}: ${e.description}`).join("\n")||"No specific technologies selected"}

ADDITIONAL REQUIREMENTS:
${e.additionalRequirements||"None specified"}

Please generate a comprehensive, actionable prompt that will help ${i} deliver exceptional results for this ${e.projectType} project.`}getModelRecommendation(e){let{projectType:t,complexity:i,technologies:a}=e;return t.includes("machine-learning")||t.includes("data-analysis")?"deepseek-chat":"advanced"===i||a.length>5?"gemini-pro":t.includes("web-application")||t.includes("mobile")?"claude-sonnet":"desktop-application"===t?"cursor-small":"api-backend"===t?"gpt-4o":t.includes("research")||e.projectIdea.toLowerCase().includes("research")?"perplexity-small":e.projectIdea.toLowerCase().includes("enterprise")?"cohere-command":"deepseek-chat"}estimateCost(e,t){let i=Object.values(c).find(e=>e.id===t);return i?e/1e3*i.costPer1kTokens:0}}let m=new d,p=()=>{let e=(0,o.useRef)(null),t=(0,o.useRef)(null),i=(0,o.useRef)([]),a=(0,o.useRef)({x:0,y:0}),[n,r]=(0,o.useState)({width:0,height:0}),l=(e,t)=>{let a=[],n=Math.floor(e*t/6e3);for(let i=0;i<n;i++)a.push({x:Math.random()*e,y:Math.random()*t,vx:(Math.random()-.5)*.8,vy:(Math.random()-.5)*.8,radius:3*Math.random()+.3,opacity:.9*Math.random()+.1,twinkleSpeed:.03*Math.random()+.005,twinklePhase:Math.random()*Math.PI*2});i.current=a},c=(0,o.useCallback)(()=>{if(e.current){let{innerWidth:t,innerHeight:i}=window;r({width:t,height:i}),e.current.width=t,e.current.height=i,l(t,i)}},[]),d=e=>{a.current={x:e.clientX,y:e.clientY}},m=(0,o.useCallback)(()=>{let r=e.current;if(!r)return;let s=r.getContext("2d");if(!s)return;let{width:o,height:l}=n,c=a.current;if(s.clearRect(0,0,o,l),i.current.forEach(e=>{let t=c.x-e.x,i=c.y-e.y,a=Math.sqrt(t*t+i*i);if(a<200&&a>0){let n=(200-a)/200;a<80?(e.vx-=t/a*n*.003,e.vy-=i/a*n*.003):(e.vx+=t/a*n*.0015,e.vy+=i/a*n*.0015)}e.x+=e.vx,e.y+=e.vy,e.x<0&&(e.x=o),e.x>o&&(e.x=0),e.y<0&&(e.y=l),e.y>l&&(e.y=0),e.vx*=.99,e.vy*=.99,e.twinklePhase+=e.twinkleSpeed;let n=.3*Math.sin(e.twinklePhase)+.7;if(s.beginPath(),s.arc(e.x,e.y,e.radius,0,2*Math.PI),s.fillStyle=`rgba(255, 255, 255, ${e.opacity*n})`,s.fill(),e.radius>1.5&&(s.beginPath(),s.arc(e.x,e.y,3*e.radius,0,2*Math.PI),s.fillStyle=`rgba(255, 255, 255, ${e.opacity*n*.05})`,s.fill(),s.beginPath(),s.arc(e.x,e.y,2*e.radius,0,2*Math.PI),s.fillStyle=`rgba(255, 255, 255, ${e.opacity*n*.1})`,s.fill(),s.beginPath(),s.arc(e.x,e.y,.5*e.radius,0,2*Math.PI),s.fillStyle=`rgba(255, 255, 255, ${Math.min(1,e.opacity*n*1.5)})`,s.fill()),e.radius>2.5){let t=4*e.radius,i=e.opacity*n*.3;s.beginPath(),s.moveTo(e.x-t,e.y),s.lineTo(e.x+t,e.y),s.moveTo(e.x,e.y-t),s.lineTo(e.x,e.y+t),s.strokeStyle=`rgba(255, 255, 255, ${i})`,s.lineWidth=.5,s.stroke()}}),i.current.forEach((e,t)=>{i.current.slice(t+1).forEach(t=>{let i=e.x-t.x,a=e.y-t.y,n=Math.sqrt(i*i+a*a);n<120&&(s.beginPath(),s.moveTo(e.x,e.y),s.lineTo(t.x,t.y),s.strokeStyle=`rgba(255, 255, 255, ${(120-n)/120*.15})`,s.lineWidth=.8,s.stroke())})}),c.x>0&&c.y>0&&i.current.forEach(e=>{let t=c.x-e.x,i=c.y-e.y,a=Math.sqrt(t*t+i*i);a<150&&(s.beginPath(),s.moveTo(c.x,c.y),s.lineTo(e.x,e.y),s.strokeStyle=`rgba(255, 255, 255, ${(150-a)/150*.3})`,s.lineWidth=1,s.stroke())}),c.x>0&&c.y>0){let e=.3*Math.sin(.005*Date.now())+.7;s.beginPath(),s.arc(c.x,c.y,60*e,0,2*Math.PI),s.strokeStyle=`rgba(255, 255, 255, ${.1*e})`,s.lineWidth=2,s.stroke(),s.beginPath(),s.arc(c.x,c.y,35*e,0,2*Math.PI),s.strokeStyle=`rgba(255, 255, 255, ${.2*e})`,s.lineWidth=1.5,s.stroke(),s.beginPath(),s.arc(c.x,c.y,15*e,0,2*Math.PI),s.strokeStyle=`rgba(255, 255, 255, ${.3*e})`,s.lineWidth=1,s.stroke(),s.beginPath(),s.arc(c.x,c.y,3,0,2*Math.PI),s.fillStyle=`rgba(255, 255, 255, ${.8*e})`,s.fill()}t.current=requestAnimationFrame(m)},[n]);return(0,o.useEffect)(()=>(c(),window.addEventListener("resize",c),window.addEventListener("mousemove",d),m(),()=>{window.removeEventListener("resize",c),window.removeEventListener("mousemove",d),t.current&&cancelAnimationFrame(t.current)}),[n.width,n.height,m,c]),(0,s.jsx)("canvas",{ref:e,className:"fixed inset-0 pointer-events-none z-0",style:{background:`
          radial-gradient(circle at 20% 80%, rgba(30, 30, 30, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(40, 40, 40, 0.2) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(20, 20, 20, 0.4) 0%, transparent 50%),
          linear-gradient(135deg, #000000 0%, #0a0a0a 25%, #111111 50%, #0a0a0a 75%, #000000 100%)
        `}})};class u{static getInstance(){return u.instance||(u.instance=new u),u.instance}register(e,t){this.callbacks.set(e,t)}unregister(e){this.callbacks.delete(e)}openDropdown(e){if(this.openDropdownId&&this.openDropdownId!==e){let e=this.callbacks.get(this.openDropdownId);e&&e()}this.openDropdownId=e}closeDropdown(e){this.openDropdownId===e&&(this.openDropdownId=null)}constructor(){this.openDropdownId=null,this.callbacks=new Map}}function h({value:e,onChange:t,options:i,placeholder:a="Select an option",className:n="",disabled:r=!1}){let[l,c]=(0,o.useState)(!1),[d,m]=(0,o.useState)(-1),p=(0,o.useRef)(null),h=(0,o.useId)(),g=u.getInstance(),f=i.find(t=>t.value===e),y=(0,o.useCallback)(()=>{c(!1),g.closeDropdown(h)},[g,h]),x=()=>{g.openDropdown(h),c(!0)};return(0,s.jsxs)("div",{ref:p,className:`relative ${n}`,children:[(0,s.jsxs)("button",{type:"button",onClick:()=>!r&&(l?y():x()),disabled:r,className:`group w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-black text-sm text-white bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl border border-white/10 transition-all duration-300 hover:border-white/20 hover:shadow-lg text-left flex items-center justify-between ${r?"opacity-50 cursor-not-allowed":"cursor-pointer hover:from-gray-700/90 hover:to-gray-800/90"} ${l?"ring-2 ring-white/20 border-white/25 shadow-xl":""}`,style:{boxShadow:l?"0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1)":"0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:[(0,s.jsx)("span",{className:`font-medium ${f?"text-white":"text-gray-400"} transition-colors duration-200`,children:f?f.label:a}),(0,s.jsx)("svg",{className:`w-5 h-5 transition-all duration-300 text-gray-300 group-hover:text-white ${l?"rotate-180 text-white":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",strokeWidth:2.5,children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19 9l-7 7-7-7"})})]}),l&&(0,s.jsx)("div",{className:"absolute z-[9999] w-full mt-2 bg-gradient-to-b from-gray-900/95 to-black/95 backdrop-blur-2xl rounded-xl shadow-2xl border border-white/15 max-h-64 overflow-y-auto animate-fade-in",style:{boxShadow:"0 32px 64px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1)",backgroundColor:"rgba(0, 0, 0, 0.92)"},children:(0,s.jsx)("div",{className:"p-1",children:i.map((i,a)=>(0,s.jsxs)("button",{type:"button",onClick:()=>{t(i.value),y()},onMouseEnter:()=>m(a),className:`group w-full text-left px-4 py-3 text-sm transition-all duration-200 rounded-lg mb-1 last:mb-0 ${a===d||i.value===e?"bg-gradient-to-r from-white/20 to-white/15 text-white shadow-lg border border-white/20":"text-gray-200 hover:bg-gradient-to-r hover:from-white/10 hover:to-white/5 hover:text-white border border-transparent hover:border-white/10"}`,style:{boxShadow:a===d||i.value===e?"0 4px 12px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)":"none"},children:[(0,s.jsx)("div",{className:"font-semibold text-sm leading-tight",children:i.label}),i.description&&(0,s.jsx)("div",{className:"text-xs text-gray-300 mt-1.5 leading-relaxed opacity-90 group-hover:opacity-100 transition-opacity duration-200",children:i.description})]},i.value))})})]})}var g=i(1215);let f=[{id:"github",name:"GitHub",description:"Create repositories and issues from prompts",category:"development",icon:"\uD83D\uDC19",baseUrl:"https://api.github.com",authType:"api_key",isEnabled:!1,config:{}},{id:"gitlab",name:"GitLab",description:"Create projects and merge requests",category:"development",icon:"\uD83E\uDD8A",baseUrl:"https://gitlab.com/api/v4",authType:"api_key",isEnabled:!1,config:{}},{id:"linear",name:"Linear",description:"Create issues and projects",category:"development",icon:"\uD83D\uDCCB",baseUrl:"https://api.linear.app/graphql",authType:"api_key",isEnabled:!1,config:{}},{id:"github-actions",name:"GitHub Actions",description:"Trigger CI/CD workflows",category:"development",icon:"⚡",baseUrl:"https://api.github.com",authType:"api_key",isEnabled:!1,config:{}},{id:"gitlab-ci",name:"GitLab CI/CD",description:"Manage CI/CD pipelines",category:"development",icon:"\uD83D\uDD04",baseUrl:"https://gitlab.com/api/v4",authType:"api_key",isEnabled:!1,config:{}},{id:"jira",name:"Jira",description:"Create tickets and manage projects",category:"productivity",icon:"\uD83C\uDFAF",baseUrl:"https://api.atlassian.com",authType:"bearer",isEnabled:!1,config:{}},{id:"asana",name:"Asana",description:"Create tasks and manage workflows",category:"productivity",icon:"\uD83D\uDCCA",baseUrl:"https://app.asana.com/api/1.0",authType:"bearer",isEnabled:!1,config:{}},{id:"monday",name:"Monday.com",description:"Create boards and manage projects",category:"productivity",icon:"\uD83D\uDCC5",baseUrl:"https://api.monday.com/v2",authType:"api_key",isEnabled:!1,config:{}},{id:"trello",name:"Trello",description:"Create cards and manage boards",category:"productivity",icon:"\uD83D\uDCCB",baseUrl:"https://api.trello.com/1",authType:"api_key",isEnabled:!1,config:{}},{id:"notion",name:"Notion",description:"Save prompts as Notion pages",category:"productivity",icon:"\uD83D\uDCDD",baseUrl:"https://api.notion.com/v1",authType:"bearer",isEnabled:!1,config:{}},{id:"slack",name:"Slack",description:"Share prompts in Slack channels",category:"communication",icon:"\uD83D\uDCAC",baseUrl:"https://slack.com/api",authType:"bearer",isEnabled:!1,config:{}},{id:"discord",name:"Discord",description:"Share prompts in Discord servers",category:"communication",icon:"\uD83C\uDFAE",baseUrl:"https://discord.com/api/v10",authType:"bearer",isEnabled:!1,config:{}},{id:"microsoft-teams",name:"Microsoft Teams",description:"Share prompts in Teams channels",category:"communication",icon:"\uD83D\uDC65",baseUrl:"https://graph.microsoft.com/v1.0",authType:"bearer",isEnabled:!1,config:{}},{id:"telegram",name:"Telegram",description:"Send prompts via Telegram bot",category:"communication",icon:"✈️",baseUrl:"https://api.telegram.org",authType:"api_key",isEnabled:!1,config:{}},{id:"aws",name:"Amazon Web Services",description:"Deploy to AWS services",category:"cloud",icon:"☁️",baseUrl:"https://aws.amazon.com",authType:"api_key",isEnabled:!1,config:{}},{id:"google-cloud",name:"Google Cloud Platform",description:"Deploy to GCP services",category:"cloud",icon:"\uD83C\uDF29️",baseUrl:"https://cloud.google.com",authType:"oauth",isEnabled:!1,config:{}},{id:"azure",name:"Microsoft Azure",description:"Deploy to Azure services",category:"cloud",icon:"\uD83D\uDD37",baseUrl:"https://management.azure.com",authType:"bearer",isEnabled:!1,config:{}},{id:"vercel",name:"Vercel",description:"Deploy web applications",category:"cloud",icon:"▲",baseUrl:"https://api.vercel.com",authType:"bearer",isEnabled:!1,config:{}},{id:"netlify",name:"Netlify",description:"Deploy static sites",category:"cloud",icon:"\uD83C\uDF10",baseUrl:"https://api.netlify.com/api/v1",authType:"bearer",isEnabled:!1,config:{}},{id:"figma",name:"Figma",description:"Create design briefs from prompts",category:"design",icon:"\uD83C\uDFA8",baseUrl:"https://api.figma.com/v1",authType:"api_key",isEnabled:!1,config:{}},{id:"sketch",name:"Sketch",description:"Create design documents",category:"design",icon:"\uD83D\uDC8E",baseUrl:"https://api.sketch.com/v1",authType:"bearer",isEnabled:!1,config:{}},{id:"datadog",name:"Datadog",description:"Set up monitoring and alerts",category:"analytics",icon:"\uD83D\uDCCA",baseUrl:"https://api.datadoghq.com/api/v1",authType:"api_key",isEnabled:!1,config:{}},{id:"new-relic",name:"New Relic",description:"Application performance monitoring",category:"analytics",icon:"\uD83D\uDCC8",baseUrl:"https://api.newrelic.com/v2",authType:"api_key",isEnabled:!1,config:{}},{id:"sentry",name:"Sentry",description:"Error tracking and monitoring",category:"analytics",icon:"\uD83D\uDEA8",baseUrl:"https://sentry.io/api/0",authType:"bearer",isEnabled:!1,config:{}},{id:"google-analytics",name:"Google Analytics",description:"Web analytics and reporting",category:"analytics",icon:"\uD83D\uDCCA",baseUrl:"https://analyticsreporting.googleapis.com/v4",authType:"oauth",isEnabled:!1,config:{}},{id:"openai",name:"OpenAI",description:"Send prompts to ChatGPT and GPT models",category:"ai",icon:"\uD83E\uDD16",baseUrl:"https://api.openai.com/v1",authType:"bearer",isEnabled:!1,config:{}},{id:"anthropic",name:"Anthropic Claude",description:"Send prompts to Claude Sonnet 4",category:"ai",icon:"\uD83E\uDDE0",baseUrl:"https://api.anthropic.com/v1",authType:"api_key",isEnabled:!1,config:{}},{id:"google-gemini",name:"Google Gemini",description:"Send prompts to Gemini Pro models",category:"ai",icon:"\uD83D\uDC8E",baseUrl:"https://generativelanguage.googleapis.com/v1",authType:"api_key",isEnabled:!1,config:{}},{id:"cursor",name:"Cursor IDE",description:"Send prompts to Cursor AI assistant",category:"ai",icon:"⚡",baseUrl:"https://api.cursor.sh/v1",authType:"bearer",isEnabled:!1,config:{}},{id:"deepseek",name:"DeepSeek",description:"Send prompts to DeepSeek V3 models",category:"ai",icon:"\uD83D\uDD0D",baseUrl:"https://api.deepseek.com/v1",authType:"bearer",isEnabled:!1,config:{}},{id:"perplexity",name:"Perplexity AI",description:"Send prompts to Perplexity models",category:"ai",icon:"\uD83D\uDD2E",baseUrl:"https://api.perplexity.ai",authType:"bearer",isEnabled:!1,config:{}},{id:"cohere",name:"Cohere",description:"Send prompts to Cohere Command models",category:"ai",icon:"\uD83C\uDF1F",baseUrl:"https://api.cohere.ai/v1",authType:"bearer",isEnabled:!1,config:{}},{id:"huggingface",name:"Hugging Face",description:"Access open-source AI models",category:"ai",icon:"\uD83E\uDD17",baseUrl:"https://api-inference.huggingface.co",authType:"bearer",isEnabled:!1,config:{}}];class y{constructor(){this.integrations=[],this.webhooks=[],this.storageKey="promptGenerator_integrations",this.webhooksKey="promptGenerator_webhooks",this.loadIntegrations(),this.loadWebhooks()}loadIntegrations(){try{let e=localStorage.getItem(this.storageKey);e?this.integrations=JSON.parse(e):this.integrations=[...f]}catch(e){console.error("Failed to load integrations:",e),this.integrations=[...f]}}loadWebhooks(){try{let e=localStorage.getItem(this.webhooksKey);e&&(this.webhooks=JSON.parse(e))}catch(e){console.error("Failed to load webhooks:",e)}}saveIntegrations(){try{localStorage.setItem(this.storageKey,JSON.stringify(this.integrations))}catch(e){console.error("Failed to save integrations:",e)}}saveWebhooks(){try{localStorage.setItem(this.webhooksKey,JSON.stringify(this.webhooks))}catch(e){console.error("Failed to save webhooks:",e)}}getIntegrations(){return this.integrations}getEnabledIntegrations(){return this.integrations.filter(e=>e.isEnabled)}enableIntegration(e,t){let i=this.integrations.find(t=>t.id===e);return!!i&&(i.isEnabled=!0,i.config={...i.config,...t},this.saveIntegrations(),!0)}disableIntegration(e){let t=this.integrations.find(t=>t.id===e);return!!t&&(t.isEnabled=!1,t.config={},this.saveIntegrations(),!0)}async testIntegration(e){let t=this.integrations.find(t=>t.id===e);if(!t||!t.isEnabled)return{success:!1,error:"Integration not found or not enabled"};try{return await new Promise(e=>setTimeout(e,1e3)),({github:{success:!0,data:{user:"demo-user",repos:42}},gitlab:{success:!0,data:{user:"demo-user",projects:28}},notion:{success:!0,data:{workspace:"Demo Workspace"}},slack:{success:!0,data:{team:"Demo Team",channels:15}},figma:{success:!0,data:{teams:3,projects:12}},linear:{success:!0,data:{workspace:"Demo Workspace",issues:28}},discord:{success:!0,data:{guild:"Demo Server",channels:8}},jira:{success:!0,data:{projects:12,issues:156}},asana:{success:!0,data:{workspaces:3,tasks:89}},monday:{success:!0,data:{boards:8,items:234}},trello:{success:!0,data:{boards:15,cards:67}},"microsoft-teams":{success:!0,data:{teams:5,channels:23}},telegram:{success:!0,data:{bot:"active",chats:12}},aws:{success:!0,data:{regions:3,services:45}},"google-cloud":{success:!0,data:{projects:2,services:32}},azure:{success:!0,data:{subscriptions:1,resources:28}},vercel:{success:!0,data:{projects:8,deployments:156}},netlify:{success:!0,data:{sites:12,builds:89}},sketch:{success:!0,data:{documents:15,symbols:234}},datadog:{success:!0,data:{dashboards:8,monitors:45}},"new-relic":{success:!0,data:{applications:6,alerts:23}},sentry:{success:!0,data:{projects:4,errors:12}},"google-analytics":{success:!0,data:{properties:3,reports:67}},openai:{success:!0,data:{models:8,usage:"active"}},anthropic:{success:!0,data:{models:3,status:"connected"}},"google-gemini":{success:!0,data:{models:5,quota:"available"}},cursor:{success:!0,data:{workspace:"connected",features:12}},deepseek:{success:!0,data:{models:4,status:"active"}},perplexity:{success:!0,data:{models:3,credits:1e3}},cohere:{success:!0,data:{models:6,usage:"normal"}},huggingface:{success:!0,data:{models:50,status:"connected"}}})[e]||{success:!0,data:{status:"connected"}}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error occurred"}}}async sendToIntegration(e,t,i,a="create"){let n=this.integrations.find(t=>t.id===e);if(!n||!n.isEnabled)return{success:!1,error:"Integration not found or not enabled"};try{return await new Promise(e=>setTimeout(e,1500)),({github:{success:!0,data:{repository:`${t.projectName.toLowerCase().replace(/\s+/g,"-")}`,url:`https://github.com/demo-user/${t.projectName.toLowerCase().replace(/\s+/g,"-")}`,action:"Repository created"}},notion:{success:!0,data:{page:t.projectName,url:"https://notion.so/demo-page",action:"Page created"}},slack:{success:!0,data:{channel:"#general",message:"Prompt shared successfully",action:"Message sent"}},figma:{success:!0,data:{file:`${t.projectName} Design Brief`,url:"https://figma.com/file/demo",action:"Design file created"}},linear:{success:!0,data:{issue:`${t.projectName} - Implementation`,url:"https://linear.app/demo/issue/DEMO-123",action:"Issue created"}},discord:{success:!0,data:{channel:"#development",message:"Prompt shared in Discord",action:"Message sent"}},gitlab:{success:!0,data:{project:`${t.projectName.toLowerCase().replace(/\s+/g,"-")}`,url:`https://gitlab.com/demo-user/${t.projectName.toLowerCase().replace(/\s+/g,"-")}`,action:"Project created"}},jira:{success:!0,data:{ticket:`${t.projectName} - Epic`,url:"https://demo.atlassian.net/browse/PROJ-123",action:"Epic created"}},asana:{success:!0,data:{task:`${t.projectName} - Project`,url:"https://app.asana.com/0/123456/789012",action:"Task created"}},monday:{success:!0,data:{item:`${t.projectName} - Board Item`,url:"https://demo.monday.com/boards/123456",action:"Item created"}},openai:{success:!0,data:{response:"Prompt sent to ChatGPT",model:"gpt-4",action:"Prompt processed"}},anthropic:{success:!0,data:{response:"Prompt sent to Claude Sonnet 4",model:"claude-3-5-sonnet",action:"Prompt processed"}},"google-gemini":{success:!0,data:{response:"Prompt sent to Gemini Pro",model:"gemini-pro",action:"Prompt processed"}},cursor:{success:!0,data:{response:"Prompt sent to Cursor AI",workspace:"active",action:"Prompt processed"}},vercel:{success:!0,data:{deployment:`${t.projectName.toLowerCase().replace(/\s+/g,"-")}`,url:`https://${t.projectName.toLowerCase().replace(/\s+/g,"-")}.vercel.app`,action:"Deployment created"}},aws:{success:!0,data:{stack:`${t.projectName}-stack`,region:"us-east-1",action:"CloudFormation stack created"}}})[e]||{success:!0,data:{action:"Completed"}}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error occurred"}}}addWebhook(e){let t={...e,id:"webhook_"+Math.random().toString(36).substring(2,11)};return this.webhooks.push(t),this.saveWebhooks(),t}updateWebhook(e,t){let i=this.webhooks.findIndex(t=>t.id===e);return -1!==i&&(this.webhooks[i]={...this.webhooks[i],...t},this.saveWebhooks(),!0)}deleteWebhook(e){let t=this.webhooks.findIndex(t=>t.id===e);return -1!==t&&(this.webhooks.splice(t,1),this.saveWebhooks(),!0)}getWebhooks(){return this.webhooks}async triggerWebhook(e,t,i){let a=this.webhooks.find(t=>t.id===e);if(!a||!a.isActive||!a.events.includes(t))return{success:!1,error:"Webhook not found, inactive, or event not supported"};try{return await new Promise(e=>setTimeout(e,800)),{success:!0,data:{webhook:a.name,event:t,delivered:!0}}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error occurred"}}}exportIntegrationData(){return JSON.stringify({integrations:this.integrations,webhooks:this.webhooks,exportedAt:new Date},null,2)}clearData(){this.integrations=[...f],this.webhooks=[],localStorage.removeItem(this.storageKey),localStorage.removeItem(this.webhooksKey)}}let x=new y;function v({isOpen:e,onClose:t,settings:i,onSettingsChange:a,recommendedModel:n,projectInput:r,generatedPrompt:l}){let[d,m]=(0,o.useState)(i),[p,u]=(0,o.useState)("ai"),[f,y]=(0,o.useState)([]),[v,b]=(0,o.useState)(null),[w,k]=(0,o.useState)(!1),[j,S]=(0,o.useState)(!1),[A,P]=(0,o.useState)(!1),[C,T]=(0,o.useState)({}),[R,M]=(0,o.useState)({}),I=()=>{y(x.getIntegrations())},N=async e=>{b(e),k(!0),M({})},E=async()=>{v&&x.enableIntegration(v.id,R)&&(I(),k(!1),b(null),M({}))},D=e=>{x.disableIntegration(e),I()},L=async e=>{S(!0);let t=await x.testIntegration(e.id);T({...C,[e.id]:t}),S(!1)},z=async e=>{if(!r||!l)return;P(!0);let t=await x.sendToIntegration(e.id,r,l);T({...C,[`${e.id}_send`]:t}),P(!1)},O=Object.entries(c).map(([,e])=>({value:e.id,label:e.name,description:`${e.provider} • ${e.description}`}));if(!e)return null;let $=Object.values(c).find(e=>e.id===d.model);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-2 sm:p-4",children:(0,s.jsxs)("div",{className:"bg-black border border-white/20 rounded-xl shadow-2xl p-4 sm:p-6 w-full max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-y-auto",style:{boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.8)"},children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4 sm:mb-6",children:[(0,s.jsx)("h3",{className:"text-lg sm:text-xl font-semibold text-white",children:"AI Settings & Integrations"}),(0,s.jsx)("button",{onClick:t,className:"bg-gray-800 text-white px-3 py-1 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm flex-shrink-0",children:"Close"})]}),(0,s.jsxs)("div",{className:"flex gap-2 sm:gap-3 mb-4 sm:mb-6 overflow-x-auto",children:[(0,s.jsx)("button",{onClick:()=>u("ai"),className:`px-3 sm:px-4 py-2 rounded-md transition-all duration-300 whitespace-nowrap text-sm sm:text-base flex-shrink-0 ${"ai"===p?"bg-white text-black":"bg-gray-800 text-gray-400 hover:bg-gray-700"}`,children:"\uD83E\uDD16 AI Settings"}),(0,s.jsx)("button",{onClick:()=>u("integrations"),className:`px-3 sm:px-4 py-2 rounded-md transition-all duration-300 whitespace-nowrap text-sm sm:text-base flex-shrink-0 ${"integrations"===p?"bg-white text-black":"bg-gray-800 text-gray-400 hover:bg-gray-700"}`,children:"\uD83D\uDD17 Integrations"})]}),"ai"===p&&(0,s.jsxs)("div",{className:"space-y-4 sm:space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-white mb-2",children:["AI Model",n&&d.model===n&&(0,s.jsx)("span",{className:"ml-2 text-xs bg-white text-black px-2 py-1 rounded",children:"Recommended"})]}),(0,s.jsx)(h,{value:d.model,onChange:e=>m(t=>({...t,model:e})),options:O,placeholder:"Select AI model"}),$&&(0,s.jsxs)("div",{className:"mt-2 p-3 bg-gray-900 border border-white/10 rounded-lg",children:[(0,s.jsx)("div",{className:"text-sm text-white font-medium mb-1",children:$.name}),(0,s.jsx)("div",{className:"text-xs text-gray-400 mb-2",children:$.description}),(0,s.jsx)("div",{className:"flex flex-wrap gap-1 mb-2",children:$.strengths.map((e,t)=>(0,s.jsx)("span",{className:"bg-gray-700 text-gray-200 text-xs px-2 py-1 rounded",children:e},t))}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["Max tokens: ",$.maxTokens.toLocaleString()," • Cost: $",$.costPer1kTokens,"/1k tokens"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"Optimization Level"}),(0,s.jsx)(h,{value:d.optimizationLevel,onChange:e=>m(t=>({...t,optimizationLevel:e})),options:[{value:"basic",label:"Basic",description:"Clear, structured prompt with essentials"},{value:"enhanced",label:"Enhanced",description:"Comprehensive with detailed specifications"},{value:"expert",label:"Expert",description:"Advanced architectural considerations"}],placeholder:"Select optimization level"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"Target Audience"}),(0,s.jsx)(h,{value:d.targetAudience,onChange:e=>m(t=>({...t,targetAudience:e})),options:[{value:"developer",label:"Developer",description:"Technical implementation focus"},{value:"business",label:"Business",description:"Business value and outcomes"},{value:"technical",label:"Technical",description:"System requirements and integration"},{value:"general",label:"General",description:"Balanced technical and business"}],placeholder:"Select target audience"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white mb-3",children:"Additional Options"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:d.includeExamples,onChange:e=>m(t=>({...t,includeExamples:e.target.checked})),className:"mr-3 accent-white"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-white text-sm font-medium",children:"Include Examples"}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:"Add relevant code examples and snippets"})]})]}),(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:d.includeConstraints,onChange:e=>m(t=>({...t,includeConstraints:e.target.checked})),className:"mr-3 accent-white"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-white text-sm font-medium",children:"Include Constraints"}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:"Specify technical limitations and requirements"})]})]}),(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:d.includeMetrics,onChange:e=>m(t=>({...t,includeMetrics:e.target.checked})),className:"mr-3 accent-white"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-white text-sm font-medium",children:"Include Success Metrics"}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:"Add KPIs and measurable outcomes"})]})]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between pt-4 sm:pt-6 border-t border-white/10 space-y-3 sm:space-y-0",children:[(0,s.jsx)("button",{onClick:()=>{m({model:n||"deepseek/deepseek-chat-v3-0324:free",optimizationLevel:"enhanced",includeExamples:!0,includeConstraints:!0,includeMetrics:!1,targetAudience:"developer"})},className:"group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-medium text-white bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl border border-white/10 transition-all duration-300 hover:border-white/20 hover:from-gray-700/90 hover:to-gray-800/90 focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-black",style:{boxShadow:"0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:(0,s.jsxs)("span",{className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-4 h-4 transition-transform duration-300 group-hover:rotate-180",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),(0,s.jsx)("span",{children:"Reset to Defaults"})]})}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3",children:[(0,s.jsx)("button",{onClick:t,className:"group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-medium text-gray-300 bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl border border-white/10 transition-all duration-300 hover:border-white/20 hover:from-gray-700/90 hover:to-gray-800/90 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-black",style:{boxShadow:"0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:(0,s.jsxs)("span",{className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),(0,s.jsx)("span",{children:"Cancel"})]})}),(0,s.jsx)("button",{onClick:()=>{a(d),t()},className:"group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-semibold text-black bg-gradient-to-br from-white to-gray-100 backdrop-blur-xl border border-white/20 transition-all duration-300 hover:from-gray-100 hover:to-gray-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-black transform hover:scale-[1.02]",style:{boxShadow:"0 6px 20px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)"},children:(0,s.jsxs)("span",{className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-4 h-4 transition-transform duration-300 group-hover:scale-110",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,s.jsx)("span",{children:"Save Settings"})]})})]})]})]}),"integrations"===p&&(0,s.jsx)("div",{className:"space-y-4 sm:space-y-6",children:(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:f.map(e=>(0,s.jsxs)("div",{className:`p-3 sm:p-4 rounded-lg border transition-all duration-300 ${e.isEnabled?"bg-gray-800 border-white":"bg-gray-900 border-white/10 hover:border-white/20"}`,children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-2 sm:space-x-3 flex-1 min-w-0",children:[(0,s.jsx)("div",{className:"text-xl sm:text-2xl flex-shrink-0",children:e.icon}),(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsx)("h4",{className:"text-white font-medium text-sm sm:text-base truncate",children:e.name}),(0,s.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm line-clamp-2",children:e.description})]})]}),(0,s.jsx)("div",{className:`w-3 h-3 rounded-full ${e.isEnabled?"bg-white":"bg-gray-500"}`})]}),(0,s.jsx)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2",children:e.isEnabled?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{onClick:()=>L(e),disabled:j,className:"flex-1 bg-gray-700 text-white py-2 px-3 rounded-md hover:bg-gray-600 transition-all duration-300 text-xs sm:text-sm disabled:opacity-50",children:j?"Testing...":"Test"}),r&&l&&(0,s.jsx)("button",{onClick:()=>z(e),disabled:A,className:"flex-1 bg-gray-800 text-white py-2 px-3 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm disabled:opacity-50",children:A?"Sending...":"Send"}),(0,s.jsx)("button",{onClick:()=>D(e.id),className:"bg-gray-800 text-white py-2 px-3 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm",children:"Disable"})]}):(0,s.jsx)("button",{onClick:()=>N(e),className:"flex-1 bg-gray-800 text-white py-2 px-3 rounded-md hover:bg-gray-700 transition-all duration-300 text-xs sm:text-sm",children:"Enable"})}),C[e.id]&&(0,s.jsxs)("div",{className:"mt-3 p-2 bg-gray-800 border border-white/10 rounded text-xs",children:[(0,s.jsx)("div",{className:`font-medium ${C[e.id].success?"text-white":"text-gray-300"}`,children:C[e.id].success?"Success":"Failed"}),(0,s.jsx)("div",{className:"text-gray-400 mt-1",children:C[e.id].error||"Connection successful"})]})]},e.id))})})]})}),w&&v&&(0,g.createPortal)((0,s.jsx)("div",{className:"fixed inset-0 bg-black/90 flex items-center justify-center p-4",style:{position:"fixed",top:0,left:0,right:0,bottom:0,zIndex:1e4,margin:0,padding:"1rem"},onClick:e=>{e.target===e.currentTarget&&k(!1)},children:(0,s.jsxs)("div",{className:"bg-black border border-white/20 rounded-xl shadow-2xl p-6 w-full max-w-md mx-auto my-auto",style:{maxHeight:"90vh",overflow:"auto",position:"relative",transform:"none"},onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4",children:["Configure ",v.name]}),(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"API Token"}),(0,s.jsx)("input",{type:"password",value:R.token||"",onChange:e=>M({...R,token:e.target.value}),className:"w-full px-3 py-2 bg-gray-900 border border-white/10 rounded-md text-white placeholder-gray-400 focus:border-white/30 focus:outline-none",placeholder:"Enter your API token"})]})}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 mt-4 sm:mt-6",children:[(0,s.jsx)("button",{onClick:()=>k(!1),className:"group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-medium text-gray-300 bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl border border-white/10 transition-all duration-300 hover:border-white/20 hover:from-gray-700/90 hover:to-gray-800/90 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-black",style:{boxShadow:"0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:(0,s.jsxs)("span",{className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),(0,s.jsx)("span",{children:"Cancel"})]})}),(0,s.jsx)("button",{onClick:E,className:"group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-semibold text-black bg-gradient-to-br from-white to-gray-100 backdrop-blur-xl border border-white/20 transition-all duration-300 hover:from-gray-100 hover:to-gray-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-black transform hover:scale-[1.02]",style:{boxShadow:"0 6px 20px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)"},children:(0,s.jsxs)("span",{className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-4 h-4 transition-transform duration-300 group-hover:scale-110",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,s.jsx)("span",{children:"Save"})]})})]})]})}),document.body)]})}function b(e){let t=(0,o.useRef)(null);return null===t.current&&(t.current=e()),t.current}class w{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map(e=>e.finished))}getAll(e){return this.animations[0][e]}setAll(e,t){for(let i=0;i<this.animations.length;i++)this.animations[i][e]=t}attachTimeline(e){let t=this.animations.map(t=>t.attachTimeline(e));return()=>{t.forEach((e,t)=>{e&&e(),this.animations[t].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let t=0;t<this.animations.length;t++)e=Math.max(e,this.animations[t].duration);return e}runAll(e){this.animations.forEach(t=>t[e]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class k extends w{then(e,t){return this.finished.finally(e).then(()=>{})}}let j=(e,t,i)=>i>t?t:i<e?e:i,S=e=>1e3*e,A=e=>e/1e3,P=(e,t,i=10)=>{let a="",n=Math.max(Math.round(t/i),2);for(let t=0;t<n;t++)a+=Math.round(1e4*e(t/(n-1)))/1e4+", ";return`linear(${a.substring(0,a.length-2)})`};function C(e){let t=0,i=e.next(t);for(;!i.done&&t<2e4;)t+=50,i=e.next(t);return t>=2e4?1/0:t}function T(e,t=100,i){let a=i({...e,keyframes:[0,t]}),n=Math.min(C(a),2e4);return{type:"keyframes",ease:e=>a.next(n*e).value/t,duration:A(n)}}function R(e,t,i){var a,n;let r=Math.max(t-5,0);return a=i-e(r),(n=t-r)?1e3/n*a:0}let M={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},I=()=>{},N=()=>{};function E(e,t){return e*Math.sqrt(1-t*t)}let D=["duration","bounce"],L=["stiffness","damping","mass"];function z(e,t){return t.some(t=>void 0!==e[t])}function O(e=M.visualDuration,t=M.bounce){let i,a="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:n,restDelta:r}=a,s=a.keyframes[0],o=a.keyframes[a.keyframes.length-1],l={done:!1,value:s},{stiffness:c,damping:d,mass:m,duration:p,velocity:u,isResolvedFromDuration:h}=function(e){let t={velocity:M.velocity,stiffness:M.stiffness,damping:M.damping,mass:M.mass,isResolvedFromDuration:!1,...e};if(!z(e,L)&&z(e,D))if(e.visualDuration){let i=2*Math.PI/(1.2*e.visualDuration),a=i*i,n=2*j(.05,1,1-(e.bounce||0))*Math.sqrt(a);t={...t,mass:M.mass,stiffness:a,damping:n}}else{let i=function({duration:e=M.duration,bounce:t=M.bounce,velocity:i=M.velocity,mass:a=M.mass}){let n,r;I(e<=S(M.maxDuration),"Spring duration must be 10 seconds or less");let s=1-t;s=j(M.minDamping,M.maxDamping,s),e=j(M.minDuration,M.maxDuration,A(e)),s<1?(n=t=>{let a=t*s,n=a*e;return .001-(a-i)/E(t,s)*Math.exp(-n)},r=t=>{let a=t*s*e,r=Math.pow(s,2)*Math.pow(t,2)*e,o=Math.exp(-a),l=E(Math.pow(t,2),s);return(a*i+i-r)*o*(-n(t)+.001>0?-1:1)/l}):(n=t=>-.001+Math.exp(-t*e)*((t-i)*e+1),r=t=>e*e*(i-t)*Math.exp(-t*e));let o=function(e,t,i){let a=i;for(let i=1;i<12;i++)a-=e(a)/t(a);return a}(n,r,5/e);if(e=S(e),isNaN(o))return{stiffness:M.stiffness,damping:M.damping,duration:e};{let t=Math.pow(o,2)*a;return{stiffness:t,damping:2*s*Math.sqrt(a*t),duration:e}}}(e);(t={...t,...i,mass:M.mass}).isResolvedFromDuration=!0}return t}({...a,velocity:-A(a.velocity||0)}),g=u||0,f=d/(2*Math.sqrt(c*m)),y=o-s,x=A(Math.sqrt(c/m)),v=5>Math.abs(y);if(n||(n=v?M.restSpeed.granular:M.restSpeed.default),r||(r=v?M.restDelta.granular:M.restDelta.default),f<1){let e=E(x,f);i=t=>o-Math.exp(-f*x*t)*((g+f*x*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===f)i=e=>o-Math.exp(-x*e)*(y+(g+x*y)*e);else{let e=x*Math.sqrt(f*f-1);i=t=>{let i=Math.exp(-f*x*t),a=Math.min(e*t,300);return o-i*((g+f*x*y)*Math.sinh(a)+e*y*Math.cosh(a))/e}}let b={calculatedDuration:h&&p||null,next:e=>{let t=i(e);if(h)l.done=e>=p;else{let a=0===e?g:0;f<1&&(a=0===e?S(g):R(i,e,t));let s=Math.abs(o-t)<=r;l.done=Math.abs(a)<=n&&s}return l.value=l.done?o:t,l},toString:()=>{let e=Math.min(C(b),2e4),t=P(t=>b.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return b}O.applyToOptions=e=>{let t=T(e,100,O);return e.ease=t.ease,e.duration=S(t.duration),e.type="keyframes",e};let $=(e,t,i)=>{let a=t-e;return 0===a?1:(i-e)/a},F=(e,t,i)=>e+(t-e)*i;function V(e,t){let i=e[e.length-1];for(let a=1;a<=t;a++){let n=$(0,t,a);e.push(F(i,1,n))}}function B(e){let t=[0];return V(t,e.length-1),t}function G(e){return"function"==typeof e&&"applyToOptions"in e}let W=e=>!!(e&&e.getVelocity),K=(e,t,i)=>{let a=t-e;return((i-e)%a+a)%a+e},_=e=>Array.isArray(e)&&"number"!=typeof e[0];function U(e,t){return _(e)?e[K(0,e.length,t)]:e}function q(e,t,i){if(e instanceof EventTarget)return[e];if("string"==typeof e){let a=document;t&&(a=t.current);let n=i?.[e]??a.querySelectorAll(e);return n?Array.from(n):[]}return Array.from(e)}function H(e){return"object"==typeof e&&!Array.isArray(e)}function Q(e,t,i,a){return"string"==typeof e&&H(t)?q(e,i,a):e instanceof NodeList?Array.from(e):Array.isArray(e)?e:[e]}function X(e,t,i,a){return"number"==typeof t?t:t.startsWith("-")||t.startsWith("+")?Math.max(0,e+parseFloat(t)):"<"===t?i:t.startsWith("<")?Math.max(0,i+parseFloat(t.slice(1))):a.get(t)??e}function J(e,t){-1===e.indexOf(t)&&e.push(t)}function Y(e,t){let i=e.indexOf(t);i>-1&&e.splice(i,1)}function Z(e,t){return e.at!==t.at?e.at-t.at:null===e.value?1:null===t.value?-1:0}function ee(e,t){return t.has(e)||t.set(e,{}),t.get(e)}function et(e,t){return t[e]||(t[e]=[]),t[e]}let ei=e=>"number"==typeof e,ea=e=>e.every(ei),en=new WeakMap;function er(e,t){return e?.[t]??e?.default??e}let es=e=>e,eo={},el=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ec={value:null,addProjectionMetrics:null};function ed(e,t){let i=!1,a=!0,n={delta:0,timestamp:0,isProcessing:!1},r=()=>i=!0,s=el.reduce((e,i)=>(e[i]=function(e,t){let i=new Set,a=new Set,n=!1,r=!1,s=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function c(t){s.has(t)&&(d.schedule(t),e()),l++,t(o)}let d={schedule:(e,t=!1,r=!1)=>{let o=r&&n?i:a;return t&&s.add(e),o.has(e)||o.add(e),e},cancel:e=>{a.delete(e),s.delete(e)},process:e=>{if(o=e,n){r=!0;return}n=!0,[i,a]=[a,i],i.forEach(c),t&&ec.value&&ec.value.frameloop[t].push(l),l=0,i.clear(),n=!1,r&&(r=!1,d.process(e))}};return d}(r,t?i:void 0),e),{}),{setup:o,read:l,resolveKeyframes:c,preUpdate:d,update:m,preRender:p,render:u,postRender:h}=s,g=()=>{let r=eo.useManualTiming?n.timestamp:performance.now();i=!1,eo.useManualTiming||(n.delta=a?1e3/60:Math.max(Math.min(r-n.timestamp,40),1)),n.timestamp=r,n.isProcessing=!0,o.process(n),l.process(n),c.process(n),d.process(n),m.process(n),p.process(n),u.process(n),h.process(n),n.isProcessing=!1,i&&t&&(a=!1,e(g))},f=()=>{i=!0,a=!0,n.isProcessing||e(g)};return{schedule:el.reduce((e,t)=>{let a=s[t];return e[t]=(e,t=!1,n=!1)=>(i||f(),a.schedule(e,t,n)),e},{}),cancel:e=>{for(let t=0;t<el.length;t++)s[el[t]].cancel(e)},state:n,steps:s}}let{schedule:em,cancel:ep,state:eu,steps:eh}=ed("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:es,!0),eg=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],ef=new Set(eg),ey=new Set(["width","height","top","left","right","bottom",...eg]);class ex{constructor(){this.subscriptions=[]}add(e){return J(this.subscriptions,e),()=>Y(this.subscriptions,e)}notify(e,t,i){let a=this.subscriptions.length;if(a)if(1===a)this.subscriptions[0](e,t,i);else for(let n=0;n<a;n++){let a=this.subscriptions[n];a&&a(e,t,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function ev(){a=void 0}let eb={now:()=>(void 0===a&&eb.set(eu.isProcessing||eo.useManualTiming?eu.timestamp:performance.now()),a),set:e=>{a=e,queueMicrotask(ev)}},ew=e=>!isNaN(parseFloat(e)),ek={current:void 0};class ej{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let i=eb.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=eb.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=ew(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new ex);let i=this.events[e].add(t);return"change"===e?()=>{i(),em.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,i){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-i}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return ek.current&&ek.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=eb.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function eS(e,t){return new ej(e,t)}let eA=e=>Array.isArray(e);function eP(e){let t=[{},{}];return e?.values.forEach((e,i)=>{t[0][i]=e.get(),t[1][i]=e.getVelocity()}),t}function eC(e,t,i,a){if("function"==typeof t){let[n,r]=eP(a);t=t(void 0!==i?i:e.custom,n,r)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[n,r]=eP(a);t=t(void 0!==i?i:e.custom,n,r)}return t}function eT(e,t,i){let a=e.getProps();return eC(a,t,void 0!==i?i:a.custom,e)}function eR(e,t){let i=e.getValue("willChange");if(W(i)&&i.add)return i.add(t);if(!i&&eo.WillChange){let i=new eo.WillChange("auto");e.addValue("willChange",i),i.add(t)}}let eM=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),eI="data-"+eM("framerAppearId"),eN=(e,t)=>i=>t(e(i)),eE=(...e)=>e.reduce(eN),eD={layout:0,mainThread:0,waapi:0},eL=e=>t=>"string"==typeof t&&t.startsWith(e),ez=eL("--"),eO=eL("var(--"),e$=e=>!!eO(e)&&eF.test(e.split("/*")[0].trim()),eF=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,eV={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},eB={...eV,transform:e=>j(0,1,e)},eG={...eV,default:1},eW=e=>Math.round(1e5*e)/1e5,eK=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,e_=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,eU=(e,t)=>i=>!!("string"==typeof i&&e_.test(i)&&i.startsWith(e)||t&&null!=i&&Object.prototype.hasOwnProperty.call(i,t)),eq=(e,t,i)=>a=>{if("string"!=typeof a)return a;let[n,r,s,o]=a.match(eK);return{[e]:parseFloat(n),[t]:parseFloat(r),[i]:parseFloat(s),alpha:void 0!==o?parseFloat(o):1}},eH=e=>j(0,255,e),eQ={...eV,transform:e=>Math.round(eH(e))},eX={test:eU("rgb","red"),parse:eq("red","green","blue"),transform:({red:e,green:t,blue:i,alpha:a=1})=>"rgba("+eQ.transform(e)+", "+eQ.transform(t)+", "+eQ.transform(i)+", "+eW(eB.transform(a))+")"},eJ={test:eU("#"),parse:function(e){let t="",i="",a="",n="";return e.length>5?(t=e.substring(1,3),i=e.substring(3,5),a=e.substring(5,7),n=e.substring(7,9)):(t=e.substring(1,2),i=e.substring(2,3),a=e.substring(3,4),n=e.substring(4,5),t+=t,i+=i,a+=a,n+=n),{red:parseInt(t,16),green:parseInt(i,16),blue:parseInt(a,16),alpha:n?parseInt(n,16)/255:1}},transform:eX.transform},eY=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),eZ=eY("deg"),e0=eY("%"),e1=eY("px"),e2=eY("vh"),e5=eY("vw"),e4={...e0,parse:e=>e0.parse(e)/100,transform:e=>e0.transform(100*e)},e3={test:eU("hsl","hue"),parse:eq("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:i,alpha:a=1})=>"hsla("+Math.round(e)+", "+e0.transform(eW(t))+", "+e0.transform(eW(i))+", "+eW(eB.transform(a))+")"},e9={test:e=>eX.test(e)||eJ.test(e)||e3.test(e),parse:e=>eX.test(e)?eX.parse(e):e3.test(e)?e3.parse(e):eJ.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eX.transform(e):e3.transform(e),getAnimatableNone:e=>{let t=e9.parse(e);return t.alpha=0,e9.transform(t)}},e8=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,e6="number",e7="color",te=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tt(e){let t=e.toString(),i=[],a={color:[],number:[],var:[]},n=[],r=0,s=t.replace(te,e=>(e9.test(e)?(a.color.push(r),n.push(e7),i.push(e9.parse(e))):e.startsWith("var(")?(a.var.push(r),n.push("var"),i.push(e)):(a.number.push(r),n.push(e6),i.push(parseFloat(e))),++r,"${}")).split("${}");return{values:i,split:s,indexes:a,types:n}}function ti(e){return tt(e).values}function ta(e){let{split:t,types:i}=tt(e),a=t.length;return e=>{let n="";for(let r=0;r<a;r++)if(n+=t[r],void 0!==e[r]){let t=i[r];t===e6?n+=eW(e[r]):t===e7?n+=e9.transform(e[r]):n+=e[r]}return n}}let tn=e=>"number"==typeof e?0:e9.test(e)?e9.getAnimatableNone(e):e,tr={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(eK)?.length||0)+(e.match(e8)?.length||0)>0},parse:ti,createTransformer:ta,getAnimatableNone:function(e){let t=ti(e);return ta(e)(t.map(tn))}};function ts(e,t,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?e+(t-e)*6*i:i<.5?t:i<2/3?e+(t-e)*(2/3-i)*6:e}function to(e,t){return i=>i>0?t:e}let tl=(e,t,i)=>{let a=e*e,n=i*(t*t-a)+a;return n<0?0:Math.sqrt(n)},tc=[eJ,eX,e3],td=e=>tc.find(t=>t.test(e));function tm(e){let t=td(e);if(I(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let i=t.parse(e);return t===e3&&(i=function({hue:e,saturation:t,lightness:i,alpha:a}){e/=360,i/=100;let n=0,r=0,s=0;if(t/=100){let a=i<.5?i*(1+t):i+t-i*t,o=2*i-a;n=ts(o,a,e+1/3),r=ts(o,a,e),s=ts(o,a,e-1/3)}else n=r=s=i;return{red:Math.round(255*n),green:Math.round(255*r),blue:Math.round(255*s),alpha:a}}(i)),i}let tp=(e,t)=>{let i=tm(e),a=tm(t);if(!i||!a)return to(e,t);let n={...i};return e=>(n.red=tl(i.red,a.red,e),n.green=tl(i.green,a.green,e),n.blue=tl(i.blue,a.blue,e),n.alpha=F(i.alpha,a.alpha,e),eX.transform(n))},tu=new Set(["none","hidden"]);function th(e,t){return i=>F(e,t,i)}function tg(e){return"number"==typeof e?th:"string"==typeof e?e$(e)?to:e9.test(e)?tp:tx:Array.isArray(e)?tf:"object"==typeof e?e9.test(e)?tp:ty:to}function tf(e,t){let i=[...e],a=i.length,n=e.map((e,i)=>tg(e)(e,t[i]));return e=>{for(let t=0;t<a;t++)i[t]=n[t](e);return i}}function ty(e,t){let i={...e,...t},a={};for(let n in i)void 0!==e[n]&&void 0!==t[n]&&(a[n]=tg(e[n])(e[n],t[n]));return e=>{for(let t in a)i[t]=a[t](e);return i}}let tx=(e,t)=>{let i=tr.createTransformer(t),a=tt(e),n=tt(t);return a.indexes.var.length===n.indexes.var.length&&a.indexes.color.length===n.indexes.color.length&&a.indexes.number.length>=n.indexes.number.length?tu.has(e)&&!n.values.length||tu.has(t)&&!a.values.length?function(e,t){return tu.has(e)?i=>i<=0?e:t:i=>i>=1?t:e}(e,t):eE(tf(function(e,t){let i=[],a={color:0,var:0,number:0};for(let n=0;n<t.values.length;n++){let r=t.types[n],s=e.indexes[r][a[r]],o=e.values[s]??0;i[n]=o,a[r]++}return i}(a,n),n.values),i):(I(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),to(e,t))};function tv(e,t,i){return"number"==typeof e&&"number"==typeof t&&"number"==typeof i?F(e,t,i):tg(e)(e,t)}let tb=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>em.update(t,e),stop:()=>ep(t),now:()=>eu.isProcessing?eu.timestamp:eb.now()}};function tw({keyframes:e,velocity:t=0,power:i=.8,timeConstant:a=325,bounceDamping:n=10,bounceStiffness:r=500,modifyTarget:s,min:o,max:l,restDelta:c=.5,restSpeed:d}){let m,p,u=e[0],h={done:!1,value:u},g=e=>void 0!==o&&e<o||void 0!==l&&e>l,f=e=>void 0===o?l:void 0===l||Math.abs(o-e)<Math.abs(l-e)?o:l,y=i*t,x=u+y,v=void 0===s?x:s(x);v!==x&&(y=v-u);let b=e=>-y*Math.exp(-e/a),w=e=>v+b(e),k=e=>{let t=b(e),i=w(e);h.done=Math.abs(t)<=c,h.value=h.done?v:i},j=e=>{g(h.value)&&(m=e,p=O({keyframes:[h.value,f(h.value)],velocity:R(w,e,h.value),damping:n,stiffness:r,restDelta:c,restSpeed:d}))};return j(0),{calculatedDuration:null,next:e=>{let t=!1;return(p||void 0!==m||(t=!0,k(e),j(e)),void 0!==m&&e>=m)?p.next(e-m):(t||k(e),h)}}}let tk=(e,t,i)=>(((1-3*i+3*t)*e+(3*i-6*t))*e+3*t)*e;function tj(e,t,i,a){if(e===t&&i===a)return es;let n=t=>(function(e,t,i,a,n){let r,s,o=0;do(r=tk(s=t+(i-t)/2,a,n)-e)>0?i=s:t=s;while(Math.abs(r)>1e-7&&++o<12);return s})(t,0,1,e,i);return e=>0===e||1===e?e:tk(n(e),t,a)}let tS=tj(.42,0,1,1),tA=tj(0,0,.58,1),tP=tj(.42,0,.58,1),tC=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,tT=e=>t=>1-e(1-t),tR=tj(.33,1.53,.69,.99),tM=tT(tR),tI=tC(tM),tN=e=>(e*=2)<1?.5*tM(e):.5*(2-Math.pow(2,-10*(e-1))),tE=e=>1-Math.sin(Math.acos(e)),tD=tT(tE),tL=tC(tE),tz=e=>Array.isArray(e)&&"number"==typeof e[0],tO={linear:es,easeIn:tS,easeInOut:tP,easeOut:tA,circIn:tE,circInOut:tL,circOut:tD,backIn:tM,backInOut:tI,backOut:tR,anticipate:tN},t$=e=>"string"==typeof e,tF=e=>{if(tz(e)){N(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,i,a,n]=e;return tj(t,i,a,n)}return t$(e)?(N(void 0!==tO[e],`Invalid easing type '${e}'`),tO[e]):e};function tV({duration:e=300,keyframes:t,times:i,ease:a="easeInOut"}){var n;let r=_(a)?a.map(tF):tF(a),s={done:!1,value:t[0]},o=function(e,t,{clamp:i=!0,ease:a,mixer:n}={}){let r=e.length;if(N(r===t.length,"Both input and output ranges must be the same length"),1===r)return()=>t[0];if(2===r&&t[0]===t[1])return()=>t[1];let s=e[0]===e[1];e[0]>e[r-1]&&(e=[...e].reverse(),t=[...t].reverse());let o=function(e,t,i){let a=[],n=i||eo.mix||tv,r=e.length-1;for(let i=0;i<r;i++){let r=n(e[i],e[i+1]);t&&(r=eE(Array.isArray(t)?t[i]||es:t,r)),a.push(r)}return a}(t,a,n),l=o.length,c=i=>{if(s&&i<e[0])return t[0];let a=0;if(l>1)for(;a<e.length-2&&!(i<e[a+1]);a++);let n=$(e[a],e[a+1],i);return o[a](n)};return i?t=>c(j(e[0],e[r-1],t)):c}((n=i&&i.length===t.length?i:B(t),n.map(t=>t*e)),t,{ease:Array.isArray(r)?r:t.map(()=>r||tP).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(s.value=o(t),s.done=t>=e,s)}}let tB=e=>null!==e;function tG(e,{repeat:t,repeatType:i="loop"},a,n=1){let r=e.filter(tB),s=n<0||t&&"loop"!==i&&t%2==1?0:r.length-1;return s&&void 0!==a?a:r[s]}let tW={decay:tw,inertia:tw,tween:tV,keyframes:tV,spring:O};function tK(e){"string"==typeof e.type&&(e.type=tW[e.type])}class t_{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let tU=e=>e/100;class tq extends t_{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==eb.now()&&this.tick(eb.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},eD.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tK(e);let{type:t=tV,repeat:i=0,repeatDelay:a=0,repeatType:n,velocity:r=0}=e,{keyframes:s}=e,o=t||tV;o!==tV&&"number"!=typeof s[0]&&(this.mixKeyframes=eE(tU,tv(s[0],s[1])),s=[0,100]);let l=o({...e,keyframes:s});"mirror"===n&&(this.mirroredGenerator=o({...e,keyframes:[...s].reverse(),velocity:-r})),null===l.calculatedDuration&&(l.calculatedDuration=C(l));let{calculatedDuration:c}=l;this.calculatedDuration=c,this.resolvedDuration=c+a,this.totalDuration=this.resolvedDuration*(i+1)-a,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:i,totalDuration:a,mixKeyframes:n,mirroredGenerator:r,resolvedDuration:s,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:c,repeat:d,repeatType:m,repeatDelay:p,type:u,onUpdate:h,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-a/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let f=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?f<0:f>a;this.currentTime=Math.max(f,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=a);let x=this.currentTime,v=i;if(d){let e=Math.min(this.currentTime,a)/s,t=Math.floor(e),i=e%1;!i&&e>=1&&(i=1),1===i&&t--,(t=Math.min(t,d+1))%2&&("reverse"===m?(i=1-i,p&&(i-=p/s)):"mirror"===m&&(v=r)),x=j(0,1,i)*s}let b=y?{done:!1,value:c[0]}:v.next(x);n&&(b.value=n(b.value));let{done:w}=b;y||null===o||(w=this.playbackSpeed>=0?this.currentTime>=a:this.currentTime<=0);let k=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return k&&u!==tw&&(b.value=tG(c,this.options,g,this.speed)),h&&h(b.value),k&&this.finish(),b}then(e,t){return this.finished.then(e,t)}get duration(){return A(this.calculatedDuration)}get time(){return A(this.currentTime)}set time(e){e=S(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(eb.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=A(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=tb,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=t??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(eb.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,eD.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tH=e=>180*e/Math.PI,tQ=e=>tJ(tH(Math.atan2(e[1],e[0]))),tX={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tQ,rotateZ:tQ,skewX:e=>tH(Math.atan(e[1])),skewY:e=>tH(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},tJ=e=>((e%=360)<0&&(e+=360),e),tY=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tZ=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),t0={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tY,scaleY:tZ,scale:e=>(tY(e)+tZ(e))/2,rotateX:e=>tJ(tH(Math.atan2(e[6],e[5]))),rotateY:e=>tJ(tH(Math.atan2(-e[2],e[0]))),rotateZ:tQ,rotate:tQ,skewX:e=>tH(Math.atan(e[4])),skewY:e=>tH(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function t1(e){return+!!e.includes("scale")}function t2(e,t){let i,a;if(!e||"none"===e)return t1(t);let n=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=t0,a=n;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=tX,a=t}if(!a)return t1(t);let r=i[t],s=a[1].split(",").map(t4);return"function"==typeof r?r(s):s[r]}let t5=(e,t)=>{let{transform:i="none"}=getComputedStyle(e);return t2(i,t)};function t4(e){return parseFloat(e.trim())}let t3=e=>e===eV||e===e1,t9=new Set(["x","y","z"]),t8=eg.filter(e=>!t9.has(e)),t6={width:({x:e},{paddingLeft:t="0",paddingRight:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),height:({y:e},{paddingTop:t="0",paddingBottom:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>t2(t,"x"),y:(e,{transform:t})=>t2(t,"y")};t6.translateX=t6.x,t6.translateY=t6.y;let t7=new Set,ie=!1,it=!1,ii=!1;function ia(){if(it){let e=Array.from(t7).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),i=new Map;t.forEach(e=>{let t=function(e){let t=[];return t8.forEach(i=>{let a=e.getValue(i);void 0!==a&&(t.push([i,a.get()]),a.set(+!!i.startsWith("scale")))}),t}(e);t.length&&(i.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=i.get(e);t&&t.forEach(([t,i])=>{e.getValue(t)?.set(i)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}it=!1,ie=!1,t7.forEach(e=>e.complete(ii)),t7.clear()}function ir(){t7.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(it=!0)})}class is{constructor(e,t,i,a,n,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=i,this.motionValue=a,this.element=n,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(t7.add(this),ie||(ie=!0,em.read(ir),em.resolveKeyframes(ia))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:i,motionValue:a}=this;if(null===e[0]){let n=a?.get(),r=e[e.length-1];if(void 0!==n)e[0]=n;else if(i&&t){let a=i.readValue(t,r);null!=a&&(e[0]=a)}void 0===e[0]&&(e[0]=r),a&&void 0===n&&a.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),t7.delete(this)}cancel(){"scheduled"===this.state&&(t7.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let io=e=>e.startsWith("--");function il(e){let t;return()=>(void 0===t&&(t=e()),t)}let ic=il(()=>void 0!==window.ScrollTimeline),id={},im=function(e,t){let i=il(e);return()=>id[t]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),ip=([e,t,i,a])=>`cubic-bezier(${e}, ${t}, ${i}, ${a})`,iu={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ip([0,.65,.55,1]),circOut:ip([.55,0,1,.45]),backIn:ip([.31,.01,.66,-.59]),backOut:ip([.33,1.53,.69,.99])};class ih extends t_{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:i,keyframes:a,pseudoElement:n,allowFlatten:r=!1,finalKeyframe:s,onComplete:o}=e;this.isPseudoElement=!!n,this.allowFlatten=r,this.options=e,N("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return G(e)&&im()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,i,{delay:a=0,duration:n=300,repeat:r=0,repeatType:s="loop",ease:o="easeOut",times:l}={},c){let d={[t]:i};l&&(d.offset=l);let m=function e(t,i){if(t)return"function"==typeof t?im()?P(t,i):"ease-out":tz(t)?ip(t):Array.isArray(t)?t.map(t=>e(t,i)||iu.easeOut):iu[t]}(o,n);Array.isArray(m)&&(d.easing=m),ec.value&&eD.waapi++;let p={delay:a,duration:n,easing:Array.isArray(m)?"linear":m,fill:"both",iterations:r+1,direction:"reverse"===s?"alternate":"normal"};c&&(p.pseudoElement=c);let u=e.animate(d,p);return ec.value&&u.finished.finally(()=>{eD.waapi--}),u}(t,i,a,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let e=tG(a,this.options,s,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,i){io(t)?e.style.setProperty(t,i):e.style[t]=i}(t,i,e),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return A(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return A(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=S(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&ic())?(this.animation.timeline=e,es):t(this)}}let ig={anticipate:tN,backInOut:tI,circInOut:tL};class iy extends ih{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in ig&&(e.ease=ig[e.ease])}(e),tK(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:i,onComplete:a,element:n,...r}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let s=new tq({...r,autoplay:!1}),o=S(this.finishedTime??this.time);t.setWithVelocity(s.sample(o-10).value,s.sample(o).value,10),s.stop()}}let ix=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(tr.test(e)||"0"===e)&&!e.startsWith("url("));function iv(e){return"object"==typeof e&&null!==e}function ib(e){return iv(e)&&"offsetHeight"in e}let iw=new Set(["opacity","clipPath","filter","transform"]),ik=il(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class ij extends t_{constructor({autoplay:e=!0,delay:t=0,type:i="keyframes",repeat:a=0,repeatDelay:n=0,repeatType:r="loop",keyframes:s,name:o,motionValue:l,element:c,...d}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=eb.now();let m={autoplay:e,delay:t,type:i,repeat:a,repeatDelay:n,repeatType:r,name:o,motionValue:l,element:c,...d},p=c?.KeyframeResolver||is;this.keyframeResolver=new p(s,(e,t,i)=>this.onKeyframesResolved(e,t,m,!i),o,l,c),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,i,a){this.keyframeResolver=void 0;let{name:n,type:r,velocity:s,delay:o,isHandoff:l,onUpdate:c}=i;this.resolvedAt=eb.now(),!function(e,t,i,a){let n=e[0];if(null===n)return!1;if("display"===t||"visibility"===t)return!0;let r=e[e.length-1],s=ix(n,t),o=ix(r,t);return I(s===o,`You are trying to animate ${t} from "${n}" to "${r}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${r} via the \`style\` property.`),!!s&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let i=0;i<e.length;i++)if(e[i]!==t)return!0}(e)||("spring"===i||G(i))&&a)}(e,n,r,s)&&((eo.instantAnimations||!o)&&c?.(tG(e,i,t)),e[0]=e[e.length-1],i.duration=0,i.repeat=0);let d={startTime:a?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...i,keyframes:e},m=!l&&function(e){let{motionValue:t,name:i,repeatDelay:a,repeatType:n,damping:r,type:s}=e;if(!ib(t?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=t.owner.getProps();return ik()&&i&&iw.has(i)&&("transform"!==i||!l)&&!o&&!a&&"mirror"!==n&&0!==r&&"inertia"!==s}(d)?new iy({...d,element:d.motionValue.owner.current}):new tq(d);m.finished.then(()=>this.notifyFinished()).catch(es),this.pendingTimeline&&(this.stopTimeline=m.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=m}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),ii=!0,ir(),ia(),ii=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let iS=e=>null!==e,iA={type:"spring",stiffness:500,damping:25,restSpeed:10},iP=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),iC={type:"keyframes",duration:.8},iT={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},iR=(e,{keyframes:t})=>t.length>2?iC:ef.has(e)?e.startsWith("scale")?iP(t[1]):iA:iT,iM=(e,t,i,a={},n,r)=>s=>{let o=er(a,e)||{},l=o.delay||a.delay||0,{elapsed:c=0}=a;c-=S(l);let d={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:t.getVelocity(),...o,delay:-c,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{s(),o.onComplete&&o.onComplete()},name:e,motionValue:t,element:r?void 0:n};!function({when:e,delay:t,delayChildren:i,staggerChildren:a,staggerDirection:n,repeat:r,repeatType:s,repeatDelay:o,from:l,elapsed:c,...d}){return!!Object.keys(d).length}(o)&&Object.assign(d,iR(e,d)),d.duration&&(d.duration=S(d.duration)),d.repeatDelay&&(d.repeatDelay=S(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let m=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0===d.delay&&(m=!0)),(eo.instantAnimations||eo.skipAnimations)&&(m=!0,d.duration=0,d.delay=0),d.allowFlatten=!o.type&&!o.ease,m&&!r&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:i="loop"},a){let n=e.filter(iS),r=t&&"loop"!==i&&t%2==1?0:n.length-1;return n[r]}(d.keyframes,o);if(void 0!==e)return void em.update(()=>{d.onUpdate(e),d.onComplete()})}return o.isSync?new tq(d):new ij(d)};function iI(e,t,{delay:i=0,transitionOverride:a,type:n}={}){let{transition:r=e.getDefaultTransition(),transitionEnd:s,...o}=t;a&&(r=a);let l=[],c=n&&e.animationState&&e.animationState.getState()[n];for(let t in o){let a=e.getValue(t,e.latestValues[t]??null),n=o[t];if(void 0===n||c&&function({protectedKeys:e,needsAnimating:t},i){let a=e.hasOwnProperty(i)&&!0!==t[i];return t[i]=!1,a}(c,t))continue;let s={delay:i,...er(r||{},t)},d=a.get();if(void 0!==d&&!a.isAnimating&&!Array.isArray(n)&&n===d&&!s.velocity)continue;let m=!1;if(window.MotionHandoffAnimation){let i=e.props[eI];if(i){let e=window.MotionHandoffAnimation(i,t,em);null!==e&&(s.startTime=e,m=!0)}}eR(e,t),a.start(iM(t,a,n,e.shouldReduceMotion&&ey.has(t)?{type:!1}:s,e,m));let p=a.animation;p&&l.push(p)}return s&&Promise.all(l).then(()=>{em.update(()=>{s&&function(e,t){let{transitionEnd:i={},transition:a={},...n}=eT(e,t)||{};for(let t in n={...n,...i}){var r;let i=eA(r=n[t])?r[r.length-1]||0:r;e.hasValue(t)?e.getValue(t).set(i):e.addValue(t,eS(i))}}(e,s)})}),l}function iN(e){return iv(e)&&"ownerSVGElement"in e}function iE(e){return iN(e)&&"svg"===e.tagName}function iD({top:e,left:t,right:i,bottom:a}){return{x:{min:t,max:i},y:{min:e,max:a}}}function iL(e){return void 0===e||1===e}function iz({scale:e,scaleX:t,scaleY:i}){return!iL(e)||!iL(t)||!iL(i)}function iO(e){return iz(e)||i$(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function i$(e){var t,i;return(t=e.x)&&"0%"!==t||(i=e.y)&&"0%"!==i}function iF(e,t,i,a,n){return void 0!==n&&(e=a+n*(e-a)),a+i*(e-a)+t}function iV(e,t=0,i=1,a,n){e.min=iF(e.min,t,i,a,n),e.max=iF(e.max,t,i,a,n)}function iB(e,{x:t,y:i}){iV(e.x,t.translate,t.scale,t.originPoint),iV(e.y,i.translate,i.scale,i.originPoint)}function iG(e,t){e.min=e.min+t,e.max=e.max+t}function iW(e,t,i,a,n=.5){let r=F(e.min,e.max,n);iV(e,t,i,r,a)}function iK(e,t){iW(e.x,t.x,t.scaleX,t.scale,t.originX),iW(e.y,t.y,t.scaleY,t.scale,t.originY)}function i_(e,t){return iD(function(e,t){if(!t)return e;let i=t({x:e.left,y:e.top}),a=t({x:e.right,y:e.bottom});return{top:i.y,left:i.x,bottom:a.y,right:a.x}}(e.getBoundingClientRect(),t))}let iU=e=>t=>t.test(e),iq=[eV,e1,e0,eZ,e5,e2,{test:e=>"auto"===e,parse:e=>e}],iH=e=>iq.find(iU(e)),iQ=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),iX=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,iJ=e=>/^0[^.\s]+$/u.test(e),iY=new Set(["brightness","contrast","saturate","opacity"]);function iZ(e){let[t,i]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[a]=i.match(eK)||[];if(!a)return e;let n=i.replace(a,""),r=+!!iY.has(t);return a!==i&&(r*=100),t+"("+r+n+")"}let i0=/\b([a-z-]*)\(.*?\)/gu,i1={...tr,getAnimatableNone:e=>{let t=e.match(i0);return t?t.map(iZ).join(" "):e}},i2={...eV,transform:Math.round},i5={borderWidth:e1,borderTopWidth:e1,borderRightWidth:e1,borderBottomWidth:e1,borderLeftWidth:e1,borderRadius:e1,radius:e1,borderTopLeftRadius:e1,borderTopRightRadius:e1,borderBottomRightRadius:e1,borderBottomLeftRadius:e1,width:e1,maxWidth:e1,height:e1,maxHeight:e1,top:e1,right:e1,bottom:e1,left:e1,padding:e1,paddingTop:e1,paddingRight:e1,paddingBottom:e1,paddingLeft:e1,margin:e1,marginTop:e1,marginRight:e1,marginBottom:e1,marginLeft:e1,backgroundPositionX:e1,backgroundPositionY:e1,rotate:eZ,rotateX:eZ,rotateY:eZ,rotateZ:eZ,scale:eG,scaleX:eG,scaleY:eG,scaleZ:eG,skew:eZ,skewX:eZ,skewY:eZ,distance:e1,translateX:e1,translateY:e1,translateZ:e1,x:e1,y:e1,z:e1,perspective:e1,transformPerspective:e1,opacity:eB,originX:e4,originY:e4,originZ:e1,zIndex:i2,fillOpacity:eB,strokeOpacity:eB,numOctaves:i2},i4={...i5,color:e9,backgroundColor:e9,outlineColor:e9,fill:e9,stroke:e9,borderColor:e9,borderTopColor:e9,borderRightColor:e9,borderBottomColor:e9,borderLeftColor:e9,filter:i1,WebkitFilter:i1},i3=e=>i4[e];function i9(e,t){let i=i3(e);return i!==i1&&(i=tr),i.getAnimatableNone?i.getAnimatableNone(t):void 0}let i8=new Set(["auto","none","0"]);class i6 extends is{constructor(e,t,i,a,n){super(e,t,i,a,n,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:i}=this;if(!t||!t.current)return;super.readKeyframes();for(let i=0;i<e.length;i++){let a=e[i];if("string"==typeof a&&e$(a=a.trim())){let n=function e(t,i,a=1){N(a<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[n,r]=function(e){let t=iX.exec(e);if(!t)return[,];let[,i,a,n]=t;return[`--${i??a}`,n]}(t);if(!n)return;let s=window.getComputedStyle(i).getPropertyValue(n);if(s){let e=s.trim();return iQ(e)?parseFloat(e):e}return e$(r)?e(r,i,a+1):r}(a,t.current);void 0!==n&&(e[i]=n),i===e.length-1&&(this.finalKeyframe=a)}}if(this.resolveNoneKeyframes(),!ey.has(i)||2!==e.length)return;let[a,n]=e,r=iH(a),s=iH(n);if(r!==s)if(t3(r)&&t3(s))for(let t=0;t<e.length;t++){let i=e[t];"string"==typeof i&&(e[t]=parseFloat(i))}else t6[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,i=[];for(let t=0;t<e.length;t++){var a;(null===e[t]||("number"==typeof(a=e[t])?0===a:null===a||"none"===a||"0"===a||iJ(a)))&&i.push(t)}i.length&&function(e,t,i){let a,n=0;for(;n<e.length&&!a;){let t=e[n];"string"==typeof t&&!i8.has(t)&&tt(t).values.length&&(a=e[n]),n++}if(a&&i)for(let n of t)e[n]=i9(i,a)}(e,i,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:i}=this;if(!e||!e.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=t6[i](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let a=t[t.length-1];void 0!==a&&e.getValue(i,a).jump(a,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:i}=this;if(!e||!e.current)return;let a=e.getValue(t);a&&a.jump(this.measuredOrigin,!1);let n=i.length-1,r=i[n];i[n]=t6[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let i7=[...iq,e9,tr],ae=e=>i7.find(iU(e)),at={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ai={};for(let e in at)ai[e]={isEnabled:t=>at[e].some(e=>!!t[e])};let aa=()=>({translate:0,scale:1,origin:0,originPoint:0}),an=()=>({x:aa(),y:aa()}),ar=()=>({min:0,max:0}),as=()=>({x:ar(),y:ar()}),ao="undefined"!=typeof window,al={current:null},ac={current:!1};function ad(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function am(e){return"string"==typeof e||Array.isArray(e)}let ap=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],au=["initial",...ap];function ah(e){return ad(e.animate)||au.some(t=>am(e[t]))}function ag(e){return!!(ah(e)||e.variants)}let af=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ay{scrapeMotionValuesFromProps(e,t,i){return{}}constructor({parent:e,props:t,presenceContext:i,reducedMotionConfig:a,blockInitialAnimation:n,visualState:r},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=is,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=eb.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,em.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=r;this.latestValues=o,this.baseTarget={...o},this.initialValues=t.initial?{...o}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=a,this.options=s,this.blockInitialAnimation=!!n,this.isControllingVariants=ah(t),this.isVariantNode=ag(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:c,...d}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in d){let t=d[e];void 0!==o[e]&&W(t)&&t.set(o[e],!1)}}mount(e){this.current=e,en.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),ac.current||function(){if(ac.current=!0,ao)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>al.current=e.matches;e.addListener(t),t()}else al.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||al.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),ep(this.notifyUpdate),ep(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let i;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let a=ef.has(e);a&&this.onBindTransform&&this.onBindTransform();let n=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&em.preRender(this.notifyUpdate),a&&this.projection&&(this.projection.isTransformDirty=!0)}),r=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{n(),r(),i&&i(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in ai){let t=ai[e];if(!t)continue;let{isEnabled:i,Feature:a}=t;if(!this.features[e]&&a&&i(this.props)&&(this.features[e]=new a(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):as()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<af.length;t++){let i=af[t];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let a=e["on"+i];a&&(this.propEventSubscriptions[i]=this.on(i,a))}this.prevMotionValues=function(e,t,i){for(let a in t){let n=t[a],r=i[a];if(W(n))e.addValue(a,n);else if(W(r))e.addValue(a,eS(n,{owner:e}));else if(r!==n)if(e.hasValue(a)){let t=e.getValue(a);!0===t.liveStyle?t.jump(n):t.hasAnimated||t.set(n)}else{let t=e.getStaticValue(a);e.addValue(a,eS(void 0!==t?t:n,{owner:e}))}}for(let a in i)void 0===t[a]&&e.removeValue(a);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let i=this.values.get(e);t!==i&&(i&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return void 0===i&&void 0!==t&&(i=eS(null===t?void 0:t,{owner:this}),this.addValue(e,i)),i}readValue(e,t){let i=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=i&&("string"==typeof i&&(iQ(i)||iJ(i))?i=parseFloat(i):!ae(i)&&tr.test(t)&&(i=i9(e,t)),this.setBaseTarget(e,W(i)?i.get():i)),W(i)?i.get():i}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let a=eC(this.props,i,this.presenceContext?.custom);a&&(t=a[e])}if(i&&void 0!==t)return t;let a=this.getBaseTargetFromProps(this.props,e);return void 0===a||W(a)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:a}on(e,t){return this.events[e]||(this.events[e]=new ex),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class ax extends ay{constructor(){super(...arguments),this.KeyframeResolver=i6}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:i}){delete t[e],delete i[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;W(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}let av=(e,t)=>t&&"number"==typeof e?t.transform(e):e,ab={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},aw=eg.length;function ak(e,t,i){let{style:a,vars:n,transformOrigin:r}=e,s=!1,o=!1;for(let e in t){let i=t[e];if(ef.has(e)){s=!0;continue}if(ez(e)){n[e]=i;continue}{let t=av(i,i5[e]);e.startsWith("origin")?(o=!0,r[e]=t):a[e]=t}}if(!t.transform&&(s||i?a.transform=function(e,t,i){let a="",n=!0;for(let r=0;r<aw;r++){let s=eg[r],o=e[s];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!s.startsWith("scale"):0===parseFloat(o))||i){let e=av(o,i5[s]);if(!l){n=!1;let t=ab[s]||s;a+=`${t}(${e}) `}i&&(t[s]=e)}}return a=a.trim(),i?a=i(t,n?"":a):n&&(a="none"),a}(t,e.transform,i):a.transform&&(a.transform="none")),o){let{originX:e="50%",originY:t="50%",originZ:i=0}=r;a.transformOrigin=`${e} ${t} ${i}`}}function aj(e,{style:t,vars:i},a,n){for(let r in Object.assign(e.style,t,n&&n.getProjectionStyles(a)),i)e.style.setProperty(r,i[r])}let aS={};function aA(e,{layout:t,layoutId:i}){return ef.has(e)||e.startsWith("origin")||(t||void 0!==i)&&(!!aS[e]||"opacity"===e)}function aP(e,t,i){let{style:a}=e,n={};for(let r in a)(W(a[r])||t.style&&W(t.style[r])||aA(r,e)||i?.getValue(r)?.liveStyle!==void 0)&&(n[r]=a[r]);return n}class aC extends ax{constructor(){super(...arguments),this.type="html",this.renderInstance=aj}readValueFromInstance(e,t){if(ef.has(t))return this.projection?.isProjecting?t1(t):t5(e,t);{let i=window.getComputedStyle(e),a=(ez(t)?i.getPropertyValue(t):i[t])||0;return"string"==typeof a?a.trim():a}}measureInstanceViewportBox(e,{transformPagePoint:t}){return i_(e,t)}build(e,t,i){ak(e,t,i.transformTemplate)}scrapeMotionValuesFromProps(e,t,i){return aP(e,t,i)}}class aT extends ay{constructor(){super(...arguments),this.type="object"}readValueFromInstance(e,t){if(t in e){let i=e[t];if("string"==typeof i||"number"==typeof i)return i}}getBaseTargetFromProps(){}removeValueFromRenderState(e,t){delete t.output[e]}measureInstanceViewportBox(){return as()}build(e,t){Object.assign(e.output,t)}renderInstance(e,{output:t}){Object.assign(e,t)}sortInstanceNodePosition(){return 0}}let aR={offset:"stroke-dashoffset",array:"stroke-dasharray"},aM={offset:"strokeDashoffset",array:"strokeDasharray"};function aI(e,{attrX:t,attrY:i,attrScale:a,pathLength:n,pathSpacing:r=1,pathOffset:s=0,...o},l,c,d){if(ak(e,o,c),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:m,style:p}=e;m.transform&&(p.transform=m.transform,delete m.transform),(p.transform||m.transformOrigin)&&(p.transformOrigin=m.transformOrigin??"50% 50%",delete m.transformOrigin),p.transform&&(p.transformBox=d?.transformBox??"fill-box",delete m.transformBox),void 0!==t&&(m.x=t),void 0!==i&&(m.y=i),void 0!==a&&(m.scale=a),void 0!==n&&function(e,t,i=1,a=0,n=!0){e.pathLength=1;let r=n?aR:aM;e[r.offset]=e1.transform(-a);let s=e1.transform(t),o=e1.transform(i);e[r.array]=`${s} ${o}`}(m,n,r,s,!1)}let aN=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),aE=e=>"string"==typeof e&&"svg"===e.toLowerCase();function aD(e,t,i){let a=aP(e,t,i);for(let i in e)(W(e[i])||W(t[i]))&&(a[-1!==eg.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]);return a}class aL extends ax{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=as}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(ef.has(t)){let e=i3(t);return e&&e.default||0}return t=aN.has(t)?t:eM(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,i){return aD(e,t,i)}build(e,t,i){aI(e,t,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(e,t,i,a){for(let i in aj(e,t,void 0,a),t.attrs)e.setAttribute(aN.has(i)?i:eM(i),t.attrs[i])}mount(e){this.isSVGTag=aE(e.tagName),super.mount(e)}}function az(e){let t={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},i=iN(e)&&!iE(e)?new aL(t):new aC(t);i.mount(e),en.set(e,i)}function aO(e){let t=new aT({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});t.mount(e),en.set(e,t)}function a$(e,t,i){let a=W(e)?e:eS(e);return a.start(iM("",a,t,i)),a.animation}function aF(e,t,i,a){let n=[];if(W(e)||"number"==typeof e||"string"==typeof e&&!H(t))n.push(a$(e,H(t)&&t.default||t,i&&i.default||i));else{let r=Q(e,t,a),s=r.length;N(!!s,"No valid elements provided.");for(let e=0;e<s;e++){let a=r[e],o=a instanceof Element?az:aO;en.has(a)||o(a);let l=en.get(a),c={...i};"delay"in c&&"function"==typeof c.delay&&(c.delay=c.delay(e,s)),n.push(...iI(l,{...t,transition:c},{}))}}return n}function aV(e){return function(t,i,a){let n=[],r=new k(n=Array.isArray(t)&&t.some(Array.isArray)?function(e,t,i){let a=[];return(function(e,{defaultTransition:t={},...i}={},a,n){let r=t.duration||.3,s=new Map,o=new Map,l={},c=new Map,d=0,m=0,p=0;for(let i=0;i<e.length;i++){let s=e[i];if("string"==typeof s){c.set(s,m);continue}if(!Array.isArray(s)){c.set(s.name,X(m,s.at,d,c));continue}let[g,f,y={}]=s;void 0!==y.at&&(m=X(m,y.at,d,c));let x=0,v=(e,i,a,s=0,o=0)=>{var l;let c=Array.isArray(l=e)?l:[l],{delay:d=0,times:u=B(c),type:h="keyframes",repeat:g,repeatType:f,repeatDelay:y=0,...v}=i,{ease:b=t.ease||"easeOut",duration:w}=i,k="function"==typeof d?d(s,o):d,j=c.length,A=G(h)?h:n?.[h||"keyframes"];if(j<=2&&A){let e=100;2===j&&ea(c)&&(e=Math.abs(c[1]-c[0]));let t={...v};void 0!==w&&(t.duration=S(w));let i=T(t,e,A);b=i.ease,w=i.duration}w??(w=r);let P=m+k;1===u.length&&0===u[0]&&(u[1]=1);let C=u.length-c.length;if(C>0&&V(u,C),1===c.length&&c.unshift(null),g){N(g<20,"Repeat count too high, must be less than 20");w*=g+1;let e=[...c],t=[...u],i=[...b=Array.isArray(b)?[...b]:[b]];for(let a=0;a<g;a++){c.push(...e);for(let n=0;n<e.length;n++)u.push(t[n]+(a+1)),b.push(0===n?"linear":U(i,n-1))}for(let e=0;e<u.length;e++)u[e]=u[e]/(g+1)}let R=P+w;!function(e,t,i,a,n,r){for(let t=0;t<e.length;t++){let i=e[t];i.at>n&&i.at<r&&(Y(e,i),t--)}for(let s=0;s<t.length;s++)e.push({value:t[s],at:F(n,r,a[s]),easing:U(i,s)})}(a,c,b,u,P,R),x=Math.max(k+w,x),p=Math.max(R,p)};if(W(g))v(f,y,et("default",ee(g,o)));else{let e=Q(g,f,a,l),t=e.length;for(let i=0;i<t;i++){let a=ee(e[i],o);for(let e in f){var u,h;v(f[e],(u=y,h=e,u&&u[h]?{...u,...u[h]}:{...u}),et(e,a),i,t)}}}d=m,m+=x}return o.forEach((e,a)=>{for(let n in e){let r=e[n];r.sort(Z);let o=[],l=[],c=[];for(let e=0;e<r.length;e++){let{at:t,value:i,easing:a}=r[e];o.push(i),l.push($(0,p,t)),c.push(a||"easeOut")}0!==l[0]&&(l.unshift(0),o.unshift(o[0]),c.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),o.push(null)),s.has(a)||s.set(a,{keyframes:{},transition:{}});let d=s.get(a);d.keyframes[n]=o,d.transition[n]={...t,duration:p,ease:c,times:l,...i}}}),s})(e,t,i,{spring:O}).forEach(({keyframes:e,transition:t},i)=>{a.push(...aF(i,e,t))}),a}(t,i,e):aF(t,i,a,e));return e&&e.animations.push(r),r}}function aB(e,t,i={}){let a=eT(e,t,"exit"===i.type?e.presenceContext?.custom:void 0),{transition:n=e.getDefaultTransition()||{}}=a||{};i.transitionOverride&&(n=i.transitionOverride);let r=a?()=>Promise.all(iI(e,a,i)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(a=0)=>{let{delayChildren:r=0,staggerChildren:s,staggerDirection:o}=n;return function(e,t,i=0,a=0,n=1,r){let s=[],o=(e.variantChildren.size-1)*a,l=1===n?(e=0)=>e*a:(e=0)=>o-e*a;return Array.from(e.variantChildren).sort(aG).forEach((e,a)=>{e.notify("AnimationStart",t),s.push(aB(e,t,{...r,delay:i+l(a)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(s)}(e,t,r+a,s,o,i)}:()=>Promise.resolve(),{when:o}=n;if(!o)return Promise.all([r(),s(i.delay)]);{let[e,t]="beforeChildren"===o?[r,s]:[s,r];return e().then(()=>t())}}function aG(e,t){return e.sortNodePosition(t)}function aW(e,t){if(!Array.isArray(t))return!1;let i=t.length;if(i!==e.length)return!1;for(let a=0;a<i;a++)if(t[a]!==e[a])return!1;return!0}aV();let aK=au.length,a_=[...ap].reverse(),aU=ap.length;function aq(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function aH(){return{animate:aq(!0),whileInView:aq(),whileHover:aq(),whileTap:aq(),whileDrag:aq(),whileFocus:aq(),exit:aq()}}class aQ{constructor(e){this.isMounted=!1,this.node=e}update(){}}class aX extends aQ{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:i})=>(function(e,t,i={}){let a;if(e.notify("AnimationStart",t),Array.isArray(t))a=Promise.all(t.map(t=>aB(e,t,i)));else if("string"==typeof t)a=aB(e,t,i);else{let n="function"==typeof t?eT(e,t,i.custom):t;a=Promise.all(iI(e,n,i))}return a.then(()=>{e.notify("AnimationComplete",t)})})(e,t,i))),i=aH(),a=!0,n=t=>(i,a)=>{let n=eT(e,a,"exit"===t?e.presenceContext?.custom:void 0);if(n){let{transition:e,transitionEnd:t,...a}=n;i={...i,...a,...t}}return i};function r(r){let{props:s}=e,o=function e(t){if(!t)return;if(!t.isControllingVariants){let i=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(i.initial=t.props.initial),i}let i={};for(let e=0;e<aK;e++){let a=au[e],n=t.props[a];(am(n)||!1===n)&&(i[a]=n)}return i}(e.parent)||{},l=[],c=new Set,d={},m=1/0;for(let t=0;t<aU;t++){var p,u;let h=a_[t],g=i[h],f=void 0!==s[h]?s[h]:o[h],y=am(f),x=h===r?g.isActive:null;!1===x&&(m=t);let v=f===o[h]&&f!==s[h]&&y;if(v&&a&&e.manuallyAnimateOnMount&&(v=!1),g.protectedKeys={...d},!g.isActive&&null===x||!f&&!g.prevProp||ad(f)||"boolean"==typeof f)continue;let b=(p=g.prevProp,"string"==typeof(u=f)?u!==p:!!Array.isArray(u)&&!aW(u,p)),w=b||h===r&&g.isActive&&!v&&y||t>m&&y,k=!1,j=Array.isArray(f)?f:[f],S=j.reduce(n(h),{});!1===x&&(S={});let{prevResolvedValues:A={}}=g,P={...A,...S},C=t=>{w=!0,c.has(t)&&(k=!0,c.delete(t)),g.needsAnimating[t]=!0;let i=e.getValue(t);i&&(i.liveStyle=!1)};for(let e in P){let t=S[e],i=A[e];if(d.hasOwnProperty(e))continue;let a=!1;(eA(t)&&eA(i)?aW(t,i):t===i)?void 0!==t&&c.has(e)?C(e):g.protectedKeys[e]=!0:null!=t?C(e):c.add(e)}g.prevProp=f,g.prevResolvedValues=S,g.isActive&&(d={...d,...S}),a&&e.blockInitialAnimation&&(w=!1);let T=!(v&&b)||k;w&&T&&l.push(...j.map(e=>({animation:e,options:{type:h}})))}if(c.size){let t={};if("boolean"!=typeof s.initial){let i=eT(e,Array.isArray(s.initial)?s.initial[0]:s.initial);i&&i.transition&&(t.transition=i.transition)}c.forEach(i=>{let a=e.getBaseTarget(i),n=e.getValue(i);n&&(n.liveStyle=!0),t[i]=a??null}),l.push({animation:t})}let h=!!l.length;return a&&(!1===s.initial||s.initial===s.animate)&&!e.manuallyAnimateOnMount&&(h=!1),a=!1,h?t(l):Promise.resolve()}return{animateChanges:r,setActive:function(t,a){if(i[t].isActive===a)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,a)),i[t].isActive=a;let n=r(t);for(let e in i)i[e].protectedKeys={};return n},setAnimateFunction:function(i){t=i(e)},getState:()=>i,reset:()=>{i=aH(),a=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();ad(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let aJ=0;class aY extends aQ{constructor(){super(...arguments),this.id=aJ++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;let a=this.node.animationState.setActive("exit",!e);t&&!e&&a.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let aZ={x:!1,y:!1};function a0(e,t,i,a={passive:!0}){return e.addEventListener(t,i,a),()=>e.removeEventListener(t,i)}let a1=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function a2(e){return{point:{x:e.pageX,y:e.pageY}}}let a5=e=>t=>a1(t)&&e(t,a2(t));function a4(e,t,i,a){return a0(e,t,a5(i),a)}function a3(e){return e.max-e.min}function a9(e,t,i,a=.5){e.origin=a,e.originPoint=F(t.min,t.max,e.origin),e.scale=a3(i)/a3(t),e.translate=F(i.min,i.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function a8(e,t,i,a){a9(e.x,t.x,i.x,a?a.originX:void 0),a9(e.y,t.y,i.y,a?a.originY:void 0)}function a6(e,t,i){e.min=i.min+t.min,e.max=e.min+a3(t)}function a7(e,t,i){e.min=t.min-i.min,e.max=e.min+a3(t)}function ne(e,t,i){a7(e.x,t.x,i.x),a7(e.y,t.y,i.y)}function nt(e){return[e("x"),e("y")]}let ni=({current:e})=>e?e.ownerDocument.defaultView:null;function na(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let nn=(e,t)=>Math.abs(e-t);class nr{constructor(e,t,{transformPagePoint:i,contextWindow:a,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=nl(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,i=function(e,t){return Math.sqrt(nn(e.x,t.x)**2+nn(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!i)return;let{point:a}=e,{timestamp:n}=eu;this.history.push({...a,timestamp:n});let{onStart:r,onMove:s}=this.handlers;t||(r&&r(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=ns(t,this.transformPagePoint),em.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:i,onSessionEnd:a,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=nl("pointercancel"===e.type?this.lastMoveEventInfo:ns(t,this.transformPagePoint),this.history);this.startEvent&&i&&i(e,r),a&&a(e,r)},!a1(e))return;this.dragSnapToOrigin=n,this.handlers=t,this.transformPagePoint=i,this.contextWindow=a||window;let r=ns(a2(e),this.transformPagePoint),{point:s}=r,{timestamp:o}=eu;this.history=[{...s,timestamp:o}];let{onSessionStart:l}=t;l&&l(e,nl(r,this.history)),this.removeListeners=eE(a4(this.contextWindow,"pointermove",this.handlePointerMove),a4(this.contextWindow,"pointerup",this.handlePointerUp),a4(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),ep(this.updatePoint)}}function ns(e,t){return t?{point:t(e.point)}:e}function no(e,t){return{x:e.x-t.x,y:e.y-t.y}}function nl({point:e},t){return{point:e,delta:no(e,nc(t)),offset:no(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let i=e.length-1,a=null,n=nc(e);for(;i>=0&&(a=e[i],!(n.timestamp-a.timestamp>S(.1)));)i--;if(!a)return{x:0,y:0};let r=A(n.timestamp-a.timestamp);if(0===r)return{x:0,y:0};let s={x:(n.x-a.x)/r,y:(n.y-a.y)/r};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(t,.1)}}function nc(e){return e[e.length-1]}function nd(e,t,i){return{min:void 0!==t?e.min+t:void 0,max:void 0!==i?e.max+i-(e.max-e.min):void 0}}function nm(e,t){let i=t.min-e.min,a=t.max-e.max;return t.max-t.min<e.max-e.min&&([i,a]=[a,i]),{min:i,max:a}}function np(e,t,i){return{min:nu(e,t),max:nu(e,i)}}function nu(e,t){return"number"==typeof e?e:e[t]||0}let nh=new WeakMap;class ng{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=as(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:a}=this.getProps();this.panSession=new nr(e,{onSessionStart:e=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(a2(e).point)},onStart:(e,t)=>{let{drag:i,dragPropagation:a,onDragStart:n}=this.getProps();if(i&&!a&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(aZ[e])return null;else return aZ[e]=!0,()=>{aZ[e]=!1};return aZ.x||aZ.y?null:(aZ.x=aZ.y=!0,()=>{aZ.x=aZ.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nt(e=>{let t=this.getAxisMotionValue(e).get()||0;if(e0.test(t)){let{projection:i}=this.visualElement;if(i&&i.layout){let a=i.layout.layoutBox[e];a&&(t=a3(a)*(parseFloat(t)/100))}}this.originPoint[e]=t}),n&&em.postRender(()=>n(e,t)),eR(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:i,dragDirectionLock:a,onDirectionLock:n,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:s}=t;if(a&&null===this.currentDirection){this.currentDirection=function(e,t=10){let i=null;return Math.abs(e.y)>t?i="y":Math.abs(e.x)>t&&(i="x"),i}(s),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",t.point,s),this.updateAxis("y",t.point,s),this.visualElement.render(),r&&r(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>nt(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:a,contextWindow:ni(this.visualElement)})}stop(e,t){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:a}=t;this.startAnimation(a);let{onDragEnd:n}=this.getProps();n&&em.postRender(()=>n(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,i){let{drag:a}=this.getProps();if(!i||!nf(e,a,this.currentDirection))return;let n=this.getAxisMotionValue(e),r=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(r=function(e,{min:t,max:i},a){return void 0!==t&&e<t?e=a?F(t,e,a.min):Math.max(e,t):void 0!==i&&e>i&&(e=a?F(i,e,a.max):Math.min(e,i)),e}(r,this.constraints[e],this.elastic[e])),n.set(r)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,a=this.constraints;e&&na(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&i?this.constraints=function(e,{top:t,left:i,bottom:a,right:n}){return{x:nd(e.x,i,n),y:nd(e.y,t,a)}}(i.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:np(e,"left","right"),y:np(e,"top","bottom")}}(t),a!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&nt(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let i={};return void 0!==t.min&&(i.min=t.min-e.min),void 0!==t.max&&(i.max=t.max-e.min),i}(i.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:i}=this.getProps();if(!t||!na(t))return!1;let a=t.current;N(null!==a,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let r=function(e,t,i){let a=i_(e,i),{scroll:n}=t;return n&&(iG(a.x,n.offset.x),iG(a.y,n.offset.y)),a}(a,n.root,this.visualElement.getTransformPagePoint()),s=(e=n.layout.layoutBox,{x:nm(e.x,r.x),y:nm(e.y,r.y)});if(i){let e=i(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(s));this.hasMutatedConstraints=!!e,e&&(s=iD(e))}return s}startAnimation(e){let{drag:t,dragMomentum:i,dragElastic:a,dragTransition:n,dragSnapToOrigin:r,onDragTransitionEnd:s}=this.getProps(),o=this.constraints||{};return Promise.all(nt(s=>{if(!nf(s,t,this.currentDirection))return;let l=o&&o[s]||{};r&&(l={min:0,max:0});let c={type:"inertia",velocity:i?e[s]:0,bounceStiffness:a?200:1e6,bounceDamping:a?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(s,c)})).then(s)}startAxisValueAnimation(e,t){let i=this.getAxisMotionValue(e);return eR(this.visualElement,e),i.start(iM(e,i,0,t,this.visualElement,!1))}stopAnimation(){nt(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){nt(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,i=this.visualElement.getProps();return i[t]||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){nt(t=>{let{drag:i}=this.getProps();if(!nf(t,i,this.currentDirection))return;let{projection:a}=this.visualElement,n=this.getAxisMotionValue(t);if(a&&a.layout){let{min:i,max:r}=a.layout.layoutBox[t];n.set(e[t]-F(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:i}=this.visualElement;if(!na(t)||!i||!this.constraints)return;this.stopAnimation();let a={x:0,y:0};nt(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let i=t.get();a[e]=function(e,t){let i=.5,a=a3(e),n=a3(t);return n>a?i=$(t.min,t.max-a,e.min):a>n&&(i=$(e.min,e.max-n,t.min)),j(0,1,i)}({min:i,max:i},this.constraints[e])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),nt(t=>{if(!nf(t,e,null))return;let i=this.getAxisMotionValue(t),{min:n,max:r}=this.constraints[t];i.set(F(n,r,a[t]))})}addListeners(){if(!this.visualElement.current)return;nh.set(this.visualElement,this);let e=a4(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:i=!0}=this.getProps();t&&i&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();na(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,a=i.addEventListener("measure",t);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),em.read(t);let n=a0(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(nt(t=>{let i=this.getAxisMotionValue(t);i&&(this.originPoint[t]+=e[t].translate,i.set(i.get()+e[t].translate))}),this.visualElement.render())});return()=>{n(),e(),a(),r&&r()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:i=!1,dragPropagation:a=!1,dragConstraints:n=!1,dragElastic:r=.35,dragMomentum:s=!0}=e;return{...e,drag:t,dragDirectionLock:i,dragPropagation:a,dragConstraints:n,dragElastic:r,dragMomentum:s}}}function nf(e,t,i){return(!0===t||t===e)&&(null===i||i===e)}class ny extends aQ{constructor(e){super(e),this.removeGroupControls=es,this.removeListeners=es,this.controls=new ng(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||es}unmount(){this.removeGroupControls(),this.removeListeners()}}let nx=e=>(t,i)=>{e&&em.postRender(()=>e(t,i))};class nv extends aQ{constructor(){super(...arguments),this.removePointerDownListener=es}onPointerDown(e){this.session=new nr(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ni(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:i,onPanEnd:a}=this.node.getProps();return{onSessionStart:nx(e),onStart:nx(t),onMove:i,onEnd:(e,t)=>{delete this.session,a&&em.postRender(()=>a(e,t))}}}mount(){this.removePointerDownListener=a4(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let{schedule:nb}=ed(queueMicrotask,!1),nw=(0,o.createContext)(null);function nk(e=!0){let t=(0,o.useContext)(nw);if(null===t)return[!0,null];let{isPresent:i,onExitComplete:a,register:n}=t,r=(0,o.useId)();(0,o.useEffect)(()=>{if(e)return n(r)},[e]);let s=(0,o.useCallback)(()=>e&&a&&a(r),[r,a,e]);return!i&&a?[!1,s]:[!0]}let nj=(0,o.createContext)({}),nS=(0,o.createContext)({}),nA={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nP(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let nC={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!e1.test(e))return e;else e=parseFloat(e);let i=nP(e,t.target.x),a=nP(e,t.target.y);return`${i}% ${a}%`}};class nT extends o.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i,layoutId:a}=this.props,{projection:n}=e;for(let e in nM)aS[e]=nM[e],ez(e)&&(aS[e].isCSSVariable=!0);n&&(t.group&&t.group.add(n),i&&i.register&&a&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),nA.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:i,drag:a,isPresent:n}=this.props,{projection:r}=i;return r&&(r.isPresent=n,a||e.layoutDependency!==t||void 0===t||e.isPresent!==n?r.willUpdate():this.safeToRemove(),e.isPresent!==n&&(n?r.promote():r.relegate()||em.postRender(()=>{let e=r.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),nb.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i}=this.props,{projection:a}=e;a&&(a.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(a),i&&i.deregister&&i.deregister(a))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function nR(e){let[t,i]=nk(),a=(0,o.useContext)(nj);return(0,s.jsx)(nT,{...e,layoutGroup:a,switchLayoutGroup:(0,o.useContext)(nS),isPresent:t,safeToRemove:i})}let nM={borderRadius:{...nC,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:nC,borderTopRightRadius:nC,borderBottomLeftRadius:nC,borderBottomRightRadius:nC,boxShadow:{correct:(e,{treeScale:t,projectionDelta:i})=>{let a=tr.parse(e);if(a.length>5)return e;let n=tr.createTransformer(e),r=+("number"!=typeof a[0]),s=i.x.scale*t.x,o=i.y.scale*t.y;a[0+r]/=s,a[1+r]/=o;let l=F(s,o,.5);return"number"==typeof a[2+r]&&(a[2+r]/=l),"number"==typeof a[3+r]&&(a[3+r]/=l),n(a)}}},nI=(e,t)=>e.depth-t.depth;class nN{constructor(){this.children=[],this.isDirty=!1}add(e){J(this.children,e),this.isDirty=!0}remove(e){Y(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(nI),this.isDirty=!1,this.children.forEach(e)}}function nE(e){return W(e)?e.get():e}let nD=["TopLeft","TopRight","BottomLeft","BottomRight"],nL=nD.length,nz=e=>"string"==typeof e?parseFloat(e):e,nO=e=>"number"==typeof e||e1.test(e);function n$(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let nF=nB(0,.5,tD),nV=nB(.5,.95,es);function nB(e,t,i){return a=>a<e?0:a>t?1:i($(e,t,a))}function nG(e,t){e.min=t.min,e.max=t.max}function nW(e,t){nG(e.x,t.x),nG(e.y,t.y)}function nK(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function n_(e,t,i,a,n){return e-=t,e=a+1/i*(e-a),void 0!==n&&(e=a+1/n*(e-a)),e}function nU(e,t,[i,a,n],r,s){!function(e,t=0,i=1,a=.5,n,r=e,s=e){if(e0.test(t)&&(t=parseFloat(t),t=F(s.min,s.max,t/100)-s.min),"number"!=typeof t)return;let o=F(r.min,r.max,a);e===r&&(o-=t),e.min=n_(e.min,t,i,o,n),e.max=n_(e.max,t,i,o,n)}(e,t[i],t[a],t[n],t.scale,r,s)}let nq=["x","scaleX","originX"],nH=["y","scaleY","originY"];function nQ(e,t,i,a){nU(e.x,t,nq,i?i.x:void 0,a?a.x:void 0),nU(e.y,t,nH,i?i.y:void 0,a?a.y:void 0)}function nX(e){return 0===e.translate&&1===e.scale}function nJ(e){return nX(e.x)&&nX(e.y)}function nY(e,t){return e.min===t.min&&e.max===t.max}function nZ(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function n0(e,t){return nZ(e.x,t.x)&&nZ(e.y,t.y)}function n1(e){return a3(e.x)/a3(e.y)}function n2(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class n5{constructor(){this.members=[]}add(e){J(this.members,e),e.scheduleRender()}remove(e){if(Y(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,i=this.members.findIndex(t=>e===t);if(0===i)return!1;for(let e=i;e>=0;e--){let i=this.members[e];if(!1!==i.isPresent){t=i;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,t&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:a}=e.options;!1===a&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:i}=e;t.onExitComplete&&t.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let n4={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},n3=["","X","Y","Z"],n9={visibility:"hidden"},n8=0;function n6(e,t,i,a){let{latestValues:n}=t;n[e]&&(i[e]=n[e],t.setStaticValue(e,0),a&&(a[e]=0))}function n7({attachResizeListener:e,defaultParent:t,measureScroll:i,checkIsScrollRoot:a,resetTransform:n}){return class{constructor(e={},i=t?.()){this.id=n8++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ec.value&&(n4.nodes=n4.calculatedTargetDeltas=n4.calculatedProjections=0),this.nodes.forEach(ri),this.nodes.forEach(rc),this.nodes.forEach(rd),this.nodes.forEach(ra),ec.addProjectionMetrics&&ec.addProjectionMetrics(n4)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new nN)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new ex),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let i=this.eventHandlers.get(e);i&&i.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=iN(t)&&!iE(t),this.instance=t;let{layoutId:i,layout:a,visualElement:n}=this.options;if(n&&!n.current&&n.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(a||i)&&(this.isLayoutDirty=!0),e){let i,a=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(e,t){let i=eb.now(),a=({timestamp:n})=>{let r=n-i;r>=250&&(ep(a),e(r-t))};return em.setup(a,!0),()=>ep(a)}(a,250),nA.hasAnimatedSinceResize&&(nA.hasAnimatedSinceResize=!1,this.nodes.forEach(rl))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||a)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:i,layout:a})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||n.getDefaultTransition()||rf,{onLayoutAnimationStart:s,onLayoutAnimationComplete:o}=n.getProps(),l=!this.targetLayout||!n0(this.targetLayout,a),c=!t&&i;if(this.options.layoutRoot||this.resumeFrom||c||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...er(r,"layout"),onPlay:s,onComplete:o};(n.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,c)}else t||rl(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=a})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),ep(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rm),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:i}=t.options;if(!i)return;let a=i.props[eI];if(window.MotionHasOptimisedAnimation(a,"transform")){let{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(a,"transform",em,!(e||i))}let{parent:n}=t;n&&!n.hasCheckedOptimisedAppear&&e(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:i}=this.options;if(void 0===t&&!i)return;let a=this.getTransformTemplate();this.prevTransformTemplateValue=a?a(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rr);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(rs);this.isUpdating||this.nodes.forEach(rs),this.animationCommitId=this.animationId,this.isUpdating=!1,this.nodes.forEach(ro),this.nodes.forEach(re),this.nodes.forEach(rt),this.clearAllSnapshots();let e=eb.now();eu.delta=j(0,1e3/60,e-eu.timestamp),eu.timestamp=e,eu.isProcessing=!0,eh.update.process(eu),eh.preRender.process(eu),eh.render.process(eu),eu.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,nb.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rn),this.sharedNodes.forEach(rp)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,em.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){em.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||a3(this.snapshot.measuredBox.x)||a3(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=as(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=a(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!n)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!nJ(this.projectionDelta),i=this.getTransformTemplate(),a=i?i(this.latestValues,""):void 0,r=a!==this.prevTransformTemplateValue;e&&this.instance&&(t||iO(this.latestValues)||r)&&(n(this.instance,a),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let i=this.measurePageBox(),a=this.removeElementScroll(i);return e&&(a=this.removeTransform(a)),rv((t=a).x),rv(t.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return as();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rw))){let{scroll:e}=this.root;e&&(iG(t.x,e.offset.x),iG(t.y,e.offset.y))}return t}removeElementScroll(e){let t=as();if(nW(t,e),this.scroll?.wasRoot)return t;for(let i=0;i<this.path.length;i++){let a=this.path[i],{scroll:n,options:r}=a;a!==this.root&&n&&r.layoutScroll&&(n.wasRoot&&nW(t,e),iG(t.x,n.offset.x),iG(t.y,n.offset.y))}return t}applyTransform(e,t=!1){let i=as();nW(i,e);for(let e=0;e<this.path.length;e++){let a=this.path[e];!t&&a.options.layoutScroll&&a.scroll&&a!==a.root&&iK(i,{x:-a.scroll.offset.x,y:-a.scroll.offset.y}),iO(a.latestValues)&&iK(i,a.latestValues)}return iO(this.latestValues)&&iK(i,this.latestValues),i}removeTransform(e){let t=as();nW(t,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];if(!i.instance||!iO(i.latestValues))continue;iz(i.latestValues)&&i.updateSnapshot();let a=as();nW(a,i.measurePageBox()),nQ(t,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,a)}return iO(this.latestValues)&&nQ(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==eu.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==t;if(!(e||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:n}=this.options;if(this.layout&&(a||n)){if(this.resolvedRelativeTargetAt=eu.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=as(),this.relativeTargetOrigin=as(),ne(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),nW(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=as(),this.targetWithTransforms=as()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,s,o;this.forceRelativeParentToResolveTarget(),r=this.target,s=this.relativeTarget,o=this.relativeParent.target,a6(r.x,s.x,o.x),a6(r.y,s.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nW(this.target,this.layout.layoutBox),iB(this.target,this.targetDelta)):nW(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=as(),this.relativeTargetOrigin=as(),ne(this.relativeTargetOrigin,this.target,e.target),nW(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ec.value&&n4.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iz(this.parent.latestValues)||i$(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===eu.timestamp&&(i=!1),i)return;let{layout:a,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(a||n))return;nW(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,s=this.treeScale.y;!function(e,t,i,a=!1){let n,r,s=i.length;if(s){t.x=t.y=1;for(let o=0;o<s;o++){r=(n=i[o]).projectionDelta;let{visualElement:s}=n.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(a&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iK(e,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(t.x*=r.x.scale,t.y*=r.y.scale,iB(e,r)),a&&iO(n.latestValues)&&iK(e,n.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=as());let{target:o}=e;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nK(this.prevProjectionDelta.x,this.projectionDelta.x),nK(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),a8(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===r&&this.treeScale.y===s&&n2(this.projectionDelta.x,this.prevProjectionDelta.x)&&n2(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),ec.value&&n4.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=an(),this.projectionDelta=an(),this.projectionDeltaWithTransform=an()}setAnimationOrigin(e,t=!1){let i,a=this.snapshot,n=a?a.latestValues:{},r={...this.latestValues},s=an();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=as(),l=(a?a.source:void 0)!==(this.layout?this.layout.source:void 0),c=this.getStack(),d=!c||c.members.length<=1,m=!!(l&&!d&&!0===this.options.crossfade&&!this.path.some(rg));this.animationProgress=0,this.mixTargetDelta=t=>{let a=t/1e3;if(ru(s.x,e.x,a),ru(s.y,e.y,a),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var c,p,u,h,g,f;ne(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),u=this.relativeTarget,h=this.relativeTargetOrigin,g=o,f=a,rh(u.x,h.x,g.x,f),rh(u.y,h.y,g.y,f),i&&(c=this.relativeTarget,p=i,nY(c.x,p.x)&&nY(c.y,p.y))&&(this.isProjectionDirty=!1),i||(i=as()),nW(i,this.relativeTarget)}l&&(this.animationValues=r,function(e,t,i,a,n,r){n?(e.opacity=F(0,i.opacity??1,nF(a)),e.opacityExit=F(t.opacity??1,0,nV(a))):r&&(e.opacity=F(t.opacity??1,i.opacity??1,a));for(let n=0;n<nL;n++){let r=`border${nD[n]}Radius`,s=n$(t,r),o=n$(i,r);(void 0!==s||void 0!==o)&&(s||(s=0),o||(o=0),0===s||0===o||nO(s)===nO(o)?(e[r]=Math.max(F(nz(s),nz(o),a),0),(e0.test(o)||e0.test(s))&&(e[r]+="%")):e[r]=o)}(t.rotate||i.rotate)&&(e.rotate=F(t.rotate||0,i.rotate||0,a))}(r,n,this.latestValues,a,m,d)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=a},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(ep(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=em.update(()=>{nA.hasAnimatedSinceResize=!0,eD.layout++,this.motionValue||(this.motionValue=eS(0)),this.currentAnimation=a$(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{eD.layout--},onComplete:()=>{eD.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:i,layout:a,latestValues:n}=e;if(t&&i&&a){if(this!==e&&this.layout&&a&&rb(this.options.animationType,this.layout.layoutBox,a.layoutBox)){i=this.target||as();let t=a3(this.layout.layoutBox.x);i.x.min=e.target.x.min,i.x.max=i.x.min+t;let a=a3(this.layout.layoutBox.y);i.y.min=e.target.y.min,i.y.max=i.y.min+a}nW(t,i),iK(t,n),a8(this.projectionDeltaWithTransform,this.layoutCorrected,t,n)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new n5),this.sharedNodes.get(e).add(t);let i=t.options.initialPromotionConfig;t.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:i}={}){let a=this.getStack();a&&a.promote(this,i),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:i}=e;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(t=!0),!t)return;let a={};i.z&&n6("z",e,a,this.animationValues);for(let t=0;t<n3.length;t++)n6(`rotate${n3[t]}`,e,a,this.animationValues),n6(`skew${n3[t]}`,e,a,this.animationValues);for(let t in e.render(),a)e.setStaticValue(t,a[t]),this.animationValues&&(this.animationValues[t]=a[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return n9;let t={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=nE(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none",t;let a=this.getLead();if(!this.projectionDelta||!this.layout||!a.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=nE(e?.pointerEvents)||""),this.hasProjected&&!iO(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}let n=a.animationValues||a.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,i){let a="",n=e.x.translate/t.x,r=e.y.translate/t.y,s=i?.z||0;if((n||r||s)&&(a=`translate3d(${n}px, ${r}px, ${s}px) `),(1!==t.x||1!==t.y)&&(a+=`scale(${1/t.x}, ${1/t.y}) `),i){let{transformPerspective:e,rotate:t,rotateX:n,rotateY:r,skewX:s,skewY:o}=i;e&&(a=`perspective(${e}px) ${a}`),t&&(a+=`rotate(${t}deg) `),n&&(a+=`rotateX(${n}deg) `),r&&(a+=`rotateY(${r}deg) `),s&&(a+=`skewX(${s}deg) `),o&&(a+=`skewY(${o}deg) `)}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(a+=`scale(${o}, ${l})`),a||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),i&&(t.transform=i(n,t.transform));let{x:r,y:s}=this.projectionDelta;for(let e in t.transformOrigin=`${100*r.origin}% ${100*s.origin}% 0`,a.animationValues?t.opacity=a===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:t.opacity=a===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,aS){if(void 0===n[e])continue;let{correct:i,applyTo:r,isCSSVariable:s}=aS[e],o="none"===t.transform?n[e]:i(n[e],a);if(r){let e=r.length;for(let i=0;i<e;i++)t[r[i]]=o}else s?this.options.visualElement.renderState.vars[e]=o:t[e]=o}return this.options.layoutId&&(t.pointerEvents=a===this?nE(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(rr),this.root.sharedNodes.clear()}}}function re(e){e.updateLayout()}function rt(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:a}=e.layout,{animationType:n}=e.options,r=t.source!==e.layout.source;"size"===n?nt(e=>{let a=r?t.measuredBox[e]:t.layoutBox[e],n=a3(a);a.min=i[e].min,a.max=a.min+n}):rb(n,t.layoutBox,i)&&nt(a=>{let n=r?t.measuredBox[a]:t.layoutBox[a],s=a3(i[a]);n.max=n.min+s,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[a].max=e.relativeTarget[a].min+s)});let s=an();a8(s,i,t.layoutBox);let o=an();r?a8(o,e.applyTransform(a,!0),t.measuredBox):a8(o,i,t.layoutBox);let l=!nJ(s),c=!1;if(!e.resumeFrom){let a=e.getClosestProjectingParent();if(a&&!a.resumeFrom){let{snapshot:n,layout:r}=a;if(n&&r){let s=as();ne(s,t.layoutBox,n.layoutBox);let o=as();ne(o,i,r.layoutBox),n0(s,o)||(c=!0),a.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=s,e.relativeParent=a)}}}e.notifyListeners("didUpdate",{layout:i,snapshot:t,delta:o,layoutDelta:s,hasLayoutChanged:l,hasRelativeLayoutChanged:c})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function ri(e){ec.value&&n4.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function ra(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rn(e){e.clearSnapshot()}function rr(e){e.clearMeasurements()}function rs(e){e.isLayoutDirty=!1}function ro(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rl(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function rc(e){e.resolveTargetDelta()}function rd(e){e.calcProjection()}function rm(e){e.resetSkewAndRotation()}function rp(e){e.removeLeadSnapshot()}function ru(e,t,i){e.translate=F(t.translate,0,i),e.scale=F(t.scale,1,i),e.origin=t.origin,e.originPoint=t.originPoint}function rh(e,t,i,a){e.min=F(t.min,i.min,a),e.max=F(t.max,i.max,a)}function rg(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let rf={duration:.45,ease:[.4,0,.1,1]},ry=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),rx=ry("applewebkit/")&&!ry("chrome/")?Math.round:es;function rv(e){e.min=rx(e.min),e.max=rx(e.max)}function rb(e,t,i){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(n1(t)-n1(i)))}function rw(e){return e!==e.root&&e.scroll?.wasRoot}let rk=n7({attachResizeListener:(e,t)=>a0(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rj={current:void 0},rS=n7({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!rj.current){let e=new rk({});e.mount(window),e.setOptions({layoutScroll:!0}),rj.current=e}return rj.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function rA(e,t){let i=q(e),a=new AbortController;return[i,{passive:!0,...t,signal:a.signal},()=>a.abort()]}function rP(e){return!("touch"===e.pointerType||aZ.x||aZ.y)}function rC(e,t,i){let{props:a}=e;e.animationState&&a.whileHover&&e.animationState.setActive("whileHover","Start"===i);let n=a["onHover"+i];n&&em.postRender(()=>n(t,a2(t)))}class rT extends aQ{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[a,n,r]=rA(e,i),s=e=>{if(!rP(e))return;let{target:i}=e,a=t(i,e);if("function"!=typeof a||!i)return;let r=e=>{rP(e)&&(a(e),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,n)};return a.forEach(e=>{e.addEventListener("pointerenter",s,n)}),r}(e,(e,t)=>(rC(this.node,t,"Start"),e=>rC(this.node,e,"End"))))}unmount(){}}class rR extends aQ{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eE(a0(this.node.current,"focus",()=>this.onFocus()),a0(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rM=(e,t)=>!!t&&(e===t||rM(e,t.parentElement)),rI=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rN=new WeakSet;function rE(e){return t=>{"Enter"===t.key&&e(t)}}function rD(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let rL=(e,t)=>{let i=e.currentTarget;if(!i)return;let a=rE(()=>{if(rN.has(i))return;rD(i,"down");let e=rE(()=>{rD(i,"up")});i.addEventListener("keyup",e,t),i.addEventListener("blur",()=>rD(i,"cancel"),t)});i.addEventListener("keydown",a,t),i.addEventListener("blur",()=>i.removeEventListener("keydown",a),t)};function rz(e){return a1(e)&&!(aZ.x||aZ.y)}function rO(e,t,i){let{props:a}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&a.whileTap&&e.animationState.setActive("whileTap","Start"===i);let n=a["onTap"+("End"===i?"":i)];n&&em.postRender(()=>n(t,a2(t)))}class r$ extends aQ{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[a,n,r]=rA(e,i),s=e=>{let a=e.currentTarget;if(!rz(e))return;rN.add(a);let r=t(a,e),s=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),rN.has(a)&&rN.delete(a),rz(e)&&"function"==typeof r&&r(e,{success:t})},o=e=>{s(e,a===window||a===document||i.useGlobalTarget||rM(a,e.target))},l=e=>{s(e,!1)};window.addEventListener("pointerup",o,n),window.addEventListener("pointercancel",l,n)};return a.forEach(e=>{((i.useGlobalTarget?window:e).addEventListener("pointerdown",s,n),ib(e))&&(e.addEventListener("focus",e=>rL(e,n)),rI.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),r}(e,(e,t)=>(rO(this.node,t,"Start"),(e,{success:t})=>rO(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rF=new WeakMap,rV=new WeakMap,rB=e=>{let t=rF.get(e.target);t&&t(e)},rG=e=>{e.forEach(rB)},rW={some:0,all:1};class rK extends aQ{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:i,amount:a="some",once:n}=e,r={root:t?t.current:void 0,rootMargin:i,threshold:"number"==typeof a?a:rW[a]};return function(e,t,i){let a=function({root:e,...t}){let i=e||document;rV.has(i)||rV.set(i,{});let a=rV.get(i),n=JSON.stringify(t);return a[n]||(a[n]=new IntersectionObserver(rG,{root:e,...t})),a[n]}(t);return rF.set(e,i),a.observe(e),()=>{rF.delete(e),a.unobserve(e)}}(this.node.current,r,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,n&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:i,onViewportLeave:a}=this.node.getProps(),r=t?i:a;r&&r(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return i=>e[i]!==t[i]}(e,t))&&this.startObserver()}unmount(){}}let r_=(0,o.createContext)({strict:!1}),rU=(0,o.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),rq=(0,o.createContext)({});function rH(e){return Array.isArray(e)?e.join(" "):e}let rQ=Symbol.for("motionComponentSymbol"),rX=ao?o.useLayoutEffect:o.useEffect,rJ=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rY(e,t,i){for(let a in t)W(t[a])||aA(a,i)||(e[a]=t[a])}let rZ=()=>({...rJ(),attrs:{}}),r0=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r1(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||r0.has(e)}let r2=e=>!r1(e);try{!function(e){"function"==typeof e&&(r2=t=>t.startsWith("on")?!r1(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let r5=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r4(e){if("string"!=typeof e||e.includes("-"));else if(r5.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}let r3=e=>(t,i)=>{let a=(0,o.useContext)(rq),n=(0,o.useContext)(nw),r=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},i,a,n){return{latestValues:function(e,t,i,a){let n={},r=a(e,{});for(let e in r)n[e]=nE(r[e]);let{initial:s,animate:o}=e,l=ah(e),c=ag(e);t&&c&&!l&&!1!==e.inherit&&(void 0===s&&(s=t.initial),void 0===o&&(o=t.animate));let d=!!i&&!1===i.initial,m=(d=d||!1===s)?o:s;if(m&&"boolean"!=typeof m&&!ad(m)){let t=Array.isArray(m)?m:[m];for(let i=0;i<t.length;i++){let a=eC(e,t[i]);if(a){let{transitionEnd:e,transition:t,...i}=a;for(let e in i){let t=i[e];if(Array.isArray(t)){let e=d?t.length-1:0;t=t[e]}null!==t&&(n[e]=t)}for(let t in e)n[t]=e[t]}}}return n}(i,a,n,e),renderState:t()}})(e,t,a,n);return i?r():b(r)},r9={useVisualState:r3({scrapeMotionValuesFromProps:aP,createRenderState:rJ})},r8={useVisualState:r3({scrapeMotionValuesFromProps:aD,createRenderState:rZ})},r6=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(i,a)=>"create"===a?e:(t.has(a)||t.set(a,e(a)),t.get(a))})}((n={animation:{Feature:aX},exit:{Feature:aY},inView:{Feature:rK},tap:{Feature:r$},focus:{Feature:rR},hover:{Feature:rT},pan:{Feature:nv},drag:{Feature:ny,ProjectionNode:rS,MeasureLayout:nR},layout:{ProjectionNode:rS,MeasureLayout:nR}},r=(e,t)=>r4(e)?new aL(t):new aC(t,{allowProjection:e!==o.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:i,useVisualState:a,Component:n}){function r(e,r){var l,c,d;let m,p={...(0,o.useContext)(rU),...e,layoutId:function({layoutId:e}){let t=(0,o.useContext)(nj).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:u}=p,h=function(e){let{initial:t,animate:i}=function(e,t){if(ah(e)){let{initial:t,animate:i}=e;return{initial:!1===t||am(t)?t:void 0,animate:am(i)?i:void 0}}return!1!==e.inherit?t:{}}(e,(0,o.useContext)(rq));return(0,o.useMemo)(()=>({initial:t,animate:i}),[rH(t),rH(i)])}(e),g=a(e,u);if(!u&&ao){c=0,d=0,(0,o.useContext)(r_).strict;let e=function(e){let{drag:t,layout:i}=ai;if(!t&&!i)return{};let a={...t,...i};return{MeasureLayout:t?.isEnabled(e)||i?.isEnabled(e)?a.MeasureLayout:void 0,ProjectionNode:a.ProjectionNode}}(p);m=e.MeasureLayout,h.visualElement=function(e,t,i,a,n){let{visualElement:r}=(0,o.useContext)(rq),s=(0,o.useContext)(r_),l=(0,o.useContext)(nw),c=(0,o.useContext)(rU).reducedMotion,d=(0,o.useRef)(null);a=a||s.renderer,!d.current&&a&&(d.current=a(e,{visualState:t,parent:r,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:c}));let m=d.current,p=(0,o.useContext)(nS);m&&!m.projection&&n&&("html"===m.type||"svg"===m.type)&&function(e,t,i,a){let{layoutId:n,layout:r,drag:s,dragConstraints:o,layoutScroll:l,layoutRoot:c,layoutCrossfade:d}=t;e.projection=new i(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:n,layout:r,alwaysMeasureLayout:!!s||o&&na(o),visualElement:e,animationType:"string"==typeof r?r:"both",initialPromotionConfig:a,crossfade:d,layoutScroll:l,layoutRoot:c})}(d.current,i,n,p);let u=(0,o.useRef)(!1);(0,o.useInsertionEffect)(()=>{m&&u.current&&m.update(i,l)});let h=i[eI],g=(0,o.useRef)(!!h&&!window.MotionHandoffIsComplete?.(h)&&window.MotionHasOptimisedAnimation?.(h));return rX(()=>{m&&(u.current=!0,window.MotionIsMounted=!0,m.updateFeatures(),nb.render(m.render),g.current&&m.animationState&&m.animationState.animateChanges())}),(0,o.useEffect)(()=>{m&&(!g.current&&m.animationState&&m.animationState.animateChanges(),g.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(h)}),g.current=!1))}),m}(n,g,p,t,e.ProjectionNode)}return(0,s.jsxs)(rq.Provider,{value:h,children:[m&&h.visualElement?(0,s.jsx)(m,{visualElement:h.visualElement,...p}):null,i(n,e,(l=h.visualElement,(0,o.useCallback)(e=>{e&&g.onMount&&g.onMount(e),l&&(e?l.mount(e):l.unmount()),r&&("function"==typeof r?r(e):na(r)&&(r.current=e))},[l])),g,u,h.visualElement)]})}e&&function(e){for(let t in e)ai[t]={...ai[t],...e[t]}}(e),r.displayName=`motion.${"string"==typeof n?n:`create(${n.displayName??n.name??""})`}`;let l=(0,o.forwardRef)(r);return l[rQ]=n,l}({...r4(e)?r8:r9,preloadedFeatures:n,useRender:function(e=!1){return(t,i,a,{latestValues:n},r)=>{let s=(r4(t)?function(e,t,i,a){let n=(0,o.useMemo)(()=>{let i=rZ();return aI(i,t,aE(a),e.transformTemplate,e.style),{...i.attrs,style:{...i.style}}},[t]);if(e.style){let t={};rY(t,e.style,e),n.style={...t,...n.style}}return n}:function(e,t){let i={},a=function(e,t){let i=e.style||{},a={};return rY(a,i,e),Object.assign(a,function({transformTemplate:e},t){return(0,o.useMemo)(()=>{let i=rJ();return ak(i,t,e),Object.assign({},i.vars,i.style)},[t])}(e,t)),a}(e,t);return e.drag&&!1!==e.dragListener&&(i.draggable=!1,a.userSelect=a.WebkitUserSelect=a.WebkitTouchCallout="none",a.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(i.tabIndex=0),i.style=a,i})(i,n,r,t),l=function(e,t,i){let a={};for(let n in e)("values"!==n||"object"!=typeof e.values)&&(r2(n)||!0===i&&r1(n)||!t&&!r1(n)||e.draggable&&n.startsWith("onDrag"))&&(a[n]=e[n]);return a}(i,"string"==typeof t,e),c=t!==o.Fragment?{...l,...s,ref:a}:{},{children:d}=i,m=(0,o.useMemo)(()=>W(d)?d.get():d,[d]);return(0,o.createElement)(t,{...c,children:m})}}(t),createVisualElement:r,Component:e})}));function r7({sequences:e=[{text:"AI Prompt Generator",deleteAfter:!0},{text:"Professional Prompts",deleteAfter:!0},{text:"Augment Agent Ready",deleteAfter:!1}],typingSpeed:t=75,startDelay:i=500,autoLoop:a=!0,loopDelay:n=3e3,className:r=""}){let[l,c]=function(){var e;let t=b(()=>({current:null,animations:[]})),i=b(()=>aV(t));return e=()=>{t.animations.forEach(e=>e.stop())},(0,o.useEffect)(()=>()=>e(),[]),[t,i]}(),[d,m]=(0,o.useState)(!1);return(0,s.jsx)("div",{className:`relative w-full max-w-4xl mx-auto py-8 ${r}`,children:(0,s.jsx)("div",{className:"relative text-center z-10 flex flex-col items-center justify-center",ref:l,children:(0,s.jsx)(r6.div,{className:"text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-mono text-white tracking-tight flex items-center gap-2",initial:{opacity:0},animate:{opacity:1},children:(0,s.jsx)("span",{"data-typewriter":!0,className:"inline-block border-r-2 border-white animate-cursor pr-1",children:e[0].text})})})})}let se=e=>{let t=sn(e),{conflictingClassGroups:i,conflictingClassGroupModifiers:a}=e;return{getClassGroupId:e=>{let i=e.split("-");return""===i[0]&&1!==i.length&&i.shift(),st(i,t)||sa(e)},getConflictingClassGroupIds:(e,t)=>{let n=i[e]||[];return t&&a[e]?[...n,...a[e]]:n}}},st=(e,t)=>{if(0===e.length)return t.classGroupId;let i=e[0],a=t.nextPart.get(i),n=a?st(e.slice(1),a):void 0;if(n)return n;if(0===t.validators.length)return;let r=e.join("-");return t.validators.find(({validator:e})=>e(r))?.classGroupId},si=/^\[(.+)\]$/,sa=e=>{if(si.test(e)){let t=si.exec(e)[1],i=t?.substring(0,t.indexOf(":"));if(i)return"arbitrary.."+i}},sn=e=>{let{theme:t,classGroups:i}=e,a={nextPart:new Map,validators:[]};for(let e in i)sr(i[e],a,e,t);return a},sr=(e,t,i,a)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:ss(t,e)).classGroupId=i;return}if("function"==typeof e)return so(e)?void sr(e(a),t,i,a):void t.validators.push({validator:e,classGroupId:i});Object.entries(e).forEach(([e,n])=>{sr(n,ss(t,e),i,a)})})},ss=(e,t)=>{let i=e;return t.split("-").forEach(e=>{i.nextPart.has(e)||i.nextPart.set(e,{nextPart:new Map,validators:[]}),i=i.nextPart.get(e)}),i},so=e=>e.isThemeGetter,sl=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,i=new Map,a=new Map,n=(n,r)=>{i.set(n,r),++t>e&&(t=0,a=i,i=new Map)};return{get(e){let t=i.get(e);return void 0!==t?t:void 0!==(t=a.get(e))?(n(e,t),t):void 0},set(e,t){i.has(e)?i.set(e,t):n(e,t)}}},sc=e=>{let{prefix:t,experimentalParseClassName:i}=e,a=e=>{let t,i=[],a=0,n=0,r=0;for(let s=0;s<e.length;s++){let o=e[s];if(0===a&&0===n){if(":"===o){i.push(e.slice(r,s)),r=s+1;continue}if("/"===o){t=s;continue}}"["===o?a++:"]"===o?a--:"("===o?n++:")"===o&&n--}let s=0===i.length?e:e.substring(r),o=sd(s);return{modifiers:i,hasImportantModifier:o!==s,baseClassName:o,maybePostfixModifierPosition:t&&t>r?t-r:void 0}};if(t){let e=t+":",i=a;a=t=>t.startsWith(e)?i(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(i){let e=a;a=t=>i({className:t,parseClassName:e})}return a},sd=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,sm=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let i=[],a=[];return e.forEach(e=>{"["===e[0]||t[e]?(i.push(...a.sort(),e),a=[]):a.push(e)}),i.push(...a.sort()),i}},sp=e=>({cache:sl(e.cacheSize),parseClassName:sc(e),sortModifiers:sm(e),...se(e)}),su=/\s+/,sh=(e,t)=>{let{parseClassName:i,getClassGroupId:a,getConflictingClassGroupIds:n,sortModifiers:r}=t,s=[],o=e.trim().split(su),l="";for(let e=o.length-1;e>=0;e-=1){let t=o[e],{isExternal:c,modifiers:d,hasImportantModifier:m,baseClassName:p,maybePostfixModifierPosition:u}=i(t);if(c){l=t+(l.length>0?" "+l:l);continue}let h=!!u,g=a(h?p.substring(0,u):p);if(!g){if(!h||!(g=a(p))){l=t+(l.length>0?" "+l:l);continue}h=!1}let f=r(d).join(":"),y=m?f+"!":f,x=y+g;if(s.includes(x))continue;s.push(x);let v=n(g,h);for(let e=0;e<v.length;++e){let t=v[e];s.push(y+t)}l=t+(l.length>0?" "+l:l)}return l};function sg(){let e,t,i=0,a="";for(;i<arguments.length;)(e=arguments[i++])&&(t=sf(e))&&(a&&(a+=" "),a+=t);return a}let sf=e=>{let t;if("string"==typeof e)return e;let i="";for(let a=0;a<e.length;a++)e[a]&&(t=sf(e[a]))&&(i&&(i+=" "),i+=t);return i},sy=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},sx=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,sv=/^\((?:(\w[\w-]*):)?(.+)\)$/i,sb=/^\d+\/\d+$/,sw=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,sk=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,sj=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,sS=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,sA=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,sP=e=>sb.test(e),sC=e=>!!e&&!Number.isNaN(Number(e)),sT=e=>!!e&&Number.isInteger(Number(e)),sR=e=>e.endsWith("%")&&sC(e.slice(0,-1)),sM=e=>sw.test(e),sI=()=>!0,sN=e=>sk.test(e)&&!sj.test(e),sE=()=>!1,sD=e=>sS.test(e),sL=e=>sA.test(e),sz=e=>!s$(e)&&!sK(e),sO=e=>sJ(e,s1,sE),s$=e=>sx.test(e),sF=e=>sJ(e,s2,sN),sV=e=>sJ(e,s5,sC),sB=e=>sJ(e,sZ,sE),sG=e=>sJ(e,s0,sL),sW=e=>sJ(e,s3,sD),sK=e=>sv.test(e),s_=e=>sY(e,s2),sU=e=>sY(e,s4),sq=e=>sY(e,sZ),sH=e=>sY(e,s1),sQ=e=>sY(e,s0),sX=e=>sY(e,s3,!0),sJ=(e,t,i)=>{let a=sx.exec(e);return!!a&&(a[1]?t(a[1]):i(a[2]))},sY=(e,t,i=!1)=>{let a=sv.exec(e);return!!a&&(a[1]?t(a[1]):i)},sZ=e=>"position"===e||"percentage"===e,s0=e=>"image"===e||"url"===e,s1=e=>"length"===e||"size"===e||"bg-size"===e,s2=e=>"length"===e,s5=e=>"number"===e,s4=e=>"family-name"===e,s3=e=>"shadow"===e;Symbol.toStringTag;let s9=function(e,...t){let i,a,n,r=function(o){return a=(i=sp(t.reduce((e,t)=>t(e),e()))).cache.get,n=i.cache.set,r=s,s(o)};function s(e){let t=a(e);if(t)return t;let r=sh(e,i);return n(e,r),r}return function(){return r(sg.apply(null,arguments))}}(()=>{let e=sy("color"),t=sy("font"),i=sy("text"),a=sy("font-weight"),n=sy("tracking"),r=sy("leading"),s=sy("breakpoint"),o=sy("container"),l=sy("spacing"),c=sy("radius"),d=sy("shadow"),m=sy("inset-shadow"),p=sy("text-shadow"),u=sy("drop-shadow"),h=sy("blur"),g=sy("perspective"),f=sy("aspect"),y=sy("ease"),x=sy("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],b=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...b(),sK,s$],k=()=>["auto","hidden","clip","visible","scroll"],j=()=>["auto","contain","none"],S=()=>[sK,s$,l],A=()=>[sP,"full","auto",...S()],P=()=>[sT,"none","subgrid",sK,s$],C=()=>["auto",{span:["full",sT,sK,s$]},sT,sK,s$],T=()=>[sT,"auto",sK,s$],R=()=>["auto","min","max","fr",sK,s$],M=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],I=()=>["start","end","center","stretch","center-safe","end-safe"],N=()=>["auto",...S()],E=()=>[sP,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...S()],D=()=>[e,sK,s$],L=()=>[...b(),sq,sB,{position:[sK,s$]}],z=()=>["no-repeat",{repeat:["","x","y","space","round"]}],O=()=>["auto","cover","contain",sH,sO,{size:[sK,s$]}],$=()=>[sR,s_,sF],F=()=>["","none","full",c,sK,s$],V=()=>["",sC,s_,sF],B=()=>["solid","dashed","dotted","double"],G=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],W=()=>[sC,sR,sq,sB],K=()=>["","none",h,sK,s$],_=()=>["none",sC,sK,s$],U=()=>["none",sC,sK,s$],q=()=>[sC,sK,s$],H=()=>[sP,"full",...S()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[sM],breakpoint:[sM],color:[sI],container:[sM],"drop-shadow":[sM],ease:["in","out","in-out"],font:[sz],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[sM],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[sM],shadow:[sM],spacing:["px",sC],text:[sM],"text-shadow":[sM],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",sP,s$,sK,f]}],container:["container"],columns:[{columns:[sC,s$,sK,o]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:k()}],"overflow-x":[{"overflow-x":k()}],"overflow-y":[{"overflow-y":k()}],overscroll:[{overscroll:j()}],"overscroll-x":[{"overscroll-x":j()}],"overscroll-y":[{"overscroll-y":j()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:A()}],"inset-x":[{"inset-x":A()}],"inset-y":[{"inset-y":A()}],start:[{start:A()}],end:[{end:A()}],top:[{top:A()}],right:[{right:A()}],bottom:[{bottom:A()}],left:[{left:A()}],visibility:["visible","invisible","collapse"],z:[{z:[sT,"auto",sK,s$]}],basis:[{basis:[sP,"full","auto",o,...S()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[sC,sP,"auto","initial","none",s$]}],grow:[{grow:["",sC,sK,s$]}],shrink:[{shrink:["",sC,sK,s$]}],order:[{order:[sT,"first","last","none",sK,s$]}],"grid-cols":[{"grid-cols":P()}],"col-start-end":[{col:C()}],"col-start":[{"col-start":T()}],"col-end":[{"col-end":T()}],"grid-rows":[{"grid-rows":P()}],"row-start-end":[{row:C()}],"row-start":[{"row-start":T()}],"row-end":[{"row-end":T()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":R()}],"auto-rows":[{"auto-rows":R()}],gap:[{gap:S()}],"gap-x":[{"gap-x":S()}],"gap-y":[{"gap-y":S()}],"justify-content":[{justify:[...M(),"normal"]}],"justify-items":[{"justify-items":[...I(),"normal"]}],"justify-self":[{"justify-self":["auto",...I()]}],"align-content":[{content:["normal",...M()]}],"align-items":[{items:[...I(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...I(),{baseline:["","last"]}]}],"place-content":[{"place-content":M()}],"place-items":[{"place-items":[...I(),"baseline"]}],"place-self":[{"place-self":["auto",...I()]}],p:[{p:S()}],px:[{px:S()}],py:[{py:S()}],ps:[{ps:S()}],pe:[{pe:S()}],pt:[{pt:S()}],pr:[{pr:S()}],pb:[{pb:S()}],pl:[{pl:S()}],m:[{m:N()}],mx:[{mx:N()}],my:[{my:N()}],ms:[{ms:N()}],me:[{me:N()}],mt:[{mt:N()}],mr:[{mr:N()}],mb:[{mb:N()}],ml:[{ml:N()}],"space-x":[{"space-x":S()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":S()}],"space-y-reverse":["space-y-reverse"],size:[{size:E()}],w:[{w:[o,"screen",...E()]}],"min-w":[{"min-w":[o,"screen","none",...E()]}],"max-w":[{"max-w":[o,"screen","none","prose",{screen:[s]},...E()]}],h:[{h:["screen","lh",...E()]}],"min-h":[{"min-h":["screen","lh","none",...E()]}],"max-h":[{"max-h":["screen","lh",...E()]}],"font-size":[{text:["base",i,s_,sF]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[a,sK,sV]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",sR,s$]}],"font-family":[{font:[sU,s$,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,sK,s$]}],"line-clamp":[{"line-clamp":[sC,"none",sK,sV]}],leading:[{leading:[r,...S()]}],"list-image":[{"list-image":["none",sK,s$]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",sK,s$]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:D()}],"text-color":[{text:D()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...B(),"wavy"]}],"text-decoration-thickness":[{decoration:[sC,"from-font","auto",sK,sF]}],"text-decoration-color":[{decoration:D()}],"underline-offset":[{"underline-offset":[sC,"auto",sK,s$]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:S()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",sK,s$]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",sK,s$]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:L()}],"bg-repeat":[{bg:z()}],"bg-size":[{bg:O()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},sT,sK,s$],radial:["",sK,s$],conic:[sT,sK,s$]},sQ,sG]}],"bg-color":[{bg:D()}],"gradient-from-pos":[{from:$()}],"gradient-via-pos":[{via:$()}],"gradient-to-pos":[{to:$()}],"gradient-from":[{from:D()}],"gradient-via":[{via:D()}],"gradient-to":[{to:D()}],rounded:[{rounded:F()}],"rounded-s":[{"rounded-s":F()}],"rounded-e":[{"rounded-e":F()}],"rounded-t":[{"rounded-t":F()}],"rounded-r":[{"rounded-r":F()}],"rounded-b":[{"rounded-b":F()}],"rounded-l":[{"rounded-l":F()}],"rounded-ss":[{"rounded-ss":F()}],"rounded-se":[{"rounded-se":F()}],"rounded-ee":[{"rounded-ee":F()}],"rounded-es":[{"rounded-es":F()}],"rounded-tl":[{"rounded-tl":F()}],"rounded-tr":[{"rounded-tr":F()}],"rounded-br":[{"rounded-br":F()}],"rounded-bl":[{"rounded-bl":F()}],"border-w":[{border:V()}],"border-w-x":[{"border-x":V()}],"border-w-y":[{"border-y":V()}],"border-w-s":[{"border-s":V()}],"border-w-e":[{"border-e":V()}],"border-w-t":[{"border-t":V()}],"border-w-r":[{"border-r":V()}],"border-w-b":[{"border-b":V()}],"border-w-l":[{"border-l":V()}],"divide-x":[{"divide-x":V()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":V()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...B(),"hidden","none"]}],"divide-style":[{divide:[...B(),"hidden","none"]}],"border-color":[{border:D()}],"border-color-x":[{"border-x":D()}],"border-color-y":[{"border-y":D()}],"border-color-s":[{"border-s":D()}],"border-color-e":[{"border-e":D()}],"border-color-t":[{"border-t":D()}],"border-color-r":[{"border-r":D()}],"border-color-b":[{"border-b":D()}],"border-color-l":[{"border-l":D()}],"divide-color":[{divide:D()}],"outline-style":[{outline:[...B(),"none","hidden"]}],"outline-offset":[{"outline-offset":[sC,sK,s$]}],"outline-w":[{outline:["",sC,s_,sF]}],"outline-color":[{outline:D()}],shadow:[{shadow:["","none",d,sX,sW]}],"shadow-color":[{shadow:D()}],"inset-shadow":[{"inset-shadow":["none",m,sX,sW]}],"inset-shadow-color":[{"inset-shadow":D()}],"ring-w":[{ring:V()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:D()}],"ring-offset-w":[{"ring-offset":[sC,sF]}],"ring-offset-color":[{"ring-offset":D()}],"inset-ring-w":[{"inset-ring":V()}],"inset-ring-color":[{"inset-ring":D()}],"text-shadow":[{"text-shadow":["none",p,sX,sW]}],"text-shadow-color":[{"text-shadow":D()}],opacity:[{opacity:[sC,sK,s$]}],"mix-blend":[{"mix-blend":[...G(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":G()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[sC]}],"mask-image-linear-from-pos":[{"mask-linear-from":W()}],"mask-image-linear-to-pos":[{"mask-linear-to":W()}],"mask-image-linear-from-color":[{"mask-linear-from":D()}],"mask-image-linear-to-color":[{"mask-linear-to":D()}],"mask-image-t-from-pos":[{"mask-t-from":W()}],"mask-image-t-to-pos":[{"mask-t-to":W()}],"mask-image-t-from-color":[{"mask-t-from":D()}],"mask-image-t-to-color":[{"mask-t-to":D()}],"mask-image-r-from-pos":[{"mask-r-from":W()}],"mask-image-r-to-pos":[{"mask-r-to":W()}],"mask-image-r-from-color":[{"mask-r-from":D()}],"mask-image-r-to-color":[{"mask-r-to":D()}],"mask-image-b-from-pos":[{"mask-b-from":W()}],"mask-image-b-to-pos":[{"mask-b-to":W()}],"mask-image-b-from-color":[{"mask-b-from":D()}],"mask-image-b-to-color":[{"mask-b-to":D()}],"mask-image-l-from-pos":[{"mask-l-from":W()}],"mask-image-l-to-pos":[{"mask-l-to":W()}],"mask-image-l-from-color":[{"mask-l-from":D()}],"mask-image-l-to-color":[{"mask-l-to":D()}],"mask-image-x-from-pos":[{"mask-x-from":W()}],"mask-image-x-to-pos":[{"mask-x-to":W()}],"mask-image-x-from-color":[{"mask-x-from":D()}],"mask-image-x-to-color":[{"mask-x-to":D()}],"mask-image-y-from-pos":[{"mask-y-from":W()}],"mask-image-y-to-pos":[{"mask-y-to":W()}],"mask-image-y-from-color":[{"mask-y-from":D()}],"mask-image-y-to-color":[{"mask-y-to":D()}],"mask-image-radial":[{"mask-radial":[sK,s$]}],"mask-image-radial-from-pos":[{"mask-radial-from":W()}],"mask-image-radial-to-pos":[{"mask-radial-to":W()}],"mask-image-radial-from-color":[{"mask-radial-from":D()}],"mask-image-radial-to-color":[{"mask-radial-to":D()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":b()}],"mask-image-conic-pos":[{"mask-conic":[sC]}],"mask-image-conic-from-pos":[{"mask-conic-from":W()}],"mask-image-conic-to-pos":[{"mask-conic-to":W()}],"mask-image-conic-from-color":[{"mask-conic-from":D()}],"mask-image-conic-to-color":[{"mask-conic-to":D()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:L()}],"mask-repeat":[{mask:z()}],"mask-size":[{mask:O()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",sK,s$]}],filter:[{filter:["","none",sK,s$]}],blur:[{blur:K()}],brightness:[{brightness:[sC,sK,s$]}],contrast:[{contrast:[sC,sK,s$]}],"drop-shadow":[{"drop-shadow":["","none",u,sX,sW]}],"drop-shadow-color":[{"drop-shadow":D()}],grayscale:[{grayscale:["",sC,sK,s$]}],"hue-rotate":[{"hue-rotate":[sC,sK,s$]}],invert:[{invert:["",sC,sK,s$]}],saturate:[{saturate:[sC,sK,s$]}],sepia:[{sepia:["",sC,sK,s$]}],"backdrop-filter":[{"backdrop-filter":["","none",sK,s$]}],"backdrop-blur":[{"backdrop-blur":K()}],"backdrop-brightness":[{"backdrop-brightness":[sC,sK,s$]}],"backdrop-contrast":[{"backdrop-contrast":[sC,sK,s$]}],"backdrop-grayscale":[{"backdrop-grayscale":["",sC,sK,s$]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[sC,sK,s$]}],"backdrop-invert":[{"backdrop-invert":["",sC,sK,s$]}],"backdrop-opacity":[{"backdrop-opacity":[sC,sK,s$]}],"backdrop-saturate":[{"backdrop-saturate":[sC,sK,s$]}],"backdrop-sepia":[{"backdrop-sepia":["",sC,sK,s$]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":S()}],"border-spacing-x":[{"border-spacing-x":S()}],"border-spacing-y":[{"border-spacing-y":S()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",sK,s$]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[sC,"initial",sK,s$]}],ease:[{ease:["linear","initial",y,sK,s$]}],delay:[{delay:[sC,sK,s$]}],animate:[{animate:["none",x,sK,s$]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,sK,s$]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:_()}],"rotate-x":[{"rotate-x":_()}],"rotate-y":[{"rotate-y":_()}],"rotate-z":[{"rotate-z":_()}],scale:[{scale:U()}],"scale-x":[{"scale-x":U()}],"scale-y":[{"scale-y":U()}],"scale-z":[{"scale-z":U()}],"scale-3d":["scale-3d"],skew:[{skew:q()}],"skew-x":[{"skew-x":q()}],"skew-y":[{"skew-y":q()}],transform:[{transform:[sK,s$,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:H()}],"translate-x":[{"translate-x":H()}],"translate-y":[{"translate-y":H()}],"translate-z":[{"translate-z":H()}],"translate-none":["translate-none"],accent:[{accent:D()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:D()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",sK,s$]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":S()}],"scroll-mx":[{"scroll-mx":S()}],"scroll-my":[{"scroll-my":S()}],"scroll-ms":[{"scroll-ms":S()}],"scroll-me":[{"scroll-me":S()}],"scroll-mt":[{"scroll-mt":S()}],"scroll-mr":[{"scroll-mr":S()}],"scroll-mb":[{"scroll-mb":S()}],"scroll-ml":[{"scroll-ml":S()}],"scroll-p":[{"scroll-p":S()}],"scroll-px":[{"scroll-px":S()}],"scroll-py":[{"scroll-py":S()}],"scroll-ps":[{"scroll-ps":S()}],"scroll-pe":[{"scroll-pe":S()}],"scroll-pt":[{"scroll-pt":S()}],"scroll-pr":[{"scroll-pr":S()}],"scroll-pb":[{"scroll-pb":S()}],"scroll-pl":[{"scroll-pl":S()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",sK,s$]}],fill:[{fill:["none",...D()]}],"stroke-w":[{stroke:[sC,s_,sF,sV]}],stroke:[{stroke:["none",...D()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function s8(...e){return s9(function(){for(var e,t,i=0,a="",n=arguments.length;i<n;i++)(e=arguments[i])&&(t=function e(t){var i,a,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t)){var r=t.length;for(i=0;i<r;i++)t[i]&&(a=e(t[i]))&&(n&&(n+=" "),n+=a)}else for(a in t)t[a]&&(n&&(n+=" "),n+=a);return n}(e))&&(a&&(a+=" "),a+=t);return a}(e))}function s6({text:e="Text Shimmer",className:t}){let[i,a]=(0,o.useState)(!1);return i?(0,s.jsx)(r6.span,{className:s8("text-white font-medium",t),initial:{opacity:.7},animate:{opacity:[.7,1,.7]},transition:{duration:2.5,ease:"easeInOut",repeat:Number.POSITIVE_INFINITY},children:e}):(0,s.jsxs)(r6.span,{className:s8("relative font-medium text-white",t),initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},style:{background:"linear-gradient(90deg, #ffffff 0%, #d1d5db 50%, #ffffff 100%)",backgroundSize:"200% 100%",WebkitBackgroundClip:"text",backgroundClip:"text",WebkitTextFillColor:"transparent",color:"transparent"},children:[(0,s.jsx)(r6.span,{animate:{backgroundPosition:["200% center","-200% center"]},transition:{duration:2.5,ease:"linear",repeat:Number.POSITIVE_INFINITY},style:{background:"inherit",backgroundSize:"inherit",WebkitBackgroundClip:"inherit",backgroundClip:"inherit",WebkitTextFillColor:"inherit",color:"inherit"},children:e}),(0,s.jsx)("span",{className:"absolute inset-0 text-white opacity-0",style:{opacity:+!!i,WebkitTextFillColor:"white",color:"white"},children:e})]})}class s7 extends o.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,i=ib(e)&&e.offsetWidth||0,a=this.props.sizeRef.current;a.height=t.offsetHeight||0,a.width=t.offsetWidth||0,a.top=t.offsetTop,a.left=t.offsetLeft,a.right=i-a.width-a.left}return null}componentDidUpdate(){}render(){return this.props.children}}function oe({children:e,isPresent:t,anchorX:i}){let a=(0,o.useId)(),n=(0,o.useRef)(null),r=(0,o.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,o.useContext)(rU);return(0,o.useInsertionEffect)(()=>{let{width:e,height:s,top:o,left:c,right:d}=r.current;if(t||!n.current||!e||!s)return;let m="left"===i?`left: ${c}`:`right: ${d}`;n.current.dataset.motionPopId=a;let p=document.createElement("style");return l&&(p.nonce=l),document.head.appendChild(p),p.sheet&&p.sheet.insertRule(`
          [data-motion-pop-id="${a}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${s}px !important;
            ${m}px !important;
            top: ${o}px !important;
          }
        `),()=>{document.head.contains(p)&&document.head.removeChild(p)}},[t]),(0,s.jsx)(s7,{isPresent:t,childRef:n,sizeRef:r,children:o.cloneElement(e,{ref:n})})}let ot=({children:e,initial:t,isPresent:i,onExitComplete:a,custom:n,presenceAffectsLayout:r,mode:l,anchorX:c})=>{let d=b(oi),m=(0,o.useId)(),p=!0,u=(0,o.useMemo)(()=>(p=!1,{id:m,initial:t,isPresent:i,custom:n,onExitComplete:e=>{for(let t of(d.set(e,!0),d.values()))if(!t)return;a&&a()},register:e=>(d.set(e,!1),()=>d.delete(e))}),[i,d,a]);return r&&p&&(u={...u}),(0,o.useMemo)(()=>{d.forEach((e,t)=>d.set(t,!1))},[i]),o.useEffect(()=>{i||d.size||!a||a()},[i]),"popLayout"===l&&(e=(0,s.jsx)(oe,{isPresent:i,anchorX:c,children:e})),(0,s.jsx)(nw.Provider,{value:u,children:e})};function oi(){return new Map}let oa=e=>e.key||"";function on(e){let t=[];return o.Children.forEach(e,e=>{(0,o.isValidElement)(e)&&t.push(e)}),t}let or=({children:e,custom:t,initial:i=!0,onExitComplete:a,presenceAffectsLayout:n=!0,mode:r="sync",propagate:l=!1,anchorX:c="left"})=>{let[d,m]=nk(l),p=(0,o.useMemo)(()=>on(e),[e]),u=l&&!d?[]:p.map(oa),h=(0,o.useRef)(!0),g=(0,o.useRef)(p),f=b(()=>new Map),[y,x]=(0,o.useState)(p),[v,w]=(0,o.useState)(p);rX(()=>{h.current=!1,g.current=p;for(let e=0;e<v.length;e++){let t=oa(v[e]);u.includes(t)?f.delete(t):!0!==f.get(t)&&f.set(t,!1)}},[v,u.length,u.join("-")]);let k=[];if(p!==y){let e=[...p];for(let t=0;t<v.length;t++){let i=v[t],a=oa(i);u.includes(a)||(e.splice(t,0,i),k.push(i))}return"wait"===r&&k.length&&(e=k),w(on(e)),x(p),null}let{forceRender:j}=(0,o.useContext)(nj);return(0,s.jsx)(s.Fragment,{children:v.map(e=>{let o=oa(e),y=(!l||!!d)&&(p===v||u.includes(o));return(0,s.jsx)(ot,{isPresent:y,initial:(!h.current||!!i)&&void 0,custom:t,presenceAffectsLayout:n,mode:r,onExitComplete:y?void 0:()=>{if(!f.has(o))return;f.set(o,!0);let e=!0;f.forEach(t=>{t||(e=!1)}),e&&(j?.(),w(g.current),l&&m?.(),a&&a())},anchorX:c,children:e},o)})})};function os({texts:e=["Thinking...","Processing...","Analyzing...","Computing...","Almost..."],className:t,interval:i=1500}){let[a,n]=(0,o.useState)(0);return(0,s.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,s.jsx)(r6.div,{className:"relative px-4 py-2 w-full",initial:{opacity:0},animate:{opacity:1},transition:{duration:.4},children:(0,s.jsx)(or,{mode:"wait",children:(0,s.jsx)(r6.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0,backgroundPosition:["200% center","-200% center"]},exit:{opacity:0,y:-20},transition:{opacity:{duration:.3},y:{duration:.3},backgroundPosition:{duration:2.5,ease:"linear",repeat:1/0}},className:s8("flex justify-center text-3xl font-bold bg-gradient-to-r from-neutral-950 via-neutral-400 to-neutral-950 dark:from-white dark:via-neutral-600 dark:to-white bg-[length:200%_100%] bg-clip-text text-transparent whitespace-nowrap min-w-max",t),children:e[a]},a)})})})}let oo=[{id:"fintech-trading-platform",name:"FinTech Trading Platform",industry:"Financial Technology",description:"Real-time trading platform with advanced analytics and risk management",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","WebSocket","PostgreSQL","Redis","Docker"],features:["Real-time trading","Portfolio management","Risk analytics","Compliance reporting"],businessGoals:["Increase trading volume","Reduce latency","Ensure regulatory compliance"],targetAudience:"Professional traders and financial institutions",timeline:"12-18 months",budget:"$500K - $2M",successMetrics:["Sub-100ms latency","99.9% uptime","Full regulatory compliance"],risks:["Regulatory changes","Market volatility","Security breaches"],template:`Build a comprehensive FinTech trading platform with real-time capabilities and regulatory compliance.

Key Requirements:
- Real-time market data processing and trading execution
- Advanced risk management and portfolio analytics
- Regulatory compliance and audit trails
- High-performance architecture with low latency
- Secure user authentication and authorization
- Integration with market data providers and clearing houses

Technical Specifications:
- Microservices architecture for scalability
- WebSocket connections for real-time updates
- Redis for caching and session management
- PostgreSQL for transactional data
- Docker containerization for deployment
- Comprehensive API documentation

Success Criteria:
- Sub-100ms trade execution latency
- 99.9% uptime during market hours
- Full regulatory compliance (MiFID II, GDPR)
- Support for 10,000+ concurrent users`},{id:"digital-banking-app",name:"Digital Banking Application",industry:"Financial Technology",description:"Mobile-first digital banking platform with AI-powered financial insights",projectType:"mobile-application",platform:"mobile",complexity:"advanced",technologies:["React Native","Node.js","PostgreSQL","AWS","TensorFlow","Plaid"],features:["Account management","Mobile payments","AI budgeting","Investment tracking"],businessGoals:["Increase customer engagement","Reduce operational costs","Expand digital services"],targetAudience:"Banking customers and financial service users",timeline:"10-15 months",budget:"$400K - $1.5M",successMetrics:["90% customer adoption","50% cost reduction","4.8+ app store rating"],risks:["Regulatory compliance","Security vulnerabilities","Competition from neobanks"],template:`Create a next-generation digital banking application with AI-powered financial management.

Key Requirements:
- Secure account management and transaction history
- Real-time mobile payments and transfers
- AI-powered budgeting and spending insights
- Investment portfolio tracking and recommendations
- Biometric authentication and fraud detection
- Integration with third-party financial services

Technical Specifications:
- Cross-platform mobile development
- End-to-end encryption for all transactions
- Real-time fraud detection algorithms
- Open banking API integrations
- Cloud-native architecture with auto-scaling
- Comprehensive security audit and penetration testing

Success Criteria:
- PCI DSS compliance certification
- 99.99% transaction success rate
- <3 second app load times
- Zero critical security vulnerabilities`},{id:"cryptocurrency-exchange",name:"Cryptocurrency Exchange Platform",industry:"Financial Technology",description:"Secure cryptocurrency trading platform with advanced order matching",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Go","PostgreSQL","Redis","Kubernetes","WebSocket"],features:["Crypto trading","Order matching","Wallet management","KYC/AML compliance"],businessGoals:["Capture crypto market share","Ensure regulatory compliance","Maximize trading volume"],targetAudience:"Cryptocurrency traders and investors",timeline:"12-18 months",budget:"$600K - $2.5M",successMetrics:["$100M+ daily volume","99.99% uptime","Full regulatory compliance"],risks:["Regulatory uncertainty","Security attacks","Market manipulation"],template:`Develop a secure and scalable cryptocurrency exchange with institutional-grade features.

Key Requirements:
- High-performance order matching engine
- Multi-cryptocurrency wallet management
- Advanced trading features (limit, market, stop orders)
- KYC/AML compliance and reporting
- Cold storage security for digital assets
- Real-time market data and charting tools

Technical Specifications:
- Microservices architecture with Go backend
- High-frequency trading support (>100k TPS)
- Multi-signature wallet security
- Real-time WebSocket market data feeds
- Kubernetes orchestration for scalability
- Comprehensive audit logging and monitoring

Success Criteria:
- Handle 1M+ transactions per day
- 99.99% platform availability
- SOC 2 Type II compliance
- Zero successful security breaches`},{id:"telemedicine-platform",name:"Telemedicine Platform",industry:"Healthcare & Medical",description:"HIPAA-compliant telemedicine platform for remote patient consultations",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","WebRTC","PostgreSQL","AWS","Socket.io"],features:["Video consultations","Patient records","Prescription management","Appointment scheduling"],businessGoals:["Improve patient access","Reduce healthcare costs","Ensure HIPAA compliance"],targetAudience:"Healthcare providers and patients",timeline:"8-12 months",budget:"$200K - $800K",successMetrics:["Patient satisfaction >4.5","Consultation completion >95%","Zero HIPAA violations"],risks:["Regulatory compliance","Data security breaches","Video quality issues"],template:`Create a HIPAA-compliant telemedicine platform enabling secure remote healthcare delivery.

Key Requirements:
- End-to-end encrypted video consultations
- Electronic health records (EHR) integration
- Prescription management and e-prescribing
- Secure patient-provider messaging
- Appointment scheduling and calendar integration
- Payment processing and insurance verification

Technical Specifications:
- WebRTC for real-time video communication
- HIPAA-compliant cloud infrastructure
- Role-based access control (RBAC)
- Audit logging for all patient interactions
- Integration with existing EHR systems
- Mobile-responsive design for accessibility

Success Criteria:
- HIPAA compliance certification
- 99.9% uptime for critical services
- <2 second video connection establishment
- Support for 1,000+ concurrent consultations`},{id:"patient-portal",name:"Patient Portal System",industry:"Healthcare & Medical",description:"Comprehensive patient portal for accessing medical records and managing healthcare",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["Vue.js","Laravel","MySQL","Redis","Docker"],features:["Medical records access","Appointment booking","Lab results","Billing management"],businessGoals:["Improve patient engagement","Reduce administrative burden","Enhance care coordination"],targetAudience:"Patients and healthcare administrators",timeline:"6-9 months",budget:"$150K - $400K",successMetrics:["Patient adoption >70%","Phone calls reduced 40%","Patient satisfaction >4.0"],risks:["Low user adoption","Integration complexity","Data privacy concerns"],template:`Develop a user-friendly patient portal that empowers patients to manage their healthcare digitally.

Key Requirements:
- Secure access to medical records and test results
- Online appointment scheduling and management
- Prescription refill requests and medication tracking
- Secure messaging with healthcare providers
- Billing and insurance information management
- Health education resources and reminders

Technical Specifications:
- Multi-factor authentication for security
- Integration with EHR and practice management systems
- Mobile-first responsive design
- Automated appointment reminders via SMS/email
- Document upload and sharing capabilities
- Accessibility compliance (WCAG 2.1)

Success Criteria:
- 70% patient adoption within 6 months
- 40% reduction in administrative phone calls
- 99.5% uptime for patient-facing services
- Full HIPAA compliance and security audit passed`},{id:"medical-inventory-system",name:"Medical Inventory Management",industry:"Healthcare & Medical",description:"Smart inventory management system for medical supplies and pharmaceuticals",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["Angular","Spring Boot","PostgreSQL","Redis","Kubernetes"],features:["Real-time inventory tracking","Automated reordering","Expiration monitoring","Compliance reporting"],businessGoals:["Reduce waste","Ensure supply availability","Maintain regulatory compliance"],targetAudience:"Hospital administrators and pharmacy staff",timeline:"5-8 months",budget:"$100K - $300K",successMetrics:["Inventory waste reduction 25%","Stockout incidents <2%","Compliance score >98%"],risks:["Integration with existing systems","Staff training requirements","Regulatory changes"],template:`Build an intelligent medical inventory management system that optimizes supply chain operations.

Key Requirements:
- Real-time tracking of medical supplies and pharmaceuticals
- Automated reorder points and purchase order generation
- Expiration date monitoring and alerts
- Barcode/RFID scanning for inventory updates
- Regulatory compliance reporting (FDA, DEA)
- Integration with supplier systems and ERP

Technical Specifications:
- Microservices architecture for scalability
- Real-time dashboard with inventory analytics
- Mobile app for warehouse staff
- API integrations with supplier systems
- Automated compliance reporting
- Role-based access control for different user types

Success Criteria:
- 25% reduction in inventory carrying costs
- 99% inventory accuracy
- Zero expired medication incidents
- Full regulatory compliance maintained`},{id:"hospital-management-system",name:"Hospital Management System",industry:"Healthcare & Medical",description:"Comprehensive hospital management system for operations and patient care",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","Redis","Docker","Elasticsearch"],features:["Patient management","Staff scheduling","Billing system","Inventory tracking"],businessGoals:["Streamline operations","Improve patient care","Reduce operational costs"],targetAudience:"Hospital administrators, doctors, and nursing staff",timeline:"12-18 months",budget:"$300K - $1M",successMetrics:["30% efficiency improvement","Patient satisfaction >4.5","Cost reduction 20%"],risks:["System integration complexity","Staff training","Data migration challenges"],template:`Create a comprehensive hospital management system that integrates all aspects of hospital operations.

Key Requirements:
- Patient admission, discharge, and transfer (ADT) management
- Electronic medical records (EMR) integration
- Staff scheduling and resource allocation
- Billing and insurance claim processing
- Pharmacy and inventory management
- Laboratory and radiology information systems

Technical Specifications:
- Modular architecture with microservices
- Real-time data synchronization across departments
- Integration with medical devices and equipment
- Advanced reporting and analytics dashboard
- Mobile applications for staff
- Disaster recovery and backup systems

Success Criteria:
- 99.9% system availability
- 30% reduction in administrative tasks
- Full integration with existing medical systems
- HIPAA compliance and security certification`},{id:"mental-health-app",name:"Mental Health Support App",industry:"Healthcare & Medical",description:"Mobile mental health platform with therapy sessions and wellness tracking",projectType:"mobile-application",platform:"mobile",complexity:"intermediate",technologies:["React Native","Node.js","MongoDB","AWS","WebRTC"],features:["Therapy sessions","Mood tracking","Meditation guides","Crisis support"],businessGoals:["Improve mental health access","Reduce therapy costs","Provide 24/7 support"],targetAudience:"Individuals seeking mental health support",timeline:"6-10 months",budget:"$150K - $500K",successMetrics:["User engagement >80%","Therapy completion >70%","Crisis response <5min"],risks:["Regulatory compliance","Crisis management","User safety concerns"],template:`Develop a comprehensive mental health support application with professional therapy integration.

Key Requirements:
- Secure video therapy sessions with licensed professionals
- Daily mood and wellness tracking with insights
- Guided meditation and mindfulness exercises
- Crisis intervention and emergency support
- Peer support communities and forums
- Integration with wearable devices for health monitoring

Technical Specifications:
- End-to-end encryption for all communications
- HIPAA-compliant data storage and processing
- AI-powered mood analysis and recommendations
- Real-time crisis detection and alert systems
- Offline functionality for core features
- Accessibility features for diverse user needs

Success Criteria:
- 80% user retention after 3 months
- 24/7 crisis support availability
- Integration with 5+ major therapy providers
- 4.5+ app store rating`},{id:"lms-platform",name:"Learning Management System",industry:"Education & E-learning",description:"Comprehensive LMS for educational institutions with advanced analytics",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Django","PostgreSQL","Redis","AWS","TensorFlow"],features:["Course management","Student tracking","Assessment tools","Analytics dashboard"],businessGoals:["Improve learning outcomes","Increase student engagement","Reduce administrative burden"],targetAudience:"Educational institutions, teachers, and students",timeline:"8-12 months",budget:"$200K - $700K",successMetrics:["90% course completion","85% student satisfaction","40% admin time reduction"],risks:["User adoption challenges","Content migration complexity","Scalability issues"],template:`Build a comprehensive learning management system that enhances educational delivery and outcomes.

Key Requirements:
- Course creation and content management tools
- Student enrollment and progress tracking
- Interactive assessments and grading systems
- Discussion forums and collaboration tools
- Advanced analytics and reporting
- Integration with existing educational systems

Technical Specifications:
- Scalable cloud architecture supporting 10,000+ concurrent users
- Mobile-responsive design for all devices
- AI-powered learning analytics and recommendations
- Video streaming and content delivery network (CDN)
- Single sign-on (SSO) integration
- Accessibility compliance (WCAG 2.1 AA)

Success Criteria:
- Support for 50,000+ students
- 99.9% platform uptime
- <3 second page load times
- Full FERPA compliance for student data`},{id:"online-tutoring-platform",name:"Online Tutoring Platform",industry:"Education & E-learning",description:"AI-powered tutoring platform connecting students with qualified tutors",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["Vue.js","Node.js","MongoDB","WebRTC","Stripe","Socket.io"],features:["Tutor matching","Video sessions","Payment processing","Progress tracking"],businessGoals:["Improve learning outcomes","Increase tutor utilization","Expand market reach"],targetAudience:"Students, parents, and tutors",timeline:"6-9 months",budget:"$150K - $400K",successMetrics:["90% session completion","Tutor utilization >75%","Student improvement 25%"],risks:["Quality control","Payment disputes","Tutor availability"],template:`Create an intelligent tutoring platform that matches students with the best tutors for their needs.

Key Requirements:
- AI-powered tutor-student matching algorithm
- High-quality video conferencing for tutoring sessions
- Integrated whiteboard and screen sharing tools
- Secure payment processing and tutor payouts
- Session recording and review capabilities
- Progress tracking and performance analytics

Technical Specifications:
- Real-time video communication with WebRTC
- Machine learning for tutor recommendation engine
- Secure payment processing with escrow system
- Mobile-responsive design for all devices
- Integration with calendar and scheduling systems
- Multi-language support for global reach

Success Criteria:
- 90% successful tutor-student matches
- Average session rating >4.5
- 75% student retention after 3 months
- Platform supports 1,000+ concurrent sessions`},{id:"property-management-platform",name:"Property Management Platform",industry:"Real Estate",description:"Comprehensive property management system for landlords and property managers",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","Stripe","AWS","Twilio"],features:["Property listings","Tenant management","Rent collection","Maintenance tracking"],businessGoals:["Streamline operations","Improve tenant satisfaction","Increase rental income"],targetAudience:"Property managers, landlords, and tenants",timeline:"6-10 months",budget:"$150K - $500K",successMetrics:["95% rent collection rate","Tenant satisfaction >4.0","30% time savings"],risks:["Regulatory compliance","Payment processing issues","Data security"],template:`Build a comprehensive property management platform that automates rental operations.

Key Requirements:
- Property portfolio management and listings
- Tenant screening and application processing
- Automated rent collection and payment processing
- Maintenance request tracking and vendor management
- Financial reporting and accounting integration
- Communication tools for landlord-tenant interactions

Technical Specifications:
- Multi-tenant architecture for property managers
- Secure payment processing with ACH and credit cards
- Mobile applications for tenants and maintenance staff
- Integration with accounting software (QuickBooks, Xero)
- Document management and e-signature capabilities
- Automated late payment notifications and collections

Success Criteria:
- 95% on-time rent collection rate
- 50% reduction in administrative tasks
- 99.5% payment processing uptime
- Full compliance with local rental regulations`},{id:"real-estate-marketplace",name:"Real Estate Marketplace",industry:"Real Estate",description:"AI-powered real estate marketplace with virtual tours and market analytics",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["Next.js","Python","PostgreSQL","Elasticsearch","AWS","TensorFlow"],features:["Property search","Virtual tours","Market analytics","Agent matching"],businessGoals:["Increase property sales","Improve user experience","Expand market share"],targetAudience:"Home buyers, sellers, and real estate agents",timeline:"10-15 months",budget:"$300K - $1M",successMetrics:["1M+ property views/month","Conversion rate >3%","Agent satisfaction >4.5"],risks:["Market competition","Data accuracy","Technology adoption"],template:`Create an innovative real estate marketplace with AI-powered features and immersive experiences.

Key Requirements:
- Advanced property search with AI-powered recommendations
- 360-degree virtual tours and augmented reality features
- Real-time market analytics and price predictions
- Agent-buyer matching and communication tools
- Mortgage calculator and financing options
- Neighborhood insights and demographic data

Technical Specifications:
- Machine learning for property valuation and recommendations
- High-performance search with Elasticsearch
- CDN for fast image and video delivery
- Integration with MLS (Multiple Listing Service) data
- Mobile-first responsive design
- Real-time chat and video calling capabilities

Success Criteria:
- 1 million monthly active users
- 3% buyer-to-sale conversion rate
- <2 second property search response time
- Integration with 50+ MLS systems nationwide`},{id:"multiplayer-game-platform",name:"Multiplayer Gaming Platform",industry:"Gaming & Entertainment",description:"Real-time multiplayer gaming platform with social features and tournaments",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","Socket.io","Redis","MongoDB","WebRTC"],features:["Real-time gameplay","Tournament system","Social features","Leaderboards"],businessGoals:["Increase player engagement","Monetize through tournaments","Build gaming community"],targetAudience:"Gamers and esports enthusiasts",timeline:"8-12 months",budget:"$200K - $800K",successMetrics:["100K+ active players","Average session >30min","Tournament participation >20%"],risks:["Server scalability","Cheating prevention","Player retention"],template:`Develop a high-performance multiplayer gaming platform with competitive features.

Key Requirements:
- Real-time multiplayer game engine with low latency
- Tournament and league management system
- Player profiles and social networking features
- In-game chat and voice communication
- Anti-cheat detection and prevention systems
- Spectator mode and live streaming integration

Technical Specifications:
- WebSocket-based real-time communication
- Distributed server architecture for global reach
- Redis for session management and caching
- Machine learning for cheat detection
- CDN for game asset delivery
- Mobile-responsive design for cross-platform play

Success Criteria:
- <50ms latency for real-time gameplay
- Support for 10,000+ concurrent players
- 99.9% server uptime during peak hours
- Zero tolerance for cheating with 99% detection rate`},{id:"streaming-platform",name:"Video Streaming Platform",industry:"Gaming & Entertainment",description:"Live streaming platform for content creators with monetization features",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","WebRTC","AWS","Redis","Elasticsearch"],features:["Live streaming","Chat system","Monetization tools","Content discovery"],businessGoals:["Attract content creators","Increase viewer engagement","Generate revenue"],targetAudience:"Content creators and viewers",timeline:"10-15 months",budget:"$400K - $1.5M",successMetrics:["1M+ monthly viewers","Creator retention >80%","Revenue growth 50%/year"],risks:["Content moderation","Bandwidth costs","Competition from major platforms"],template:`Build a comprehensive live streaming platform that empowers content creators.

Key Requirements:
- High-quality live video streaming with adaptive bitrate
- Real-time chat and interaction features
- Creator monetization tools (subscriptions, donations, ads)
- Content discovery and recommendation engine
- Mobile streaming applications for creators
- Advanced analytics and creator dashboard

Technical Specifications:
- WebRTC and HLS for video streaming
- Global CDN for low-latency delivery
- Real-time chat with moderation tools
- Machine learning for content recommendations
- Payment processing for creator monetization
- Scalable architecture supporting millions of viewers

Success Criteria:
- Support for 100,000+ concurrent viewers per stream
- <3 second stream start time globally
- 99.9% streaming uptime
- Creator payout processing within 24 hours`},{id:"task-management-app",name:"TaskMaster Pro",industry:"Productivity & Collaboration",description:"Comprehensive task management application for teams and individuals",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["Next.js","Tailwind CSS","Supabase","NextAuth.js","Vercel"],features:["Task assignment","Deadline tracking","Progress visualization","Team collaboration"],businessGoals:["Improve team productivity","Enhance project visibility","Reduce missed deadlines"],targetAudience:"Project managers, teams, and individual professionals",timeline:"4-6 months",budget:"$80K - $250K",successMetrics:["90% task completion rate","Team productivity increase 25%","User adoption >80%"],risks:["User adoption challenges","Integration complexity","Competition from established tools"],template:`Build a comprehensive task management application that helps teams organize, track, and collaborate on projects.

Key Requirements:
- User authentication and team management
- Task creation, assignment, and tracking
- Project dashboard with progress visualization
- Real-time collaboration and comments
- File attachments and document sharing
- Email notifications and reminders
- Time tracking and reporting
- Integration with calendar apps

Technical Specifications:
- Modern React-based frontend with Next.js
- Real-time updates using WebSocket connections
- Role-based permissions and workspace management
- Mobile-responsive design for all devices
- Integration with popular productivity tools
- Automated backup and data recovery

Success Criteria:
- Support for teams of up to 500 members
- 99.5% uptime for critical features
- <2 second page load times
- Integration with 10+ popular productivity apps`},{id:"fitness-tracking-app",name:"FitTracker Mobile",industry:"Health & Fitness",description:"Mobile fitness tracking app with wearable integration and social features",projectType:"mobile-application",platform:"mobile",complexity:"advanced",technologies:["React Native","Firebase","Firebase Auth","Jest","HealthKit"],features:["Workout tracking","Nutrition logging","Progress photos","Social challenges"],businessGoals:["Improve user health outcomes","Increase app engagement","Build fitness community"],targetAudience:"Fitness enthusiasts and health-conscious individuals",timeline:"8-12 months",budget:"$200K - $600K",successMetrics:["Daily active users >50K","Workout completion >80%","User retention >70%"],risks:["Wearable integration complexity","Health data privacy","User motivation"],template:`Create a comprehensive fitness tracking mobile app with advanced features and social integration.

Key Requirements:
- User profiles and personalized goal setting
- Comprehensive workout tracking and exercise library
- Nutrition logging with calorie and macro counting
- Progress photos and body measurements tracking
- Social features including challenges and leaderboards
- Integration with popular fitness wearables and health apps
- Offline workout mode with data synchronization
- Push notifications for motivation and reminders

Technical Specifications:
- Cross-platform mobile development with React Native
- Integration with HealthKit (iOS) and Google Fit (Android)
- Real-time data synchronization with cloud backend
- Machine learning for personalized recommendations
- Social networking features with privacy controls
- Offline-first architecture with background sync

Success Criteria:
- Integration with 10+ major fitness wearables
- 80% workout completion rate
- 4.5+ app store rating
- Support for offline usage in 90% of features`},{id:"data-visualization-platform",name:"DataViz Dashboard",industry:"Business Intelligence & Analytics",description:"Interactive data visualization platform with advanced analytics capabilities",projectType:"data-analysis",platform:"web",complexity:"advanced",technologies:["React","Python/FastAPI","PostgreSQL","Material-UI","AWS","D3.js"],features:["Data import","Interactive charts","Real-time processing","Collaborative sharing"],businessGoals:["Enable data-driven decisions","Improve analytics accessibility","Reduce reporting time"],targetAudience:"Data analysts, business users, and executives",timeline:"10-14 months",budget:"$300K - $900K",successMetrics:["Processing 1M+ rows","Report generation <30sec","User adoption >85%"],risks:["Data security","Performance with large datasets","User training requirements"],template:`Develop an advanced data visualization platform that transforms raw data into actionable insights.

Key Requirements:
- Support for multiple data sources (CSV, JSON, databases, APIs)
- Interactive chart and graph creation with drag-and-drop interface
- Real-time data processing and streaming capabilities
- Collaborative dashboard sharing and permissions
- Advanced statistical analysis and machine learning integration
- Custom visualization components and templates
- Automated report generation and scheduling
- RESTful API for data integration and embedding

Technical Specifications:
- High-performance backend with Python/FastAPI
- Scalable data processing with Apache Spark
- Interactive visualizations using D3.js and custom components
- Real-time data streaming with WebSocket connections
- Cloud-native architecture with auto-scaling
- Advanced caching for improved performance

Success Criteria:
- Process datasets with millions of rows in real-time
- Generate complex reports in under 30 seconds
- Support for 1,000+ concurrent users
- 99.9% data accuracy and integrity`},{id:"hospital-management-system",name:"Hospital Management System",industry:"Healthcare & Medical",description:"Comprehensive HIPAA-compliant hospital management platform with patient records and scheduling",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","AWS","HL7 FHIR","Redis"],features:["Patient records","Appointment scheduling","Billing system","Inventory management","Staff management"],businessGoals:["Improve patient care","Reduce administrative costs","Ensure HIPAA compliance","Streamline operations"],targetAudience:"Hospitals, clinics, and healthcare administrators",timeline:"12-18 months",budget:"$400K - $1.2M",successMetrics:["HIPAA compliance certification","50% reduction in paperwork","99.9% uptime"],risks:["Data breaches","Regulatory compliance","Staff training requirements"],template:`Build a comprehensive hospital management system with full HIPAA compliance and integrated patient care workflows.

Key Requirements:
- Electronic Health Records (EHR) with HL7 FHIR compliance
- Patient appointment scheduling and management
- Billing and insurance claim processing
- Medical inventory and pharmacy management
- Staff scheduling and role-based access control
- Integration with medical devices and lab systems
- Telemedicine capabilities for remote consultations
- Automated reporting and analytics dashboard

Technical Specifications:
- HIPAA-compliant cloud infrastructure
- End-to-end encryption for all patient data
- Role-based access control with audit trails
- Integration with existing hospital systems
- Real-time notifications and alerts
- Mobile-responsive design for tablets and smartphones
- Backup and disaster recovery systems
- API integrations with insurance providers

Success Criteria:
- Full HIPAA compliance certification
- 99.9% system uptime during critical hours
- 50% reduction in administrative paperwork
- Integration with 95% of existing hospital systems`},{id:"mental-health-platform",name:"Mental Health Support Platform",industry:"Healthcare & Medical",description:"Digital mental health platform with therapy matching, progress tracking, and crisis intervention",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","Stripe","Twilio","TensorFlow"],features:["Therapist matching","Video sessions","Progress tracking","Crisis intervention","Resource library"],businessGoals:["Improve mental health access","Reduce therapy wait times","Provide 24/7 support"],targetAudience:"Individuals seeking mental health support and licensed therapists",timeline:"8-12 months",budget:"$200K - $600K",successMetrics:["90% user satisfaction","70% therapy completion rate","24/7 crisis response"],risks:["Privacy concerns","Therapist availability","Crisis management protocols"],template:`Create a comprehensive mental health platform that connects users with licensed therapists and provides ongoing support.

Key Requirements:
- AI-powered therapist matching based on specialties and preferences
- Secure video conferencing for therapy sessions
- Progress tracking with mood journals and assessments
- 24/7 crisis intervention with emergency protocols
- Comprehensive resource library with self-help tools
- Insurance integration and billing management
- Mobile app for on-the-go access
- Community support groups and forums

Technical Specifications:
- HIPAA-compliant video conferencing
- Encrypted messaging and file sharing
- AI algorithms for therapist-client matching
- Real-time crisis detection and alert systems
- Integration with electronic health records
- Payment processing with insurance claims
- Multi-platform mobile and web applications
- Advanced analytics for treatment outcomes

Success Criteria:
- 90% user satisfaction rating
- 70% therapy session completion rate
- Sub-5 minute crisis response time
- Integration with major insurance providers`},{id:"medical-imaging-ai",name:"AI Medical Imaging Platform",industry:"Healthcare & Medical",description:"AI-powered medical imaging analysis platform for radiology and diagnostic imaging",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["Python","TensorFlow","React","FastAPI","PostgreSQL","DICOM"],features:["Image analysis","AI diagnostics","Report generation","DICOM integration","Radiologist workflow"],businessGoals:["Improve diagnostic accuracy","Reduce analysis time","Support radiologists"],targetAudience:"Radiologists, hospitals, and diagnostic imaging centers",timeline:"15-24 months",budget:"$800K - $2.5M",successMetrics:["95% diagnostic accuracy","60% faster analysis","FDA approval"],risks:["Regulatory approval","AI model accuracy","Integration complexity"],template:`Develop an AI-powered medical imaging platform that assists radiologists in diagnostic analysis and reporting.

Key Requirements:
- Advanced AI models for medical image analysis (X-ray, CT, MRI, ultrasound)
- DICOM standard compliance for medical imaging
- Automated report generation with confidence scores
- Integration with existing radiology workflows (PACS/RIS)
- Real-time collaboration tools for radiologists
- Quality assurance and peer review systems
- Mobile access for emergency consultations
- Comprehensive audit trails and version control

Technical Specifications:
- Deep learning models trained on medical datasets
- GPU-accelerated image processing infrastructure
- DICOM viewer with advanced visualization tools
- RESTful APIs for PACS/RIS integration
- Cloud-based storage with HIPAA compliance
- Real-time image streaming and processing
- Advanced security with role-based access
- Scalable architecture for high-volume processing

Success Criteria:
- 95% diagnostic accuracy compared to expert radiologists
- 60% reduction in image analysis time
- FDA 510(k) clearance for clinical use
- Integration with 90% of major PACS systems`},{id:"pharmacy-management",name:"Pharmacy Management System",industry:"Healthcare & Medical",description:"Complete pharmacy management solution with inventory, prescriptions, and insurance processing",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","Stripe","Twilio","AWS"],features:["Prescription management","Inventory tracking","Insurance processing","Patient profiles","Drug interaction alerts"],businessGoals:["Streamline pharmacy operations","Reduce medication errors","Improve customer service"],targetAudience:"Pharmacies, pharmacists, and pharmacy technicians",timeline:"6-10 months",budget:"$150K - $500K",successMetrics:["99% prescription accuracy","50% faster processing","95% insurance approval"],risks:["Regulatory compliance","Drug database accuracy","Insurance integration"],template:`Build a comprehensive pharmacy management system that streamlines prescription processing and inventory management.

Key Requirements:
- Electronic prescription processing and verification
- Real-time inventory management with automatic reordering
- Insurance claim processing and prior authorization
- Patient profile management with medication history
- Drug interaction and allergy checking
- Automated refill reminders and notifications
- Point-of-sale integration with payment processing
- Regulatory compliance reporting (DEA, state boards)

Technical Specifications:
- Integration with electronic health records (EHR)
- Real-time drug database updates (First Databank, Lexicomp)
- Secure prescription transmission (SCRIPT standard)
- Barcode scanning for medication verification
- Cloud-based backup and disaster recovery
- Mobile app for prescription management
- Advanced reporting and analytics dashboard
- Multi-location support for pharmacy chains

Success Criteria:
- 99% prescription accuracy with error checking
- 50% reduction in prescription processing time
- 95% insurance claim approval rate
- Full compliance with pharmacy regulations`},{id:"fitness-wellness-app",name:"Fitness & Wellness Tracking App",industry:"Healthcare & Medical",description:"Comprehensive fitness and wellness platform with personalized coaching and health monitoring",projectType:"mobile-application",platform:"mobile",complexity:"intermediate",technologies:["React Native","Node.js","MongoDB","AWS","HealthKit","Google Fit"],features:["Workout tracking","Nutrition logging","Health monitoring","Personal coaching","Social features"],businessGoals:["Promote healthy lifestyles","Increase user engagement","Provide personalized guidance"],targetAudience:"Fitness enthusiasts, health-conscious individuals, and personal trainers",timeline:"6-9 months",budget:"$100K - $400K",successMetrics:["80% daily active users","70% goal completion","4.5+ app rating"],risks:["User retention","Data accuracy","Competition from established apps"],template:`Create a comprehensive fitness and wellness platform that motivates users to achieve their health goals.

Key Requirements:
- Comprehensive workout tracking with exercise library
- Nutrition logging with barcode scanning and meal planning
- Health metrics monitoring (weight, heart rate, sleep, steps)
- AI-powered personal coaching and recommendations
- Social features with challenges and community support
- Integration with wearable devices and health apps
- Progress tracking with detailed analytics and insights
- Customizable workout plans and nutrition programs

Technical Specifications:
- Cross-platform mobile development (iOS/Android)
- Integration with HealthKit (iOS) and Google Fit (Android)
- Real-time synchronization across devices
- Machine learning for personalized recommendations
- Social networking features with privacy controls
- Push notifications for motivation and reminders
- Offline mode for workout tracking
- Advanced analytics and reporting dashboard

Success Criteria:
- 80% daily active user retention
- 70% user goal completion rate
- 4.5+ average app store rating
- Integration with 95% of popular fitness wearables`},{id:"cryptocurrency-exchange",name:"Cryptocurrency Exchange Platform",industry:"Financial Services & Fintech",description:"Secure cryptocurrency trading platform with advanced order matching and wallet management",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","Redis","WebSocket","Blockchain APIs"],features:["Crypto trading","Wallet management","Order matching","Security features","Market analysis"],businessGoals:["Enable crypto trading","Ensure security","Provide liquidity","Comply with regulations"],targetAudience:"Cryptocurrency traders and investors",timeline:"12-18 months",budget:"$600K - $2M",successMetrics:["$1M+ daily volume","99.9% uptime","Zero security breaches"],risks:["Regulatory changes","Security threats","Market volatility"],template:`Build a secure and scalable cryptocurrency exchange platform with advanced trading features.

Key Requirements:
- Multi-cryptocurrency support (Bitcoin, Ethereum, altcoins)
- Advanced order matching engine with high throughput
- Secure wallet management with cold storage integration
- Real-time market data and charting tools
- KYC/AML compliance and identity verification
- Two-factor authentication and security measures
- API for algorithmic trading and third-party integrations
- Liquidity management and market making tools

Technical Specifications:
- High-performance order matching engine (100k+ orders/sec)
- Multi-signature wallet security with hardware security modules
- Real-time WebSocket connections for market data
- Microservices architecture for scalability
- Advanced monitoring and alerting systems
- Compliance reporting and audit trails
- DDoS protection and security hardening
- Multi-region deployment for global access

Success Criteria:
- Process $1M+ in daily trading volume
- 99.9% platform uptime during market hours
- Zero security breaches or fund losses
- Full regulatory compliance in target jurisdictions`},{id:"robo-advisor-platform",name:"Robo-Advisor Investment Platform",industry:"Financial Services & Fintech",description:"AI-powered investment advisory platform with automated portfolio management and rebalancing",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Python","TensorFlow","PostgreSQL","AWS","Alpaca API"],features:["Portfolio management","Risk assessment","Automated rebalancing","Tax optimization","Goal tracking"],businessGoals:["Democratize investing","Reduce management fees","Provide personalized advice"],targetAudience:"Individual investors and financial advisors",timeline:"10-15 months",budget:"$400K - $1.2M",successMetrics:["$100M+ assets under management","8%+ annual returns","0.5% management fee"],risks:["Market volatility","Regulatory compliance","Algorithm performance"],template:`Create an AI-powered robo-advisor platform that provides automated investment management and financial planning.

Key Requirements:
- AI-driven portfolio construction and optimization
- Automated rebalancing based on market conditions
- Risk tolerance assessment and goal-based investing
- Tax-loss harvesting and optimization strategies
- Integration with brokerage accounts and custodians
- Comprehensive financial planning tools
- Real-time performance tracking and reporting
- Educational content and investment insights

Technical Specifications:
- Machine learning algorithms for portfolio optimization
- Real-time market data integration and analysis
- Automated trading execution with best execution
- Advanced risk management and compliance monitoring
- Secure account aggregation and data synchronization
- Mobile-first responsive design
- Comprehensive API for third-party integrations
- Advanced analytics and performance attribution

Success Criteria:
- Manage $100M+ in assets under management
- Achieve 8%+ average annual returns net of fees
- Maintain 0.5% or lower management fee structure
- 95% client satisfaction and retention rate`},{id:"peer-to-peer-lending",name:"Peer-to-Peer Lending Platform",industry:"Financial Services & Fintech",description:"P2P lending marketplace connecting borrowers with investors for personal and business loans",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","Stripe","Plaid","TensorFlow"],features:["Loan marketplace","Credit scoring","Automated investing","Risk assessment","Payment processing"],businessGoals:["Connect borrowers and lenders","Reduce lending costs","Improve access to credit"],targetAudience:"Individual borrowers, investors, and small businesses",timeline:"12-16 months",budget:"$500K - $1.5M",successMetrics:["$50M+ loan origination","5% default rate","12% investor returns"],risks:["Credit risk","Regulatory compliance","Economic downturns"],template:`Build a comprehensive peer-to-peer lending platform that efficiently matches borrowers with investors.

Key Requirements:
- Borrower application and verification system
- AI-powered credit scoring and risk assessment
- Investor dashboard with automated investing options
- Loan marketplace with filtering and search capabilities
- Integrated payment processing and loan servicing
- Regulatory compliance and reporting tools
- Mobile applications for borrowers and investors
- Advanced analytics and performance tracking

Technical Specifications:
- Machine learning models for credit risk assessment
- Integration with credit bureaus and financial data providers
- Automated loan servicing and payment processing
- Real-time loan performance monitoring
- Secure document upload and verification
- Advanced fraud detection and prevention
- Comprehensive reporting and compliance tools
- Scalable architecture for high transaction volume

Success Criteria:
- Originate $50M+ in loans annually
- Maintain sub-5% default rate across loan portfolio
- Achieve 12%+ average annual returns for investors
- Full compliance with lending regulations`},{id:"expense-management-app",name:"Corporate Expense Management",industry:"Financial Services & Fintech",description:"AI-powered expense management platform with receipt scanning and automated approval workflows",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","AWS","OCR API","Stripe"],features:["Receipt scanning","Expense tracking","Approval workflows","Reimbursement processing","Analytics"],businessGoals:["Streamline expense reporting","Reduce processing time","Improve compliance"],targetAudience:"Businesses, employees, and finance teams",timeline:"6-10 months",budget:"$200K - $600K",successMetrics:["80% faster processing","95% receipt accuracy","90% user adoption"],risks:["OCR accuracy","Integration complexity","User adoption"],template:`Create an intelligent expense management platform that automates expense reporting and approval processes.

Key Requirements:
- AI-powered receipt scanning and data extraction
- Mobile app for expense capture and submission
- Customizable approval workflows and policies
- Integration with accounting systems (QuickBooks, SAP, etc.)
- Real-time expense tracking and budget monitoring
- Automated mileage tracking and calculation
- Corporate credit card integration and reconciliation
- Comprehensive reporting and analytics dashboard

Technical Specifications:
- OCR technology for receipt data extraction
- Machine learning for expense categorization
- Real-time synchronization across devices
- Integration with major accounting platforms
- Automated policy compliance checking
- Advanced reporting with custom dashboards
- Mobile-first responsive design
- Secure document storage and retrieval

Success Criteria:
- 80% reduction in expense processing time
- 95% accuracy in receipt data extraction
- 90% employee adoption within 6 months
- Integration with 95% of popular accounting systems`},{id:"insurance-claims-platform",name:"Digital Insurance Claims Platform",industry:"Financial Services & Fintech",description:"AI-powered insurance claims processing platform with automated assessment and fraud detection",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Python","TensorFlow","PostgreSQL","AWS","Computer Vision"],features:["Claims processing","Damage assessment","Fraud detection","Customer portal","Agent dashboard"],businessGoals:["Accelerate claims processing","Reduce fraud","Improve customer satisfaction"],targetAudience:"Insurance companies, claims adjusters, and policyholders",timeline:"12-18 months",budget:"$600K - $1.8M",successMetrics:["70% faster processing","90% fraud detection","95% customer satisfaction"],risks:["AI accuracy","Regulatory compliance","Integration complexity"],template:`Develop an AI-powered insurance claims platform that automates assessment and accelerates processing.

Key Requirements:
- AI-powered damage assessment using computer vision
- Automated fraud detection and risk scoring
- Customer self-service portal for claim submission
- Claims adjuster dashboard with workflow management
- Integration with existing insurance systems
- Real-time claim status tracking and notifications
- Mobile app for photo capture and documentation
- Comprehensive reporting and analytics tools

Technical Specifications:
- Computer vision models for damage assessment
- Machine learning algorithms for fraud detection
- Real-time image processing and analysis
- Integration with core insurance systems
- Automated workflow orchestration
- Advanced security and data protection
- Scalable cloud infrastructure
- Mobile-optimized user interfaces

Success Criteria:
- 70% reduction in claims processing time
- 90% accuracy in fraud detection
- 95% customer satisfaction rating
- Integration with major insurance carriers`},{id:"marketplace-platform",name:"Multi-Vendor Marketplace",industry:"E-commerce & Retail",description:"Comprehensive multi-vendor marketplace with seller management and advanced analytics",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","Stripe","AWS","Elasticsearch"],features:["Vendor management","Product catalog","Order processing","Payment gateway","Analytics dashboard"],businessGoals:["Create marketplace ecosystem","Generate commission revenue","Scale vendor network"],targetAudience:"Online sellers, buyers, and marketplace operators",timeline:"10-15 months",budget:"$400K - $1.2M",successMetrics:["1000+ active vendors","$10M+ GMV","95% uptime"],risks:["Vendor quality control","Payment disputes","Competition"],template:`Build a comprehensive multi-vendor marketplace platform that connects sellers with buyers globally.

Key Requirements:
- Vendor onboarding and management system
- Advanced product catalog with search and filtering
- Integrated payment processing with split payments
- Order management and fulfillment tracking
- Review and rating system for vendors and products
- Commission management and payout automation
- Mobile-responsive design with PWA capabilities
- Advanced analytics and reporting dashboard

Technical Specifications:
- Microservices architecture for scalability
- Elasticsearch for advanced product search
- Real-time inventory management across vendors
- Automated commission calculation and distribution
- Multi-currency and multi-language support
- Advanced fraud detection and prevention
- CDN integration for fast global content delivery
- Comprehensive API for third-party integrations

Success Criteria:
- Onboard 1000+ active vendors within first year
- Achieve $10M+ gross merchandise value (GMV)
- Maintain 95% platform uptime
- Process 100,000+ orders monthly`},{id:"fashion-ecommerce",name:"Fashion E-commerce Platform",industry:"E-commerce & Retail",description:"AI-powered fashion e-commerce with virtual try-on and personalized recommendations",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","TensorFlow","PostgreSQL","AWS","AR.js"],features:["Virtual try-on","Style recommendations","Size matching","Social shopping","Inventory management"],businessGoals:["Reduce returns","Increase conversions","Enhance shopping experience"],targetAudience:"Fashion-conscious consumers and clothing retailers",timeline:"12-16 months",budget:"$500K - $1.5M",successMetrics:["30% reduction in returns","25% increase in conversion","4.5+ user rating"],risks:["AR technology adoption","Size accuracy","Fashion trend changes"],template:`Create an innovative fashion e-commerce platform with AI-powered features and virtual try-on capabilities.

Key Requirements:
- AI-powered virtual try-on using augmented reality
- Personalized style recommendations based on preferences
- Advanced size matching and fit prediction
- Social shopping features with style sharing
- Comprehensive inventory management system
- Integration with fashion brands and suppliers
- Mobile-first design with AR capabilities
- Advanced search with visual similarity matching

Technical Specifications:
- Computer vision for virtual try-on and fit analysis
- Machine learning for personalized recommendations
- Augmented reality integration for mobile devices
- Real-time inventory synchronization
- Advanced image processing and optimization
- Social media integration for style sharing
- Progressive web app for mobile experience
- Analytics dashboard for fashion insights

Success Criteria:
- 30% reduction in return rates through better fit
- 25% increase in conversion rates
- 4.5+ average user rating in app stores
- Integration with 100+ fashion brands`},{id:"grocery-delivery-app",name:"Grocery Delivery Platform",industry:"E-commerce & Retail",description:"On-demand grocery delivery platform with real-time tracking and inventory management",projectType:"mobile-application",platform:"mobile",complexity:"intermediate",technologies:["React Native","Node.js","PostgreSQL","Stripe","Google Maps","Firebase"],features:["Product browsing","Real-time tracking","Delivery scheduling","Payment processing","Inventory sync"],businessGoals:["Provide convenient shopping","Optimize delivery routes","Increase customer retention"],targetAudience:"Busy consumers, families, and grocery stores",timeline:"8-12 months",budget:"$250K - $750K",successMetrics:["30-minute delivery","95% on-time delivery","4.8+ app rating"],risks:["Delivery logistics","Inventory accuracy","Driver availability"],template:`Build a comprehensive grocery delivery platform that provides fast and reliable service to customers.

Key Requirements:
- Intuitive product browsing with categories and search
- Real-time inventory synchronization with stores
- Flexible delivery scheduling and time slots
- Live order tracking with GPS integration
- Multiple payment options and secure processing
- Driver management and route optimization
- Customer support and order management
- Loyalty programs and promotional campaigns

Technical Specifications:
- Cross-platform mobile development (iOS/Android)
- Real-time GPS tracking and route optimization
- Integration with grocery store POS systems
- Automated inventory management and updates
- Push notifications for order status updates
- Advanced analytics for demand forecasting
- Driver app with navigation and order management
- Admin dashboard for operations management

Success Criteria:
- Achieve 30-minute average delivery time
- Maintain 95% on-time delivery rate
- 4.8+ average app store rating
- Process 10,000+ orders monthly`},{id:"subscription-box-platform",name:"Subscription Box Service",industry:"E-commerce & Retail",description:"Customizable subscription box platform with curation algorithms and customer preferences",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","Stripe","AWS","Machine Learning"],features:["Subscription management","Product curation","Customer preferences","Billing automation","Analytics"],businessGoals:["Build recurring revenue","Increase customer lifetime value","Personalize experiences"],targetAudience:"Subscription box enthusiasts and niche product consumers",timeline:"6-10 months",budget:"$200K - $600K",successMetrics:["90% retention rate","$100 average LTV","95% satisfaction"],risks:["Customer churn","Inventory management","Shipping costs"],template:`Create a personalized subscription box platform that delivers curated products based on customer preferences.

Key Requirements:
- Flexible subscription management with pause/skip options
- AI-powered product curation based on preferences
- Customer preference profiling and feedback system
- Automated billing and payment processing
- Inventory management with supplier integration
- Shipping and logistics management
- Customer portal for subscription customization
- Analytics dashboard for business insights

Technical Specifications:
- Machine learning algorithms for product recommendations
- Automated subscription billing and dunning management
- Integration with shipping carriers and tracking
- Customer feedback and rating system
- Inventory forecasting and procurement automation
- Mobile-responsive customer portal
- Advanced analytics and cohort analysis
- Integration with e-commerce platforms

Success Criteria:
- Achieve 90% monthly customer retention rate
- $100+ average customer lifetime value
- 95% customer satisfaction rating
- Process 50,000+ subscription boxes monthly`},{id:"b2b-wholesale-platform",name:"B2B Wholesale Marketplace",industry:"E-commerce & Retail",description:"B2B wholesale platform connecting manufacturers with retailers and distributors",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","Stripe","AWS","ERP Integration"],features:["Bulk ordering","Price negotiation","Credit management","Logistics coordination","Analytics"],businessGoals:["Connect B2B buyers and sellers","Streamline wholesale processes","Increase trade volume"],targetAudience:"Manufacturers, wholesalers, retailers, and distributors",timeline:"12-18 months",budget:"$500K - $1.5M",successMetrics:["$50M+ trade volume","500+ active buyers","99% order accuracy"],risks:["Credit risk","Logistics complexity","Market competition"],template:`Build a comprehensive B2B wholesale marketplace that facilitates large-scale trade between businesses.

Key Requirements:
- Advanced product catalog with bulk pricing tiers
- Quote request and negotiation system
- Credit management and payment terms
- Bulk order processing and fulfillment
- Logistics coordination and shipping management
- Supplier verification and quality assurance
- Integration with ERP and accounting systems
- Advanced analytics and market insights

Technical Specifications:
- Enterprise-grade security and compliance
- Integration with major ERP systems (SAP, Oracle)
- Advanced search and filtering for B2B products
- Automated credit checking and approval workflows
- Real-time inventory management across suppliers
- Comprehensive reporting and analytics dashboard
- API integrations for third-party logistics
- Multi-currency and international trade support

Success Criteria:
- Facilitate $50M+ in annual trade volume
- Onboard 500+ active business buyers
- Achieve 99% order accuracy and fulfillment
- Process 10,000+ B2B transactions monthly`},{id:"online-learning-platform",name:"Online Learning Platform",industry:"Education & EdTech",description:"Comprehensive online learning platform with interactive courses and progress tracking",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","AWS","WebRTC","TensorFlow"],features:["Course creation","Video streaming","Interactive assessments","Progress tracking","Certification"],businessGoals:["Democratize education","Scale learning delivery","Improve learning outcomes"],targetAudience:"Students, educators, and educational institutions",timeline:"10-15 months",budget:"$400K - $1.2M",successMetrics:["100K+ active learners","85% course completion","4.7+ rating"],risks:["Content quality","Technology adoption","Competition"],template:`Create a comprehensive online learning platform that delivers engaging educational experiences at scale.

Key Requirements:
- Intuitive course creation tools for educators
- High-quality video streaming with adaptive bitrate
- Interactive assessments and quizzes with instant feedback
- Comprehensive progress tracking and analytics
- Certification and badge system for achievements
- Discussion forums and peer collaboration tools
- Mobile-responsive design with offline capabilities
- Integration with existing educational systems (LTI)

Technical Specifications:
- Scalable video delivery with CDN integration
- Real-time collaboration tools for group projects
- AI-powered content recommendations
- Advanced analytics for learning insights
- Secure payment processing for course purchases
- Multi-language support and accessibility features
- API integrations with educational tools
- Comprehensive admin dashboard for institutions

Success Criteria:
- Onboard 100,000+ active learners
- Achieve 85% average course completion rate
- Maintain 4.7+ average course rating
- Support 1,000+ concurrent video streams`},{id:"student-information-system",name:"Student Information System",industry:"Education & EdTech",description:"Comprehensive SIS for K-12 schools with gradebook, attendance, and parent communication",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","AWS","Twilio","Chart.js"],features:["Student records","Gradebook","Attendance tracking","Parent portal","Reporting"],businessGoals:["Streamline school administration","Improve parent engagement","Enhance student outcomes"],targetAudience:"K-12 schools, teachers, students, and parents",timeline:"12-18 months",budget:"$300K - $900K",successMetrics:["99% data accuracy","90% parent engagement","50% admin time savings"],risks:["Data privacy","System integration","User training"],template:`Build a comprehensive student information system that manages all aspects of K-12 school operations.

Key Requirements:
- Complete student record management with academic history
- Digital gradebook with standards-based grading
- Automated attendance tracking with notifications
- Parent portal with real-time access to student progress
- Comprehensive reporting and analytics dashboard
- Integration with state reporting systems
- Mobile app for teachers and parents
- Secure communication tools between stakeholders

Technical Specifications:
- FERPA-compliant data security and privacy
- Integration with existing school systems (HR, Finance)
- Real-time synchronization across all modules
- Advanced reporting with custom dashboard creation
- Automated notifications via email and SMS
- Role-based access control for different user types
- Backup and disaster recovery systems
- API integrations with educational tools

Success Criteria:
- Achieve 99% data accuracy across all records
- 90% parent engagement through portal usage
- 50% reduction in administrative processing time
- Full compliance with educational data regulations`},{id:"language-learning-app",name:"AI Language Learning App",industry:"Education & EdTech",description:"AI-powered language learning app with speech recognition and personalized curriculum",projectType:"mobile-application",platform:"mobile",complexity:"advanced",technologies:["React Native","Python","TensorFlow","PostgreSQL","AWS","Speech API"],features:["Speech recognition","Adaptive learning","Gamification","Progress tracking","Cultural content"],businessGoals:["Make language learning accessible","Improve learning efficiency","Increase user engagement"],targetAudience:"Language learners of all ages and proficiency levels",timeline:"12-16 months",budget:"$500K - $1.5M",successMetrics:["1M+ downloads","70% retention rate","B2+ proficiency achievement"],risks:["Speech recognition accuracy","Content localization","User motivation"],template:`Develop an AI-powered language learning app that adapts to individual learning styles and pace.

Key Requirements:
- Advanced speech recognition for pronunciation practice
- AI-driven adaptive learning curriculum
- Gamification elements with achievements and streaks
- Comprehensive progress tracking and analytics
- Cultural context and real-world conversation practice
- Offline mode for learning without internet
- Social features for language exchange
- Integration with language proficiency standards (CEFR)

Technical Specifications:
- Machine learning models for personalized learning paths
- Advanced speech processing and pronunciation analysis
- Real-time progress adaptation based on performance
- Gamification engine with rewards and challenges
- Cross-platform mobile development (iOS/Android)
- Offline content synchronization and storage
- Social networking features for peer interaction
- Analytics dashboard for learning insights

Success Criteria:
- Achieve 1M+ app downloads within first year
- Maintain 70% user retention after 30 days
- Help users achieve B2+ proficiency level
- Support 20+ languages with native speaker quality`},{id:"virtual-classroom-platform",name:"Virtual Classroom Platform",industry:"Education & EdTech",description:"Interactive virtual classroom with real-time collaboration and engagement tools",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","WebRTC","Socket.io","PostgreSQL","AWS"],features:["Video conferencing","Screen sharing","Interactive whiteboard","Breakout rooms","Recording"],businessGoals:["Enable remote learning","Increase engagement","Reduce technology barriers"],targetAudience:"Educators, students, and educational institutions",timeline:"8-12 months",budget:"$300K - $900K",successMetrics:["500+ concurrent users","95% uptime","4.5+ user satisfaction"],risks:["Bandwidth limitations","Technology adoption","Security concerns"],template:`Create an interactive virtual classroom platform that replicates and enhances in-person learning experiences.

Key Requirements:
- High-quality video conferencing with screen sharing
- Interactive whiteboard with real-time collaboration
- Breakout rooms for small group activities
- Session recording and playback capabilities
- Chat and messaging with moderation tools
- Attendance tracking and engagement analytics
- Integration with learning management systems
- Mobile support for tablets and smartphones

Technical Specifications:
- WebRTC for peer-to-peer video communication
- Real-time collaboration using WebSocket connections
- Scalable architecture supporting 500+ concurrent users
- Advanced audio/video processing and optimization
- Cloud recording with automatic transcription
- Comprehensive security and privacy controls
- API integrations with popular LMS platforms
- Responsive design for multiple device types

Success Criteria:
- Support 500+ concurrent users per session
- Maintain 95% platform uptime during peak hours
- Achieve 4.5+ user satisfaction rating
- Process 10,000+ virtual classroom sessions monthly`},{id:"skill-assessment-platform",name:"Skills Assessment Platform",industry:"Education & EdTech",description:"AI-powered skills assessment platform with adaptive testing and competency mapping",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Python","TensorFlow","PostgreSQL","AWS","Proctoring API"],features:["Adaptive testing","Skill mapping","Proctoring","Analytics","Certification"],businessGoals:["Validate skills accurately","Reduce assessment time","Provide actionable insights"],targetAudience:"Educational institutions, employers, and certification bodies",timeline:"10-14 months",budget:"$400K - $1.2M",successMetrics:["95% assessment accuracy","50% time reduction","90% user satisfaction"],risks:["Cheating prevention","Algorithm bias","Technical complexity"],template:`Build an AI-powered skills assessment platform that accurately measures competencies and provides actionable insights.

Key Requirements:
- Adaptive testing algorithms that adjust difficulty in real-time
- Comprehensive skill mapping and competency frameworks
- Advanced proctoring with AI-powered monitoring
- Detailed analytics and performance insights
- Automated certification and badge generation
- Integration with HR systems and job platforms
- Multi-format questions (multiple choice, coding, simulation)
- Accessibility features for diverse learners

Technical Specifications:
- Machine learning models for adaptive question selection
- Computer vision for proctoring and identity verification
- Advanced analytics engine for skill gap analysis
- Secure test delivery with anti-cheating measures
- Real-time performance monitoring and alerts
- Integration APIs for third-party systems
- Comprehensive reporting and dashboard tools
- Multi-language support and localization

Success Criteria:
- Achieve 95% assessment accuracy compared to expert evaluation
- Reduce assessment time by 50% through adaptive testing
- Maintain 90% user satisfaction rating
- Process 100,000+ assessments annually`},{id:"real-estate-marketplace",name:"Real Estate Marketplace",industry:"Real Estate & PropTech",description:"Comprehensive real estate marketplace with virtual tours and AI-powered property matching",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","AWS","Google Maps","Three.js"],features:["Property listings","Virtual tours","Search filters","Agent profiles","Market analytics"],businessGoals:["Connect buyers and sellers","Streamline property discovery","Provide market insights"],targetAudience:"Home buyers, sellers, real estate agents, and investors",timeline:"10-15 months",budget:"$400K - $1.2M",successMetrics:["100K+ property listings","1M+ monthly visitors","15% conversion rate"],risks:["Data accuracy","Market competition","Technology adoption"],template:`Create a comprehensive real estate marketplace that revolutionizes property discovery and transactions.

Key Requirements:
- Advanced property search with AI-powered matching
- Immersive virtual tours and 360-degree photography
- Comprehensive property details with market analytics
- Agent profiles with ratings and transaction history
- Mortgage calculator and financing options
- Neighborhood insights and demographic data
- Mobile app for property viewing and notifications
- Integration with MLS and real estate databases

Technical Specifications:
- AI algorithms for property recommendation and matching
- 3D virtual tour technology with WebGL/Three.js
- Advanced mapping with Google Maps integration
- Real-time property data synchronization
- Image optimization and CDN for fast loading
- Advanced search with filters and sorting options
- Mobile-responsive design with native app features
- Analytics dashboard for market trends and insights

Success Criteria:
- List 100,000+ active properties across major markets
- Achieve 1M+ monthly unique visitors
- Maintain 15% lead-to-sale conversion rate
- Support virtual tours for 80% of listings`},{id:"property-investment-platform",name:"Property Investment Platform",industry:"Real Estate & PropTech",description:"Real estate investment platform with crowdfunding and portfolio management tools",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","Stripe","AWS","TensorFlow"],features:["Investment opportunities","Portfolio tracking","Due diligence","Returns calculation","Investor dashboard"],businessGoals:["Democratize real estate investing","Provide passive income opportunities","Reduce investment barriers"],targetAudience:"Individual investors, accredited investors, and real estate sponsors",timeline:"12-18 months",budget:"$500K - $1.5M",successMetrics:["$100M+ invested","12% average returns","95% investor satisfaction"],risks:["Regulatory compliance","Market volatility","Due diligence accuracy"],template:`Build a comprehensive real estate investment platform that enables fractional ownership and passive investing.

Key Requirements:
- Curated investment opportunities with detailed analysis
- Fractional ownership and crowdfunding capabilities
- Comprehensive due diligence and property evaluation
- Real-time portfolio tracking and performance analytics
- Automated distribution of rental income and profits
- Investor education and market insights
- Regulatory compliance and investor accreditation
- Mobile app for investment monitoring

Technical Specifications:
- SEC-compliant investment processing and documentation
- AI-powered property valuation and risk assessment
- Automated distribution and tax reporting systems
- Real-time portfolio performance tracking
- Integration with property management systems
- Advanced analytics for investment insights
- Secure document storage and e-signature capabilities
- Comprehensive investor dashboard and reporting

Success Criteria:
- Facilitate $100M+ in real estate investments
- Achieve 12% average annual returns for investors
- Maintain 95% investor satisfaction rating
- Process 1,000+ investment transactions annually`},{id:"smart-building-management",name:"Smart Building Management System",industry:"Real Estate & PropTech",description:"IoT-enabled building management platform with energy optimization and predictive maintenance",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","AWS IoT","TensorFlow","MQTT"],features:["IoT monitoring","Energy optimization","Predictive maintenance","Tenant portal","Analytics dashboard"],businessGoals:["Reduce operating costs","Improve tenant satisfaction","Optimize energy usage"],targetAudience:"Property managers, building owners, and commercial tenants",timeline:"12-16 months",budget:"$600K - $1.8M",successMetrics:["30% energy savings","50% maintenance cost reduction","95% tenant satisfaction"],risks:["IoT integration complexity","Data security","Hardware compatibility"],template:`Develop a smart building management system that optimizes operations through IoT and AI technologies.

Key Requirements:
- Comprehensive IoT sensor integration for monitoring
- AI-powered energy optimization and demand management
- Predictive maintenance with equipment health monitoring
- Tenant portal for service requests and building information
- Real-time analytics dashboard for building performance
- Integration with existing building automation systems
- Mobile app for facility managers and maintenance teams
- Advanced reporting and compliance tools

Technical Specifications:
- IoT device management with MQTT protocol
- Machine learning models for predictive analytics
- Real-time data processing and alerting systems
- Integration with HVAC, lighting, and security systems
- Cloud-based architecture with edge computing
- Advanced visualization and dashboard tools
- API integrations with third-party building systems
- Comprehensive security and access control

Success Criteria:
- Achieve 30% reduction in energy consumption
- Reduce maintenance costs by 50% through predictive analytics
- Maintain 95% tenant satisfaction rating
- Monitor 10,000+ IoT devices across multiple buildings`},{id:"rental-management-platform",name:"Rental Property Management",industry:"Real Estate & PropTech",description:"Comprehensive rental property management platform with tenant screening and maintenance tracking",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","Stripe","Twilio","AWS"],features:["Property listings","Tenant screening","Rent collection","Maintenance requests","Financial reporting"],businessGoals:["Streamline property management","Improve tenant relations","Maximize rental income"],targetAudience:"Property managers, landlords, and tenants",timeline:"8-12 months",budget:"$250K - $750K",successMetrics:["95% rent collection","24-hour maintenance response","90% tenant retention"],risks:["Tenant screening accuracy","Payment processing issues","Maintenance coordination"],template:`Create a comprehensive rental property management platform that automates operations and improves tenant experiences.

Key Requirements:
- Online property listings with virtual tours
- Automated tenant screening with credit and background checks
- Digital lease signing and document management
- Automated rent collection with late fee processing
- Maintenance request system with vendor coordination
- Financial reporting and expense tracking
- Tenant portal for payments and communication
- Mobile app for property managers and tenants

Technical Specifications:
- Integration with credit reporting agencies
- Automated payment processing with ACH and credit cards
- Document management with e-signature capabilities
- Real-time communication tools for tenants and managers
- Advanced reporting and analytics dashboard
- Integration with accounting systems (QuickBooks, etc.)
- Mobile-responsive design with native app features
- Comprehensive security and data protection

Success Criteria:
- Achieve 95% on-time rent collection rate
- Respond to maintenance requests within 24 hours
- Maintain 90% tenant retention rate
- Manage 10,000+ rental units across multiple properties`},{id:"construction-project-management",name:"Construction Project Management",industry:"Real Estate & PropTech",description:"Digital construction management platform with project tracking and collaboration tools",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","AWS","AutoCAD API","Drone API"],features:["Project planning","Progress tracking","Document management","Team collaboration","Budget monitoring"],businessGoals:["Improve project efficiency","Reduce construction delays","Enhance collaboration"],targetAudience:"Construction companies, project managers, and contractors",timeline:"10-14 months",budget:"$400K - $1.2M",successMetrics:["20% faster completion","15% cost savings","95% on-time delivery"],risks:["Project complexity","Team adoption","Integration challenges"],template:`Build a comprehensive construction project management platform that streamlines workflows and improves collaboration.

Key Requirements:
- Comprehensive project planning with Gantt charts and timelines
- Real-time progress tracking with photo documentation
- Document management with version control and approvals
- Team collaboration tools with role-based access
- Budget monitoring and cost tracking with alerts
- Integration with CAD software and building plans
- Mobile app for field workers and site managers
- Reporting and analytics for project insights

Technical Specifications:
- Integration with AutoCAD and BIM software
- Real-time collaboration with WebSocket connections
- Document versioning and approval workflows
- Mobile-first design for field use
- Advanced project analytics and reporting
- Integration with accounting and ERP systems
- Drone integration for aerial progress monitoring
- Comprehensive security and access control

Success Criteria:
- Achieve 20% faster project completion times
- Reduce project costs by 15% through better planning
- Maintain 95% on-time project delivery rate
- Manage 500+ concurrent construction projects`},{id:"smart-factory-platform",name:"Smart Factory Management Platform",industry:"Manufacturing & IoT",description:"IoT-enabled smart factory platform with predictive maintenance and production optimization",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","AWS IoT","TensorFlow","InfluxDB"],features:["Production monitoring","Predictive maintenance","Quality control","Inventory management","Analytics dashboard"],businessGoals:["Increase production efficiency","Reduce downtime","Improve product quality"],targetAudience:"Manufacturing companies, plant managers, and operations teams",timeline:"12-18 months",budget:"$600K - $2M",successMetrics:["25% efficiency increase","40% downtime reduction","99.5% quality rate"],risks:["IoT integration complexity","Legacy system compatibility","Data security"],template:`Build a comprehensive smart factory platform that leverages IoT and AI to optimize manufacturing operations.

Key Requirements:
- Real-time production monitoring with IoT sensors
- AI-powered predictive maintenance for equipment
- Automated quality control with computer vision
- Intelligent inventory management and supply chain optimization
- Energy consumption monitoring and optimization
- Worker safety monitoring and alert systems
- Integration with existing ERP and MES systems
- Advanced analytics dashboard for operational insights

Technical Specifications:
- IoT device management with industrial protocols (OPC-UA, Modbus)
- Machine learning models for predictive analytics
- Time-series database for sensor data storage
- Real-time data processing and alerting systems
- Computer vision for quality inspection
- Edge computing for low-latency processing
- Comprehensive security for industrial networks
- API integrations with manufacturing systems

Success Criteria:
- Achieve 25% increase in production efficiency
- Reduce unplanned downtime by 40%
- Maintain 99.5% product quality rate
- Monitor 10,000+ IoT devices across production lines`},{id:"supply-chain-optimization",name:"Supply Chain Optimization Platform",industry:"Manufacturing & IoT",description:"AI-powered supply chain platform with demand forecasting and logistics optimization",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Python","TensorFlow","PostgreSQL","AWS","Blockchain"],features:["Demand forecasting","Inventory optimization","Supplier management","Logistics tracking","Risk assessment"],businessGoals:["Optimize inventory levels","Reduce supply chain costs","Improve delivery times"],targetAudience:"Supply chain managers, procurement teams, and logistics coordinators",timeline:"10-15 months",budget:"$500K - $1.5M",successMetrics:["30% inventory reduction","20% cost savings","95% on-time delivery"],risks:["Demand volatility","Supplier reliability","Integration complexity"],template:`Create an AI-powered supply chain optimization platform that enhances efficiency and reduces costs.

Key Requirements:
- Advanced demand forecasting using machine learning
- Intelligent inventory optimization with safety stock calculations
- Comprehensive supplier management and performance tracking
- Real-time logistics tracking and route optimization
- Risk assessment and mitigation strategies
- Blockchain integration for supply chain transparency
- Integration with ERP and procurement systems
- Advanced analytics and reporting dashboard

Technical Specifications:
- Machine learning models for demand prediction
- Optimization algorithms for inventory and logistics
- Real-time tracking with GPS and IoT integration
- Blockchain for supply chain traceability
- API integrations with suppliers and logistics providers
- Advanced analytics and visualization tools
- Mobile app for field operations and tracking
- Comprehensive security and data protection

Success Criteria:
- Reduce inventory holding costs by 30%
- Achieve 20% overall supply chain cost savings
- Maintain 95% on-time delivery performance
- Process 100,000+ supply chain transactions monthly`},{id:"industrial-iot-platform",name:"Industrial IoT Management Platform",industry:"Manufacturing & IoT",description:"Comprehensive IIoT platform for device management, data analytics, and remote monitoring",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","InfluxDB","AWS IoT","TensorFlow","MQTT"],features:["Device management","Data visualization","Remote monitoring","Predictive analytics","Alert systems"],businessGoals:["Enable digital transformation","Improve operational visibility","Reduce maintenance costs"],targetAudience:"Industrial companies, IoT engineers, and operations managers",timeline:"12-16 months",budget:"$500K - $1.5M",successMetrics:["50K+ connected devices","99.9% uptime","60% maintenance savings"],risks:["Device compatibility","Network reliability","Data security"],template:`Develop a comprehensive Industrial IoT platform that connects, monitors, and optimizes industrial operations.

Key Requirements:
- Scalable device management for thousands of IoT sensors
- Real-time data visualization with customizable dashboards
- Remote monitoring and control capabilities
- Predictive analytics for equipment health and performance
- Automated alert and notification systems
- Edge computing for low-latency processing
- Integration with existing industrial systems
- Comprehensive security and access control

Technical Specifications:
- Support for multiple IoT protocols (MQTT, CoAP, OPC-UA)
- Time-series database for efficient data storage
- Real-time data streaming and processing
- Machine learning models for predictive maintenance
- Edge computing deployment for critical applications
- Advanced visualization with 3D plant models
- API integrations for third-party systems
- Enterprise-grade security and compliance

Success Criteria:
- Connect and manage 50,000+ IoT devices
- Achieve 99.9% platform uptime and reliability
- Reduce maintenance costs by 60% through predictive analytics
- Process 1M+ sensor readings per minute`},{id:"quality-management-system",name:"Digital Quality Management System",industry:"Manufacturing & IoT",description:"Comprehensive QMS with automated inspections, compliance tracking, and corrective actions",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","AWS","Computer Vision","OCR"],features:["Quality inspections","Compliance tracking","Corrective actions","Document control","Audit management"],businessGoals:["Ensure product quality","Maintain compliance","Reduce defects"],targetAudience:"Quality managers, inspectors, and compliance teams",timeline:"8-12 months",budget:"$300K - $900K",successMetrics:["99% compliance rate","50% defect reduction","90% audit success"],risks:["Regulatory changes","Process complexity","User adoption"],template:`Build a comprehensive digital quality management system that ensures compliance and continuous improvement.

Key Requirements:
- Digital quality inspection workflows with mobile support
- Automated compliance tracking and reporting
- Corrective and preventive action (CAPA) management
- Document control with version management
- Audit management and preparation tools
- Statistical process control (SPC) with real-time monitoring
- Integration with manufacturing execution systems
- Training management and competency tracking

Technical Specifications:
- Computer vision for automated quality inspections
- OCR technology for document digitization
- Real-time statistical analysis and control charts
- Workflow automation for quality processes
- Integration with ERP and manufacturing systems
- Mobile app for field inspections and data collection
- Advanced reporting and analytics dashboard
- Compliance templates for industry standards (ISO, FDA)

Success Criteria:
- Achieve 99% regulatory compliance rate
- Reduce product defects by 50%
- Pass 90% of external audits on first attempt
- Process 10,000+ quality inspections monthly`},{id:"asset-tracking-system",name:"Industrial Asset Tracking System",industry:"Manufacturing & IoT",description:"RFID/IoT-enabled asset tracking platform with maintenance scheduling and lifecycle management",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","AWS","RFID","GPS"],features:["Asset tracking","Maintenance scheduling","Lifecycle management","Location monitoring","Reporting"],businessGoals:["Improve asset utilization","Reduce asset loss","Optimize maintenance"],targetAudience:"Asset managers, maintenance teams, and operations staff",timeline:"6-10 months",budget:"$200K - $600K",successMetrics:["99% asset visibility","30% utilization increase","25% maintenance savings"],risks:["RFID implementation","Data accuracy","System integration"],template:`Create a comprehensive asset tracking system that provides real-time visibility and optimizes asset management.

Key Requirements:
- Real-time asset tracking with RFID and GPS technology
- Comprehensive asset database with specifications and history
- Automated maintenance scheduling based on usage and time
- Asset lifecycle management from procurement to disposal
- Location monitoring and geofencing capabilities
- Mobile app for asset scanning and updates
- Integration with ERP and maintenance systems
- Advanced reporting and analytics dashboard

Technical Specifications:
- RFID reader integration for automated tracking
- GPS tracking for mobile and outdoor assets
- Barcode and QR code scanning capabilities
- Real-time location updates and alerts
- Integration with CMMS and ERP systems
- Mobile app for field asset management
- Advanced analytics for asset optimization
- Comprehensive security and access control

Success Criteria:
- Achieve 99% asset visibility and tracking accuracy
- Increase asset utilization by 30%
- Reduce maintenance costs by 25%
- Track 100,000+ assets across multiple facilities`},{id:"streaming-platform",name:"Video Streaming Platform",industry:"Media & Entertainment",description:"Scalable video streaming platform with content management and personalized recommendations",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","AWS","CDN","TensorFlow","FFmpeg"],features:["Video streaming","Content management","User profiles","Recommendations","Analytics"],businessGoals:["Deliver high-quality streaming","Increase user engagement","Monetize content"],targetAudience:"Content creators, viewers, and media companies",timeline:"12-18 months",budget:"$600K - $2M",successMetrics:["1M+ concurrent streams","80% user retention","4K streaming quality"],risks:["Bandwidth costs","Content licensing","Competition"],template:`Build a scalable video streaming platform that delivers high-quality content with personalized experiences.

Key Requirements:
- Adaptive bitrate streaming for optimal quality
- Comprehensive content management system
- User profiles with viewing history and preferences
- AI-powered content recommendations
- Multi-device support (web, mobile, TV, gaming consoles)
- Live streaming capabilities with real-time chat
- Content protection and DRM integration
- Advanced analytics and viewer insights

Technical Specifications:
- CDN integration for global content delivery
- Video transcoding and optimization pipeline
- Machine learning for personalized recommendations
- Real-time streaming with low latency
- Scalable architecture supporting millions of users
- Advanced video player with adaptive streaming
- Content protection with digital rights management
- Comprehensive analytics and reporting dashboard

Success Criteria:
- Support 1M+ concurrent video streams
- Achieve 80% user retention after 30 days
- Deliver 4K streaming quality with minimal buffering
- Process 100TB+ of video content monthly`},{id:"music-streaming-app",name:"Music Streaming Application",industry:"Media & Entertainment",description:"AI-powered music streaming platform with social features and artist collaboration tools",projectType:"mobile-application",platform:"mobile",complexity:"advanced",technologies:["React Native","Node.js","PostgreSQL","AWS","TensorFlow","Spotify API"],features:["Music streaming","Playlist creation","Social sharing","Artist profiles","Discovery"],businessGoals:["Build music community","Support artists","Increase user engagement"],targetAudience:"Music lovers, artists, and content creators",timeline:"10-15 months",budget:"$400K - $1.2M",successMetrics:["10M+ songs catalog","5M+ active users","90% user satisfaction"],risks:["Music licensing","Artist acquisition","Platform competition"],template:`Create an innovative music streaming platform that connects artists with fans and builds music communities.

Key Requirements:
- High-quality audio streaming with offline capabilities
- AI-powered music discovery and recommendations
- Social features with playlist sharing and collaboration
- Artist profiles with direct fan engagement
- Podcast and audio content support
- Live streaming for concerts and events
- Music creation tools and collaboration features
- Advanced search and music discovery

Technical Specifications:
- High-quality audio streaming with lossless options
- Machine learning for music recommendation algorithms
- Real-time social features and messaging
- Integration with music distribution platforms
- Cross-platform mobile development (iOS/Android)
- Offline music storage and synchronization
- Advanced audio processing and equalization
- Comprehensive analytics for artists and labels

Success Criteria:
- Build catalog of 10M+ licensed songs
- Achieve 5M+ monthly active users
- Maintain 90% user satisfaction rating
- Support 100,000+ independent artists`},{id:"content-creation-platform",name:"Content Creation Platform",industry:"Media & Entertainment",description:"All-in-one content creation platform with editing tools and collaboration features",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","WebGL","FFmpeg","AWS","WebRTC"],features:["Video editing","Audio editing","Collaboration tools","Asset library","Publishing"],businessGoals:["Democratize content creation","Enable collaboration","Streamline workflows"],targetAudience:"Content creators, video editors, and creative teams",timeline:"12-16 months",budget:"$500K - $1.5M",successMetrics:["1M+ projects created","500K+ active creators","4.8+ user rating"],risks:["Performance optimization","Browser compatibility","Feature complexity"],template:`Build a comprehensive content creation platform that empowers creators with professional-grade tools.

Key Requirements:
- Browser-based video and audio editing with timeline interface
- Real-time collaboration with multiple editors
- Comprehensive asset library with stock media
- AI-powered editing assistance and automation
- Multi-format export and publishing options
- Cloud storage with version control
- Template library for quick content creation
- Integration with social media platforms

Technical Specifications:
- WebGL-based video rendering and effects
- Real-time collaboration using WebRTC
- Cloud-based media processing and storage
- AI algorithms for automated editing suggestions
- Progressive web app for offline editing
- Advanced timeline interface with precision controls
- Multi-format media support and conversion
- Comprehensive project management and sharing

Success Criteria:
- Enable creation of 1M+ video projects
- Onboard 500,000+ active content creators
- Achieve 4.8+ average user rating
- Process 10PB+ of media content annually`},{id:"podcast-platform",name:"Podcast Hosting Platform",industry:"Media & Entertainment",description:"Complete podcast hosting and distribution platform with analytics and monetization tools",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","AWS","Stripe","RSS"],features:["Podcast hosting","Distribution","Analytics","Monetization","Audience engagement"],businessGoals:["Support podcast creators","Enable monetization","Grow podcast ecosystem"],targetAudience:"Podcast creators, listeners, and media companies",timeline:"8-12 months",budget:"$250K - $750K",successMetrics:["100K+ podcasts hosted","10M+ downloads monthly","95% uptime"],risks:["Content moderation","Bandwidth costs","Platform competition"],template:`Create a comprehensive podcast platform that supports creators from recording to monetization.

Key Requirements:
- Easy podcast upload and hosting with unlimited storage
- Automatic distribution to major podcast platforms
- Comprehensive analytics with listener demographics
- Monetization tools including ads and subscriptions
- Audience engagement features with comments and ratings
- Recording and editing tools for content creation
- RSS feed management and customization
- Mobile app for podcast management and listening

Technical Specifications:
- Scalable audio hosting with global CDN
- Automated distribution to Apple Podcasts, Spotify, etc.
- Advanced analytics with real-time listener tracking
- Payment processing for subscriptions and donations
- Audio processing and optimization pipeline
- RSS feed generation and management
- Mobile-responsive design with native app features
- Comprehensive creator dashboard and tools

Success Criteria:
- Host 100,000+ active podcasts
- Deliver 10M+ podcast downloads monthly
- Maintain 95% platform uptime
- Generate $1M+ in creator revenue annually`},{id:"live-streaming-platform",name:"Live Streaming Platform",industry:"Media & Entertainment",description:"Interactive live streaming platform with real-time chat and monetization features",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","WebRTC","Socket.io","AWS","Stripe"],features:["Live streaming","Real-time chat","Virtual gifts","Subscriptions","Analytics"],businessGoals:["Enable live content creation","Build creator economy","Increase engagement"],targetAudience:"Live streamers, content creators, and viewers",timeline:"10-14 months",budget:"$400K - $1.2M",successMetrics:["100K+ concurrent viewers","50K+ active streamers","$5M+ creator earnings"],risks:["Streaming quality","Content moderation","Monetization balance"],template:`Build an interactive live streaming platform that empowers creators and engages audiences in real-time.

Key Requirements:
- High-quality live video streaming with low latency
- Real-time chat with moderation tools
- Virtual gifts and tipping system for monetization
- Subscription and membership features
- Stream recording and highlight creation
- Multi-platform streaming (simultaneous broadcast)
- Creator dashboard with analytics and earnings
- Mobile app for streaming and viewing

Technical Specifications:
- WebRTC for low-latency live streaming
- Real-time messaging with Socket.io
- Scalable architecture supporting 100K+ concurrent viewers
- Payment processing for virtual gifts and subscriptions
- Content delivery network for global streaming
- Advanced moderation tools and AI content filtering
- Mobile streaming with camera and screen capture
- Comprehensive analytics and revenue tracking

Success Criteria:
- Support 100,000+ concurrent viewers during peak times
- Onboard 50,000+ active streamers
- Generate $5M+ in creator earnings annually
- Achieve sub-3 second streaming latency`},{id:"hotel-booking-platform",name:"Hotel Booking Platform",industry:"Travel & Hospitality",description:"Comprehensive hotel booking platform with real-time availability and dynamic pricing",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","Stripe","Google Maps","Redis"],features:["Hotel search","Real-time booking","Dynamic pricing","Reviews","Mobile app"],businessGoals:["Increase bookings","Optimize pricing","Improve guest experience"],targetAudience:"Travelers, hotels, and hospitality businesses",timeline:"10-15 months",budget:"$400K - $1.2M",successMetrics:["1M+ bookings annually","95% booking accuracy","4.5+ user rating"],risks:["Inventory management","Payment processing","Competition"],template:`Create a comprehensive hotel booking platform that connects travelers with accommodations worldwide.

Key Requirements:
- Advanced hotel search with filters and map integration
- Real-time availability and instant booking confirmation
- Dynamic pricing based on demand and seasonality
- Comprehensive hotel profiles with photos and amenities
- Guest review and rating system
- Multi-currency and multi-language support
- Mobile app for booking management and check-in
- Integration with hotel management systems

Technical Specifications:
- Real-time inventory management with Redis caching
- Payment processing with multiple gateways
- Advanced search with Elasticsearch
- Map integration with Google Maps API
- Mobile-responsive design with PWA capabilities
- Integration with hotel PMS and channel managers
- Advanced analytics and revenue optimization
- Comprehensive security and fraud prevention

Success Criteria:
- Process 1M+ hotel bookings annually
- Achieve 95% booking accuracy and confirmation
- Maintain 4.5+ average user rating
- Partner with 100,000+ hotels globally`},{id:"travel-planning-app",name:"AI Travel Planning App",industry:"Travel & Hospitality",description:"AI-powered travel planning app with personalized itineraries and local recommendations",projectType:"mobile-application",platform:"mobile",complexity:"advanced",technologies:["React Native","Python","TensorFlow","PostgreSQL","Google Maps","AWS"],features:["Itinerary planning","Local recommendations","Budget tracking","Social sharing","Offline maps"],businessGoals:["Personalize travel experiences","Increase user engagement","Monetize recommendations"],targetAudience:"Travelers, tourists, and travel enthusiasts",timeline:"8-12 months",budget:"$300K - $900K",successMetrics:["5M+ app downloads","80% trip completion","4.7+ app rating"],risks:["Data accuracy","Local content quality","User adoption"],template:`Build an AI-powered travel planning app that creates personalized itineraries and enhances travel experiences.

Key Requirements:
- AI-powered itinerary generation based on preferences
- Local recommendations for restaurants, attractions, and activities
- Budget tracking and expense management
- Social features for trip sharing and collaboration
- Offline maps and navigation capabilities
- Real-time travel updates and notifications
- Integration with booking platforms and services
- Photo sharing and travel journal features

Technical Specifications:
- Machine learning for personalized recommendations
- Integration with travel APIs (flights, hotels, activities)
- Offline map storage and GPS navigation
- Real-time data synchronization across devices
- Social networking features with privacy controls
- Advanced analytics for travel insights
- Cross-platform mobile development (iOS/Android)
- Comprehensive travel database and content management

Success Criteria:
- Achieve 5M+ app downloads within first year
- 80% of planned trips completed using the app
- Maintain 4.7+ average app store rating
- Generate 1M+ personalized itineraries`},{id:"restaurant-management-system",name:"Restaurant Management System",industry:"Food & Restaurant",description:"Complete restaurant management platform with POS, inventory, and staff scheduling",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","Stripe","Twilio","AWS"],features:["POS system","Inventory management","Staff scheduling","Customer management","Analytics"],businessGoals:["Streamline operations","Reduce costs","Improve customer service"],targetAudience:"Restaurant owners, managers, and staff",timeline:"8-12 months",budget:"$250K - $750K",successMetrics:["30% cost reduction","95% order accuracy","90% staff satisfaction"],risks:["Hardware integration","Staff training","System reliability"],template:`Create a comprehensive restaurant management system that optimizes operations and enhances customer experiences.

Key Requirements:
- Point-of-sale system with order management
- Real-time inventory tracking and automatic reordering
- Staff scheduling and time tracking
- Customer relationship management with loyalty programs
- Table reservation and waitlist management
- Kitchen display system for order coordination
- Financial reporting and analytics dashboard
- Mobile app for staff and customer interactions

Technical Specifications:
- Integration with payment processors and hardware
- Real-time order synchronization across devices
- Inventory management with supplier integration
- Staff scheduling with labor cost optimization
- Customer data management with privacy compliance
- Kitchen workflow optimization and timing
- Advanced reporting and business intelligence
- Mobile-responsive design with tablet support

Success Criteria:
- Reduce operational costs by 30%
- Achieve 95% order accuracy and customer satisfaction
- Improve staff satisfaction to 90%
- Process 100,000+ orders monthly`},{id:"food-delivery-platform",name:"Food Delivery Platform",industry:"Food & Restaurant",description:"Multi-restaurant food delivery platform with real-time tracking and driver management",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","Stripe","Google Maps","Socket.io"],features:["Restaurant listings","Order management","Real-time tracking","Driver coordination","Payment processing"],businessGoals:["Connect restaurants with customers","Optimize delivery routes","Increase order volume"],targetAudience:"Restaurants, customers, and delivery drivers",timeline:"10-15 months",budget:"$400K - $1.2M",successMetrics:["30-minute delivery","95% on-time delivery","1M+ orders monthly"],risks:["Driver availability","Delivery logistics","Restaurant partnerships"],template:`Build a comprehensive food delivery platform that connects restaurants, customers, and drivers efficiently.

Key Requirements:
- Restaurant onboarding and menu management
- Customer app with search, ordering, and payment
- Real-time order tracking with GPS integration
- Driver app with route optimization and earnings tracking
- Restaurant dashboard for order management
- Dynamic pricing and delivery fee calculation
- Customer support and dispute resolution
- Analytics dashboard for all stakeholders

Technical Specifications:
- Real-time order processing and status updates
- GPS tracking and route optimization algorithms
- Payment processing with split payments to restaurants
- Push notifications for order status updates
- Machine learning for delivery time estimation
- Integration with restaurant POS systems
- Advanced analytics and reporting dashboard
- Scalable architecture for high order volume

Success Criteria:
- Achieve 30-minute average delivery time
- Maintain 95% on-time delivery rate
- Process 1M+ food orders monthly
- Partner with 10,000+ restaurants`},{id:"recipe-sharing-platform",name:"Recipe Sharing Community",industry:"Food & Restaurant",description:"Social recipe sharing platform with meal planning and grocery integration",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","AWS","Stripe","Computer Vision"],features:["Recipe sharing","Meal planning","Grocery lists","Social features","Nutrition tracking"],businessGoals:["Build cooking community","Monetize through partnerships","Promote healthy eating"],targetAudience:"Home cooks, food enthusiasts, and health-conscious individuals",timeline:"6-10 months",budget:"$200K - $600K",successMetrics:["1M+ registered users","100K+ recipes shared","4.6+ user rating"],risks:["Content quality","User engagement","Monetization strategy"],template:`Create a vibrant recipe sharing community that helps people discover, plan, and cook delicious meals.

Key Requirements:
- Recipe creation and sharing with photo uploads
- Advanced search and filtering by ingredients, diet, cuisine
- Meal planning calendar with automated grocery lists
- Social features with following, likes, and comments
- Nutrition tracking and dietary restriction support
- Integration with grocery delivery services
- Video recipe tutorials and cooking tips
- Personal recipe collections and favorites

Technical Specifications:
- Image recognition for recipe ingredient detection
- Advanced search with ingredient-based filtering
- Social networking features with user profiles
- Integration with grocery APIs for shopping lists
- Nutrition calculation and dietary analysis
- Video streaming for cooking tutorials
- Mobile-responsive design with PWA capabilities
- Content moderation and quality control systems

Success Criteria:
- Build community of 1M+ registered users
- Facilitate sharing of 100,000+ unique recipes
- Achieve 4.6+ average user rating
- Generate 10M+ monthly recipe views`},{id:"fleet-management-system",name:"Fleet Management System",industry:"Logistics & Supply Chain",description:"Comprehensive fleet management platform with GPS tracking and maintenance scheduling",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","AWS","GPS API","IoT"],features:["Vehicle tracking","Route optimization","Maintenance scheduling","Driver management","Fuel monitoring"],businessGoals:["Optimize fleet operations","Reduce fuel costs","Improve safety"],targetAudience:"Fleet managers, logistics companies, and transportation businesses",timeline:"10-14 months",budget:"$400K - $1.2M",successMetrics:["25% fuel savings","30% route optimization","99% vehicle uptime"],risks:["GPS accuracy","Driver adoption","Hardware integration"],template:`Build a comprehensive fleet management system that optimizes vehicle operations and reduces costs.

Key Requirements:
- Real-time GPS tracking and vehicle monitoring
- Route optimization and traffic-aware navigation
- Preventive maintenance scheduling and alerts
- Driver behavior monitoring and safety scoring
- Fuel consumption tracking and cost analysis
- Electronic logging device (ELD) compliance
- Mobile app for drivers and field operations
- Advanced analytics and reporting dashboard

Technical Specifications:
- Integration with GPS and telematics devices
- Real-time data processing and alerts
- Machine learning for route optimization
- IoT integration for vehicle diagnostics
- Compliance reporting for transportation regulations
- Mobile app with offline capabilities
- Advanced analytics and predictive maintenance
- Comprehensive security and data protection

Success Criteria:
- Achieve 25% reduction in fuel costs
- Improve route efficiency by 30%
- Maintain 99% vehicle uptime through predictive maintenance
- Manage 10,000+ vehicles across multiple fleets`},{id:"warehouse-management-system",name:"Warehouse Management System",industry:"Logistics & Supply Chain",description:"Advanced WMS with automated inventory tracking and order fulfillment optimization",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","AWS","RFID","Barcode"],features:["Inventory tracking","Order fulfillment","Warehouse optimization","Staff management","Reporting"],businessGoals:["Optimize warehouse operations","Reduce fulfillment time","Improve accuracy"],targetAudience:"Warehouse managers, logistics coordinators, and fulfillment centers",timeline:"8-12 months",budget:"$300K - $900K",successMetrics:["99.5% inventory accuracy","50% faster fulfillment","30% space optimization"],risks:["System integration","Staff training","Inventory complexity"],template:`Create an advanced warehouse management system that automates operations and optimizes fulfillment processes.

Key Requirements:
- Real-time inventory tracking with RFID and barcode scanning
- Automated order picking and fulfillment workflows
- Warehouse layout optimization and slotting
- Staff task management and productivity tracking
- Integration with ERP and e-commerce platforms
- Returns processing and quality control
- Mobile devices for warehouse operations
- Advanced reporting and analytics dashboard

Technical Specifications:
- Integration with barcode and RFID systems
- Real-time inventory synchronization
- Automated workflow orchestration
- Mobile app for warehouse staff
- Integration with shipping carriers and systems
- Advanced analytics for warehouse optimization
- API integrations with e-commerce platforms
- Comprehensive security and access control

Success Criteria:
- Achieve 99.5% inventory accuracy
- Reduce order fulfillment time by 50%
- Optimize warehouse space utilization by 30%
- Process 1M+ orders annually`},{id:"smart-grid-management",name:"Smart Grid Management Platform",industry:"Energy & Utilities",description:"IoT-enabled smart grid platform with energy optimization and demand response",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","InfluxDB","AWS IoT","TensorFlow","SCADA"],features:["Grid monitoring","Energy optimization","Demand response","Outage management","Analytics"],businessGoals:["Optimize energy distribution","Reduce outages","Enable renewable integration"],targetAudience:"Utility companies, grid operators, and energy managers",timeline:"15-24 months",budget:"$800K - $2.5M",successMetrics:["20% efficiency gain","50% outage reduction","99.9% grid reliability"],risks:["Critical infrastructure security","Regulatory compliance","System complexity"],template:`Develop a smart grid management platform that optimizes energy distribution and integrates renewable sources.

Key Requirements:
- Real-time grid monitoring with IoT sensors and smart meters
- AI-powered energy demand forecasting and optimization
- Automated demand response and load balancing
- Outage detection and restoration management
- Renewable energy integration and storage management
- Customer energy usage analytics and billing
- Cybersecurity and critical infrastructure protection
- Regulatory compliance and reporting tools

Technical Specifications:
- Integration with SCADA and grid control systems
- Time-series database for energy data storage
- Machine learning for demand prediction and optimization
- Real-time data processing and alerting
- Cybersecurity frameworks for critical infrastructure
- Advanced visualization and control dashboards
- API integrations with energy markets and systems
- Comprehensive backup and disaster recovery

Success Criteria:
- Achieve 20% improvement in grid efficiency
- Reduce power outages by 50%
- Maintain 99.9% grid reliability
- Integrate 50% renewable energy sources`},{id:"renewable-energy-platform",name:"Renewable Energy Management",industry:"Energy & Utilities",description:"Comprehensive platform for managing solar, wind, and other renewable energy assets",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Python","InfluxDB","AWS","TensorFlow","Weather API"],features:["Asset monitoring","Performance optimization","Predictive maintenance","Energy trading","Analytics"],businessGoals:["Maximize energy production","Reduce maintenance costs","Optimize trading"],targetAudience:"Renewable energy companies, asset managers, and energy traders",timeline:"12-18 months",budget:"$500K - $1.5M",successMetrics:["95% asset uptime","15% production increase","30% maintenance savings"],risks:["Weather dependency","Equipment reliability","Market volatility"],template:`Build a comprehensive renewable energy management platform that maximizes production and optimizes operations.

Key Requirements:
- Real-time monitoring of solar, wind, and other renewable assets
- Weather-based production forecasting and optimization
- Predictive maintenance for renewable energy equipment
- Energy trading and market participation tools
- Performance analytics and benchmarking
- Integration with grid systems and energy markets
- Mobile app for field technicians and asset managers
- Environmental impact tracking and reporting

Technical Specifications:
- Integration with renewable energy equipment and inverters
- Weather data integration for production forecasting
- Machine learning for performance optimization
- Time-series database for energy production data
- Trading algorithms for energy market participation
- Advanced analytics and visualization tools
- Mobile app for remote monitoring and maintenance
- Comprehensive reporting and compliance tools

Success Criteria:
- Maintain 95% renewable asset uptime
- Increase energy production by 15% through optimization
- Reduce maintenance costs by 30%
- Manage 1GW+ of renewable energy capacity`},{id:"precision-farming-platform",name:"Precision Farming Platform",industry:"Agriculture & AgTech",description:"IoT-enabled precision agriculture platform with crop monitoring and yield optimization",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Python","InfluxDB","AWS IoT","TensorFlow","Drone API"],features:["Crop monitoring","Soil analysis","Weather integration","Yield prediction","Equipment tracking"],businessGoals:["Increase crop yields","Reduce resource usage","Optimize farming operations"],targetAudience:"Farmers, agricultural consultants, and agribusiness companies",timeline:"12-16 months",budget:"$400K - $1.2M",successMetrics:["20% yield increase","30% water savings","25% cost reduction"],risks:["Weather dependency","Technology adoption","Data accuracy"],template:`Create a precision farming platform that leverages IoT and AI to optimize agricultural operations and increase yields.

Key Requirements:
- IoT sensor networks for soil moisture, temperature, and nutrient monitoring
- Drone integration for aerial crop monitoring and analysis
- Weather data integration and microclimate monitoring
- AI-powered yield prediction and crop health analysis
- Irrigation and fertilizer optimization recommendations
- Equipment tracking and maintenance scheduling
- Mobile app for field operations and data collection
- Integration with farm management systems

Technical Specifications:
- IoT device management with agricultural sensors
- Computer vision for crop health analysis from drone imagery
- Machine learning for yield prediction and optimization
- Weather API integration for localized forecasting
- Time-series database for agricultural data storage
- Mobile app with offline capabilities for field use
- Advanced analytics and reporting dashboard
- Integration with agricultural equipment and systems

Success Criteria:
- Achieve 20% increase in crop yields
- Reduce water usage by 30% through precision irrigation
- Lower farming costs by 25%
- Monitor 100,000+ acres of farmland`},{id:"livestock-management-system",name:"Livestock Management System",industry:"Agriculture & AgTech",description:"Comprehensive livestock tracking and health monitoring platform with RFID and IoT sensors",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","AWS","RFID","IoT"],features:["Animal tracking","Health monitoring","Breeding management","Feed optimization","Veterinary records"],businessGoals:["Improve animal health","Optimize breeding programs","Reduce veterinary costs"],targetAudience:"Livestock farmers, ranchers, and veterinarians",timeline:"8-12 months",budget:"$250K - $750K",successMetrics:["95% animal health tracking","20% breeding efficiency","30% vet cost reduction"],risks:["Animal welfare concerns","Technology durability","Data privacy"],template:`Build a comprehensive livestock management system that monitors animal health and optimizes farm operations.

Key Requirements:
- RFID tagging and tracking for individual animal identification
- Health monitoring with wearable sensors and alerts
- Breeding program management with genetic tracking
- Feed optimization and nutrition management
- Veterinary records and treatment history
- Milk production tracking for dairy operations
- Mobile app for field operations and animal care
- Integration with veterinary and feed supplier systems

Technical Specifications:
- RFID reader integration for animal identification
- IoT sensors for health and activity monitoring
- Real-time alerts for health issues and breeding cycles
- Genetic database for breeding optimization
- Mobile app with barcode scanning capabilities
- Integration with veterinary management systems
- Advanced analytics for herd performance
- Comprehensive reporting and compliance tools

Success Criteria:
- Track health status of 95% of livestock
- Improve breeding efficiency by 20%
- Reduce veterinary costs by 30%
- Manage 50,000+ head of livestock`},{id:"sports-analytics-platform",name:"Sports Analytics Platform",industry:"Sports & Fitness",description:"Advanced sports analytics platform with player performance tracking and game analysis",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Python","TensorFlow","PostgreSQL","AWS","Computer Vision"],features:["Performance tracking","Game analysis","Player statistics","Video analysis","Predictive modeling"],businessGoals:["Improve team performance","Optimize player development","Gain competitive advantage"],targetAudience:"Sports teams, coaches, and performance analysts",timeline:"10-15 months",budget:"$400K - $1.2M",successMetrics:["15% performance improvement","90% prediction accuracy","100% coach adoption"],risks:["Data accuracy","Technology complexity","Coach acceptance"],template:`Develop an advanced sports analytics platform that provides deep insights into player and team performance.

Key Requirements:
- Real-time player performance tracking with wearable sensors
- Video analysis with computer vision for game breakdown
- Advanced statistics and performance metrics
- Predictive modeling for injury prevention and performance
- Game strategy analysis and opponent scouting
- Player development tracking and recommendations
- Mobile app for coaches and players
- Integration with existing sports management systems

Technical Specifications:
- Computer vision for automated video analysis
- Machine learning for performance prediction and optimization
- Real-time data processing from wearable devices
- Advanced statistical analysis and visualization
- Video streaming and annotation tools
- Mobile app with real-time performance monitoring
- Integration with sports equipment and tracking systems
- Comprehensive reporting and dashboard tools

Success Criteria:
- Achieve 15% improvement in team performance metrics
- Maintain 90% accuracy in performance predictions
- Achieve 100% adoption by coaching staff
- Analyze 1,000+ hours of game footage monthly`},{id:"fitness-coaching-app",name:"AI Fitness Coaching App",industry:"Sports & Fitness",description:"AI-powered personal fitness coaching app with workout generation and progress tracking",projectType:"mobile-application",platform:"mobile",complexity:"advanced",technologies:["React Native","Python","TensorFlow","PostgreSQL","AWS","HealthKit"],features:["Workout generation","Form analysis","Progress tracking","Nutrition guidance","Social features"],businessGoals:["Personalize fitness experiences","Improve user engagement","Reduce trainer costs"],targetAudience:"Fitness enthusiasts, personal trainers, and gym members",timeline:"8-12 months",budget:"$300K - $900K",successMetrics:["1M+ app downloads","80% user retention","90% goal achievement"],risks:["AI accuracy","User motivation","Competition"],template:`Create an AI-powered fitness coaching app that provides personalized workouts and real-time form feedback.

Key Requirements:
- AI-generated personalized workout plans based on goals and fitness level
- Computer vision for exercise form analysis and correction
- Progress tracking with detailed analytics and insights
- Nutrition guidance and meal planning integration
- Social features with challenges and community support
- Integration with wearable devices and health apps
- Video exercise library with professional demonstrations
- Personal trainer marketplace and virtual coaching

Technical Specifications:
- Machine learning for workout personalization
- Computer vision for real-time form analysis
- Integration with HealthKit (iOS) and Google Fit (Android)
- Real-time video processing and feedback
- Social networking features with privacy controls
- Advanced analytics for fitness progress tracking
- Cross-platform mobile development (iOS/Android)
- Comprehensive exercise database and content management

Success Criteria:
- Achieve 1M+ app downloads within first year
- Maintain 80% user retention after 30 days
- Help 90% of users achieve their fitness goals
- Process 10M+ workout sessions annually`},{id:"esports-tournament-platform",name:"Esports Tournament Platform",industry:"Gaming & Interactive Media",description:"Comprehensive esports tournament platform with live streaming and prize management",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","WebRTC","Stripe","Socket.io"],features:["Tournament management","Live streaming","Player registration","Prize distribution","Analytics"],businessGoals:["Grow esports ecosystem","Monetize tournaments","Engage gaming community"],targetAudience:"Esports players, tournament organizers, and gaming enthusiasts",timeline:"10-14 months",budget:"$400K - $1.2M",successMetrics:["1000+ tournaments hosted","100K+ registered players","$1M+ prize pool"],risks:["Technical complexity","Player acquisition","Monetization challenges"],template:`Build a comprehensive esports tournament platform that connects players, organizers, and audiences globally.

Key Requirements:
- Tournament creation and management tools for organizers
- Player registration and team formation systems
- Live streaming integration with chat and commentary
- Automated bracket generation and match scheduling
- Prize pool management and distribution
- Anti-cheat integration and fair play monitoring
- Mobile app for players and spectators
- Analytics dashboard for tournament insights

Technical Specifications:
- Real-time tournament bracket updates
- Live streaming with low-latency video delivery
- Payment processing for entry fees and prize distribution
- Integration with popular gaming platforms and APIs
- Real-time chat and social features
- Advanced analytics for player and tournament performance
- Mobile-responsive design with native app features
- Comprehensive security and anti-fraud measures

Success Criteria:
- Host 1,000+ tournaments annually
- Register 100,000+ active players
- Distribute $1M+ in tournament prizes
- Stream 10,000+ hours of live esports content`},{id:"volunteer-management-platform",name:"Volunteer Management Platform",industry:"Non-profit & Social Impact",description:"Comprehensive volunteer management platform with scheduling and impact tracking",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","Stripe","Twilio","AWS"],features:["Volunteer registration","Event scheduling","Impact tracking","Communication tools","Reporting"],businessGoals:["Increase volunteer engagement","Streamline operations","Measure social impact"],targetAudience:"Non-profit organizations, volunteers, and community groups",timeline:"6-10 months",budget:"$200K - $600K",successMetrics:["10K+ active volunteers","90% event attendance","50% admin time savings"],risks:["Volunteer retention","Technology adoption","Funding constraints"],template:`Create a volunteer management platform that helps non-profits organize, engage, and track volunteer activities.

Key Requirements:
- Volunteer registration and profile management
- Event creation and scheduling with automated notifications
- Skill-based volunteer matching for optimal placement
- Impact tracking and measurement tools
- Communication tools for volunteer coordination
- Training module management and certification tracking
- Mobile app for volunteers and coordinators
- Integration with fundraising and CRM systems

Technical Specifications:
- User management with role-based access control
- Automated email and SMS notifications
- Calendar integration for event scheduling
- Mobile-responsive design with PWA capabilities
- Integration with popular CRM and fundraising platforms
- Advanced reporting and analytics dashboard
- Volunteer hour tracking and verification
- Comprehensive security and data protection

Success Criteria:
- Engage 10,000+ active volunteers
- Achieve 90% volunteer event attendance rate
- Reduce administrative time by 50%
- Track 1M+ volunteer hours annually`},{id:"donation-crowdfunding-platform",name:"Donation & Crowdfunding Platform",industry:"Non-profit & Social Impact",description:"Social impact crowdfunding platform with transparent donation tracking and impact reporting",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","Stripe","Blockchain","AWS"],features:["Campaign creation","Donation processing","Impact tracking","Social sharing","Transparency tools"],businessGoals:["Enable social impact funding","Increase donation transparency","Build donor trust"],targetAudience:"Non-profits, social entrepreneurs, and donors",timeline:"8-12 months",budget:"$300K - $900K",successMetrics:["$10M+ raised","95% donor satisfaction","1000+ successful campaigns"],risks:["Fraud prevention","Regulatory compliance","Platform trust"],template:`Build a transparent crowdfunding platform that connects social impact projects with donors worldwide.

Key Requirements:
- Campaign creation tools with multimedia content support
- Secure donation processing with multiple payment methods
- Blockchain-based transparency for donation tracking
- Impact reporting and progress updates
- Social sharing and viral campaign features
- Donor management and engagement tools
- Mobile app for campaign management and donations
- Integration with social media and marketing platforms

Technical Specifications:
- Secure payment processing with fraud prevention
- Blockchain integration for donation transparency
- Social media integration for campaign promotion
- Advanced analytics for campaign performance
- Mobile-responsive design with native app features
- Integration with email marketing and CRM systems
- Comprehensive reporting and impact measurement
- Multi-currency and international payment support

Success Criteria:
- Facilitate $10M+ in donations annually
- Achieve 95% donor satisfaction rating
- Launch 1,000+ successful fundraising campaigns
- Maintain 99.9% payment processing reliability`}];function ol({isOpen:e,onClose:t,onLoadTemplate:i}){let[a,n]=(0,o.useState)("all"),[r,l]=(0,o.useState)("all"),[c,d]=(0,o.useState)(""),[m,p]=(0,o.useState)(null),u=Array.from(new Set(oo.map(e=>e.industry))),h=oo.filter(e=>{let t="all"===a||e.industry===a,i="all"===r||e.complexity===r,n=""===c||e.name.toLowerCase().includes(c.toLowerCase())||e.description.toLowerCase().includes(c.toLowerCase())||e.industry.toLowerCase().includes(c.toLowerCase());return t&&i&&n}),g=e=>{i({projectType:e.projectType,projectName:e.name,projectIdea:`${e.description}

Business Goals:
${e.businessGoals.map(e=>`• ${e}`).join("\n")}

Target Audience: ${e.targetAudience}

Key Features:
${e.features.map(e=>`• ${e}`).join("\n")}`,platform:e.platform,complexity:e.complexity,technologies:e.technologies.slice(0,4).map(t=>({category:"frontend",name:t,description:`${t} for ${e.industry.toLowerCase()} solutions`})),additionalRequirements:`Timeline: ${e.timeline}
Budget: ${e.budget}

Success Metrics:
${e.successMetrics.map(e=>`• ${e}`).join("\n")}

Risk Considerations:
${e.risks.map(e=>`• ${e}`).join("\n")}`}),t()},f=e=>{switch(e){case"basic":return"bg-green-600 text-green-200";case"intermediate":return"bg-yellow-600 text-yellow-200";case"advanced":return"bg-red-600 text-red-200";default:return"bg-gray-600 text-gray-200"}};return e?(0,s.jsx)("div",{className:"fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-2 sm:p-4",children:(0,s.jsxs)("div",{className:"bg-black border border-white/20 rounded-xl shadow-2xl p-4 sm:p-6 max-w-7xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-hidden",style:{boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.8)"},children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4 sm:mb-6",children:[(0,s.jsx)("h3",{className:"text-lg sm:text-xl font-semibold text-white",children:"Industry Templates"}),(0,s.jsx)("button",{onClick:t,className:"bg-gray-800 text-white px-3 py-1 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm flex-shrink-0",children:"Close"})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Industry Templates"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Choose from comprehensive industry-specific project templates with detailed specifications and best practices."})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-4 sm:mb-6",children:[(0,s.jsx)("input",{type:"text",value:c,onChange:e=>d(e.target.value),placeholder:"Search templates...",className:"px-3 py-2 rounded-md bg-gray-900 text-white placeholder-gray-400 border border-white/10 focus:border-white/30 focus:outline-none text-sm sm:text-base"}),(0,s.jsxs)("select",{value:a,onChange:e=>n(e.target.value),className:"px-3 py-2 rounded-md bg-gray-900 text-white border border-white/10 focus:border-white/30 focus:outline-none text-sm sm:text-base",children:[(0,s.jsx)("option",{value:"all",children:"All Industries"}),u.map(e=>(0,s.jsx)("option",{value:e,children:e},e))]}),(0,s.jsxs)("select",{value:r,onChange:e=>l(e.target.value),className:"px-3 py-2 rounded-md bg-gray-900 text-white border border-white/10 focus:border-white/30 focus:outline-none text-sm sm:text-base sm:col-span-2 lg:col-span-1",children:[(0,s.jsx)("option",{value:"all",children:"All Complexity Levels"}),["basic","intermediate","advanced"].map(e=>(0,s.jsx)("option",{value:e,className:"capitalize",children:e},e))]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 max-h-[55vh] sm:max-h-[65vh] overflow-y-auto",children:[(0,s.jsxs)("div",{className:`space-y-3 sm:space-y-4 ${m?"hidden lg:block":""}`,children:[h.map(e=>(0,s.jsxs)("div",{className:`bg-gray-900 border border-white/10 rounded-lg p-3 sm:p-4 transition-all duration-300 cursor-pointer ${m?.id===e.id?"ring-2 ring-white bg-gray-800":"hover:bg-gray-800"}`,onClick:()=>p(e),children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,s.jsx)("h4",{className:"text-white font-medium text-sm sm:text-base pr-2 flex-1 min-w-0",children:e.name}),(0,s.jsx)("span",{className:`px-2 py-1 rounded text-xs capitalize flex-shrink-0 ${f(e.complexity)}`,children:e.complexity})]}),(0,s.jsx)("p",{className:"text-gray-300 text-xs sm:text-sm mb-2",children:e.industry}),(0,s.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm mb-3 line-clamp-2",children:e.description}),(0,s.jsxs)("div",{className:"flex justify-between items-center text-xs text-gray-500",children:[(0,s.jsx)("span",{className:"truncate pr-2",children:e.timeline}),(0,s.jsx)("span",{className:"truncate",children:e.budget})]})]},e.id)),0===h.length&&(0,s.jsxs)("div",{className:"text-center text-gray-400 py-12",children:[(0,s.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDD0D"}),(0,s.jsx)("h4",{className:"text-lg font-semibold mb-2",children:"No Templates Found"}),(0,s.jsx)("p",{className:"text-sm",children:"Try adjusting your filters or search query"})]})]}),(0,s.jsx)("div",{className:`bg-gray-900 border border-white/10 rounded-lg p-3 sm:p-4 ${m?"lg:col-span-1":"hidden lg:block"} ${m?"flex flex-col":""}`,children:m?(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsxs)("button",{onClick:()=>p(null),className:"lg:hidden flex items-center space-x-2 text-gray-400 hover:text-white transition-colors mb-4 flex-shrink-0",children:[(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),(0,s.jsx)("span",{className:"text-sm",children:"Back to Templates"})]}),(0,s.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-3 sm:space-y-4 mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-white font-medium text-base sm:text-lg mb-2",children:m.name}),(0,s.jsx)("p",{className:"text-gray-300 text-xs sm:text-sm mb-2",children:m.industry}),(0,s.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm",children:m.description})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h5",{className:"text-white font-medium mb-2 text-sm sm:text-base",children:"Key Features"}),(0,s.jsx)("div",{className:"space-y-1",children:m.features.map((e,t)=>(0,s.jsxs)("div",{className:"text-gray-400 text-xs sm:text-sm",children:["• ",e]},t))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h5",{className:"text-white font-medium mb-2 text-sm sm:text-base",children:"Technologies"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-1 sm:gap-2",children:m.technologies.map((e,t)=>(0,s.jsx)("span",{className:"bg-gray-700 text-gray-200 text-xs px-2 py-1 rounded",children:e},t))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h5",{className:"text-white font-medium mb-2 text-sm sm:text-base",children:"Business Goals"}),(0,s.jsx)("div",{className:"space-y-1",children:m.businessGoals.map((e,t)=>(0,s.jsxs)("div",{className:"text-gray-400 text-xs sm:text-sm",children:["• ",e]},t))})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 text-sm",children:[(0,s.jsxs)("div",{className:"p-2 sm:p-3 rounded-lg bg-white/5 border border-white/10",children:[(0,s.jsx)("span",{className:"text-gray-400 text-xs sm:text-sm",children:"Timeline:"}),(0,s.jsx)("div",{className:"text-white text-sm sm:text-base font-medium",children:m.timeline})]}),(0,s.jsxs)("div",{className:"p-2 sm:p-3 rounded-lg bg-white/5 border border-white/10",children:[(0,s.jsx)("span",{className:"text-gray-400 text-xs sm:text-sm",children:"Budget:"}),(0,s.jsx)("div",{className:"text-white text-sm sm:text-base font-medium",children:m.budget})]})]})]}),(0,s.jsx)("div",{className:"flex-shrink-0 pt-3 border-t border-white/10",children:(0,s.jsx)("button",{onClick:()=>g(m),className:"w-full bg-white text-black py-3 px-4 rounded-lg hover:bg-gray-200 transition-all duration-300 font-semibold text-sm sm:text-base shadow-lg hover:shadow-xl transform hover:scale-[1.02]",children:"Use This Template"})})]}):(0,s.jsxs)("div",{className:"text-center text-gray-400 py-8 sm:py-12",children:[(0,s.jsx)("div",{className:"text-3xl sm:text-4xl mb-3 sm:mb-4",children:"\uD83D\uDCCB"}),(0,s.jsx)("h4",{className:"text-base sm:text-lg font-semibold mb-2",children:"Select a Template"}),(0,s.jsx)("p",{className:"text-xs sm:text-sm",children:"Choose a template from the list to see detailed information"})]})})]})]})}):null}let oc=()=>"undefined"!=typeof crypto&&crypto.randomUUID?crypto.randomUUID():"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)});function od(){let[e,t]=(0,o.useState)({projectName:"",projectIdea:"",projectType:"web-application",platform:"web",technologies:[],complexity:"intermediate",features:[],additionalRequirements:""}),[i,a]=(0,o.useState)(""),[n,r]=(0,o.useState)(!1),[d,u]=(0,o.useState)(0),[g,f]=(0,o.useState)({}),[y,x]=(0,o.useState)({}),[b,w]=(0,o.useState)([]),[k,j]=(0,o.useState)(!1),[S,A]=(0,o.useState)([]),[P,C]=(0,o.useState)(!1),[T,R]=(0,o.useState)(!1),[M,I]=(0,o.useState)(null),[N,E]=(0,o.useState)(!1),[D,L]=(0,o.useState)(!1),[z,O]=(0,o.useState)({model:"deepseek/deepseek-chat-v3-0324:free",optimizationLevel:"enhanced",includeExamples:!0,includeConstraints:!0,includeMetrics:!1,targetAudience:"developer"}),[$,F]=(0,o.useState)(null),V=(0,o.useCallback)((e,t="info")=>{I({message:e,type:t}),setTimeout(()=>I(null),3e3)},[]),B=(e,t)=>({"web-application":["TaskMaster Pro","DataViz Dashboard","EcoTracker","ShopSmart","ConnectHub"],"mobile-application":["FitTrack Mobile","LocalEats App","StudyBuddy","WeatherWise","PhotoShare"],"desktop-application":["CodeEditor Pro","MediaManager","TaskPlanner","FileSync","NoteTaker"],"api-backend":["UserAuth API","Payment Gateway","Analytics Service","Notification Hub","Data Processor"],"data-analysis":["Sales Analytics","Customer Insights","Market Research Tool","Performance Dashboard","Trend Analyzer"],"machine-learning":["Recommendation Engine","Image Classifier","Sentiment Analyzer","Fraud Detector","Chatbot AI"],"devops-infrastructure":["CI/CD Pipeline","Monitoring Stack","Container Platform","Auto Scaler","Log Aggregator"],"chrome-extension":["Productivity Booster","Tab Manager","Password Helper","Web Scraper","Time Tracker"],"cli-tool":["File Processor","Code Generator","Deployment Tool","Data Migrator","System Monitor"],"library-package":["UI Components","Utility Library","API Client","Data Validator","Chart Library"]})[t]?.filter(t=>t.toLowerCase().includes(e.toLowerCase()))||[],G=(0,o.useCallback)((e,t)=>{let i=[{id:oc(),timestamp:new Date,projectInput:e,generatedPrompt:t,isFavorite:!1},...b.slice(0,19)];w(i),localStorage.setItem("promptHistory",JSON.stringify(i))},[b]),W=e=>{let t=b.map(t=>t.id===e?{...t,isFavorite:!t.isFavorite}:t);w(t),localStorage.setItem("promptHistory",JSON.stringify(t))},K=e=>{t(e.projectInput),a(e.generatedPrompt),j(!1)},_=(0,o.useCallback)((e,t)=>{let i={...g};switch(e){case"projectName":t.trim()?t.length<3?i.projectName="Project name must be at least 3 characters":t.length>50?i.projectName="Project name must be less than 50 characters":delete i.projectName:i.projectName="Project name is required";break;case"projectIdea":t.trim()?t.length<20?i.projectIdea="Please provide more details (minimum 20 characters)":t.length>1e3?i.projectIdea="Project concept must be less than 1000 characters":delete i.projectIdea:i.projectIdea="Project concept is required"}return f(i),0===Object.keys(i).length},[g]),U=(0,o.useCallback)(()=>{let t=_("projectName",e.projectName),i=_("projectIdea",e.projectIdea);return t&&i},[e.projectName,e.projectIdea,_]),q=(e,i)=>{t(t=>({...t,[e]:i})),x(t=>({...t,[e]:!0})),("projectName"===e||"projectIdea"===e)&&_(e,i)},H=(0,o.useCallback)(async()=>{if(!U())return void x({projectName:!0,projectIdea:!0});r(!0),u(0);let t=setInterval(()=>{u(e=>Math.min(e+10,90))},200);try{let t=m.getModelRecommendation(e),i={...z};i.model&&"auto"!==i.model||(i.model=t);let n=await m.generatePrompt(e,i);u(100),a(n.prompt),F(n.metadata),G(e,n.prompt);let r=Object.values(c).find(e=>e.id===i.model)?.name||"AI";V(`Prompt generated with ${r}`,"success")}catch(t){console.error("Error generating prompt:",t);let e=t instanceof Error?t.message:"Unknown error occurred";f({general:`Error generating prompt: ${e}`}),V("Failed to generate prompt","error")}finally{clearInterval(t),r(!1),setTimeout(()=>u(0),1e3)}},[e,U,V,z,G]),Q={copyToClipboard:async e=>{try{await navigator.clipboard.writeText(e),V("Copied to clipboard!","success")}catch{V("Failed to copy to clipboard","error")}},downloadAsFile:(e,t)=>{try{let i=new Blob([e],{type:"text/plain"}),a=URL.createObjectURL(i),n=document.createElement("a");n.href=a,n.download=`${t}.txt`,document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(a),V("File downloaded successfully!","success")}catch{V("Failed to download file","error")}},shareViaAPI:async e=>{if(navigator.share)try{await navigator.share({title:"AI Generated Prompt for Augment Agent",text:e}),V("Shared successfully!","success")}catch(e){e instanceof Error&&"AbortError"!==e.name&&V("Failed to share","error")}else V("Sharing not supported on this device","info")}},X=async()=>{await Q.copyToClipboard(i)},J=e=>{t(t=>({...t,technologies:t.technologies.some(t=>t.name===e.name)?t.technologies.filter(t=>t.name!==e.name):[...t.technologies,e]}))};return(0,s.jsxs)("div",{className:"min-h-screen bg-black py-4 sm:py-8 px-2 sm:px-4 relative",children:[(0,s.jsx)(p,{}),(0,s.jsxs)("div",{className:"max-w-6xl mx-auto relative z-10",children:[(0,s.jsxs)("header",{className:"text-center mb-4 sm:mb-8 px-2",children:[(0,s.jsx)(r7,{sequences:[{text:"AI Prompt Generator",deleteAfter:!0,pauseAfter:1200},{text:"Professional Prompts",deleteAfter:!0,pauseAfter:1200},{text:"DeepSeek V3 Powered",deleteAfter:!0,pauseAfter:1200},{text:"Augment Agent Ready",deleteAfter:!0,pauseAfter:2e3}],typingSpeed:60,autoLoop:!0,loopDelay:3e3,className:"mb-4"}),(0,s.jsx)(s6,{text:"AI-Powered Prompt Generation with DeepSeek V3 for Augment Agent",className:"text-sm sm:text-base lg:text-lg"})]}),(0,s.jsxs)("div",{className:"space-y-4 sm:space-y-8",children:[(0,s.jsxs)("div",{className:"rounded-xl shadow-2xl p-3 sm:p-4 lg:p-6 transition-all duration-300 ease-in-out max-w-2xl mx-auto",style:{boxShadow:"0 20px 40px -12px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center mb-3 space-y-2 sm:space-y-0",children:[(0,s.jsx)(s6,{text:"Project Details",className:"text-lg sm:text-xl font-semibold"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2",children:[(0,s.jsx)("button",{onClick:()=>{t({projectName:"",projectIdea:"",projectType:"web-application",platform:"web",technologies:[],complexity:"intermediate",features:[],additionalRequirements:""}),a(""),f({}),x({}),V("Form cleared","info")},className:"bg-black/5 text-white px-3 py-2 sm:px-2 sm:py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg min-h-[44px] sm:min-h-auto",style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:"Clear"}),(0,s.jsxs)("button",{onClick:()=>j(!0),className:"bg-black/5 text-white px-3 py-2 sm:px-2 sm:py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg min-h-[44px] sm:min-h-auto",style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:["History (",b.length,")"]}),(0,s.jsx)("button",{onClick:()=>L(!0),className:"bg-gray-800 text-white px-3 py-2 sm:px-2 sm:py-1 rounded-md hover:bg-gray-700 transition-all duration-300 text-xs hover:shadow-lg min-h-[44px] sm:min-h-auto",style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:"Industry Templates"}),(0,s.jsx)("button",{onClick:()=>E(!0),className:"bg-black/5 text-white px-3 py-2 sm:px-2 sm:py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg min-h-[44px] sm:min-h-auto",style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:"AI Settings & Integrations"})]})]}),g.general&&(0,s.jsx)("div",{className:"mb-3 p-2 bg-gray-800 border border-gray-600 rounded-md",children:(0,s.jsx)("p",{className:"text-gray-200 text-xs",children:g.general})}),g.success&&(0,s.jsx)("div",{className:"mb-3 p-2 bg-gray-800 border border-gray-600 rounded-md",children:(0,s.jsx)("p",{className:"text-gray-200 text-xs",children:g.success})}),(0,s.jsxs)("div",{className:"mb-3 sm:mb-2 relative",children:[(0,s.jsx)("label",{className:"block text-sm sm:text-xs font-medium mb-2 sm:mb-1",children:(0,s.jsx)(s6,{text:"Project Name *",className:"text-sm sm:text-xs"})}),(0,s.jsx)("input",{type:"text",value:e.projectName,onChange:t=>{q("projectName",t.target.value);let i=B(t.target.value,e.projectType);A(i),C(t.target.value.length>0&&i.length>0)},onBlur:()=>{x(e=>({...e,projectName:!0})),setTimeout(()=>C(!1),200)},onFocus:()=>{let t=B(e.projectName,e.projectType);A(t),C(e.projectName.length>0&&t.length>0)},className:`w-full px-3 py-3 sm:px-2 sm:py-1.5 rounded-md focus:outline-none focus:ring-1 text-base sm:text-sm text-white bg-black/5 backdrop-blur-md placeholder-gray-400 transition-all duration-300 hover:bg-black/10 min-h-[44px] sm:min-h-auto ${g.projectName&&y.projectName?"ring-red-400 border-red-400":"focus:ring-white/20"}`,style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},placeholder:"e.g., TaskMaster Pro"}),P&&S.length>0&&(0,s.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-black/20 backdrop-blur-xl rounded-md shadow-lg border border-white/10 max-h-40 overflow-y-auto",children:S.map((e,t)=>(0,s.jsx)("button",{type:"button",onClick:()=>{q("projectName",e),C(!1)},className:"w-full text-left px-3 py-2 text-sm text-white hover:bg-black/20 transition-all duration-200 first:rounded-t-md last:rounded-b-md",children:e},t))}),g.projectName&&y.projectName&&(0,s.jsx)("p",{className:"text-gray-300 text-xs mt-1",children:g.projectName}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[e.projectName.length,"/50 characters"]})]}),(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsx)("label",{className:"block text-xs font-medium mb-1",children:(0,s.jsx)(s6,{text:"Project Concept *",className:"text-xs"})}),(0,s.jsx)("textarea",{value:e.projectIdea,onChange:e=>q("projectIdea",e.target.value),onBlur:()=>x(e=>({...e,projectIdea:!0})),className:`w-full px-2 py-1.5 rounded-md focus:outline-none focus:ring-1 h-16 text-sm text-white bg-black/5 backdrop-blur-md placeholder-gray-400 transition-all duration-300 hover:bg-black/10 ${g.projectIdea&&y.projectIdea?"ring-red-400 border-red-400":"focus:ring-white/20"}`,style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},placeholder:"Describe your project idea, goals, and target users..."}),g.projectIdea&&y.projectIdea&&(0,s.jsx)("p",{className:"text-gray-300 text-xs mt-1",children:g.projectIdea}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 mt-1 space-y-1",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsxs)("span",{children:[e.projectIdea.length,"/1000 characters"]}),(0,s.jsx)("span",{className:`${e.projectIdea.length<50?"text-gray-400":e.projectIdea.length<100?"text-gray-300":"text-white"}`,children:e.projectIdea.length<50?"Too short":e.projectIdea.length<100?"Good start":"Great detail!"})]}),e.projectIdea.length<100&&(0,s.jsx)("div",{className:"text-gray-300 text-xs",children:"\uD83D\uDCA1 Tip: Include target users, main features, and business goals for better AI prompts"})]})]}),(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsx)("label",{className:"block text-xs font-medium mb-1",children:(0,s.jsx)(s6,{text:"Project Type",className:"text-xs"})}),(0,s.jsx)(h,{value:e.projectType,onChange:e=>q("projectType",e),options:[{value:"web-application",label:"Web Application",description:"Frontend web apps, SPAs, and websites"},{value:"mobile-application",label:"Mobile Application",description:"iOS, Android, and cross-platform apps"},{value:"desktop-application",label:"Desktop Application",description:"Native desktop software and tools"},{value:"api-backend",label:"API/Backend Service",description:"REST APIs, GraphQL, and backend services"},{value:"data-analysis",label:"Data Analysis Tool",description:"Analytics, reporting, and data processing"},{value:"machine-learning",label:"Machine Learning Project",description:"AI/ML models and applications"},{value:"devops-infrastructure",label:"DevOps Infrastructure",description:"CI/CD, deployment, and automation"},{value:"chrome-extension",label:"Chrome Extension",description:"Browser extensions and add-ons"},{value:"cli-tool",label:"CLI Tool",description:"Command-line utilities and scripts"},{value:"library-package",label:"Library/Package",description:"Reusable libraries and npm packages"}],placeholder:"Select project type"})]}),(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsx)("label",{className:"block text-xs font-medium mb-1",children:(0,s.jsx)(s6,{text:"Target Platform",className:"text-xs"})}),(0,s.jsx)(h,{value:e.platform,onChange:e=>q("platform",e),options:[{value:"web",label:"Web",description:"Browser-based applications"},{value:"mobile",label:"Mobile",description:"iOS and Android platforms"},{value:"desktop",label:"Desktop",description:"Windows, macOS, and Linux"},{value:"server",label:"Server",description:"Backend and cloud services"},{value:"cross-platform",label:"Cross-Platform",description:"Multiple platforms"}],placeholder:"Select target platform"})]}),(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsx)("label",{className:"block text-xs font-medium mb-1",children:(0,s.jsx)(s6,{text:"Complexity Level",className:"text-xs"})}),(0,s.jsx)("div",{className:"flex space-x-2",children:["simple","intermediate","advanced"].map(t=>(0,s.jsxs)("label",{className:`group relative flex-1 cursor-pointer transition-all duration-300 ${e.complexity===t?"transform scale-[1.02]":""}`,children:[(0,s.jsx)("input",{type:"radio",value:t,checked:e.complexity===t,onChange:e=>q("complexity",e.target.value),className:"sr-only"}),(0,s.jsx)("div",{className:`
                      px-4 py-3 rounded-lg text-center text-sm font-medium transition-all duration-300 border backdrop-blur-xl
                      ${e.complexity===t?"bg-gradient-to-br from-white/20 to-white/10 border-white/30 text-white shadow-lg ring-2 ring-white/20":"bg-gradient-to-br from-gray-800/90 to-gray-900/90 border-white/10 text-gray-300 hover:border-white/20 hover:from-gray-700/90 hover:to-gray-800/90 hover:text-white"}
                    `,style:{boxShadow:e.complexity===t?"0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1)":"0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:(0,s.jsx)("span",{className:"capitalize",children:t})})]},t))})]}),(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsx)("label",{className:"block text-xs font-medium mb-1",children:(0,s.jsx)(s6,{text:"Technology Stack (Optional)",className:"text-xs"})}),(0,s.jsx)("div",{className:"space-y-2",children:Object.entries(l).map(([t,i])=>(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-white mb-1 capitalize text-xs",children:t.replace("-"," ")}),(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:i.map(t=>(0,s.jsx)("button",{type:"button",onClick:()=>J(t),className:`px-2 py-0.5 text-xs rounded-full transition-all duration-300 ease-in-out ${e.technologies.some(e=>e.name===t.name)?"bg-white/10 text-white backdrop-blur-md shadow-lg hover:bg-white/15":"bg-black/5 text-white backdrop-blur-md hover:bg-black/10 hover:shadow-lg"}`,style:{boxShadow:e.technologies.some(e=>e.name===t.name)?"0 2px 8px rgba(255, 255, 255, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.05)":"0 1px 4px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:t.name},t.name))})]},t))})]}),(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsx)("label",{className:"block text-xs font-medium mb-1",children:(0,s.jsx)(s6,{text:"Additional Requirements (Optional)",className:"text-xs"})}),(0,s.jsx)("textarea",{value:e.additionalRequirements,onChange:e=>q("additionalRequirements",e.target.value),className:"w-full px-2 py-1.5 rounded-md focus:outline-none focus:ring-1 focus:ring-white/20 h-12 text-sm text-white bg-black/5 backdrop-blur-md placeholder-gray-400 transition-all duration-300 hover:bg-black/10",style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},placeholder:"Any specific requirements, constraints, or preferences..."})]}),(0,s.jsx)("div",{className:"space-y-1.5",children:(0,s.jsx)("button",{onClick:H,disabled:n||!e.projectName||!e.projectIdea,className:"w-full bg-black/10 text-white py-2 px-4 rounded-lg hover:bg-black/20 disabled:bg-gray-600/10 disabled:cursor-not-allowed transition-all duration-300 font-semibold text-sm backdrop-blur-md hover:shadow-xl",style:{boxShadow:"0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:n?(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,s.jsx)(os,{texts:["Analyzing requirements...","Structuring prompt...","Optimizing for AI...","Finalizing output...","Almost ready..."],interval:1800,className:"text-lg"}),(0,s.jsx)("div",{className:"w-full bg-black/20 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-gradient-to-r from-white to-gray-300 h-2 rounded-full transition-all duration-500 ease-out",style:{width:`${d}%`}})}),(0,s.jsxs)("span",{className:"text-xs text-gray-400",children:[d,"%"]})]}):"Generate AI Prompt"})})]}),(0,s.jsxs)("div",{className:"rounded-xl shadow-2xl p-3 transition-all duration-300 ease-in-out max-w-2xl mx-auto",style:{boxShadow:"0 20px 40px -12px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(s6,{text:"Generated Prompt",className:"text-xl font-semibold"}),i&&(0,s.jsxs)("div",{className:"text-xs text-gray-400 mt-1 space-y-1",children:[(0,s.jsxs)("div",{children:[i.split(" ").length," words • ",Math.ceil(i.split(" ").length/200)," min read"]}),$&&(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)("span",{children:["Generated with ",Object.values(c).find(e=>e.id===$.model)?.name||"AI"]}),$.tokensUsed&&(0,s.jsxs)("span",{children:["• ",$.tokensUsed," tokens"]}),(0,s.jsx)("span",{className:`px-1.5 py-0.5 rounded text-xs ${"expert"===$.optimizationLevel?"bg-gray-700 text-gray-200":"enhanced"===$.optimizationLevel?"bg-gray-600 text-gray-200":"bg-gray-500 text-gray-200"}`,children:$.optimizationLevel})]})]})]}),i&&(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:X,className:"bg-black/5 text-white px-2 py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg",style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:"Copy"}),(0,s.jsx)("button",{onClick:()=>Q.downloadAsFile(i,`augment-prompt-${Date.now()}`),className:"bg-black/5 text-white px-2 py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg",style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:"Download"}),"undefined"!=typeof navigator&&"share"in navigator&&(0,s.jsx)("button",{onClick:()=>Q.shareViaAPI(i),className:"bg-black/5 text-white px-2 py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg",style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:"Share"})]})]}),i?(0,s.jsx)("div",{className:"bg-black/5 backdrop-blur-md rounded-lg p-2 max-h-60 overflow-y-auto transition-all duration-300 hover:bg-black/10",style:{boxShadow:"0 4px 16px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:(0,s.jsx)("pre",{className:"whitespace-pre-wrap text-xs text-white font-mono",children:i})}):(0,s.jsxs)("div",{className:"bg-black/3 backdrop-blur-lg rounded-lg p-4 text-center transition-all duration-300 hover:bg-black/8",style:{boxShadow:"0 4px 16px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"AI-Powered Prompt Generation"}),(0,s.jsx)("p",{className:"text-gray-400 mb-2 text-sm",children:"Fill in your project details and let DeepSeek V3 create the perfect Augment Agent prompt for you."}),(0,s.jsx)("div",{className:"text-xs text-white font-medium",children:"Personalized • Optimized • Professional"})]})]})]}),(0,s.jsx)("div",{className:"mt-8 sm:mt-16 text-center",children:(0,s.jsxs)("button",{onClick:()=>R(!T),className:"bg-black/5 backdrop-blur-xl text-white px-6 py-3 sm:px-4 sm:py-2 rounded-xl hover:bg-black/10 transition-all duration-300 ease-in-out min-h-[44px] sm:min-h-auto text-base sm:text-sm font-medium",style:{boxShadow:"0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:["\uD83D\uDCD6 ",T?"Hide":"Show"," Complete User Guide"]})}),(0,s.jsx)("div",{className:`mt-4 sm:mt-8 rounded-2xl shadow-2xl transition-all duration-500 ease-in-out overflow-hidden ${T?"max-h-none opacity-100 p-4 sm:p-6":"max-h-0 opacity-0 p-0"}`,style:{boxShadow:T?"0 25px 50px -12px rgba(0, 0, 0, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.05)":"none"},children:T&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4 sm:mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-white",children:"Complete User Guide"}),(0,s.jsx)("button",{onClick:()=>R(!1),className:"bg-black/5 text-white px-3 py-2 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg min-h-[44px] sm:min-h-auto",style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:"✕ Close"})]}),(0,s.jsx)("p",{className:"text-center text-gray-400 mb-4 sm:mb-6 text-sm sm:text-lg",children:"Learn how to use each feature effectively to generate the best AI-powered prompts for your projects"}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out",style:{boxShadow:"0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-white mb-3",children:"Project Details"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Project Name"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Enter a descriptive name for your project. This helps the AI understand the scope and purpose. Examples: “TaskMaster Pro”, “E-commerce Platform”, “Mobile Banking App”"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Project Concept"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Describe your project idea in detail. Include the main purpose, target users, key features, and goals. The more specific you are, the better the AI can tailor the prompt to your needs."})]})]})]}),(0,s.jsxs)("div",{className:"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out",style:{boxShadow:"0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-white mb-3",children:"Project Types"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Web Application"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Choose this for websites, web apps, or browser-based applications. Includes both frontend and backend development."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Mobile Application"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Select for iOS, Android, or cross-platform mobile apps. Covers native and hybrid development approaches."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Desktop Application"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"For Windows, macOS, or Linux desktop software. Includes cross-platform and native desktop solutions."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"API/Backend Service"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Choose for server-side applications, REST APIs, microservices, or backend systems without a user interface."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Data Analysis Tool"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"For data processing, analytics, visualization, or machine learning applications and tools."})]})]})]}),(0,s.jsxs)("div",{className:"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out",style:{boxShadow:"0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-white mb-3",children:"Platform Options"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Web"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Browser-based applications accessible via web browsers. Includes responsive design for all devices."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Mobile"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Applications designed specifically for smartphones and tablets, with mobile-optimized interfaces."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Desktop"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Native applications that run directly on desktop operating systems like Windows, macOS, or Linux."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Server"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Backend services, APIs, and server-side applications that run on servers or cloud infrastructure."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Cloud"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Cloud-native applications designed to leverage cloud services and distributed computing resources."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Cross-platform"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Applications that work across multiple platforms using frameworks like Electron, Flutter, or React Native."})]})]})]}),(0,s.jsxs)("div",{className:"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out",style:{boxShadow:"0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-white mb-3",children:"Complexity Levels"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Simple"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Basic functionality with minimal features. Quick to develop, perfect for MVPs, prototypes, or learning projects. Focuses on core features only."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Intermediate"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Moderate complexity with additional features, user authentication, database integration, and proper error handling. Suitable for most business applications."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Advanced"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Enterprise-level complexity with scalability, performance optimization, advanced security, microservices, and comprehensive testing strategies."})]})]})]}),(0,s.jsxs)("div",{className:"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out",style:{boxShadow:"0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-white mb-3",children:"Technology Stack Guide"}),(0,s.jsx)("p",{className:"text-gray-400 mb-4",children:"A technology stack is the combination of programming languages, frameworks, libraries, and tools used to build your application. Selecting the right technologies is crucial for project success."}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Frontend Technologies"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Frontend development creates the user interface and user experience. Choose technologies based on your project needs:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"React:"})," Popular, component-based, large ecosystem"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Vue.js:"})," Progressive, easy to learn, flexible"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Angular:"})," Full framework, TypeScript-based, enterprise-ready"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Next.js:"})," React framework with SSR and static generation"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Svelte:"})," Compile-time optimized, smaller bundle sizes"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"TypeScript:"})," Adds type safety to JavaScript"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Backend Technologies"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Backend development handles server-side logic, databases, and APIs:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Node.js:"})," JavaScript runtime, fast development"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Python/Django:"})," Rapid development, batteries included"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Python/FastAPI:"})," Modern, fast, automatic API docs"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Go:"})," High performance, excellent concurrency"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Rust:"})," Memory safety, extreme performance"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Java/Spring:"})," Enterprise-grade, mature ecosystem"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Database Options"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Choose databases based on your data structure and scalability needs:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"PostgreSQL:"})," Advanced relational database, JSON support"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"MongoDB:"})," NoSQL document database, flexible schema"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"SQLite:"})," Lightweight, embedded, perfect for small apps"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Redis:"})," In-memory store, caching, real-time features"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Supabase:"})," Open-source Firebase alternative"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Firebase:"})," Google's platform, real-time features"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Styling Technologies"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Choose styling approaches based on your project needs and team preferences:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Tailwind CSS:"})," Utility-first CSS framework"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"CSS Modules:"})," Scoped CSS with local class names"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Styled Components:"})," CSS-in-JS with component styling"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"SCSS/Sass:"})," CSS preprocessor with variables and mixins"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Material-UI:"})," React components with Material Design"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Chakra UI:"})," Simple, modular React component library"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Mobile Frameworks"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Cross-platform and native mobile development frameworks:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"React Native:"})," Cross-platform with React"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Flutter:"})," Google's UI toolkit for mobile"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Swift:"})," Native iOS development language"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Kotlin:"})," Modern Android development language"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Expo:"})," Platform for React Native development"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Desktop Frameworks"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Cross-platform and native desktop application frameworks:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Electron:"})," Web technologies for desktop apps"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Tauri:"})," Rust-based lightweight desktop framework"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Qt:"})," Cross-platform C++ application framework"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"WPF:"})," Windows Presentation Foundation for .NET"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"JavaFX:"})," Java platform for desktop applications"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Deployment Platforms"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Modern deployment and hosting solutions for applications:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Vercel:"})," Frontend deployment with edge functions"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Netlify:"})," JAMstack deployment and hosting"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"AWS:"})," Amazon Web Services cloud platform"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Google Cloud:"})," Google's cloud computing services"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Docker:"})," Containerization platform"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Kubernetes:"})," Container orchestration system"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Testing Frameworks"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Testing tools and frameworks for quality assurance:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Jest:"})," JavaScript testing framework"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Vitest:"})," Fast unit testing framework"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Cypress:"})," End-to-end testing framework"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Playwright:"})," Cross-browser automation testing"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"React Testing Library:"})," React component testing"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Authentication Solutions"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"User authentication and authorization services:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Auth0:"})," Identity platform as a service"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Firebase Auth:"})," Google's authentication service"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"NextAuth.js:"})," Authentication for Next.js"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Supabase Auth:"})," Open-source authentication"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"JWT:"})," JSON Web Tokens for stateless auth"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"State Management"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Tools for managing application state across components and user sessions:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Redux Toolkit:"})," Modern Redux with simplified API"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Zustand:"})," Lightweight state management solution"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Recoil:"})," Experimental state management for React"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"MobX:"})," Reactive state management through observables"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Context API:"})," React's built-in state management"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Valtio:"})," Proxy-based state management"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"API Tools"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Tools for API development, data fetching, and client-server communication:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"GraphQL:"})," Query language for APIs"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Apollo Client:"})," Comprehensive GraphQL client"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"React Query:"})," Data fetching and caching library"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"SWR:"})," Data fetching with caching and revalidation"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Axios:"})," Promise-based HTTP client"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"tRPC:"})," End-to-end typesafe APIs"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Monitoring & Analytics"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Tools for application monitoring, error tracking, and performance analysis:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Sentry:"})," Error tracking and performance monitoring"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"LogRocket:"})," Session replay and logging"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"New Relic:"})," Application performance monitoring"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Datadog:"})," Infrastructure and application monitoring"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Prometheus:"})," Open-source monitoring and alerting"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Grafana:"})," Analytics and monitoring dashboards"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"CI/CD & DevOps"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Continuous integration and deployment tools for automated workflows:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"GitHub Actions:"})," CI/CD platform integrated with GitHub"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"GitLab CI:"})," Built-in CI/CD for GitLab"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Jenkins:"})," Open-source automation server"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"CircleCI:"})," Cloud-based CI/CD platform"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Azure DevOps:"})," Microsoft's DevOps platform"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Travis CI:"})," Hosted CI service for GitHub projects"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Version Control"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Version control systems and repository hosting platforms:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Git:"})," Distributed version control system"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"GitHub:"})," Git hosting with collaboration features"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"GitLab:"})," DevOps platform with Git repository"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Bitbucket:"})," Git solution for teams"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Azure Repos:"})," Git repositories in Azure DevOps"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Package Managers"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Tools for managing project dependencies and packages:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"npm:"})," Node.js package manager"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Yarn:"})," Fast, reliable package manager"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"pnpm:"})," Efficient package manager with shared dependencies"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Bun:"})," Fast all-in-one JavaScript runtime and package manager"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Build Tools"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Tools for bundling, compiling, and optimizing application code:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Webpack:"})," Module bundler for JavaScript applications"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Vite:"})," Fast build tool for modern web projects"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Rollup:"})," Module bundler for JavaScript libraries"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Parcel:"})," Zero-configuration build tool"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"esbuild:"})," Extremely fast JavaScript bundler"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Turbopack:"})," Incremental bundler optimized for JavaScript and TypeScript"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Code Quality"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Tools for maintaining code quality, formatting, and automated code review:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"ESLint:"})," JavaScript linting utility"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Prettier:"})," Code formatter"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Husky:"})," Git hooks for code quality"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"lint-staged:"})," Run linters on staged files"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"SonarQube:"})," Code quality and security analysis"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"CodeClimate:"})," Automated code review and quality analytics"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Documentation"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Tools for creating and maintaining project documentation:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Storybook:"})," Tool for building UI components in isolation"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Docusaurus:"})," Documentation website generator"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"GitBook:"})," Documentation platform"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Notion:"})," All-in-one workspace for documentation"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"Confluence:"})," Team collaboration and documentation"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{className:"text-white",children:"JSDoc:"})," API documentation generator for JavaScript"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Additional Requirements"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Use this field to specify any special requirements, constraints, integrations, performance needs, security requirements, or specific features not covered in the main form. Be as detailed as possible to help the AI generate the most relevant prompt."})]})]})]}),(0,s.jsxs)("div",{className:"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out",style:{boxShadow:"0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-white mb-3",children:"Tips for Best Results"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,s.jsx)("span",{className:"text-white font-bold",children:"1."}),(0,s.jsxs)("p",{className:"text-gray-400 text-sm",children:[(0,s.jsx)("strong",{className:"text-white",children:"Be Specific:"})," The more detailed your project description, the better the AI can tailor the prompt to your exact needs."]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,s.jsx)("span",{className:"text-white font-bold",children:"2."}),(0,s.jsxs)("p",{className:"text-gray-400 text-sm",children:[(0,s.jsx)("strong",{className:"text-white",children:"Choose Appropriate Complexity:"})," Match the complexity level to your timeline, budget, and technical requirements."]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,s.jsx)("span",{className:"text-white font-bold",children:"3."}),(0,s.jsxs)("p",{className:"text-gray-400 text-sm",children:[(0,s.jsx)("strong",{className:"text-white",children:"Select Relevant Technologies:"})," If you're unsure about technologies, leave them blank and let the AI suggest appropriate options."]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,s.jsx)("span",{className:"text-white font-bold",children:"4."}),(0,s.jsxs)("p",{className:"text-gray-400 text-sm",children:[(0,s.jsx)("strong",{className:"text-white",children:"Use Templates:"}),' Click "Project Templates" to explore examples and see how to structure your project information effectively.']})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,s.jsx)("span",{className:"text-white font-bold",children:"5."}),(0,s.jsxs)("p",{className:"text-gray-400 text-sm",children:[(0,s.jsx)("strong",{className:"text-white",children:"Include Context:"})," Mention your target audience, business goals, and any existing systems you need to integrate with."]})]})]})]}),(0,s.jsxs)("div",{className:"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out",style:{boxShadow:"0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-white mb-3",children:"Keyboard Shortcuts"}),(0,s.jsx)("p",{className:"text-gray-400 mb-4 text-sm",children:"Use these keyboard shortcuts to work more efficiently:"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:"Generate Prompt"}),(0,s.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + Enter"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:"Focus Project Name"}),(0,s.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + K"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:"Open Templates"}),(0,s.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + T"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:"AI Settings"}),(0,s.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + ,"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:"Open History"}),(0,s.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + H"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:"AI Settings & Integrations"}),(0,s.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + A"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:"Team Dashboard"}),(0,s.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + M"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:"Share Prompt"}),(0,s.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + S"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:"Integrations"}),(0,s.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + I"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:"Live Collaboration"}),(0,s.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + R"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:"Industry Templates"}),(0,s.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + B"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:"Close Modal"}),(0,s.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Escape"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:"Auto-save"}),(0,s.jsx)("span",{className:"text-gray-300 text-xs",children:"Automatic"})]})]})]})]})]})]})}),k&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"bg-black border border-white/20 rounded-xl shadow-2xl p-6 max-w-4xl w-full max-h-[80vh] overflow-hidden",style:{boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.8)"},children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-white",children:"Prompt History"}),(0,s.jsx)("button",{onClick:()=>j(!1),className:"bg-gray-800 text-white px-3 py-1 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm",children:"Close"})]}),(0,s.jsx)("div",{className:"overflow-y-auto max-h-[60vh] space-y-3",children:0===b.length?(0,s.jsx)("p",{className:"text-gray-400 text-center py-8",children:"No prompts generated yet"}):b.map(e=>(0,s.jsxs)("div",{className:"bg-gray-900 border border-white/10 rounded-lg p-4 hover:bg-gray-800 transition-all duration-300",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-white font-medium",children:e.projectInput.projectName}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:e.timestamp.toLocaleString()})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:()=>W(e.id),className:`px-2 py-1 rounded text-xs transition-all duration-300 ${e.isFavorite?"bg-white text-black":"bg-gray-700 text-gray-400 hover:bg-gray-600"}`,children:e.isFavorite?"★":"☆"}),(0,s.jsx)("button",{onClick:()=>K(e),className:"bg-gray-700 text-white px-2 py-1 rounded text-xs hover:bg-gray-600 transition-all duration-300",children:"Load"})]})]}),(0,s.jsx)("p",{className:"text-sm text-gray-300 mb-2 line-clamp-2",children:e.projectInput.projectIdea}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-1 mb-2",children:[(0,s.jsx)("span",{className:"bg-gray-700 text-xs px-2 py-1 rounded text-gray-300",children:e.projectInput.projectType}),(0,s.jsx)("span",{className:"bg-gray-700 text-xs px-2 py-1 rounded text-gray-300",children:e.projectInput.platform}),(0,s.jsx)("span",{className:"bg-gray-700 text-xs px-2 py-1 rounded text-gray-300",children:e.projectInput.complexity})]})]},e.id))})]})}),(0,s.jsx)(v,{isOpen:N,onClose:()=>E(!1),settings:z,onSettingsChange:O,recommendedModel:m.getModelRecommendation(e),projectInput:e,generatedPrompt:i}),(0,s.jsx)(ol,{isOpen:D,onClose:()=>L(!1),onLoadTemplate:e=>{t(t=>({...t,...e})),V(`Loaded advanced template: ${e.projectName}`,"success")}}),M&&(0,s.jsx)("div",{className:`fixed top-4 right-4 z-50 px-4 py-2 rounded-lg transition-all duration-300 animate-slide-up ${"success"===M.type?"bg-white border border-gray-300 text-black":"error"===M.type?"bg-gray-900 border border-gray-600 text-white":"bg-gray-800 border border-gray-600 text-white"}`,children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-sm font-medium",children:M.message}),(0,s.jsx)("button",{onClick:()=>I(null),className:"text-xs opacity-70 hover:opacity-100 transition-opacity",children:"✕"})]})})]})]})}},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return x},MissingStaticPage:function(){return y},NormalizeError:function(){return g},PageNotFoundError:function(){return f},SP:function(){return p},ST:function(){return u},WEB_VITALS:function(){return i},execOnce:function(){return a},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return o},isAbsoluteUrl:function(){return r},isResSent:function(){return c},loadGetInitialProps:function(){return m},normalizeRepeatedSlashes:function(){return d},stringifyError:function(){return v}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function a(e){let t,i=!1;return function(){for(var a=arguments.length,n=Array(a),r=0;r<a;r++)n[r]=arguments[r];return i||(i=!0,t=e(...n)),t}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,r=e=>n.test(e);function s(){let{protocol:e,hostname:t,port:i}=window.location;return e+"//"+t+(i?":"+i:"")}function o(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function d(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function m(e,t){let i=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await m(t.Component,t.ctx)}:{};let a=await e.getInitialProps(t);if(i&&c(i))return a;if(!a)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+a+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return a}let p="undefined"!=typeof performance,u=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class g extends Error{}class f extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class x extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},4963:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let a=i(687),n=i(3210),r=i(6780),s=i(4777);function o(e){return{default:e&&"default"in e?e.default:e}}let l={loader:()=>Promise.resolve(o(()=>null)),loading:null,ssr:!0},c=function(e){let t={...l,...e},i=(0,n.lazy)(()=>t.loader().then(o)),c=t.loading;function d(e){let o=c?(0,a.jsx)(c,{isLoading:!0,pastDelay:!0,error:null}):null,l=!t.ssr||!!t.loading,d=l?n.Suspense:n.Fragment,m=t.ssr?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s.PreloadChunks,{moduleIds:t.modules}),(0,a.jsx)(i,{...e})]}):(0,a.jsx)(r.BailoutToCSR,{reason:"next/dynamic",children:(0,a.jsx)(i,{...e})});return(0,a.jsx)(d,{...l?{fallback:o}:{},children:m})}return d.displayName="LoadableComponent",d}},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var i=function(e){for(var t=[],i=0;i<e.length;){var a=e[i];if("*"===a||"+"===a||"?"===a){t.push({type:"MODIFIER",index:i,value:e[i++]});continue}if("\\"===a){t.push({type:"ESCAPED_CHAR",index:i++,value:e[i++]});continue}if("{"===a){t.push({type:"OPEN",index:i,value:e[i++]});continue}if("}"===a){t.push({type:"CLOSE",index:i,value:e[i++]});continue}if(":"===a){for(var n="",r=i+1;r<e.length;){var s=e.charCodeAt(r);if(s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122||95===s){n+=e[r++];continue}break}if(!n)throw TypeError("Missing parameter name at "+i);t.push({type:"NAME",index:i,value:n}),i=r;continue}if("("===a){var o=1,l="",r=i+1;if("?"===e[r])throw TypeError('Pattern cannot start with "?" at '+r);for(;r<e.length;){if("\\"===e[r]){l+=e[r++]+e[r++];continue}if(")"===e[r]){if(0==--o){r++;break}}else if("("===e[r]&&(o++,"?"!==e[r+1]))throw TypeError("Capturing groups are not allowed at "+r);l+=e[r++]}if(o)throw TypeError("Unbalanced pattern at "+i);if(!l)throw TypeError("Missing pattern at "+i);t.push({type:"PATTERN",index:i,value:l}),i=r;continue}t.push({type:"CHAR",index:i,value:e[i++]})}return t.push({type:"END",index:i,value:""}),t}(e),a=t.prefixes,r=void 0===a?"./":a,s="[^"+n(t.delimiter||"/#?")+"]+?",o=[],l=0,c=0,d="",m=function(e){if(c<i.length&&i[c].type===e)return i[c++].value},p=function(e){var t=m(e);if(void 0!==t)return t;var a=i[c];throw TypeError("Unexpected "+a.type+" at "+a.index+", expected "+e)},u=function(){for(var e,t="";e=m("CHAR")||m("ESCAPED_CHAR");)t+=e;return t};c<i.length;){var h=m("CHAR"),g=m("NAME"),f=m("PATTERN");if(g||f){var y=h||"";-1===r.indexOf(y)&&(d+=y,y=""),d&&(o.push(d),d=""),o.push({name:g||l++,prefix:y,suffix:"",pattern:f||s,modifier:m("MODIFIER")||""});continue}var x=h||m("ESCAPED_CHAR");if(x){d+=x;continue}if(d&&(o.push(d),d=""),m("OPEN")){var y=u(),v=m("NAME")||"",b=m("PATTERN")||"",w=u();p("CLOSE"),o.push({name:v||(b?l++:""),pattern:v&&!b?s:b,prefix:y,suffix:w,modifier:m("MODIFIER")||""});continue}p("END")}return o}function i(e,t){void 0===t&&(t={});var i=r(t),a=t.encode,n=void 0===a?function(e){return e}:a,s=t.validate,o=void 0===s||s,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",i)});return function(t){for(var i="",a=0;a<e.length;a++){var r=e[a];if("string"==typeof r){i+=r;continue}var s=t?t[r.name]:void 0,c="?"===r.modifier||"*"===r.modifier,d="*"===r.modifier||"+"===r.modifier;if(Array.isArray(s)){if(!d)throw TypeError('Expected "'+r.name+'" to not repeat, but got an array');if(0===s.length){if(c)continue;throw TypeError('Expected "'+r.name+'" to not be empty')}for(var m=0;m<s.length;m++){var p=n(s[m],r);if(o&&!l[a].test(p))throw TypeError('Expected all "'+r.name+'" to match "'+r.pattern+'", but got "'+p+'"');i+=r.prefix+p+r.suffix}continue}if("string"==typeof s||"number"==typeof s){var p=n(String(s),r);if(o&&!l[a].test(p))throw TypeError('Expected "'+r.name+'" to match "'+r.pattern+'", but got "'+p+'"');i+=r.prefix+p+r.suffix;continue}if(!c){var u=d?"an array":"a string";throw TypeError('Expected "'+r.name+'" to be '+u)}}return i}}function a(e,t,i){void 0===i&&(i={});var a=i.decode,n=void 0===a?function(e){return e}:a;return function(i){var a=e.exec(i);if(!a)return!1;for(var r=a[0],s=a.index,o=Object.create(null),l=1;l<a.length;l++)!function(e){if(void 0!==a[e]){var i=t[e-1];"*"===i.modifier||"+"===i.modifier?o[i.name]=a[e].split(i.prefix+i.suffix).map(function(e){return n(e,i)}):o[i.name]=n(a[e],i)}}(l);return{path:r,index:s,params:o}}}function n(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function r(e){return e&&e.sensitive?"":"i"}function s(e,t,i){void 0===i&&(i={});for(var a=i.strict,s=void 0!==a&&a,o=i.start,l=i.end,c=i.encode,d=void 0===c?function(e){return e}:c,m="["+n(i.endsWith||"")+"]|$",p="["+n(i.delimiter||"/#?")+"]",u=void 0===o||o?"^":"",h=0;h<e.length;h++){var g=e[h];if("string"==typeof g)u+=n(d(g));else{var f=n(d(g.prefix)),y=n(d(g.suffix));if(g.pattern)if(t&&t.push(g),f||y)if("+"===g.modifier||"*"===g.modifier){var x="*"===g.modifier?"?":"";u+="(?:"+f+"((?:"+g.pattern+")(?:"+y+f+"(?:"+g.pattern+"))*)"+y+")"+x}else u+="(?:"+f+"("+g.pattern+")"+y+")"+g.modifier;else u+="("+g.pattern+")"+g.modifier;else u+="(?:"+f+y+")"+g.modifier}}if(void 0===l||l)s||(u+=p+"?"),u+=i.endsWith?"(?="+m+")":"$";else{var v=e[e.length-1],b="string"==typeof v?p.indexOf(v[v.length-1])>-1:void 0===v;s||(u+="(?:"+p+"(?="+m+"))?"),b||(u+="(?="+p+"|"+m+")")}return new RegExp(u,r(i))}function o(t,i,a){if(t instanceof RegExp){if(!i)return t;var n=t.source.match(/\((?!\?)/g);if(n)for(var l=0;l<n.length;l++)i.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return o(e,i,a).source}).join("|")+")",r(a)):s(e(t,a),i,a)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,a){return i(e(t,a),a)},t.tokensToFunction=i,t.match=function(e,t){var i=[];return a(o(e,i,t),i,t)},t.regexpToFunction=a,t.tokensToRegexp=s,t.pathToRegexp=o})(),e.exports=t})()},5526:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{compileNonPath:function(){return d},matchHas:function(){return c},parseDestination:function(){return m},prepareDestination:function(){return p}});let a=i(5362),n=i(3293),r=i(6759),s=i(1437),o=i(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,t,i,a){void 0===i&&(i=[]),void 0===a&&(a=[]);let n={},r=i=>{let a,r=i.key;switch(i.type){case"header":r=r.toLowerCase(),a=e.headers[r];break;case"cookie":a="cookies"in e?e.cookies[i.key]:(0,o.getCookieParser)(e.headers)()[i.key];break;case"query":a=t[r];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};a=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!i.value&&a)return n[function(e){let t="";for(let i=0;i<e.length;i++){let a=e.charCodeAt(i);(a>64&&a<91||a>96&&a<123)&&(t+=e[i])}return t}(r)]=a,!0;if(a){let e=RegExp("^"+i.value+"$"),t=Array.isArray(a)?a.slice(-1)[0].match(e):a.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{n[e]=t.groups[e]}):"host"===i.type&&t[0]&&(n.host=t[0])),!0}return!1};return!(!i.every(e=>r(e))||a.some(e=>r(e)))&&n}function d(e,t){if(!e.includes(":"))return e;for(let i of Object.keys(t))e.includes(":"+i)&&(e=e.replace(RegExp(":"+i+"\\*","g"),":"+i+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+i+"\\?","g"),":"+i+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+i+"\\+","g"),":"+i+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+i+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+i));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,a.compile)("/"+e,{validate:!1})(t).slice(1)}function m(e){let t=e.destination;for(let i of Object.keys({...e.params,...e.query}))i&&(t=t.replace(RegExp(":"+(0,n.escapeStringRegexp)(i),"g"),"__ESC_COLON_"+i));let i=(0,r.parseUrl)(t),a=i.pathname;a&&(a=l(a));let s=i.href;s&&(s=l(s));let o=i.hostname;o&&(o=l(o));let c=i.hash;return c&&(c=l(c)),{...i,pathname:a,hostname:o,href:s,hash:c}}function p(e){let t,i,n=Object.assign({},e.query),r=m(e),{hostname:o,query:c}=r,p=r.pathname;r.hash&&(p=""+p+r.hash);let u=[],h=[];for(let e of((0,a.pathToRegexp)(p,h),h))u.push(e.name);if(o){let e=[];for(let t of((0,a.pathToRegexp)(o,e),e))u.push(t.name)}let g=(0,a.compile)(p,{validate:!1});for(let[i,n]of(o&&(t=(0,a.compile)(o,{validate:!1})),Object.entries(c)))Array.isArray(n)?c[i]=n.map(t=>d(l(t),e.params)):"string"==typeof n&&(c[i]=d(l(n),e.params));let f=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!f.some(e=>u.includes(e)))for(let t of f)t in c||(c[t]=e.params[t]);if((0,s.isInterceptionRouteAppPath)(p))for(let t of p.split("/")){let i=s.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(i){"(..)(..)"===i?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=i;break}}try{let[a,n]=(i=g(e.params)).split("#",2);t&&(r.hostname=t(e.params)),r.pathname=a,r.hash=(n?"#":"")+(n||""),delete r.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return r.query={...n,...r.query},{newUrl:i,destQuery:c,parsedDestination:r}}},5531:(e,t)=>{"use strict";function i(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return i}})},5840:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});let a=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\augment-prompt-generator\\\\src\\\\components\\\\PromptGeneratorApp.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\test\\augment-prompt-generator\\src\\components\\PromptGeneratorApp.tsx","default")},6341:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{getPreviouslyRevalidatedTags:function(){return y},getUtils:function(){return f},interpolateDynamicPath:function(){return h},normalizeDynamicRouteParams:function(){return g},normalizeVercelUrl:function(){return u}});let a=i(9551),n=i(1959),r=i(2437),s=i(4396),o=i(8034),l=i(5526),c=i(2887),d=i(4722),m=i(6143),p=i(7912);function u(e,t,i){let n=(0,a.parse)(e.url,!0);for(let e of(delete n.search,Object.keys(n.query))){let a=e!==m.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(m.NEXT_QUERY_PARAM_PREFIX),r=e!==m.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(m.NEXT_INTERCEPTION_MARKER_PREFIX);(a||r||t.includes(e)||i&&Object.keys(i.groups).includes(e))&&delete n.query[e]}e.url=(0,a.format)(n)}function h(e,t,i){if(!i)return e;for(let a of Object.keys(i.groups)){let n,{optional:r,repeat:s}=i.groups[a],o=`[${s?"...":""}${a}]`;r&&(o=`[${o}]`);let l=t[a];n=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(o,n)}return e}function g(e,t,i,a){let n={};for(let r of Object.keys(t.groups)){let s=e[r];"string"==typeof s?s=(0,d.normalizeRscURL)(s):Array.isArray(s)&&(s=s.map(d.normalizeRscURL));let o=i[r],l=t.groups[r].optional;if((Array.isArray(o)?o.some(e=>Array.isArray(s)?s.some(t=>t.includes(e)):null==s?void 0:s.includes(e)):null==s?void 0:s.includes(o))||void 0===s&&!(l&&a))return{params:{},hasValidParams:!1};l&&(!s||Array.isArray(s)&&1===s.length&&("index"===s[0]||s[0]===`[[...${r}]]`))&&(s=void 0,delete e[r]),s&&"string"==typeof s&&t.groups[r].repeat&&(s=s.split("/")),s&&(n[r]=s)}return{params:n,hasValidParams:!0}}function f({page:e,i18n:t,basePath:i,rewrites:a,pageIsDynamic:d,trailingSlash:m,caseSensitive:f}){let y,x,v;return d&&(y=(0,s.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),v=(x=(0,o.getRouteMatcher)(y))(e)),{handleRewrites:function(s,o){let p={},u=o.pathname,h=a=>{let c=(0,r.getPathMatch)(a.source+(m?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!f});if(!o.pathname)return!1;let h=c(o.pathname);if((a.has||a.missing)&&h){let e=(0,l.matchHas)(s,o.query,a.has,a.missing);e?Object.assign(h,e):h=!1}if(h){let{parsedDestination:r,destQuery:s}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:a.destination,params:h,query:o.query});if(r.protocol)return!0;if(Object.assign(p,s,h),Object.assign(o.query,r.query),delete r.query,Object.assign(o,r),!(u=o.pathname))return!1;if(i&&(u=u.replace(RegExp(`^${i}`),"")||"/"),t){let e=(0,n.normalizeLocalePath)(u,t.locales);u=e.pathname,o.query.nextInternalLocale=e.detectedLocale||h.nextInternalLocale}if(u===e)return!0;if(d&&x){let e=x(u);if(e)return o.query={...o.query,...e},!0}}return!1};for(let e of a.beforeFiles||[])h(e);if(u!==e){let t=!1;for(let e of a.afterFiles||[])if(t=h(e))break;if(!t&&!(()=>{let t=(0,c.removeTrailingSlash)(u||"");return t===(0,c.removeTrailingSlash)(e)||(null==x?void 0:x(t))})()){for(let e of a.fallback||[])if(t=h(e))break}}return p},defaultRouteRegex:y,dynamicRouteMatcher:x,defaultRouteMatches:v,getParamsFromRouteMatches:function(e){if(!y)return null;let{groups:t,routeKeys:i}=y,a=(0,o.getRouteMatcher)({re:{exec:e=>{let a=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(a)){let i=(0,p.normalizeNextQueryParam)(e);i&&(a[i]=t,delete a[e])}let n={};for(let e of Object.keys(i)){let r=i[e];if(!r)continue;let s=t[r],o=a[e];if(!s.optional&&!o)return null;n[s.pos]=o}return n}},groups:t})(e);return a||null},normalizeDynamicRouteParams:(e,t)=>y&&v?g(e,y,v,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>u(e,t,y),interpolateDynamicPath:(e,t)=>h(e,t,y)}}function y(e,t){return"string"==typeof e[m.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[m.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[m.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,i){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var n={},r=t.split(a),s=(i||{}).decode||e,o=0;o<r.length;o++){var l=r[o],c=l.indexOf("=");if(!(c<0)){var d=l.substr(0,c).trim(),m=l.substr(++c,l.length).trim();'"'==m[0]&&(m=m.slice(1,-1)),void 0==n[d]&&(n[d]=function(e,t){try{return t(e)}catch(t){return e}}(m,s))}}return n},t.serialize=function(e,t,a){var r=a||{},s=r.encode||i;if("function"!=typeof s)throw TypeError("option encode is invalid");if(!n.test(e))throw TypeError("argument name is invalid");var o=s(t);if(o&&!n.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=r.maxAge){var c=r.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(r.domain){if(!n.test(r.domain))throw TypeError("option domain is invalid");l+="; Domain="+r.domain}if(r.path){if(!n.test(r.path))throw TypeError("option path is invalid");l+="; Path="+r.path}if(r.expires){if("function"!=typeof r.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(l+="; HttpOnly"),r.secure&&(l+="; Secure"),r.sameSite)switch("string"==typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,i=encodeURIComponent,a=/; */,n=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6515:()=>{},6759:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return r}});let a=i(2785),n=i(3736);function r(e){if(e.startsWith("/"))return(0,n.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,a.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},6780:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return n}});let a=i(1208);function n(e){let{reason:t,children:i}=e;throw Object.defineProperty(new a.BailoutToCSRError(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},6821:(e,t,i)=>{"use strict";i.d(t,{default:()=>o});var a=i(687),n=i(9587),r=i.n(n);let s=({children:e})=>(0,a.jsx)(a.Fragment,{children:e}),o=r()(()=>Promise.resolve(s),{ssr:!1,loading:()=>(0,a.jsx)("div",{className:"min-h-screen bg-black py-8 px-4",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("header",{className:"text-center mb-8",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-white mb-2",children:"\uD83E\uDD16 AI Prompt Generator for Augment Agent"}),(0,a.jsx)("p",{className:"text-lg text-gray-300",children:"Loading AI-powered prompt generation..."})]}),(0,a.jsx)("div",{className:"flex justify-center items-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white"})})]})})})},7463:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});let a=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\test\\\\augment-prompt-generator\\\\src\\\\components\\\\NoSSR.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\test\\augment-prompt-generator\\src\\components\\NoSSR.tsx","default")},8034:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return n}});let a=i(4827);function n(e){let{re:t,groups:i}=e;return e=>{let n=t.exec(e);if(!n)return!1;let r=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new a.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},s={};for(let[e,t]of Object.entries(i)){let i=n[t.pos];void 0!==i&&(t.repeat?s[e]=i.split("/").map(e=>r(e)):s[e]=r(i))}return s}}},8212:(e,t,i)=>{"use strict";function a(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:a}=i(6415);return a(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return a}})},8304:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return o},STATIC_METADATA_IMAGES:function(){return s},getExtensionRegexString:function(){return l},isMetadataPage:function(){return m},isMetadataRoute:function(){return p},isMetadataRouteFile:function(){return c},isStaticMetadataRoute:function(){return d}});let a=i(2958),n=i(4722),r=i(554),s={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function c(e,t,i){let n=(i?"":"?")+"$",r=`\\d?${i?"":"(-\\w{6})?"}`,o=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${n}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${n}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${n}`),RegExp(`[\\\\/]${s.icon.filename}${r}${l(s.icon.extensions,t)}${n}`),RegExp(`[\\\\/]${s.apple.filename}${r}${l(s.apple.extensions,t)}${n}`),RegExp(`[\\\\/]${s.openGraph.filename}${r}${l(s.openGraph.extensions,t)}${n}`),RegExp(`[\\\\/]${s.twitter.filename}${r}${l(s.twitter.extensions,t)}${n}`)],c=(0,a.normalizePathSep)(e);return o.some(e=>e.test(c))}function d(e){let t=e.replace(/\/route$/,"");return(0,r.isAppRouteRoute)(e)&&c(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function m(e){return!(0,r.isAppRouteRoute)(e)&&c(e,[],!1)}function p(e){let t=(0,n.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,r.isAppRouteRoute)(e)&&c(t,[],!1)}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9587:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}});let a=i(4985)._(i(4963));function n(e,t){var i;let n={};"function"==typeof e&&(n.loader=e);let r={...n,...t};return(0,a.default)({...r,modules:null==(i=r.loadableGenerated)?void 0:i.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9703:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6346,23)),Promise.resolve().then(i.t.bind(i,7924,23)),Promise.resolve().then(i.t.bind(i,5656,23)),Promise.resolve().then(i.t.bind(i,99,23)),Promise.resolve().then(i.t.bind(i,8243,23)),Promise.resolve().then(i.t.bind(i,8827,23)),Promise.resolve().then(i.t.bind(i,2763,23)),Promise.resolve().then(i.t.bind(i,7173,23))}};var t=require("../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[447,145],()=>i(2e3));module.exports=a})();