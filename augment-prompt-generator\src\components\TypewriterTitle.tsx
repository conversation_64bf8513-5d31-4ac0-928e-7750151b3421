"use client";

/**
 * @author: @dorian_baffier
 * @description: Typewriter
 * @version: 1.0.0
 * @date: 2025-06-26
 * @license: MIT
 * @website: https://kokonutui.com
 * @github: https://github.com/kokonut-labs/kokonutui
 */

import { motion, useAnimate } from "motion/react";
import { useEffect, useState } from "react";

interface TypewriterSequence {
    text: string;
    deleteAfter?: boolean;
    pauseAfter?: number;
}

interface TypewriterTitleProps {
    sequences?: TypewriterSequence[];
    typingSpeed?: number;
    startDelay?: number;
    autoLoop?: boolean;
    loopDelay?: number;
    className?: string;
}

export default function TypewriterTitle({
    sequences = [
        { text: "AI Prompt Generator", deleteAfter: true },
        { text: "Professional Prompts", deleteAfter: true },
        { text: "Augment Agent Ready", deleteAfter: false },
    ],
    typingSpeed = 75,
    startDelay = 500,
    autoLoop = true,
    loopDelay = 3000,
    className = "",
}: TypewriterTitleProps) {
    const [scope, animate] = useAnimate();
    const [hasRun, setHasRun] = useState(false);

    useEffect(() => {
        // Only run once when component mounts
        if (hasRun) return;
        let isActive = true;

        const typeText = async () => {
            const titleElement =
                scope.current?.querySelector("[data-typewriter]");
            if (!titleElement) return;

            // Mark as having run to prevent restarts
            setHasRun(true);

            // Reset the text content
            if (scope.current) {
                await animate(scope.current, { opacity: 1 });
            }
            titleElement.textContent = "";

            // Wait for initial delay
            await new Promise((resolve) => setTimeout(resolve, startDelay));

            // Process each sequence once
            for (const sequence of sequences) {
                if (!isActive) break;

                // Type out the sequence text
                for (let i = 0; i < sequence.text.length; i++) {
                    if (!isActive) break;
                    titleElement.textContent = sequence.text.slice(
                        0,
                        i + 1
                    );
                    await new Promise((resolve) =>
                        setTimeout(resolve, typingSpeed)
                    );
                }

                // Pause after typing if specified
                if (sequence.pauseAfter) {
                    await new Promise((resolve) =>
                        setTimeout(resolve, sequence.pauseAfter)
                    );
                }

                // Delete the text if specified
                if (sequence.deleteAfter) {
                    // Small pause before deleting
                    await new Promise((resolve) =>
                        setTimeout(resolve, 500)
                    );

                    for (let i = sequence.text.length; i > 0; i--) {
                        if (!isActive) break;
                        titleElement.textContent = sequence.text.slice(
                            0,
                            i
                        );
                        await new Promise((resolve) =>
                            setTimeout(resolve, typingSpeed / 2)
                        );
                    }
                }
            }

            // If autoLoop is enabled, continue looping
            if (autoLoop && isActive) {
                while (isActive) {
                    // Wait before starting next loop
                    await new Promise((resolve) => setTimeout(resolve, loopDelay));

                    if (!isActive) break;

                    // Reset and repeat
                    titleElement.textContent = "";

                    for (const sequence of sequences) {
                        if (!isActive) break;

                        // Type out the sequence text
                        for (let i = 0; i < sequence.text.length; i++) {
                            if (!isActive) break;
                            titleElement.textContent = sequence.text.slice(
                                0,
                                i + 1
                            );
                            await new Promise((resolve) =>
                                setTimeout(resolve, typingSpeed)
                            );
                        }

                        // Pause after typing if specified
                        if (sequence.pauseAfter) {
                            await new Promise((resolve) =>
                                setTimeout(resolve, sequence.pauseAfter)
                            );
                        }

                        // Delete the text if specified
                        if (sequence.deleteAfter) {
                            // Small pause before deleting
                            await new Promise((resolve) =>
                                setTimeout(resolve, 500)
                            );

                            for (let i = sequence.text.length; i > 0; i--) {
                                if (!isActive) break;
                                titleElement.textContent = sequence.text.slice(
                                    0,
                                    i
                                );
                                await new Promise((resolve) =>
                                    setTimeout(resolve, typingSpeed / 2)
                                );
                            }
                        }
                    }
                }
            }
        };

        typeText();

        // Cleanup function to stop the animation when component unmounts
        return () => {
            isActive = false;
        };
    }, []); // Empty dependency array - only run on mount

    return (
        <div className={`relative w-full max-w-4xl mx-auto py-8 ${className}`}>
            <div
                className="relative text-center z-10 flex flex-col items-center justify-center"
                ref={scope}
            >
                <motion.div
                    className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-mono text-white tracking-tight flex items-center gap-2"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                >
                    <span
                        data-typewriter
                        className="inline-block border-r-2 border-white animate-cursor pr-1"
                    >
                        {sequences[0].text}
                    </span>
                </motion.div>
            </div>
        </div>
    );
}
