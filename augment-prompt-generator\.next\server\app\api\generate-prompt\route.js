(()=>{var e={};e.id=618,e.ids=[618],e.modules={700:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>u});var o={};r.r(o),r.d(o,{POST:()=>c});var n=r(6559),s=r(8088),a=r(7719),i=r(2190);let p=process.env.OPENROUTER_API_KEY;async function c(e){try{if(!p)return i.NextResponse.json({error:"OpenRouter API key not configured"},{status:500});let t=await e.json(),r=`You are an expert prompt engineer specializing in creating optimized prompts for Augment Agent - the world's most advanced coding AI assistant with codebase context engine and task management capabilities.

Your task is to generate a comprehensive, professional prompt that:
1. Leverages Augment Agent's unique capabilities (codebase context, task management, code analysis)
2. Follows software development best practices
3. Includes step-by-step implementation approach
4. Covers the entire development lifecycle
5. Is specific to the project type and technology stack
6. Includes testing and deployment considerations

Generate a detailed, actionable prompt that will help developers build high-quality software with Augment Agent's assistance.`,o=`Generate an optimized prompt for Augment Agent to build the following project:

**Project Details:**
- Name: ${t.projectName}
- Type: ${t.projectType}
- Platform: ${t.platform}
- Complexity: ${t.complexity}
- Concept: ${t.projectIdea}
- Technologies: ${t.technologies.map(e=>e.name).join(", ")||"Not specified - suggest appropriate stack"}
- Features: ${t.features?.join(", ")||"Core functionality as described"}
- Additional Requirements: ${t.additionalRequirements||"None specified"}

**Requirements for the generated prompt:**
1. Start with a clear project description and goals
2. Include specific technical requirements and architecture considerations
3. Provide a detailed step-by-step implementation plan
4. Include Augment Agent specific instructions:
   - Use codebase-retrieval tool before making changes
   - Break down work into manageable tasks
   - Request code reviews and optimizations
   - Follow best practices for the chosen technology stack
5. Include comprehensive testing strategy
6. Cover deployment and DevOps considerations
7. Be professional, detailed, and actionable

Generate a prompt that will result in a high-quality, production-ready application when used with Augment Agent.`,n=await fetch("https://openrouter.ai/api/v1/chat/completions",{method:"POST",headers:{Authorization:`Bearer ${p}`,"Content-Type":"application/json","HTTP-Referer":"https://augment-prompt-generator.vercel.app","X-Title":"Augment Prompt Generator"},body:JSON.stringify({model:"deepseek/deepseek-chat-v3-0324:free",messages:[{role:"system",content:r},{role:"user",content:o}],temperature:.7,max_tokens:4e3,top_p:.9,frequency_penalty:0,presence_penalty:0})});if(!n.ok){let e=await n.text();return console.error("OpenRouter API error:",e),i.NextResponse.json({error:"Failed to generate prompt with AI"},{status:n.status})}let s=await n.json(),a=s.choices[0]?.message?.content;if(!a)return i.NextResponse.json({error:"No prompt generated"},{status:500});return i.NextResponse.json({prompt:a,model:"deepseek-chat-v3-0324",usage:s.usage})}catch(e){return console.error("Error generating prompt:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/generate-prompt/route",pathname:"/api/generate-prompt",filename:"route",bundlePath:"app/api/generate-prompt/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\test\\augment-prompt-generator\\src\\app\\api\\generate-prompt\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:l,workUnitAsyncStorage:u,serverHooks:m}=d;function g(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:u})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,580],()=>r(700));module.exports=o})();