{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/test/augment-prompt-generator/src/components/NoSSR.tsx"], "sourcesContent": ["'use client';\n\nimport dynamic from 'next/dynamic';\nimport { ReactNode } from 'react';\n\ninterface NoSSRProps {\n  children: ReactNode;\n  fallback?: ReactNode;\n}\n\nconst NoSSR = ({ children, fallback = null }: NoSSRProps) => {\n  return <>{children}</>;\n};\n\nexport default dynamic(() => Promise.resolve(NoSSR), {\n  ssr: false,\n  loading: () => (\n    <div className=\"min-h-screen bg-black py-8 px-4\">\n      <div className=\"max-w-6xl mx-auto\">\n        <header className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-white mb-2\">\n            🤖 AI Prompt Generator for Augment Agent\n          </h1>\n          <p className=\"text-lg text-gray-300\">\n            Loading AI-powered prompt generation...\n          </p>\n        </header>\n        <div className=\"flex justify-center items-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-white\"></div>\n        </div>\n      </div>\n    </div>\n  ),\n});\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUA,MAAM,QAAQ,CAAC,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAc;IACtD,qBAAO;kBAAG;;AACZ;KAFM;6CAIS,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,QAAE,IAAM,QAAQ,OAAO,CAAC,QAAQ;IACnD,KAAK;IACL,SAAS,kBACP,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CAGnD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/test/augment-prompt-generator/src/lib/technologies.ts"], "sourcesContent": ["import { Technology, TechnologyStack } from './types';\n\nexport const TECHNOLOGIES: Record<string, Technology[]> = {\n  frontend: [\n    { category: 'frontend', name: 'React', description: 'Popular JavaScript library for building user interfaces' },\n    { category: 'frontend', name: 'Next.js', description: 'React framework with SSR and static generation' },\n    { category: 'frontend', name: 'Vue.js', description: 'Progressive JavaScript framework' },\n    { category: 'frontend', name: 'Angular', description: 'TypeScript-based web application framework' },\n    { category: 'frontend', name: '<PERSON><PERSON><PERSON>', description: 'Compile-time optimized framework' },\n    { category: 'frontend', name: 'Vanilla JavaScript', description: 'Pure JavaScript without frameworks' },\n    { category: 'frontend', name: 'TypeScript', description: 'Typed superset of JavaScript' },\n  ],\n  \n  backend: [\n    { category: 'backend', name: 'Node.js', description: 'JavaScript runtime for server-side development' },\n    { category: 'backend', name: 'Express.js', description: 'Fast, unopinionated web framework for Node.js' },\n    { category: 'backend', name: 'Python/Django', description: 'High-level Python web framework' },\n    { category: 'backend', name: 'Python/Flask', description: 'Lightweight Python web framework' },\n    { category: 'backend', name: 'Python/FastAPI', description: 'Modern, fast Python API framework' },\n    { category: 'backend', name: 'Go', description: 'Efficient, compiled programming language' },\n    { category: 'backend', name: 'Rust', description: 'Systems programming language focused on safety' },\n    { category: 'backend', name: 'Java/Spring Boot', description: 'Enterprise Java framework' },\n    { category: 'backend', name: 'C#/.NET', description: 'Microsoft\\'s cross-platform framework' },\n  ],\n  \n  database: [\n    { category: 'database', name: 'PostgreSQL', description: 'Advanced open-source relational database' },\n    { category: 'database', name: 'MongoDB', description: 'NoSQL document database' },\n    { category: 'database', name: 'SQLite', description: 'Lightweight embedded database' },\n    { category: 'database', name: 'MySQL', description: 'Popular open-source relational database' },\n    { category: 'database', name: 'Redis', description: 'In-memory data structure store' },\n    { category: 'database', name: 'Supabase', description: 'Open-source Firebase alternative' },\n    { category: 'database', name: 'Firebase', description: 'Google\\'s mobile and web development platform' },\n  ],\n  \n  styling: [\n    { category: 'styling', name: 'Tailwind CSS', description: 'Utility-first CSS framework' },\n    { category: 'styling', name: 'CSS Modules', description: 'Localized CSS' },\n    { category: 'styling', name: 'Styled Components', description: 'CSS-in-JS library' },\n    { category: 'styling', name: 'SCSS/Sass', description: 'CSS preprocessor' },\n    { category: 'styling', name: 'Material-UI', description: 'React components implementing Material Design' },\n    { category: 'styling', name: 'Chakra UI', description: 'Simple, modular and accessible component library' },\n  ],\n  \n  'mobile-framework': [\n    { category: 'mobile-framework', name: 'React Native', description: 'Cross-platform mobile development with React' },\n    { category: 'mobile-framework', name: 'Flutter', description: 'Google\\'s UI toolkit for mobile, web, and desktop' },\n    { category: 'mobile-framework', name: 'Swift', description: 'Native iOS development language' },\n    { category: 'mobile-framework', name: 'Kotlin', description: 'Modern programming language for Android' },\n    { category: 'mobile-framework', name: 'Expo', description: 'Platform for universal React applications' },\n  ],\n  \n  'desktop-framework': [\n    { category: 'desktop-framework', name: 'Electron', description: 'Build desktop apps with web technologies' },\n    { category: 'desktop-framework', name: 'Tauri', description: 'Rust-based desktop app framework' },\n    { category: 'desktop-framework', name: 'Qt', description: 'Cross-platform application framework' },\n    { category: 'desktop-framework', name: 'WPF', description: 'Windows Presentation Foundation' },\n    { category: 'desktop-framework', name: 'JavaFX', description: 'Java platform for desktop applications' },\n  ],\n  \n  deployment: [\n    { category: 'deployment', name: 'Vercel', description: 'Platform for frontend frameworks and static sites' },\n    { category: 'deployment', name: 'Netlify', description: 'Platform for modern web projects' },\n    { category: 'deployment', name: 'AWS', description: 'Amazon Web Services cloud platform' },\n    { category: 'deployment', name: 'Google Cloud', description: 'Google\\'s cloud computing services' },\n    { category: 'deployment', name: 'Docker', description: 'Containerization platform' },\n    { category: 'deployment', name: 'Kubernetes', description: 'Container orchestration platform' },\n  ],\n  \n  testing: [\n    { category: 'testing', name: 'Jest', description: 'JavaScript testing framework' },\n    { category: 'testing', name: 'Vitest', description: 'Fast unit test framework' },\n    { category: 'testing', name: 'Cypress', description: 'End-to-end testing framework' },\n    { category: 'testing', name: 'Playwright', description: 'Cross-browser automation library' },\n    { category: 'testing', name: 'React Testing Library', description: 'Testing utilities for React components' },\n  ],\n  \n  authentication: [\n    { category: 'authentication', name: 'Auth0', description: 'Identity platform for developers' },\n    { category: 'authentication', name: 'Firebase Auth', description: 'Google\\'s authentication service' },\n    { category: 'authentication', name: 'NextAuth.js', description: 'Authentication for Next.js' },\n    { category: 'authentication', name: 'Supabase Auth', description: 'Open-source authentication' },\n    { category: 'authentication', name: 'JWT', description: 'JSON Web Tokens for stateless authentication' },\n  ],\n\n  'state-management': [\n    { category: 'state-management', name: 'Redux Toolkit', description: 'Modern Redux with simplified API' },\n    { category: 'state-management', name: 'Zustand', description: 'Lightweight state management solution' },\n    { category: 'state-management', name: 'Recoil', description: 'Experimental state management for React' },\n    { category: 'state-management', name: 'MobX', description: 'Reactive state management through observables' },\n    { category: 'state-management', name: 'Context API', description: 'React\\'s built-in state management' },\n    { category: 'state-management', name: 'Valtio', description: 'Proxy-based state management' },\n  ],\n\n  'api-tools': [\n    { category: 'api-tools', name: 'GraphQL', description: 'Query language for APIs' },\n    { category: 'api-tools', name: 'Apollo Client', description: 'Comprehensive GraphQL client' },\n    { category: 'api-tools', name: 'React Query', description: 'Data fetching and caching library' },\n    { category: 'api-tools', name: 'SWR', description: 'Data fetching with caching and revalidation' },\n    { category: 'api-tools', name: 'Axios', description: 'Promise-based HTTP client' },\n    { category: 'api-tools', name: 'Fetch API', description: 'Native browser API for HTTP requests' },\n    { category: 'api-tools', name: 'tRPC', description: 'End-to-end typesafe APIs' },\n  ],\n\n  monitoring: [\n    { category: 'monitoring', name: 'Sentry', description: 'Error tracking and performance monitoring' },\n    { category: 'monitoring', name: 'LogRocket', description: 'Session replay and logging' },\n    { category: 'monitoring', name: 'New Relic', description: 'Application performance monitoring' },\n    { category: 'monitoring', name: 'Datadog', description: 'Infrastructure and application monitoring' },\n    { category: 'monitoring', name: 'Prometheus', description: 'Open-source monitoring and alerting' },\n    { category: 'monitoring', name: 'Grafana', description: 'Analytics and monitoring dashboards' },\n  ],\n\n  'ci-cd': [\n    { category: 'ci-cd', name: 'GitHub Actions', description: 'CI/CD platform integrated with GitHub' },\n    { category: 'ci-cd', name: 'GitLab CI', description: 'Built-in CI/CD for GitLab' },\n    { category: 'ci-cd', name: 'Jenkins', description: 'Open-source automation server' },\n    { category: 'ci-cd', name: 'CircleCI', description: 'Cloud-based CI/CD platform' },\n    { category: 'ci-cd', name: 'Azure DevOps', description: 'Microsoft\\'s DevOps platform' },\n    { category: 'ci-cd', name: 'Travis CI', description: 'Hosted CI service for GitHub projects' },\n  ],\n\n  'version-control': [\n    { category: 'version-control', name: 'Git', description: 'Distributed version control system' },\n    { category: 'version-control', name: 'GitHub', description: 'Git hosting with collaboration features' },\n    { category: 'version-control', name: 'GitLab', description: 'DevOps platform with Git repository' },\n    { category: 'version-control', name: 'Bitbucket', description: 'Git solution for teams' },\n    { category: 'version-control', name: 'Azure Repos', description: 'Git repositories in Azure DevOps' },\n  ],\n\n  'package-managers': [\n    { category: 'package-managers', name: 'npm', description: 'Node.js package manager' },\n    { category: 'package-managers', name: 'Yarn', description: 'Fast, reliable package manager' },\n    { category: 'package-managers', name: 'pnpm', description: 'Efficient package manager with shared dependencies' },\n    { category: 'package-managers', name: 'Bun', description: 'Fast all-in-one JavaScript runtime and package manager' },\n  ],\n\n  'build-tools': [\n    { category: 'build-tools', name: 'Webpack', description: 'Module bundler for JavaScript applications' },\n    { category: 'build-tools', name: 'Vite', description: 'Fast build tool for modern web projects' },\n    { category: 'build-tools', name: 'Rollup', description: 'Module bundler for JavaScript libraries' },\n    { category: 'build-tools', name: 'Parcel', description: 'Zero-configuration build tool' },\n    { category: 'build-tools', name: 'esbuild', description: 'Extremely fast JavaScript bundler' },\n    { category: 'build-tools', name: 'Turbopack', description: 'Incremental bundler optimized for JavaScript and TypeScript' },\n  ],\n\n  'code-quality': [\n    { category: 'code-quality', name: 'ESLint', description: 'JavaScript linting utility' },\n    { category: 'code-quality', name: 'Prettier', description: 'Code formatter' },\n    { category: 'code-quality', name: 'Husky', description: 'Git hooks for code quality' },\n    { category: 'code-quality', name: 'lint-staged', description: 'Run linters on staged files' },\n    { category: 'code-quality', name: 'SonarQube', description: 'Code quality and security analysis' },\n    { category: 'code-quality', name: 'CodeClimate', description: 'Automated code review and quality analytics' },\n  ],\n\n  'documentation': [\n    { category: 'documentation', name: 'Storybook', description: 'Tool for building UI components in isolation' },\n    { category: 'documentation', name: 'Docusaurus', description: 'Documentation website generator' },\n    { category: 'documentation', name: 'GitBook', description: 'Documentation platform' },\n    { category: 'documentation', name: 'Notion', description: 'All-in-one workspace for documentation' },\n    { category: 'documentation', name: 'Confluence', description: 'Team collaboration and documentation' },\n    { category: 'documentation', name: 'JSDoc', description: 'API documentation generator for JavaScript' },\n  ],\n};\n\nexport const getDefaultTechStack = (projectType: string): TechnologyStack => {\n  const stacks: Record<string, TechnologyStack> = {\n    'web-application': {\n      frontend: [TECHNOLOGIES.frontend[1]], // Next.js\n      styling: [TECHNOLOGIES.styling[0]], // Tailwind CSS\n      database: [TECHNOLOGIES.database[5]], // Supabase\n      deployment: [TECHNOLOGIES.deployment[0]], // Vercel\n      testing: [TECHNOLOGIES.testing[0]], // Jest\n    },\n    'mobile-application': {\n      'mobile-framework': [TECHNOLOGIES['mobile-framework'][0]], // React Native\n      database: [TECHNOLOGIES.database[6]], // Firebase\n      testing: [TECHNOLOGIES.testing[0]], // Jest\n    },\n    'api-backend': {\n      backend: [TECHNOLOGIES.backend[0]], // Node.js\n      database: [TECHNOLOGIES.database[0]], // PostgreSQL\n      deployment: [TECHNOLOGIES.deployment[2]], // AWS\n      testing: [TECHNOLOGIES.testing[0]], // Jest\n    },\n  };\n  \n  return stacks[projectType] || {};\n};\n"], "names": [], "mappings": ";;;;AAEO,MAAM,eAA6C;IACxD,UAAU;QACR;YAAE,UAAU;YAAY,MAAM;YAAS,aAAa;QAA0D;QAC9G;YAAE,UAAU;YAAY,MAAM;YAAW,aAAa;QAAiD;QACvG;YAAE,UAAU;YAAY,MAAM;YAAU,aAAa;QAAmC;QACxF;YAAE,UAAU;YAAY,MAAM;YAAW,aAAa;QAA6C;QACnG;YAAE,UAAU;YAAY,MAAM;YAAU,aAAa;QAAmC;QACxF;YAAE,UAAU;YAAY,MAAM;YAAsB,aAAa;QAAqC;QACtG;YAAE,UAAU;YAAY,MAAM;YAAc,aAAa;QAA+B;KACzF;IAED,SAAS;QACP;YAAE,UAAU;YAAW,MAAM;YAAW,aAAa;QAAiD;QACtG;YAAE,UAAU;YAAW,MAAM;YAAc,aAAa;QAAgD;QACxG;YAAE,UAAU;YAAW,MAAM;YAAiB,aAAa;QAAkC;QAC7F;YAAE,UAAU;YAAW,MAAM;YAAgB,aAAa;QAAmC;QAC7F;YAAE,UAAU;YAAW,MAAM;YAAkB,aAAa;QAAoC;QAChG;YAAE,UAAU;YAAW,MAAM;YAAM,aAAa;QAA2C;QAC3F;YAAE,UAAU;YAAW,MAAM;YAAQ,aAAa;QAAiD;QACnG;YAAE,UAAU;YAAW,MAAM;YAAoB,aAAa;QAA4B;QAC1F;YAAE,UAAU;YAAW,MAAM;YAAW,aAAa;QAAwC;KAC9F;IAED,UAAU;QACR;YAAE,UAAU;YAAY,MAAM;YAAc,aAAa;QAA2C;QACpG;YAAE,UAAU;YAAY,MAAM;YAAW,aAAa;QAA0B;QAChF;YAAE,UAAU;YAAY,MAAM;YAAU,aAAa;QAAgC;QACrF;YAAE,UAAU;YAAY,MAAM;YAAS,aAAa;QAA0C;QAC9F;YAAE,UAAU;YAAY,MAAM;YAAS,aAAa;QAAiC;QACrF;YAAE,UAAU;YAAY,MAAM;YAAY,aAAa;QAAmC;QAC1F;YAAE,UAAU;YAAY,MAAM;YAAY,aAAa;QAAgD;KACxG;IAED,SAAS;QACP;YAAE,UAAU;YAAW,MAAM;YAAgB,aAAa;QAA8B;QACxF;YAAE,UAAU;YAAW,MAAM;YAAe,aAAa;QAAgB;QACzE;YAAE,UAAU;YAAW,MAAM;YAAqB,aAAa;QAAoB;QACnF;YAAE,UAAU;YAAW,MAAM;YAAa,aAAa;QAAmB;QAC1E;YAAE,UAAU;YAAW,MAAM;YAAe,aAAa;QAAgD;QACzG;YAAE,UAAU;YAAW,MAAM;YAAa,aAAa;QAAmD;KAC3G;IAED,oBAAoB;QAClB;YAAE,UAAU;YAAoB,MAAM;YAAgB,aAAa;QAA+C;QAClH;YAAE,UAAU;YAAoB,MAAM;YAAW,aAAa;QAAoD;QAClH;YAAE,UAAU;YAAoB,MAAM;YAAS,aAAa;QAAkC;QAC9F;YAAE,UAAU;YAAoB,MAAM;YAAU,aAAa;QAA0C;QACvG;YAAE,UAAU;YAAoB,MAAM;YAAQ,aAAa;QAA4C;KACxG;IAED,qBAAqB;QACnB;YAAE,UAAU;YAAqB,MAAM;YAAY,aAAa;QAA2C;QAC3G;YAAE,UAAU;YAAqB,MAAM;YAAS,aAAa;QAAmC;QAChG;YAAE,UAAU;YAAqB,MAAM;YAAM,aAAa;QAAuC;QACjG;YAAE,UAAU;YAAqB,MAAM;YAAO,aAAa;QAAkC;QAC7F;YAAE,UAAU;YAAqB,MAAM;YAAU,aAAa;QAAyC;KACxG;IAED,YAAY;QACV;YAAE,UAAU;YAAc,MAAM;YAAU,aAAa;QAAoD;QAC3G;YAAE,UAAU;YAAc,MAAM;YAAW,aAAa;QAAmC;QAC3F;YAAE,UAAU;YAAc,MAAM;YAAO,aAAa;QAAqC;QACzF;YAAE,UAAU;YAAc,MAAM;YAAgB,aAAa;QAAqC;QAClG;YAAE,UAAU;YAAc,MAAM;YAAU,aAAa;QAA4B;QACnF;YAAE,UAAU;YAAc,MAAM;YAAc,aAAa;QAAmC;KAC/F;IAED,SAAS;QACP;YAAE,UAAU;YAAW,MAAM;YAAQ,aAAa;QAA+B;QACjF;YAAE,UAAU;YAAW,MAAM;YAAU,aAAa;QAA2B;QAC/E;YAAE,UAAU;YAAW,MAAM;YAAW,aAAa;QAA+B;QACpF;YAAE,UAAU;YAAW,MAAM;YAAc,aAAa;QAAmC;QAC3F;YAAE,UAAU;YAAW,MAAM;YAAyB,aAAa;QAAyC;KAC7G;IAED,gBAAgB;QACd;YAAE,UAAU;YAAkB,MAAM;YAAS,aAAa;QAAmC;QAC7F;YAAE,UAAU;YAAkB,MAAM;YAAiB,aAAa;QAAmC;QACrG;YAAE,UAAU;YAAkB,MAAM;YAAe,aAAa;QAA6B;QAC7F;YAAE,UAAU;YAAkB,MAAM;YAAiB,aAAa;QAA6B;QAC/F;YAAE,UAAU;YAAkB,MAAM;YAAO,aAAa;QAA+C;KACxG;IAED,oBAAoB;QAClB;YAAE,UAAU;YAAoB,MAAM;YAAiB,aAAa;QAAmC;QACvG;YAAE,UAAU;YAAoB,MAAM;YAAW,aAAa;QAAwC;QACtG;YAAE,UAAU;YAAoB,MAAM;YAAU,aAAa;QAA0C;QACvG;YAAE,UAAU;YAAoB,MAAM;YAAQ,aAAa;QAAgD;QAC3G;YAAE,UAAU;YAAoB,MAAM;YAAe,aAAa;QAAqC;QACvG;YAAE,UAAU;YAAoB,MAAM;YAAU,aAAa;QAA+B;KAC7F;IAED,aAAa;QACX;YAAE,UAAU;YAAa,MAAM;YAAW,aAAa;QAA0B;QACjF;YAAE,UAAU;YAAa,MAAM;YAAiB,aAAa;QAA+B;QAC5F;YAAE,UAAU;YAAa,MAAM;YAAe,aAAa;QAAoC;QAC/F;YAAE,UAAU;YAAa,MAAM;YAAO,aAAa;QAA8C;QACjG;YAAE,UAAU;YAAa,MAAM;YAAS,aAAa;QAA4B;QACjF;YAAE,UAAU;YAAa,MAAM;YAAa,aAAa;QAAuC;QAChG;YAAE,UAAU;YAAa,MAAM;YAAQ,aAAa;QAA2B;KAChF;IAED,YAAY;QACV;YAAE,UAAU;YAAc,MAAM;YAAU,aAAa;QAA4C;QACnG;YAAE,UAAU;YAAc,MAAM;YAAa,aAAa;QAA6B;QACvF;YAAE,UAAU;YAAc,MAAM;YAAa,aAAa;QAAqC;QAC/F;YAAE,UAAU;YAAc,MAAM;YAAW,aAAa;QAA4C;QACpG;YAAE,UAAU;YAAc,MAAM;YAAc,aAAa;QAAsC;QACjG;YAAE,UAAU;YAAc,MAAM;YAAW,aAAa;QAAsC;KAC/F;IAED,SAAS;QACP;YAAE,UAAU;YAAS,MAAM;YAAkB,aAAa;QAAwC;QAClG;YAAE,UAAU;YAAS,MAAM;YAAa,aAAa;QAA4B;QACjF;YAAE,UAAU;YAAS,MAAM;YAAW,aAAa;QAAgC;QACnF;YAAE,UAAU;YAAS,MAAM;YAAY,aAAa;QAA6B;QACjF;YAAE,UAAU;YAAS,MAAM;YAAgB,aAAa;QAA+B;QACvF;YAAE,UAAU;YAAS,MAAM;YAAa,aAAa;QAAwC;KAC9F;IAED,mBAAmB;QACjB;YAAE,UAAU;YAAmB,MAAM;YAAO,aAAa;QAAqC;QAC9F;YAAE,UAAU;YAAmB,MAAM;YAAU,aAAa;QAA0C;QACtG;YAAE,UAAU;YAAmB,MAAM;YAAU,aAAa;QAAsC;QAClG;YAAE,UAAU;YAAmB,MAAM;YAAa,aAAa;QAAyB;QACxF;YAAE,UAAU;YAAmB,MAAM;YAAe,aAAa;QAAmC;KACrG;IAED,oBAAoB;QAClB;YAAE,UAAU;YAAoB,MAAM;YAAO,aAAa;QAA0B;QACpF;YAAE,UAAU;YAAoB,MAAM;YAAQ,aAAa;QAAiC;QAC5F;YAAE,UAAU;YAAoB,MAAM;YAAQ,aAAa;QAAqD;QAChH;YAAE,UAAU;YAAoB,MAAM;YAAO,aAAa;QAAyD;KACpH;IAED,eAAe;QACb;YAAE,UAAU;YAAe,MAAM;YAAW,aAAa;QAA6C;QACtG;YAAE,UAAU;YAAe,MAAM;YAAQ,aAAa;QAA0C;QAChG;YAAE,UAAU;YAAe,MAAM;YAAU,aAAa;QAA0C;QAClG;YAAE,UAAU;YAAe,MAAM;YAAU,aAAa;QAAgC;QACxF;YAAE,UAAU;YAAe,MAAM;YAAW,aAAa;QAAoC;QAC7F;YAAE,UAAU;YAAe,MAAM;YAAa,aAAa;QAA8D;KAC1H;IAED,gBAAgB;QACd;YAAE,UAAU;YAAgB,MAAM;YAAU,aAAa;QAA6B;QACtF;YAAE,UAAU;YAAgB,MAAM;YAAY,aAAa;QAAiB;QAC5E;YAAE,UAAU;YAAgB,MAAM;YAAS,aAAa;QAA6B;QACrF;YAAE,UAAU;YAAgB,MAAM;YAAe,aAAa;QAA8B;QAC5F;YAAE,UAAU;YAAgB,MAAM;YAAa,aAAa;QAAqC;QACjG;YAAE,UAAU;YAAgB,MAAM;YAAe,aAAa;QAA8C;KAC7G;IAED,iBAAiB;QACf;YAAE,UAAU;YAAiB,MAAM;YAAa,aAAa;QAA+C;QAC5G;YAAE,UAAU;YAAiB,MAAM;YAAc,aAAa;QAAkC;QAChG;YAAE,UAAU;YAAiB,MAAM;YAAW,aAAa;QAAyB;QACpF;YAAE,UAAU;YAAiB,MAAM;YAAU,aAAa;QAAyC;QACnG;YAAE,UAAU;YAAiB,MAAM;YAAc,aAAa;QAAuC;QACrG;YAAE,UAAU;YAAiB,MAAM;YAAS,aAAa;QAA6C;KACvG;AACH;AAEO,MAAM,sBAAsB,CAAC;IAClC,MAAM,SAA0C;QAC9C,mBAAmB;YACjB,UAAU;gBAAC,aAAa,QAAQ,CAAC,EAAE;aAAC;YACpC,SAAS;gBAAC,aAAa,OAAO,CAAC,EAAE;aAAC;YAClC,UAAU;gBAAC,aAAa,QAAQ,CAAC,EAAE;aAAC;YACpC,YAAY;gBAAC,aAAa,UAAU,CAAC,EAAE;aAAC;YACxC,SAAS;gBAAC,aAAa,OAAO,CAAC,EAAE;aAAC;QACpC;QACA,sBAAsB;YACpB,oBAAoB;gBAAC,YAAY,CAAC,mBAAmB,CAAC,EAAE;aAAC;YACzD,UAAU;gBAAC,aAAa,QAAQ,CAAC,EAAE;aAAC;YACpC,SAAS;gBAAC,aAAa,OAAO,CAAC,EAAE;aAAC;QACpC;QACA,eAAe;YACb,SAAS;gBAAC,aAAa,OAAO,CAAC,EAAE;aAAC;YAClC,UAAU;gBAAC,aAAa,QAAQ,CAAC,EAAE;aAAC;YACpC,YAAY;gBAAC,aAAa,UAAU,CAAC,EAAE;aAAC;YACxC,SAAS;gBAAC,aAAa,OAAO,CAAC,EAAE;aAAC;QACpC;IACF;IAEA,OAAO,MAAM,CAAC,YAAY,IAAI,CAAC;AACjC", "debugId": null}}, {"offset": {"line": 725, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/test/augment-prompt-generator/src/lib/ai-service.ts"], "sourcesContent": ["import { ProjectInput } from './types';\n\nexport interface AIModel {\n  id: string;\n  name: string;\n  provider: string;\n  description: string;\n  maxTokens: number;\n  costPer1kTokens: number;\n  strengths: string[];\n  bestFor: string[];\n}\n\nexport const AI_MODELS: Record<string, AIModel> = {\n  'deepseek-chat': {\n    id: 'deepseek/deepseek-chat-v3-0324:free',\n    name: 'DeepSeek V3',\n    provider: 'DeepSeek',\n    description: 'Advanced reasoning and code generation with free access',\n    maxTokens: 8192,\n    costPer1kTokens: 0,\n    strengths: ['Code generation', 'Technical documentation', 'Complex reasoning', 'Mathematical thinking'],\n    bestFor: ['Development projects', 'Technical specifications', 'API documentation', 'Algorithm design']\n  },\n  'claude-sonnet': {\n    id: 'anthropic/claude-3-5-sonnet',\n    name: 'Claude Sonnet 4',\n    provider: 'Anthropic',\n    description: 'Excellent for creative and analytical tasks with structured thinking',\n    maxTokens: 8192,\n    costPer1kTokens: 3.0,\n    strengths: ['Creative writing', 'Analysis', 'Structured thinking', 'User experience design'],\n    bestFor: ['Content creation', 'Business planning', 'User experience', 'Complex project planning']\n  },\n  'gpt-4o': {\n    id: 'openai/gpt-4o',\n    name: 'ChatGPT (GPT-4o)',\n    provider: 'OpenAI',\n    description: 'Versatile multimodal AI with strong general capabilities',\n    maxTokens: 8192,\n    costPer1kTokens: 5.0,\n    strengths: ['General intelligence', 'Multimodal', 'Versatile', 'Creative solutions'],\n    bestFor: ['General projects', 'Multimodal apps', 'Versatile solutions', 'Creative development']\n  },\n  'gemini-pro': {\n    id: 'google/gemini-pro',\n    name: 'Google Gemini Pro',\n    provider: 'Google',\n    description: 'Large context window and multimodal capabilities',\n    maxTokens: 32768,\n    costPer1kTokens: 2.5,\n    strengths: ['Large context', 'Multimodal', 'Data analysis', 'Research synthesis'],\n    bestFor: ['Large documents', 'Data projects', 'Research tools', 'Complex analysis']\n  },\n  'cursor-small': {\n    id: 'cursor/cursor-small',\n    name: 'Cursor AI',\n    provider: 'Cursor',\n    description: 'IDE-integrated AI assistant for development workflows',\n    maxTokens: 4096,\n    costPer1kTokens: 1.0,\n    strengths: ['IDE integration', 'Code completion', 'Workflow optimization', 'Real-time assistance'],\n    bestFor: ['IDE workflows', 'Code completion', 'Development assistance', 'Project setup']\n  },\n  'perplexity-small': {\n    id: 'perplexity/llama-3.1-sonar-small-128k-online',\n    name: 'Perplexity AI',\n    provider: 'Perplexity',\n    description: 'Research-focused AI with real-time web access',\n    maxTokens: 8192,\n    costPer1kTokens: 1.5,\n    strengths: ['Research', 'Real-time data', 'Information synthesis', 'Fact-checking'],\n    bestFor: ['Research projects', 'Data gathering', 'Market analysis', 'Technical research']\n  },\n  'cohere-command': {\n    id: 'cohere/command-r-plus',\n    name: 'Cohere Command R+',\n    provider: 'Cohere',\n    description: 'Enterprise-focused AI with strong reasoning capabilities',\n    maxTokens: 8192,\n    costPer1kTokens: 2.0,\n    strengths: ['Enterprise solutions', 'Business logic', 'Structured output', 'Professional writing'],\n    bestFor: ['Enterprise projects', 'Business applications', 'Professional documentation', 'Workflow automation']\n  },\n  'huggingface-mixtral': {\n    id: 'mistralai/mixtral-8x7b-instruct',\n    name: 'Mixtral 8x7B (HuggingFace)',\n    provider: 'Hugging Face',\n    description: 'Open-source mixture of experts model',\n    maxTokens: 8192,\n    costPer1kTokens: 0.5,\n    strengths: ['Open source', 'Cost effective', 'Multilingual', 'Code generation'],\n    bestFor: ['Open source projects', 'Cost-sensitive applications', 'Multilingual support', 'Experimentation']\n  }\n};\n\nexport interface PromptOptimizationOptions {\n  model: string;\n  optimizationLevel: 'basic' | 'enhanced' | 'expert';\n  includeExamples: boolean;\n  includeConstraints: boolean;\n  includeMetrics: boolean;\n  targetAudience: 'developer' | 'business' | 'technical' | 'general';\n}\n\nexport class AIService {\n  private apiKey: string;\n  private baseUrl: string;\n\n  constructor(apiKey: string = '', baseUrl: string = 'https://openrouter.ai/api/v1') {\n    this.apiKey = apiKey;\n    this.baseUrl = baseUrl;\n  }\n\n  async generatePrompt(\n    projectInput: ProjectInput,\n    options: PromptOptimizationOptions\n  ): Promise<{ prompt: string; metadata: any }> {\n    // If no API key is provided, fall back to the existing API endpoint\n    if (!this.apiKey) {\n      return this.generatePromptFallback(projectInput, options);\n    }\n\n    const optimizedSystemPrompt = this.buildSystemPrompt(options);\n    const userPrompt = this.buildUserPrompt(projectInput, options);\n\n    try {\n      const response = await fetch(`${this.baseUrl}/chat/completions`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'AI Prompt Generator'\n        },\n        body: JSON.stringify({\n          model: options.model,\n          messages: [\n            { role: 'system', content: optimizedSystemPrompt },\n            { role: 'user', content: userPrompt }\n          ],\n          temperature: 0.7,\n          max_tokens: 2048,\n          top_p: 0.9,\n          frequency_penalty: 0.1,\n          presence_penalty: 0.1\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`AI API Error: ${response.status} ${response.statusText}`);\n      }\n\n      const data = await response.json();\n      const generatedPrompt = data.choices[0]?.message?.content || '';\n\n      return {\n        prompt: generatedPrompt,\n        metadata: {\n          model: options.model,\n          tokensUsed: data.usage?.total_tokens || 0,\n          optimizationLevel: options.optimizationLevel,\n          timestamp: new Date().toISOString()\n        }\n      };\n    } catch (error) {\n      console.error('AI Service Error:', error);\n      // Fall back to existing API if external API fails\n      return this.generatePromptFallback(projectInput, options);\n    }\n  }\n\n  private async generatePromptFallback(\n    projectInput: ProjectInput,\n    options: PromptOptimizationOptions\n  ): Promise<{ prompt: string; metadata: any }> {\n    try {\n      const response = await fetch('/api/generate-prompt', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(projectInput),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to generate AI prompt');\n      }\n\n      const data = await response.json();\n\n      return {\n        prompt: data.prompt,\n        metadata: {\n          model: options.model,\n          tokensUsed: data.metadata?.tokensUsed || 0,\n          optimizationLevel: options.optimizationLevel,\n          timestamp: new Date().toISOString()\n        }\n      };\n    } catch (error) {\n      console.error('Fallback API Error:', error);\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n      throw new Error(`Failed to generate prompt: ${errorMessage}`);\n    }\n  }\n\n  private buildSystemPrompt(options: PromptOptimizationOptions): string {\n    const aiToolName = this.getAIToolName(options.model);\n    const aiCapabilities = this.getAICapabilities(options.model);\n\n    const basePrompt = `You are an expert AI prompt engineer specializing in creating optimized prompts for ${aiToolName}, a powerful AI coding assistant.\n\nYour task is to transform user project descriptions into highly effective, detailed prompts that leverage ${aiToolName}'s capabilities:\n${aiCapabilities.map(cap => `- ${cap}`).join('\\n')}`;\n\n    const optimizationInstructions = {\n      basic: 'Create a clear, structured prompt with essential project details.',\n      enhanced: 'Create a comprehensive prompt with detailed specifications, best practices, and implementation guidance.',\n      expert: 'Create an expert-level prompt with advanced architectural considerations, performance optimization, security requirements, and scalability planning.'\n    };\n\n    const audienceInstructions = {\n      developer: 'Focus on technical implementation details, code architecture, and development best practices.',\n      business: 'Emphasize business value, user experience, and project outcomes.',\n      technical: 'Include technical specifications, system requirements, and integration details.',\n      general: 'Balance technical and business considerations for a general audience.'\n    };\n\n    return `${basePrompt}\n\nOPTIMIZATION LEVEL: ${options.optimizationLevel.toUpperCase()}\n${optimizationInstructions[options.optimizationLevel]}\n\nTARGET AUDIENCE: ${options.targetAudience.toUpperCase()}\n${audienceInstructions[options.targetAudience]}\n\n${options.includeExamples ? 'Include relevant examples and code snippets where appropriate.' : ''}\n${options.includeConstraints ? 'Specify technical constraints, limitations, and requirements.' : ''}\n${options.includeMetrics ? 'Include success metrics, KPIs, and measurable outcomes.' : ''}\n\nGenerate a prompt that will help ${this.getAIToolName(options.model)} create exceptional results for this project.`;\n  }\n\n  private getAIToolName(modelId: string): string {\n    if (modelId.includes('deepseek')) return 'DeepSeek AI';\n    if (modelId.includes('claude')) return 'Claude Sonnet 4';\n    if (modelId.includes('gpt') || modelId.includes('openai')) return 'ChatGPT';\n    if (modelId.includes('gemini')) return 'Google Gemini';\n    if (modelId.includes('cursor')) return 'Cursor AI';\n    if (modelId.includes('perplexity')) return 'Perplexity AI';\n    if (modelId.includes('cohere')) return 'Cohere Command';\n    return 'AI Assistant';\n  }\n\n  private getAICapabilities(modelId: string): string[] {\n    const baseCapabilities = [\n      'Advanced code generation and analysis',\n      'Multi-language programming support',\n      'Best practices and design patterns',\n      'Code optimization and refactoring'\n    ];\n\n    if (modelId.includes('deepseek')) {\n      return [\n        ...baseCapabilities,\n        'Deep reasoning and problem-solving',\n        'Mathematical and algorithmic thinking',\n        'Technical documentation generation'\n      ];\n    }\n\n    if (modelId.includes('claude')) {\n      return [\n        ...baseCapabilities,\n        'Structured thinking and analysis',\n        'Creative problem-solving approaches',\n        'Comprehensive project planning',\n        'User experience considerations'\n      ];\n    }\n\n    if (modelId.includes('gpt') || modelId.includes('openai')) {\n      return [\n        ...baseCapabilities,\n        'Versatile general intelligence',\n        'Creative and innovative solutions',\n        'Integration with development tools',\n        'Comprehensive documentation'\n      ];\n    }\n\n    if (modelId.includes('gemini')) {\n      return [\n        ...baseCapabilities,\n        'Large context understanding',\n        'Multimodal capabilities',\n        'Data analysis and insights',\n        'Research and information synthesis'\n      ];\n    }\n\n    if (modelId.includes('cursor')) {\n      return [\n        ...baseCapabilities,\n        'IDE integration and workflow optimization',\n        'Real-time code suggestions',\n        'Project-aware assistance',\n        'Development environment setup'\n      ];\n    }\n\n    return baseCapabilities;\n  }\n\n  private buildUserPrompt(projectInput: ProjectInput, options: PromptOptimizationOptions): string {\n    const aiToolName = this.getAIToolName(options.model);\n    return `Please create an optimized prompt for ${aiToolName} based on this project:\n\nPROJECT NAME: ${projectInput.projectName}\nPROJECT TYPE: ${projectInput.projectType}\nTARGET PLATFORM: ${projectInput.platform}\nCOMPLEXITY: ${projectInput.complexity}\n\nPROJECT DESCRIPTION:\n${projectInput.projectIdea}\n\nSELECTED TECHNOLOGIES:\n${projectInput.technologies.map(tech => `- ${tech.name}: ${tech.description}`).join('\\n') || 'No specific technologies selected'}\n\nADDITIONAL REQUIREMENTS:\n${projectInput.additionalRequirements || 'None specified'}\n\nPlease generate a comprehensive, actionable prompt that will help ${aiToolName} deliver exceptional results for this ${projectInput.projectType} project.`;\n  }\n\n  getModelRecommendation(projectInput: ProjectInput): string {\n    const { projectType, complexity, technologies } = projectInput;\n    \n    // AI/ML projects work best with DeepSeek for technical depth\n    if (projectType.includes('machine-learning') || projectType.includes('data-analysis')) {\n      return 'deepseek-chat';\n    }\n\n    // Large, complex projects benefit from Gemini's large context\n    if (complexity === 'advanced' || technologies.length > 5) {\n      return 'gemini-pro';\n    }\n\n    // Creative and business-focused projects work well with Claude\n    if (projectType.includes('web-application') || projectType.includes('mobile')) {\n      return 'claude-sonnet';\n    }\n\n    // Desktop applications work well with Cursor for IDE integration\n    if (projectType === 'desktop-application') {\n      return 'cursor-small';\n    }\n\n    // API backends benefit from ChatGPT's versatility\n    if (projectType === 'api-backend') {\n      return 'gpt-4o';\n    }\n\n    // Research projects benefit from Perplexity's web access\n    if (projectType.includes('research') || projectInput.projectIdea.toLowerCase().includes('research')) {\n      return 'perplexity-small';\n    }\n\n    // Enterprise projects work well with Cohere\n    if (projectInput.projectIdea.toLowerCase().includes('enterprise')) {\n      return 'cohere-command';\n    }\n\n    // Default to DeepSeek for development projects\n    return 'deepseek-chat';\n  }\n\n  estimateCost(tokensUsed: number, modelId: string): number {\n    const model = Object.values(AI_MODELS).find(m => m.id === modelId);\n    if (!model) return 0;\n    \n    return (tokensUsed / 1000) * model.costPer1kTokens;\n  }\n}\n\nexport const aiService = new AIService();\n"], "names": [], "mappings": ";;;;;AAaO,MAAM,YAAqC;IAChD,iBAAiB;QACf,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,iBAAiB;QACjB,WAAW;YAAC;YAAmB;YAA2B;YAAqB;SAAwB;QACvG,SAAS;YAAC;YAAwB;YAA4B;YAAqB;SAAmB;IACxG;IACA,iBAAiB;QACf,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,iBAAiB;QACjB,WAAW;YAAC;YAAoB;YAAY;YAAuB;SAAyB;QAC5F,SAAS;YAAC;YAAoB;YAAqB;YAAmB;SAA2B;IACnG;IACA,UAAU;QACR,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,iBAAiB;QACjB,WAAW;YAAC;YAAwB;YAAc;YAAa;SAAqB;QACpF,SAAS;YAAC;YAAoB;YAAmB;YAAuB;SAAuB;IACjG;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,iBAAiB;QACjB,WAAW;YAAC;YAAiB;YAAc;YAAiB;SAAqB;QACjF,SAAS;YAAC;YAAmB;YAAiB;YAAkB;SAAmB;IACrF;IACA,gBAAgB;QACd,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,iBAAiB;QACjB,WAAW;YAAC;YAAmB;YAAmB;YAAyB;SAAuB;QAClG,SAAS;YAAC;YAAiB;YAAmB;YAA0B;SAAgB;IAC1F;IACA,oBAAoB;QAClB,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,iBAAiB;QACjB,WAAW;YAAC;YAAY;YAAkB;YAAyB;SAAgB;QACnF,SAAS;YAAC;YAAqB;YAAkB;YAAmB;SAAqB;IAC3F;IACA,kBAAkB;QAChB,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,iBAAiB;QACjB,WAAW;YAAC;YAAwB;YAAkB;YAAqB;SAAuB;QAClG,SAAS;YAAC;YAAuB;YAAyB;YAA8B;SAAsB;IAChH;IACA,uBAAuB;QACrB,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,iBAAiB;QACjB,WAAW;YAAC;YAAe;YAAkB;YAAgB;SAAkB;QAC/E,SAAS;YAAC;YAAwB;YAA+B;YAAwB;SAAkB;IAC7G;AACF;AAWO,MAAM;IACH,OAAe;IACf,QAAgB;IAExB,YAAY,SAAiB,EAAE,EAAE,UAAkB,8BAA8B,CAAE;QACjF,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAM,eACJ,YAA0B,EAC1B,OAAkC,EACU;QAC5C,oEAAoE;QACpE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,OAAO,IAAI,CAAC,sBAAsB,CAAC,cAAc;QACnD;QAEA,MAAM,wBAAwB,IAAI,CAAC,iBAAiB,CAAC;QACrD,MAAM,aAAa,IAAI,CAAC,eAAe,CAAC,cAAc;QAEtD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;gBAC/D,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;oBACxC,gBAAgB;oBAChB,gBAAgB,OAAO,QAAQ,CAAC,MAAM;oBACtC,WAAW;gBACb;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,QAAQ,KAAK;oBACpB,UAAU;wBACR;4BAAE,MAAM;4BAAU,SAAS;wBAAsB;wBACjD;4BAAE,MAAM;4BAAQ,SAAS;wBAAW;qBACrC;oBACD,aAAa;oBACb,YAAY;oBACZ,OAAO;oBACP,mBAAmB;oBACnB,kBAAkB;gBACpB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;YAC3E;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,kBAAkB,KAAK,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;YAE7D,OAAO;gBACL,QAAQ;gBACR,UAAU;oBACR,OAAO,QAAQ,KAAK;oBACpB,YAAY,KAAK,KAAK,EAAE,gBAAgB;oBACxC,mBAAmB,QAAQ,iBAAiB;oBAC5C,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,kDAAkD;YAClD,OAAO,IAAI,CAAC,sBAAsB,CAAC,cAAc;QACnD;IACF;IAEA,MAAc,uBACZ,YAA0B,EAC1B,OAAkC,EACU;QAC5C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,OAAO;gBACL,QAAQ,KAAK,MAAM;gBACnB,UAAU;oBACR,OAAO,QAAQ,KAAK;oBACpB,YAAY,KAAK,QAAQ,EAAE,cAAc;oBACzC,mBAAmB,QAAQ,iBAAiB;oBAC5C,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,cAAc;QAC9D;IACF;IAEQ,kBAAkB,OAAkC,EAAU;QACpE,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK;QACnD,MAAM,iBAAiB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,KAAK;QAE3D,MAAM,aAAa,CAAC,oFAAoF,EAAE,WAAW;;0GAEf,EAAE,WAAW;AACvH,EAAE,eAAe,GAAG,CAAC,CAAA,MAAO,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO;QAEhD,MAAM,2BAA2B;YAC/B,OAAO;YACP,UAAU;YACV,QAAQ;QACV;QAEA,MAAM,uBAAuB;YAC3B,WAAW;YACX,UAAU;YACV,WAAW;YACX,SAAS;QACX;QAEA,OAAO,GAAG,WAAW;;oBAEL,EAAE,QAAQ,iBAAiB,CAAC,WAAW,GAAG;AAC9D,EAAE,wBAAwB,CAAC,QAAQ,iBAAiB,CAAC,CAAC;;iBAErC,EAAE,QAAQ,cAAc,CAAC,WAAW,GAAG;AACxD,EAAE,oBAAoB,CAAC,QAAQ,cAAc,CAAC,CAAC;;AAE/C,EAAE,QAAQ,eAAe,GAAG,mEAAmE,GAAG;AAClG,EAAE,QAAQ,kBAAkB,GAAG,kEAAkE,GAAG;AACpG,EAAE,QAAQ,cAAc,GAAG,4DAA4D,GAAG;;iCAEzD,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,EAAE,6CAA6C,CAAC;IACjH;IAEQ,cAAc,OAAe,EAAU;QAC7C,IAAI,QAAQ,QAAQ,CAAC,aAAa,OAAO;QACzC,IAAI,QAAQ,QAAQ,CAAC,WAAW,OAAO;QACvC,IAAI,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,WAAW,OAAO;QAClE,IAAI,QAAQ,QAAQ,CAAC,WAAW,OAAO;QACvC,IAAI,QAAQ,QAAQ,CAAC,WAAW,OAAO;QACvC,IAAI,QAAQ,QAAQ,CAAC,eAAe,OAAO;QAC3C,IAAI,QAAQ,QAAQ,CAAC,WAAW,OAAO;QACvC,OAAO;IACT;IAEQ,kBAAkB,OAAe,EAAY;QACnD,MAAM,mBAAmB;YACvB;YACA;YACA;YACA;SACD;QAED,IAAI,QAAQ,QAAQ,CAAC,aAAa;YAChC,OAAO;mBACF;gBACH;gBACA;gBACA;aACD;QACH;QAEA,IAAI,QAAQ,QAAQ,CAAC,WAAW;YAC9B,OAAO;mBACF;gBACH;gBACA;gBACA;gBACA;aACD;QACH;QAEA,IAAI,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,WAAW;YACzD,OAAO;mBACF;gBACH;gBACA;gBACA;gBACA;aACD;QACH;QAEA,IAAI,QAAQ,QAAQ,CAAC,WAAW;YAC9B,OAAO;mBACF;gBACH;gBACA;gBACA;gBACA;aACD;QACH;QAEA,IAAI,QAAQ,QAAQ,CAAC,WAAW;YAC9B,OAAO;mBACF;gBACH;gBACA;gBACA;gBACA;aACD;QACH;QAEA,OAAO;IACT;IAEQ,gBAAgB,YAA0B,EAAE,OAAkC,EAAU;QAC9F,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK;QACnD,OAAO,CAAC,sCAAsC,EAAE,WAAW;;cAEjD,EAAE,aAAa,WAAW,CAAC;cAC3B,EAAE,aAAa,WAAW,CAAC;iBACxB,EAAE,aAAa,QAAQ,CAAC;YAC7B,EAAE,aAAa,UAAU,CAAC;;;AAGtC,EAAE,aAAa,WAAW,CAAC;;;AAG3B,EAAE,aAAa,YAAY,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,WAAW,EAAE,EAAE,IAAI,CAAC,SAAS,oCAAoC;;;AAGjI,EAAE,aAAa,sBAAsB,IAAI,iBAAiB;;kEAEQ,EAAE,WAAW,sCAAsC,EAAE,aAAa,WAAW,CAAC,SAAS,CAAC;IACxJ;IAEA,uBAAuB,YAA0B,EAAU;QACzD,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG;QAElD,6DAA6D;QAC7D,IAAI,YAAY,QAAQ,CAAC,uBAAuB,YAAY,QAAQ,CAAC,kBAAkB;YACrF,OAAO;QACT;QAEA,8DAA8D;QAC9D,IAAI,eAAe,cAAc,aAAa,MAAM,GAAG,GAAG;YACxD,OAAO;QACT;QAEA,+DAA+D;QAC/D,IAAI,YAAY,QAAQ,CAAC,sBAAsB,YAAY,QAAQ,CAAC,WAAW;YAC7E,OAAO;QACT;QAEA,iEAAiE;QACjE,IAAI,gBAAgB,uBAAuB;YACzC,OAAO;QACT;QAEA,kDAAkD;QAClD,IAAI,gBAAgB,eAAe;YACjC,OAAO;QACT;QAEA,yDAAyD;QACzD,IAAI,YAAY,QAAQ,CAAC,eAAe,aAAa,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa;YACnG,OAAO;QACT;QAEA,4CAA4C;QAC5C,IAAI,aAAa,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,eAAe;YACjE,OAAO;QACT;QAEA,+CAA+C;QAC/C,OAAO;IACT;IAEA,aAAa,UAAkB,EAAE,OAAe,EAAU;QACxD,MAAM,QAAQ,OAAO,MAAM,CAAC,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1D,IAAI,CAAC,OAAO,OAAO;QAEnB,OAAO,AAAC,aAAa,OAAQ,MAAM,eAAe;IACpD;AACF;AAEO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 1146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/test/augment-prompt-generator/src/components/AnimatedBackground.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useState } from 'react';\n\ninterface Star {\n  x: number;\n  y: number;\n  vx: number;\n  vy: number;\n  radius: number;\n  opacity: number;\n  twinkleSpeed: number;\n  twinklePhase: number;\n}\n\ninterface MousePosition {\n  x: number;\n  y: number;\n}\n\nconst AnimatedBackground: React.FC = () => {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const animationRef = useRef<number | null>(null);\n  const starsRef = useRef<Star[]>([]);\n  const mouseRef = useRef<MousePosition>({ x: 0, y: 0 });\n  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });\n\n  // Initialize stars\n  const initStars = (width: number, height: number) => {\n    const stars: Star[] = [];\n    const numStars = Math.floor((width * height) / 6000); // More stars for richer effect\n\n    for (let i = 0; i < numStars; i++) {\n      stars.push({\n        x: Math.random() * width,\n        y: Math.random() * height,\n        vx: (Math.random() - 0.5) * 0.8,\n        vy: (Math.random() - 0.5) * 0.8,\n        radius: Math.random() * 3 + 0.3, // Varied sizes\n        opacity: Math.random() * 0.9 + 0.1,\n        twinkleSpeed: Math.random() * 0.03 + 0.005,\n        twinklePhase: Math.random() * Math.PI * 2,\n      });\n    }\n    starsRef.current = stars;\n  };\n\n  // Update canvas dimensions\n  const updateDimensions = () => {\n    if (canvasRef.current) {\n      const { innerWidth, innerHeight } = window;\n      setDimensions({ width: innerWidth, height: innerHeight });\n      canvasRef.current.width = innerWidth;\n      canvasRef.current.height = innerHeight;\n      initStars(innerWidth, innerHeight);\n    }\n  };\n\n  // Mouse move handler\n  const handleMouseMove = (event: MouseEvent) => {\n    mouseRef.current = {\n      x: event.clientX,\n      y: event.clientY,\n    };\n  };\n\n  // Animation loop\n  const animate = () => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    const { width, height } = dimensions;\n    const mouse = mouseRef.current;\n\n    // Clear canvas\n    ctx.clearRect(0, 0, width, height);\n\n    // Update and draw stars\n    starsRef.current.forEach((star, index) => {\n      // Mouse interaction - attraction and repulsion effect\n      const dx = mouse.x - star.x;\n      const dy = mouse.y - star.y;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      const maxDistance = 200;\n      const repulsionDistance = 80;\n\n      if (distance < maxDistance && distance > 0) {\n        const force = (maxDistance - distance) / maxDistance;\n        if (distance < repulsionDistance) {\n          // Repulsion effect for close stars\n          star.vx -= (dx / distance) * force * 0.003;\n          star.vy -= (dy / distance) * force * 0.003;\n        } else {\n          // Attraction effect for distant stars\n          star.vx += (dx / distance) * force * 0.0015;\n          star.vy += (dy / distance) * force * 0.0015;\n        }\n      }\n\n      // Update position\n      star.x += star.vx;\n      star.y += star.vy;\n\n      // Boundary wrapping\n      if (star.x < 0) star.x = width;\n      if (star.x > width) star.x = 0;\n      if (star.y < 0) star.y = height;\n      if (star.y > height) star.y = 0;\n\n      // Damping\n      star.vx *= 0.99;\n      star.vy *= 0.99;\n\n      // Twinkle effect\n      star.twinklePhase += star.twinkleSpeed;\n      const twinkle = Math.sin(star.twinklePhase) * 0.3 + 0.7;\n\n      // Draw star with enhanced effects\n      ctx.beginPath();\n      ctx.arc(star.x, star.y, star.radius, 0, Math.PI * 2);\n      ctx.fillStyle = `rgba(255, 255, 255, ${star.opacity * twinkle})`;\n      ctx.fill();\n\n      // Draw multiple glow layers for larger stars\n      if (star.radius > 1.5) {\n        // Outer glow\n        ctx.beginPath();\n        ctx.arc(star.x, star.y, star.radius * 3, 0, Math.PI * 2);\n        ctx.fillStyle = `rgba(255, 255, 255, ${star.opacity * twinkle * 0.05})`;\n        ctx.fill();\n\n        // Middle glow\n        ctx.beginPath();\n        ctx.arc(star.x, star.y, star.radius * 2, 0, Math.PI * 2);\n        ctx.fillStyle = `rgba(255, 255, 255, ${star.opacity * twinkle * 0.1})`;\n        ctx.fill();\n\n        // Inner bright core\n        ctx.beginPath();\n        ctx.arc(star.x, star.y, star.radius * 0.5, 0, Math.PI * 2);\n        ctx.fillStyle = `rgba(255, 255, 255, ${Math.min(1, star.opacity * twinkle * 1.5)})`;\n        ctx.fill();\n      }\n\n      // Special effect for very large stars (create cross pattern)\n      if (star.radius > 2.5) {\n        const crossLength = star.radius * 4;\n        const crossOpacity = star.opacity * twinkle * 0.3;\n\n        ctx.beginPath();\n        ctx.moveTo(star.x - crossLength, star.y);\n        ctx.lineTo(star.x + crossLength, star.y);\n        ctx.moveTo(star.x, star.y - crossLength);\n        ctx.lineTo(star.x, star.y + crossLength);\n        ctx.strokeStyle = `rgba(255, 255, 255, ${crossOpacity})`;\n        ctx.lineWidth = 0.5;\n        ctx.stroke();\n      }\n    });\n\n    // Draw connections between nearby stars\n    starsRef.current.forEach((star, i) => {\n      starsRef.current.slice(i + 1).forEach((otherStar) => {\n        const dx = star.x - otherStar.x;\n        const dy = star.y - otherStar.y;\n        const distance = Math.sqrt(dx * dx + dy * dy);\n\n        if (distance < 120) {\n          const opacity = (120 - distance) / 120 * 0.15;\n          ctx.beginPath();\n          ctx.moveTo(star.x, star.y);\n          ctx.lineTo(otherStar.x, otherStar.y);\n          ctx.strokeStyle = `rgba(255, 255, 255, ${opacity})`;\n          ctx.lineWidth = 0.8;\n          ctx.stroke();\n        }\n      });\n    });\n\n    // Draw mouse connections to nearby stars\n    if (mouse.x > 0 && mouse.y > 0) {\n      starsRef.current.forEach((star) => {\n        const dx = mouse.x - star.x;\n        const dy = mouse.y - star.y;\n        const distance = Math.sqrt(dx * dx + dy * dy);\n\n        if (distance < 150) {\n          const opacity = (150 - distance) / 150 * 0.3;\n          ctx.beginPath();\n          ctx.moveTo(mouse.x, mouse.y);\n          ctx.lineTo(star.x, star.y);\n          ctx.strokeStyle = `rgba(255, 255, 255, ${opacity})`;\n          ctx.lineWidth = 1;\n          ctx.stroke();\n        }\n      });\n    }\n\n    // Enhanced mouse cursor effect with pulsing animation\n    if (mouse.x > 0 && mouse.y > 0) {\n      const time = Date.now() * 0.005;\n      const pulse = Math.sin(time) * 0.3 + 0.7;\n\n      // Outer ring\n      ctx.beginPath();\n      ctx.arc(mouse.x, mouse.y, 60 * pulse, 0, Math.PI * 2);\n      ctx.strokeStyle = `rgba(255, 255, 255, ${0.1 * pulse})`;\n      ctx.lineWidth = 2;\n      ctx.stroke();\n\n      // Middle ring\n      ctx.beginPath();\n      ctx.arc(mouse.x, mouse.y, 35 * pulse, 0, Math.PI * 2);\n      ctx.strokeStyle = `rgba(255, 255, 255, ${0.2 * pulse})`;\n      ctx.lineWidth = 1.5;\n      ctx.stroke();\n\n      // Inner ring\n      ctx.beginPath();\n      ctx.arc(mouse.x, mouse.y, 15 * pulse, 0, Math.PI * 2);\n      ctx.strokeStyle = `rgba(255, 255, 255, ${0.3 * pulse})`;\n      ctx.lineWidth = 1;\n      ctx.stroke();\n\n      // Center dot\n      ctx.beginPath();\n      ctx.arc(mouse.x, mouse.y, 3, 0, Math.PI * 2);\n      ctx.fillStyle = `rgba(255, 255, 255, ${0.8 * pulse})`;\n      ctx.fill();\n    }\n\n    animationRef.current = requestAnimationFrame(animate);\n  };\n\n  useEffect(() => {\n    updateDimensions();\n    window.addEventListener('resize', updateDimensions);\n    window.addEventListener('mousemove', handleMouseMove);\n\n    // Start animation\n    animate();\n\n    return () => {\n      window.removeEventListener('resize', updateDimensions);\n      window.removeEventListener('mousemove', handleMouseMove);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, [dimensions.width, dimensions.height]);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className=\"fixed inset-0 pointer-events-none z-0\"\n      style={{\n        background: `\n          radial-gradient(circle at 20% 80%, rgba(30, 30, 30, 0.3) 0%, transparent 50%),\n          radial-gradient(circle at 80% 20%, rgba(40, 40, 40, 0.2) 0%, transparent 50%),\n          radial-gradient(circle at 40% 40%, rgba(20, 20, 20, 0.4) 0%, transparent 50%),\n          linear-gradient(135deg, #000000 0%, #0a0a0a 25%, #111111 50%, #0a0a0a 75%, #**********%)\n        `,\n      }}\n    />\n  );\n};\n\nexport default AnimatedBackground;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAoBA,MAAM,qBAA+B;;IACnC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IAC3C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU,EAAE;IAClC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;QAAE,GAAG;QAAG,GAAG;IAAE;IACpD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,OAAO;QAAG,QAAQ;IAAE;IAEnE,mBAAmB;IACnB,MAAM,YAAY,CAAC,OAAe;QAChC,MAAM,QAAgB,EAAE;QACxB,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,QAAQ,SAAU,OAAO,+BAA+B;QAErF,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;YACjC,MAAM,IAAI,CAAC;gBACT,GAAG,KAAK,MAAM,KAAK;gBACnB,GAAG,KAAK,MAAM,KAAK;gBACnB,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC5B,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC5B,QAAQ,KAAK,MAAM,KAAK,IAAI;gBAC5B,SAAS,KAAK,MAAM,KAAK,MAAM;gBAC/B,cAAc,KAAK,MAAM,KAAK,OAAO;gBACrC,cAAc,KAAK,MAAM,KAAK,KAAK,EAAE,GAAG;YAC1C;QACF;QACA,SAAS,OAAO,GAAG;IACrB;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB;QACvB,IAAI,UAAU,OAAO,EAAE;YACrB,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG;YACpC,cAAc;gBAAE,OAAO;gBAAY,QAAQ;YAAY;YACvD,UAAU,OAAO,CAAC,KAAK,GAAG;YAC1B,UAAU,OAAO,CAAC,MAAM,GAAG;YAC3B,UAAU,YAAY;QACxB;IACF;IAEA,qBAAqB;IACrB,MAAM,kBAAkB,CAAC;QACvB,SAAS,OAAO,GAAG;YACjB,GAAG,MAAM,OAAO;YAChB,GAAG,MAAM,OAAO;QAClB;IACF;IAEA,iBAAiB;IACjB,MAAM,UAAU;QACd,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK;QAEV,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;QAC1B,MAAM,QAAQ,SAAS,OAAO;QAE9B,eAAe;QACf,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO;QAE3B,wBAAwB;QACxB,SAAS,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM;YAC9B,sDAAsD;YACtD,MAAM,KAAK,MAAM,CAAC,GAAG,KAAK,CAAC;YAC3B,MAAM,KAAK,MAAM,CAAC,GAAG,KAAK,CAAC;YAC3B,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;YAC1C,MAAM,cAAc;YACpB,MAAM,oBAAoB;YAE1B,IAAI,WAAW,eAAe,WAAW,GAAG;gBAC1C,MAAM,QAAQ,CAAC,cAAc,QAAQ,IAAI;gBACzC,IAAI,WAAW,mBAAmB;oBAChC,mCAAmC;oBACnC,KAAK,EAAE,IAAI,AAAC,KAAK,WAAY,QAAQ;oBACrC,KAAK,EAAE,IAAI,AAAC,KAAK,WAAY,QAAQ;gBACvC,OAAO;oBACL,sCAAsC;oBACtC,KAAK,EAAE,IAAI,AAAC,KAAK,WAAY,QAAQ;oBACrC,KAAK,EAAE,IAAI,AAAC,KAAK,WAAY,QAAQ;gBACvC;YACF;YAEA,kBAAkB;YAClB,KAAK,CAAC,IAAI,KAAK,EAAE;YACjB,KAAK,CAAC,IAAI,KAAK,EAAE;YAEjB,oBAAoB;YACpB,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG;YACzB,IAAI,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,GAAG;YAC7B,IAAI,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG;YACzB,IAAI,KAAK,CAAC,GAAG,QAAQ,KAAK,CAAC,GAAG;YAE9B,UAAU;YACV,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,IAAI;YAEX,iBAAiB;YACjB,KAAK,YAAY,IAAI,KAAK,YAAY;YACtC,MAAM,UAAU,KAAK,GAAG,CAAC,KAAK,YAAY,IAAI,MAAM;YAEpD,kCAAkC;YAClC,IAAI,SAAS;YACb,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,MAAM,EAAE,GAAG,KAAK,EAAE,GAAG;YAClD,IAAI,SAAS,GAAG,CAAC,oBAAoB,EAAE,KAAK,OAAO,GAAG,QAAQ,CAAC,CAAC;YAChE,IAAI,IAAI;YAER,6CAA6C;YAC7C,IAAI,KAAK,MAAM,GAAG,KAAK;gBACrB,aAAa;gBACb,IAAI,SAAS;gBACb,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,GAAG,GAAG,KAAK,EAAE,GAAG;gBACtD,IAAI,SAAS,GAAG,CAAC,oBAAoB,EAAE,KAAK,OAAO,GAAG,UAAU,KAAK,CAAC,CAAC;gBACvE,IAAI,IAAI;gBAER,cAAc;gBACd,IAAI,SAAS;gBACb,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,GAAG,GAAG,KAAK,EAAE,GAAG;gBACtD,IAAI,SAAS,GAAG,CAAC,oBAAoB,EAAE,KAAK,OAAO,GAAG,UAAU,IAAI,CAAC,CAAC;gBACtE,IAAI,IAAI;gBAER,oBAAoB;gBACpB,IAAI,SAAS;gBACb,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG,KAAK,GAAG,KAAK,EAAE,GAAG;gBACxD,IAAI,SAAS,GAAG,CAAC,oBAAoB,EAAE,KAAK,GAAG,CAAC,GAAG,KAAK,OAAO,GAAG,UAAU,KAAK,CAAC,CAAC;gBACnF,IAAI,IAAI;YACV;YAEA,6DAA6D;YAC7D,IAAI,KAAK,MAAM,GAAG,KAAK;gBACrB,MAAM,cAAc,KAAK,MAAM,GAAG;gBAClC,MAAM,eAAe,KAAK,OAAO,GAAG,UAAU;gBAE9C,IAAI,SAAS;gBACb,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,aAAa,KAAK,CAAC;gBACvC,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,aAAa,KAAK,CAAC;gBACvC,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG;gBAC5B,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG;gBAC5B,IAAI,WAAW,GAAG,CAAC,oBAAoB,EAAE,aAAa,CAAC,CAAC;gBACxD,IAAI,SAAS,GAAG;gBAChB,IAAI,MAAM;YACZ;QACF;QAEA,wCAAwC;QACxC,SAAS,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM;YAC9B,SAAS,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC;gBACrC,MAAM,KAAK,KAAK,CAAC,GAAG,UAAU,CAAC;gBAC/B,MAAM,KAAK,KAAK,CAAC,GAAG,UAAU,CAAC;gBAC/B,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;gBAE1C,IAAI,WAAW,KAAK;oBAClB,MAAM,UAAU,CAAC,MAAM,QAAQ,IAAI,MAAM;oBACzC,IAAI,SAAS;oBACb,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC;oBACzB,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC;oBACnC,IAAI,WAAW,GAAG,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;oBACnD,IAAI,SAAS,GAAG;oBAChB,IAAI,MAAM;gBACZ;YACF;QACF;QAEA,yCAAyC;QACzC,IAAI,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,GAAG;YAC9B,SAAS,OAAO,CAAC,OAAO,CAAC,CAAC;gBACxB,MAAM,KAAK,MAAM,CAAC,GAAG,KAAK,CAAC;gBAC3B,MAAM,KAAK,MAAM,CAAC,GAAG,KAAK,CAAC;gBAC3B,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;gBAE1C,IAAI,WAAW,KAAK;oBAClB,MAAM,UAAU,CAAC,MAAM,QAAQ,IAAI,MAAM;oBACzC,IAAI,SAAS;oBACb,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;oBAC3B,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC;oBACzB,IAAI,WAAW,GAAG,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;oBACnD,IAAI,SAAS,GAAG;oBAChB,IAAI,MAAM;gBACZ;YACF;QACF;QAEA,sDAAsD;QACtD,IAAI,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,GAAG;YAC9B,MAAM,OAAO,KAAK,GAAG,KAAK;YAC1B,MAAM,QAAQ,KAAK,GAAG,CAAC,QAAQ,MAAM;YAErC,aAAa;YACb,IAAI,SAAS;YACb,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK,OAAO,GAAG,KAAK,EAAE,GAAG;YACnD,IAAI,WAAW,GAAG,CAAC,oBAAoB,EAAE,MAAM,MAAM,CAAC,CAAC;YACvD,IAAI,SAAS,GAAG;YAChB,IAAI,MAAM;YAEV,cAAc;YACd,IAAI,SAAS;YACb,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK,OAAO,GAAG,KAAK,EAAE,GAAG;YACnD,IAAI,WAAW,GAAG,CAAC,oBAAoB,EAAE,MAAM,MAAM,CAAC,CAAC;YACvD,IAAI,SAAS,GAAG;YAChB,IAAI,MAAM;YAEV,aAAa;YACb,IAAI,SAAS;YACb,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK,OAAO,GAAG,KAAK,EAAE,GAAG;YACnD,IAAI,WAAW,GAAG,CAAC,oBAAoB,EAAE,MAAM,MAAM,CAAC,CAAC;YACvD,IAAI,SAAS,GAAG;YAChB,IAAI,MAAM;YAEV,aAAa;YACb,IAAI,SAAS;YACb,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG;YAC1C,IAAI,SAAS,GAAG,CAAC,oBAAoB,EAAE,MAAM,MAAM,CAAC,CAAC;YACrD,IAAI,IAAI;QACV;QAEA,aAAa,OAAO,GAAG,sBAAsB;IAC/C;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,aAAa;YAErC,kBAAkB;YAClB;YAEA;gDAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,aAAa;oBACxC,IAAI,aAAa,OAAO,EAAE;wBACxB,qBAAqB,aAAa,OAAO;oBAC3C;gBACF;;QACF;uCAAG;QAAC,WAAW,KAAK;QAAE,WAAW,MAAM;KAAC;IAExC,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YACL,YAAY,CAAC;;;;;QAKb,CAAC;QACH;;;;;;AAGN;GAxPM;KAAA;uCA0PS", "debugId": null}}, {"offset": {"line": 1403, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/test/augment-prompt-generator/src/components/CustomSelect.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect, useId } from 'react';\n\n// Global dropdown manager to ensure only one dropdown is open at a time\nclass DropdownManager {\n  private static instance: DropdownManager;\n  private openDropdownId: string | null = null;\n  private callbacks: Map<string, () => void> = new Map();\n\n  static getInstance(): DropdownManager {\n    if (!DropdownManager.instance) {\n      DropdownManager.instance = new DropdownManager();\n    }\n    return DropdownManager.instance;\n  }\n\n  register(id: string, closeCallback: () => void) {\n    this.callbacks.set(id, closeCallback);\n  }\n\n  unregister(id: string) {\n    this.callbacks.delete(id);\n  }\n\n  openDropdown(id: string) {\n    if (this.openDropdownId && this.openDropdownId !== id) {\n      const closeCallback = this.callbacks.get(this.openDropdownId);\n      if (closeCallback) {\n        closeCallback();\n      }\n    }\n    this.openDropdownId = id;\n  }\n\n  closeDropdown(id: string) {\n    if (this.openDropdownId === id) {\n      this.openDropdownId = null;\n    }\n  }\n}\n\ninterface Option {\n  value: string;\n  label: string;\n  description?: string;\n}\n\ninterface CustomSelectProps {\n  value: string;\n  onChange: (value: string) => void;\n  options: Option[];\n  placeholder?: string;\n  className?: string;\n  disabled?: boolean;\n}\n\nexport default function CustomSelect({\n  value,\n  onChange,\n  options,\n  placeholder = \"Select an option\",\n  className = \"\",\n  disabled = false\n}: CustomSelectProps) {\n  const [isOpen, setIsOpen] = useState(false);\n  const [highlightedIndex, setHighlightedIndex] = useState(-1);\n  const selectRef = useRef<HTMLDivElement>(null);\n  const dropdownId = useId();\n  const dropdownManager = DropdownManager.getInstance();\n\n  const selectedOption = options.find(option => option.value === value);\n\n  const closeDropdown = () => {\n    setIsOpen(false);\n    dropdownManager.closeDropdown(dropdownId);\n  };\n\n  const openDropdown = () => {\n    dropdownManager.openDropdown(dropdownId);\n    setIsOpen(true);\n  };\n\n  useEffect(() => {\n    // Register with dropdown manager\n    dropdownManager.register(dropdownId, closeDropdown);\n\n    const handleClickOutside = (event: MouseEvent) => {\n      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {\n        closeDropdown();\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      dropdownManager.unregister(dropdownId);\n    };\n  }, [dropdownId]);\n\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (!isOpen) return;\n\n      switch (event.key) {\n        case 'Escape':\n          closeDropdown();\n          break;\n        case 'ArrowDown':\n          event.preventDefault();\n          setHighlightedIndex(prev => \n            prev < options.length - 1 ? prev + 1 : 0\n          );\n          break;\n        case 'ArrowUp':\n          event.preventDefault();\n          setHighlightedIndex(prev => \n            prev > 0 ? prev - 1 : options.length - 1\n          );\n          break;\n        case 'Enter':\n          event.preventDefault();\n          if (highlightedIndex >= 0) {\n            onChange(options[highlightedIndex].value);\n            closeDropdown();\n          }\n          break;\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [isOpen, highlightedIndex, options, onChange]);\n\n  return (\n    <div ref={selectRef} className={`relative ${className}`}>\n      <button\n        type=\"button\"\n        onClick={() => !disabled && (isOpen ? closeDropdown() : openDropdown())}\n        disabled={disabled}\n        className={`group w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-black text-sm text-white bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl border border-white/10 transition-all duration-300 hover:border-white/20 hover:shadow-lg text-left flex items-center justify-between ${\n          disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:from-gray-700/90 hover:to-gray-800/90'\n        } ${isOpen ? 'ring-2 ring-white/20 border-white/25 shadow-xl' : ''}`}\n        style={{\n          boxShadow: isOpen\n            ? '0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1)'\n            : '0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'\n        }}\n      >\n        <span className={`font-medium ${selectedOption ? 'text-white' : 'text-gray-400'} transition-colors duration-200`}>\n          {selectedOption ? selectedOption.label : placeholder}\n        </span>\n        <svg\n          className={`w-5 h-5 transition-all duration-300 text-gray-300 group-hover:text-white ${isOpen ? 'rotate-180 text-white' : ''}`}\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          strokeWidth={2.5}\n        >\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19 9l-7 7-7-7\" />\n        </svg>\n      </button>\n\n      {isOpen && (\n        <div\n          className=\"absolute z-[9999] w-full mt-2 bg-gradient-to-b from-gray-900/95 to-black/95 backdrop-blur-2xl rounded-xl shadow-2xl border border-white/15 max-h-64 overflow-y-auto animate-fade-in\"\n          style={{\n            boxShadow: '0 32px 64px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1)',\n            backgroundColor: 'rgba(0, 0, 0, 0.92)'\n          }}\n        >\n          <div className=\"p-1\">\n            {options.map((option, index) => (\n              <button\n                key={option.value}\n                type=\"button\"\n                onClick={() => {\n                  onChange(option.value);\n                  closeDropdown();\n                }}\n                onMouseEnter={() => setHighlightedIndex(index)}\n                className={`group w-full text-left px-4 py-3 text-sm transition-all duration-200 rounded-lg mb-1 last:mb-0 ${\n                  index === highlightedIndex || option.value === value\n                    ? 'bg-gradient-to-r from-white/20 to-white/15 text-white shadow-lg border border-white/20'\n                    : 'text-gray-200 hover:bg-gradient-to-r hover:from-white/10 hover:to-white/5 hover:text-white border border-transparent hover:border-white/10'\n                }`}\n                style={{\n                  boxShadow: (index === highlightedIndex || option.value === value)\n                    ? '0 4px 12px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'\n                    : 'none'\n                }}\n              >\n                <div className=\"font-semibold text-sm leading-tight\">{option.label}</div>\n                {option.description && (\n                  <div className=\"text-xs text-gray-300 mt-1.5 leading-relaxed opacity-90 group-hover:opacity-100 transition-opacity duration-200\">\n                    {option.description}\n                  </div>\n                )}\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;;AAEA,wEAAwE;AACxE,MAAM;IACJ,OAAe,SAA0B;IACjC,iBAAgC,KAAK;IACrC,YAAqC,IAAI,MAAM;IAEvD,OAAO,cAA+B;QACpC,IAAI,CAAC,gBAAgB,QAAQ,EAAE;YAC7B,gBAAgB,QAAQ,GAAG,IAAI;QACjC;QACA,OAAO,gBAAgB,QAAQ;IACjC;IAEA,SAAS,EAAU,EAAE,aAAyB,EAAE;QAC9C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI;IACzB;IAEA,WAAW,EAAU,EAAE;QACrB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IACxB;IAEA,aAAa,EAAU,EAAE;QACvB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI;YACrD,MAAM,gBAAgB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc;YAC5D,IAAI,eAAe;gBACjB;YACF;QACF;QACA,IAAI,CAAC,cAAc,GAAG;IACxB;IAEA,cAAc,EAAU,EAAE;QACxB,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI;YAC9B,IAAI,CAAC,cAAc,GAAG;QACxB;IACF;AACF;AAiBe,SAAS,aAAa,EACnC,KAAK,EACL,QAAQ,EACR,OAAO,EACP,cAAc,kBAAkB,EAChC,YAAY,EAAE,EACd,WAAW,KAAK,EACE;;IAClB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC1D,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,QAAK,AAAD;IACvB,MAAM,kBAAkB,gBAAgB,WAAW;IAEnD,MAAM,iBAAiB,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK;IAE/D,MAAM,gBAAgB;QACpB,UAAU;QACV,gBAAgB,aAAa,CAAC;IAChC;IAEA,MAAM,eAAe;QACnB,gBAAgB,YAAY,CAAC;QAC7B,UAAU;IACZ;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,iCAAiC;YACjC,gBAAgB,QAAQ,CAAC,YAAY;YAErC,MAAM;6DAAqB,CAAC;oBAC1B,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC1E;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;0CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,gBAAgB,UAAU,CAAC;gBAC7B;;QACF;iCAAG;QAAC;KAAW;IAEf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;wDAAgB,CAAC;oBACrB,IAAI,CAAC,QAAQ;oBAEb,OAAQ,MAAM,GAAG;wBACf,KAAK;4BACH;4BACA;wBACF,KAAK;4BACH,MAAM,cAAc;4BACpB;wEAAoB,CAAA,OAClB,OAAO,QAAQ,MAAM,GAAG,IAAI,OAAO,IAAI;;4BAEzC;wBACF,KAAK;4BACH,MAAM,cAAc;4BACpB;wEAAoB,CAAA,OAClB,OAAO,IAAI,OAAO,IAAI,QAAQ,MAAM,GAAG;;4BAEzC;wBACF,KAAK;4BACH,MAAM,cAAc;4BACpB,IAAI,oBAAoB,GAAG;gCACzB,SAAS,OAAO,CAAC,iBAAiB,CAAC,KAAK;gCACxC;4BACF;4BACA;oBACJ;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;0CAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;iCAAG;QAAC;QAAQ;QAAkB;QAAS;KAAS;IAEhD,qBACE,6LAAC;QAAI,KAAK;QAAW,WAAW,CAAC,SAAS,EAAE,WAAW;;0BACrD,6LAAC;gBACC,MAAK;gBACL,SAAS,IAAM,CAAC,YAAY,CAAC,SAAS,kBAAkB,cAAc;gBACtE,UAAU;gBACV,WAAW,CAAC,6VAA6V,EACvW,WAAW,kCAAkC,6DAC9C,CAAC,EAAE,SAAS,mDAAmD,IAAI;gBACpE,OAAO;oBACL,WAAW,SACP,0EACA;gBACN;;kCAEA,6LAAC;wBAAK,WAAW,CAAC,YAAY,EAAE,iBAAiB,eAAe,gBAAgB,+BAA+B,CAAC;kCAC7G,iBAAiB,eAAe,KAAK,GAAG;;;;;;kCAE3C,6LAAC;wBACC,WAAW,CAAC,yEAAyE,EAAE,SAAS,0BAA0B,IAAI;wBAC9H,MAAK;wBACL,QAAO;wBACP,SAAQ;wBACR,aAAa;kCAEb,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,GAAE;;;;;;;;;;;;;;;;;YAIxD,wBACC,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,WAAW;oBACX,iBAAiB;gBACnB;0BAEA,cAAA,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;4BAEC,MAAK;4BACL,SAAS;gCACP,SAAS,OAAO,KAAK;gCACrB;4BACF;4BACA,cAAc,IAAM,oBAAoB;4BACxC,WAAW,CAAC,+FAA+F,EACzG,UAAU,oBAAoB,OAAO,KAAK,KAAK,QAC3C,2FACA,8IACJ;4BACF,OAAO;gCACL,WAAW,AAAC,UAAU,oBAAoB,OAAO,KAAK,KAAK,QACvD,0EACA;4BACN;;8CAEA,6LAAC;oCAAI,WAAU;8CAAuC,OAAO,KAAK;;;;;;gCACjE,OAAO,WAAW,kBACjB,6LAAC;oCAAI,WAAU;8CACZ,OAAO,WAAW;;;;;;;2BArBlB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;AA+BjC;GApJwB;;QAWH,6JAAA,CAAA,QAAK;;;KAXF", "debugId": null}}, {"offset": {"line": 1646, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/test/augment-prompt-generator/src/lib/api-integrations.ts"], "sourcesContent": ["import { ProjectInput } from './types';\n\nexport interface APIIntegration {\n  id: string;\n  name: string;\n  description: string;\n  category: 'development' | 'design' | 'productivity' | 'ai' | 'communication' | 'cloud' | 'analytics';\n  icon: string;\n  baseUrl: string;\n  authType: 'none' | 'api_key' | 'oauth' | 'bearer';\n  isEnabled: boolean;\n  config: Record<string, any>;\n}\n\nexport interface WebhookConfig {\n  id: string;\n  name: string;\n  url: string;\n  events: string[];\n  isActive: boolean;\n  secret?: string;\n  headers: Record<string, string>;\n}\n\nexport interface APIResponse {\n  success: boolean;\n  data?: any;\n  error?: string;\n  statusCode?: number;\n}\n\nexport const AVAILABLE_INTEGRATIONS: APIIntegration[] = [\n  // Development Tools\n  {\n    id: 'github',\n    name: 'GitHub',\n    description: 'Create repositories and issues from prompts',\n    category: 'development',\n    icon: '🐙',\n    baseUrl: 'https://api.github.com',\n    authType: 'api_key',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'gitlab',\n    name: 'Git<PERSON><PERSON>',\n    description: 'Create projects and merge requests',\n    category: 'development',\n    icon: '🦊',\n    baseUrl: 'https://gitlab.com/api/v4',\n    authType: 'api_key',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'linear',\n    name: 'Linear',\n    description: 'Create issues and projects',\n    category: 'development',\n    icon: '📋',\n    baseUrl: 'https://api.linear.app/graphql',\n    authType: 'api_key',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'github-actions',\n    name: 'GitHub Actions',\n    description: 'Trigger CI/CD workflows',\n    category: 'development',\n    icon: '⚡',\n    baseUrl: 'https://api.github.com',\n    authType: 'api_key',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'gitlab-ci',\n    name: 'GitLab CI/CD',\n    description: 'Manage CI/CD pipelines',\n    category: 'development',\n    icon: '🔄',\n    baseUrl: 'https://gitlab.com/api/v4',\n    authType: 'api_key',\n    isEnabled: false,\n    config: {}\n  },\n\n  // Project Management\n  {\n    id: 'jira',\n    name: 'Jira',\n    description: 'Create tickets and manage projects',\n    category: 'productivity',\n    icon: '🎯',\n    baseUrl: 'https://api.atlassian.com',\n    authType: 'bearer',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'asana',\n    name: 'Asana',\n    description: 'Create tasks and manage workflows',\n    category: 'productivity',\n    icon: '📊',\n    baseUrl: 'https://app.asana.com/api/1.0',\n    authType: 'bearer',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'monday',\n    name: 'Monday.com',\n    description: 'Create boards and manage projects',\n    category: 'productivity',\n    icon: '📅',\n    baseUrl: 'https://api.monday.com/v2',\n    authType: 'api_key',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'trello',\n    name: 'Trello',\n    description: 'Create cards and manage boards',\n    category: 'productivity',\n    icon: '📋',\n    baseUrl: 'https://api.trello.com/1',\n    authType: 'api_key',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'notion',\n    name: 'Notion',\n    description: 'Save prompts as Notion pages',\n    category: 'productivity',\n    icon: '📝',\n    baseUrl: 'https://api.notion.com/v1',\n    authType: 'bearer',\n    isEnabled: false,\n    config: {}\n  },\n\n  // Communication Platforms\n  {\n    id: 'slack',\n    name: 'Slack',\n    description: 'Share prompts in Slack channels',\n    category: 'communication',\n    icon: '💬',\n    baseUrl: 'https://slack.com/api',\n    authType: 'bearer',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'discord',\n    name: 'Discord',\n    description: 'Share prompts in Discord servers',\n    category: 'communication',\n    icon: '🎮',\n    baseUrl: 'https://discord.com/api/v10',\n    authType: 'bearer',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'microsoft-teams',\n    name: 'Microsoft Teams',\n    description: 'Share prompts in Teams channels',\n    category: 'communication',\n    icon: '👥',\n    baseUrl: 'https://graph.microsoft.com/v1.0',\n    authType: 'bearer',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'telegram',\n    name: 'Telegram',\n    description: 'Send prompts via Telegram bot',\n    category: 'communication',\n    icon: '✈️',\n    baseUrl: 'https://api.telegram.org',\n    authType: 'api_key',\n    isEnabled: false,\n    config: {}\n  },\n\n  // Cloud Services\n  {\n    id: 'aws',\n    name: 'Amazon Web Services',\n    description: 'Deploy to AWS services',\n    category: 'cloud',\n    icon: '☁️',\n    baseUrl: 'https://aws.amazon.com',\n    authType: 'api_key',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'google-cloud',\n    name: 'Google Cloud Platform',\n    description: 'Deploy to GCP services',\n    category: 'cloud',\n    icon: '🌩️',\n    baseUrl: 'https://cloud.google.com',\n    authType: 'oauth',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'azure',\n    name: 'Microsoft Azure',\n    description: 'Deploy to Azure services',\n    category: 'cloud',\n    icon: '🔷',\n    baseUrl: 'https://management.azure.com',\n    authType: 'bearer',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'vercel',\n    name: 'Vercel',\n    description: 'Deploy web applications',\n    category: 'cloud',\n    icon: '▲',\n    baseUrl: 'https://api.vercel.com',\n    authType: 'bearer',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'netlify',\n    name: 'Netlify',\n    description: 'Deploy static sites',\n    category: 'cloud',\n    icon: '🌐',\n    baseUrl: 'https://api.netlify.com/api/v1',\n    authType: 'bearer',\n    isEnabled: false,\n    config: {}\n  },\n\n  // Design Tools\n  {\n    id: 'figma',\n    name: 'Figma',\n    description: 'Create design briefs from prompts',\n    category: 'design',\n    icon: '🎨',\n    baseUrl: 'https://api.figma.com/v1',\n    authType: 'api_key',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'sketch',\n    name: 'Sketch',\n    description: 'Create design documents',\n    category: 'design',\n    icon: '💎',\n    baseUrl: 'https://api.sketch.com/v1',\n    authType: 'bearer',\n    isEnabled: false,\n    config: {}\n  },\n\n  // Analytics & Monitoring\n  {\n    id: 'datadog',\n    name: 'Datadog',\n    description: 'Set up monitoring and alerts',\n    category: 'analytics',\n    icon: '📊',\n    baseUrl: 'https://api.datadoghq.com/api/v1',\n    authType: 'api_key',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'new-relic',\n    name: 'New Relic',\n    description: 'Application performance monitoring',\n    category: 'analytics',\n    icon: '📈',\n    baseUrl: 'https://api.newrelic.com/v2',\n    authType: 'api_key',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'sentry',\n    name: 'Sentry',\n    description: 'Error tracking and monitoring',\n    category: 'analytics',\n    icon: '🚨',\n    baseUrl: 'https://sentry.io/api/0',\n    authType: 'bearer',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'google-analytics',\n    name: 'Google Analytics',\n    description: 'Web analytics and reporting',\n    category: 'analytics',\n    icon: '📊',\n    baseUrl: 'https://analyticsreporting.googleapis.com/v4',\n    authType: 'oauth',\n    isEnabled: false,\n    config: {}\n  },\n\n  // AI Services\n  {\n    id: 'openai',\n    name: 'OpenAI',\n    description: 'Send prompts to ChatGPT and GPT models',\n    category: 'ai',\n    icon: '🤖',\n    baseUrl: 'https://api.openai.com/v1',\n    authType: 'bearer',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'anthropic',\n    name: 'Anthropic Claude',\n    description: 'Send prompts to Claude Sonnet 4',\n    category: 'ai',\n    icon: '🧠',\n    baseUrl: 'https://api.anthropic.com/v1',\n    authType: 'api_key',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'google-gemini',\n    name: 'Google Gemini',\n    description: 'Send prompts to Gemini Pro models',\n    category: 'ai',\n    icon: '💎',\n    baseUrl: 'https://generativelanguage.googleapis.com/v1',\n    authType: 'api_key',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'cursor',\n    name: 'Cursor IDE',\n    description: 'Send prompts to Cursor AI assistant',\n    category: 'ai',\n    icon: '⚡',\n    baseUrl: 'https://api.cursor.sh/v1',\n    authType: 'bearer',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'deepseek',\n    name: 'DeepSeek',\n    description: 'Send prompts to DeepSeek V3 models',\n    category: 'ai',\n    icon: '🔍',\n    baseUrl: 'https://api.deepseek.com/v1',\n    authType: 'bearer',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'perplexity',\n    name: 'Perplexity AI',\n    description: 'Send prompts to Perplexity models',\n    category: 'ai',\n    icon: '🔮',\n    baseUrl: 'https://api.perplexity.ai',\n    authType: 'bearer',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'cohere',\n    name: 'Cohere',\n    description: 'Send prompts to Cohere Command models',\n    category: 'ai',\n    icon: '🌟',\n    baseUrl: 'https://api.cohere.ai/v1',\n    authType: 'bearer',\n    isEnabled: false,\n    config: {}\n  },\n  {\n    id: 'huggingface',\n    name: 'Hugging Face',\n    description: 'Access open-source AI models',\n    category: 'ai',\n    icon: '🤗',\n    baseUrl: 'https://api-inference.huggingface.co',\n    authType: 'bearer',\n    isEnabled: false,\n    config: {}\n  }\n];\n\nclass APIIntegrationsService {\n  private integrations: APIIntegration[] = [];\n  private webhooks: WebhookConfig[] = [];\n  private storageKey = 'promptGenerator_integrations';\n  private webhooksKey = 'promptGenerator_webhooks';\n\n  constructor() {\n    this.loadIntegrations();\n    this.loadWebhooks();\n  }\n\n  private loadIntegrations(): void {\n    try {\n      const stored = localStorage.getItem(this.storageKey);\n      if (stored) {\n        this.integrations = JSON.parse(stored);\n      } else {\n        this.integrations = [...AVAILABLE_INTEGRATIONS];\n      }\n    } catch (error) {\n      console.error('Failed to load integrations:', error);\n      this.integrations = [...AVAILABLE_INTEGRATIONS];\n    }\n  }\n\n  private loadWebhooks(): void {\n    try {\n      const stored = localStorage.getItem(this.webhooksKey);\n      if (stored) {\n        this.webhooks = JSON.parse(stored);\n      }\n    } catch (error) {\n      console.error('Failed to load webhooks:', error);\n    }\n  }\n\n  private saveIntegrations(): void {\n    try {\n      localStorage.setItem(this.storageKey, JSON.stringify(this.integrations));\n    } catch (error) {\n      console.error('Failed to save integrations:', error);\n    }\n  }\n\n  private saveWebhooks(): void {\n    try {\n      localStorage.setItem(this.webhooksKey, JSON.stringify(this.webhooks));\n    } catch (error) {\n      console.error('Failed to save webhooks:', error);\n    }\n  }\n\n  getIntegrations(): APIIntegration[] {\n    return this.integrations;\n  }\n\n  getEnabledIntegrations(): APIIntegration[] {\n    return this.integrations.filter(integration => integration.isEnabled);\n  }\n\n  enableIntegration(id: string, config: Record<string, any>): boolean {\n    const integration = this.integrations.find(i => i.id === id);\n    if (!integration) return false;\n\n    integration.isEnabled = true;\n    integration.config = { ...integration.config, ...config };\n    this.saveIntegrations();\n    return true;\n  }\n\n  disableIntegration(id: string): boolean {\n    const integration = this.integrations.find(i => i.id === id);\n    if (!integration) return false;\n\n    integration.isEnabled = false;\n    integration.config = {};\n    this.saveIntegrations();\n    return true;\n  }\n\n  async testIntegration(id: string): Promise<APIResponse> {\n    const integration = this.integrations.find(i => i.id === id);\n    if (!integration || !integration.isEnabled) {\n      return { success: false, error: 'Integration not found or not enabled' };\n    }\n\n    try {\n      // Mock API test - in real implementation, this would make actual API calls\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Simulate different responses based on integration\n      const mockResponses: Record<string, any> = {\n        github: { success: true, data: { user: 'demo-user', repos: 42 } },\n        gitlab: { success: true, data: { user: 'demo-user', projects: 28 } },\n        notion: { success: true, data: { workspace: 'Demo Workspace' } },\n        slack: { success: true, data: { team: 'Demo Team', channels: 15 } },\n        figma: { success: true, data: { teams: 3, projects: 12 } },\n        linear: { success: true, data: { workspace: 'Demo Workspace', issues: 28 } },\n        discord: { success: true, data: { guild: 'Demo Server', channels: 8 } },\n        jira: { success: true, data: { projects: 12, issues: 156 } },\n        asana: { success: true, data: { workspaces: 3, tasks: 89 } },\n        monday: { success: true, data: { boards: 8, items: 234 } },\n        trello: { success: true, data: { boards: 15, cards: 67 } },\n        'microsoft-teams': { success: true, data: { teams: 5, channels: 23 } },\n        telegram: { success: true, data: { bot: 'active', chats: 12 } },\n        aws: { success: true, data: { regions: 3, services: 45 } },\n        'google-cloud': { success: true, data: { projects: 2, services: 32 } },\n        azure: { success: true, data: { subscriptions: 1, resources: 28 } },\n        vercel: { success: true, data: { projects: 8, deployments: 156 } },\n        netlify: { success: true, data: { sites: 12, builds: 89 } },\n        sketch: { success: true, data: { documents: 15, symbols: 234 } },\n        datadog: { success: true, data: { dashboards: 8, monitors: 45 } },\n        'new-relic': { success: true, data: { applications: 6, alerts: 23 } },\n        sentry: { success: true, data: { projects: 4, errors: 12 } },\n        'google-analytics': { success: true, data: { properties: 3, reports: 67 } },\n        openai: { success: true, data: { models: 8, usage: 'active' } },\n        anthropic: { success: true, data: { models: 3, status: 'connected' } },\n        'google-gemini': { success: true, data: { models: 5, quota: 'available' } },\n        cursor: { success: true, data: { workspace: 'connected', features: 12 } },\n        deepseek: { success: true, data: { models: 4, status: 'active' } },\n        perplexity: { success: true, data: { models: 3, credits: 1000 } },\n        cohere: { success: true, data: { models: 6, usage: 'normal' } },\n        huggingface: { success: true, data: { models: 50, status: 'connected' } }\n      };\n\n      return mockResponses[id] || { success: true, data: { status: 'connected' } };\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n      return { success: false, error: errorMessage };\n    }\n  }\n\n  async sendToIntegration(\n    integrationId: string,\n    projectInput: ProjectInput,\n    generatedPrompt: string,\n    action: string = 'create'\n  ): Promise<APIResponse> {\n    const integration = this.integrations.find(i => i.id === integrationId);\n    if (!integration || !integration.isEnabled) {\n      return { success: false, error: 'Integration not found or not enabled' };\n    }\n\n    try {\n      // Mock API call - in real implementation, this would make actual API calls\n      await new Promise(resolve => setTimeout(resolve, 1500));\n\n      const mockResponses: Record<string, any> = {\n        github: {\n          success: true,\n          data: {\n            repository: `${projectInput.projectName.toLowerCase().replace(/\\s+/g, '-')}`,\n            url: `https://github.com/demo-user/${projectInput.projectName.toLowerCase().replace(/\\s+/g, '-')}`,\n            action: 'Repository created'\n          }\n        },\n        notion: {\n          success: true,\n          data: {\n            page: projectInput.projectName,\n            url: 'https://notion.so/demo-page',\n            action: 'Page created'\n          }\n        },\n        slack: {\n          success: true,\n          data: {\n            channel: '#general',\n            message: 'Prompt shared successfully',\n            action: 'Message sent'\n          }\n        },\n        figma: {\n          success: true,\n          data: {\n            file: `${projectInput.projectName} Design Brief`,\n            url: 'https://figma.com/file/demo',\n            action: 'Design file created'\n          }\n        },\n        linear: {\n          success: true,\n          data: {\n            issue: `${projectInput.projectName} - Implementation`,\n            url: 'https://linear.app/demo/issue/DEMO-123',\n            action: 'Issue created'\n          }\n        },\n        discord: {\n          success: true,\n          data: {\n            channel: '#development',\n            message: 'Prompt shared in Discord',\n            action: 'Message sent'\n          }\n        },\n        gitlab: {\n          success: true,\n          data: {\n            project: `${projectInput.projectName.toLowerCase().replace(/\\s+/g, '-')}`,\n            url: `https://gitlab.com/demo-user/${projectInput.projectName.toLowerCase().replace(/\\s+/g, '-')}`,\n            action: 'Project created'\n          }\n        },\n        jira: {\n          success: true,\n          data: {\n            ticket: `${projectInput.projectName} - Epic`,\n            url: 'https://demo.atlassian.net/browse/PROJ-123',\n            action: 'Epic created'\n          }\n        },\n        asana: {\n          success: true,\n          data: {\n            task: `${projectInput.projectName} - Project`,\n            url: 'https://app.asana.com/0/123456/789012',\n            action: 'Task created'\n          }\n        },\n        monday: {\n          success: true,\n          data: {\n            item: `${projectInput.projectName} - Board Item`,\n            url: 'https://demo.monday.com/boards/123456',\n            action: 'Item created'\n          }\n        },\n        openai: {\n          success: true,\n          data: {\n            response: 'Prompt sent to ChatGPT',\n            model: 'gpt-4',\n            action: 'Prompt processed'\n          }\n        },\n        anthropic: {\n          success: true,\n          data: {\n            response: 'Prompt sent to Claude Sonnet 4',\n            model: 'claude-3-5-sonnet',\n            action: 'Prompt processed'\n          }\n        },\n        'google-gemini': {\n          success: true,\n          data: {\n            response: 'Prompt sent to Gemini Pro',\n            model: 'gemini-pro',\n            action: 'Prompt processed'\n          }\n        },\n        cursor: {\n          success: true,\n          data: {\n            response: 'Prompt sent to Cursor AI',\n            workspace: 'active',\n            action: 'Prompt processed'\n          }\n        },\n        vercel: {\n          success: true,\n          data: {\n            deployment: `${projectInput.projectName.toLowerCase().replace(/\\s+/g, '-')}`,\n            url: `https://${projectInput.projectName.toLowerCase().replace(/\\s+/g, '-')}.vercel.app`,\n            action: 'Deployment created'\n          }\n        },\n        aws: {\n          success: true,\n          data: {\n            stack: `${projectInput.projectName}-stack`,\n            region: 'us-east-1',\n            action: 'CloudFormation stack created'\n          }\n        }\n      };\n\n      return mockResponses[integrationId] || { success: true, data: { action: 'Completed' } };\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n      return { success: false, error: errorMessage };\n    }\n  }\n\n  // Webhook management\n  addWebhook(config: Omit<WebhookConfig, 'id'>): WebhookConfig {\n    const webhook: WebhookConfig = {\n      ...config,\n      id: 'webhook_' + Math.random().toString(36).substring(2, 11)\n    };\n    \n    this.webhooks.push(webhook);\n    this.saveWebhooks();\n    return webhook;\n  }\n\n  updateWebhook(id: string, updates: Partial<WebhookConfig>): boolean {\n    const index = this.webhooks.findIndex(w => w.id === id);\n    if (index === -1) return false;\n\n    this.webhooks[index] = { ...this.webhooks[index], ...updates };\n    this.saveWebhooks();\n    return true;\n  }\n\n  deleteWebhook(id: string): boolean {\n    const index = this.webhooks.findIndex(w => w.id === id);\n    if (index === -1) return false;\n\n    this.webhooks.splice(index, 1);\n    this.saveWebhooks();\n    return true;\n  }\n\n  getWebhooks(): WebhookConfig[] {\n    return this.webhooks;\n  }\n\n  async triggerWebhook(webhookId: string, event: string, data: any): Promise<APIResponse> {\n    const webhook = this.webhooks.find(w => w.id === webhookId);\n    if (!webhook || !webhook.isActive || !webhook.events.includes(event)) {\n      return { success: false, error: 'Webhook not found, inactive, or event not supported' };\n    }\n\n    try {\n      // Mock webhook call\n      await new Promise(resolve => setTimeout(resolve, 800));\n      return { success: true, data: { webhook: webhook.name, event, delivered: true } };\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n      return { success: false, error: errorMessage };\n    }\n  }\n\n  exportIntegrationData(): string {\n    return JSON.stringify({\n      integrations: this.integrations,\n      webhooks: this.webhooks,\n      exportedAt: new Date()\n    }, null, 2);\n  }\n\n  clearData(): void {\n    this.integrations = [...AVAILABLE_INTEGRATIONS];\n    this.webhooks = [];\n    localStorage.removeItem(this.storageKey);\n    localStorage.removeItem(this.webhooksKey);\n  }\n}\n\nexport const apiIntegrationsService = new APIIntegrationsService();\n"], "names": [], "mappings": ";;;;AA+BO,MAAM,yBAA2C;IACtD,oBAAoB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IAEA,qBAAqB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IAEA,0BAA0B;IAC1B;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IAEA,iBAAiB;IACjB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IAEA,eAAe;IACf;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IAEA,yBAAyB;IACzB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IAEA,cAAc;IACd;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;IACX;CACD;AAED,MAAM;IACI,eAAiC,EAAE,CAAC;IACpC,WAA4B,EAAE,CAAC;IAC/B,aAAa,+BAA+B;IAC5C,cAAc,2BAA2B;IAEjD,aAAc;QACZ,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,YAAY;IACnB;IAEQ,mBAAyB;QAC/B,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU;YACnD,IAAI,QAAQ;gBACV,IAAI,CAAC,YAAY,GAAG,KAAK,KAAK,CAAC;YACjC,OAAO;gBACL,IAAI,CAAC,YAAY,GAAG;uBAAI;iBAAuB;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,IAAI,CAAC,YAAY,GAAG;mBAAI;aAAuB;QACjD;IACF;IAEQ,eAAqB;QAC3B,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC,IAAI,CAAC,WAAW;YACpD,IAAI,QAAQ;gBACV,IAAI,CAAC,QAAQ,GAAG,KAAK,KAAK,CAAC;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEQ,mBAAyB;QAC/B,IAAI;YACF,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC,IAAI,CAAC,YAAY;QACxE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEQ,eAAqB;QAC3B,IAAI;YACF,aAAa,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS,CAAC,IAAI,CAAC,QAAQ;QACrE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,kBAAoC;QAClC,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA,yBAA2C;QACzC,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA,cAAe,YAAY,SAAS;IACtE;IAEA,kBAAkB,EAAU,EAAE,MAA2B,EAAW;QAClE,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACzD,IAAI,CAAC,aAAa,OAAO;QAEzB,YAAY,SAAS,GAAG;QACxB,YAAY,MAAM,GAAG;YAAE,GAAG,YAAY,MAAM;YAAE,GAAG,MAAM;QAAC;QACxD,IAAI,CAAC,gBAAgB;QACrB,OAAO;IACT;IAEA,mBAAmB,EAAU,EAAW;QACtC,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACzD,IAAI,CAAC,aAAa,OAAO;QAEzB,YAAY,SAAS,GAAG;QACxB,YAAY,MAAM,GAAG,CAAC;QACtB,IAAI,CAAC,gBAAgB;QACrB,OAAO;IACT;IAEA,MAAM,gBAAgB,EAAU,EAAwB;QACtD,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACzD,IAAI,CAAC,eAAe,CAAC,YAAY,SAAS,EAAE;YAC1C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAuC;QACzE;QAEA,IAAI;YACF,2EAA2E;YAC3E,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,oDAAoD;YACpD,MAAM,gBAAqC;gBACzC,QAAQ;oBAAE,SAAS;oBAAM,MAAM;wBAAE,MAAM;wBAAa,OAAO;oBAAG;gBAAE;gBAChE,QAAQ;oBAAE,SAAS;oBAAM,MAAM;wBAAE,MAAM;wBAAa,UAAU;oBAAG;gBAAE;gBACnE,QAAQ;oBAAE,SAAS;oBAAM,MAAM;wBAAE,WAAW;oBAAiB;gBAAE;gBAC/D,OAAO;oBAAE,SAAS;oBAAM,MAAM;wBAAE,MAAM;wBAAa,UAAU;oBAAG;gBAAE;gBAClE,OAAO;oBAAE,SAAS;oBAAM,MAAM;wBAAE,OAAO;wBAAG,UAAU;oBAAG;gBAAE;gBACzD,QAAQ;oBAAE,SAAS;oBAAM,MAAM;wBAAE,WAAW;wBAAkB,QAAQ;oBAAG;gBAAE;gBAC3E,SAAS;oBAAE,SAAS;oBAAM,MAAM;wBAAE,OAAO;wBAAe,UAAU;oBAAE;gBAAE;gBACtE,MAAM;oBAAE,SAAS;oBAAM,MAAM;wBAAE,UAAU;wBAAI,QAAQ;oBAAI;gBAAE;gBAC3D,OAAO;oBAAE,SAAS;oBAAM,MAAM;wBAAE,YAAY;wBAAG,OAAO;oBAAG;gBAAE;gBAC3D,QAAQ;oBAAE,SAAS;oBAAM,MAAM;wBAAE,QAAQ;wBAAG,OAAO;oBAAI;gBAAE;gBACzD,QAAQ;oBAAE,SAAS;oBAAM,MAAM;wBAAE,QAAQ;wBAAI,OAAO;oBAAG;gBAAE;gBACzD,mBAAmB;oBAAE,SAAS;oBAAM,MAAM;wBAAE,OAAO;wBAAG,UAAU;oBAAG;gBAAE;gBACrE,UAAU;oBAAE,SAAS;oBAAM,MAAM;wBAAE,KAAK;wBAAU,OAAO;oBAAG;gBAAE;gBAC9D,KAAK;oBAAE,SAAS;oBAAM,MAAM;wBAAE,SAAS;wBAAG,UAAU;oBAAG;gBAAE;gBACzD,gBAAgB;oBAAE,SAAS;oBAAM,MAAM;wBAAE,UAAU;wBAAG,UAAU;oBAAG;gBAAE;gBACrE,OAAO;oBAAE,SAAS;oBAAM,MAAM;wBAAE,eAAe;wBAAG,WAAW;oBAAG;gBAAE;gBAClE,QAAQ;oBAAE,SAAS;oBAAM,MAAM;wBAAE,UAAU;wBAAG,aAAa;oBAAI;gBAAE;gBACjE,SAAS;oBAAE,SAAS;oBAAM,MAAM;wBAAE,OAAO;wBAAI,QAAQ;oBAAG;gBAAE;gBAC1D,QAAQ;oBAAE,SAAS;oBAAM,MAAM;wBAAE,WAAW;wBAAI,SAAS;oBAAI;gBAAE;gBAC/D,SAAS;oBAAE,SAAS;oBAAM,MAAM;wBAAE,YAAY;wBAAG,UAAU;oBAAG;gBAAE;gBAChE,aAAa;oBAAE,SAAS;oBAAM,MAAM;wBAAE,cAAc;wBAAG,QAAQ;oBAAG;gBAAE;gBACpE,QAAQ;oBAAE,SAAS;oBAAM,MAAM;wBAAE,UAAU;wBAAG,QAAQ;oBAAG;gBAAE;gBAC3D,oBAAoB;oBAAE,SAAS;oBAAM,MAAM;wBAAE,YAAY;wBAAG,SAAS;oBAAG;gBAAE;gBAC1E,QAAQ;oBAAE,SAAS;oBAAM,MAAM;wBAAE,QAAQ;wBAAG,OAAO;oBAAS;gBAAE;gBAC9D,WAAW;oBAAE,SAAS;oBAAM,MAAM;wBAAE,QAAQ;wBAAG,QAAQ;oBAAY;gBAAE;gBACrE,iBAAiB;oBAAE,SAAS;oBAAM,MAAM;wBAAE,QAAQ;wBAAG,OAAO;oBAAY;gBAAE;gBAC1E,QAAQ;oBAAE,SAAS;oBAAM,MAAM;wBAAE,WAAW;wBAAa,UAAU;oBAAG;gBAAE;gBACxE,UAAU;oBAAE,SAAS;oBAAM,MAAM;wBAAE,QAAQ;wBAAG,QAAQ;oBAAS;gBAAE;gBACjE,YAAY;oBAAE,SAAS;oBAAM,MAAM;wBAAE,QAAQ;wBAAG,SAAS;oBAAK;gBAAE;gBAChE,QAAQ;oBAAE,SAAS;oBAAM,MAAM;wBAAE,QAAQ;wBAAG,OAAO;oBAAS;gBAAE;gBAC9D,aAAa;oBAAE,SAAS;oBAAM,MAAM;wBAAE,QAAQ;wBAAI,QAAQ;oBAAY;gBAAE;YAC1E;YAEA,OAAO,aAAa,CAAC,GAAG,IAAI;gBAAE,SAAS;gBAAM,MAAM;oBAAE,QAAQ;gBAAY;YAAE;QAC7E,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAa;QAC/C;IACF;IAEA,MAAM,kBACJ,aAAqB,EACrB,YAA0B,EAC1B,eAAuB,EACvB,SAAiB,QAAQ,EACH;QACtB,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACzD,IAAI,CAAC,eAAe,CAAC,YAAY,SAAS,EAAE;YAC1C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAuC;QACzE;QAEA,IAAI;YACF,2EAA2E;YAC3E,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,gBAAqC;gBACzC,QAAQ;oBACN,SAAS;oBACT,MAAM;wBACJ,YAAY,GAAG,aAAa,WAAW,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM;wBAC5E,KAAK,CAAC,6BAA6B,EAAE,aAAa,WAAW,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM;wBAClG,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN,SAAS;oBACT,MAAM;wBACJ,MAAM,aAAa,WAAW;wBAC9B,KAAK;wBACL,QAAQ;oBACV;gBACF;gBACA,OAAO;oBACL,SAAS;oBACT,MAAM;wBACJ,SAAS;wBACT,SAAS;wBACT,QAAQ;oBACV;gBACF;gBACA,OAAO;oBACL,SAAS;oBACT,MAAM;wBACJ,MAAM,GAAG,aAAa,WAAW,CAAC,aAAa,CAAC;wBAChD,KAAK;wBACL,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN,SAAS;oBACT,MAAM;wBACJ,OAAO,GAAG,aAAa,WAAW,CAAC,iBAAiB,CAAC;wBACrD,KAAK;wBACL,QAAQ;oBACV;gBACF;gBACA,SAAS;oBACP,SAAS;oBACT,MAAM;wBACJ,SAAS;wBACT,SAAS;wBACT,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN,SAAS;oBACT,MAAM;wBACJ,SAAS,GAAG,aAAa,WAAW,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM;wBACzE,KAAK,CAAC,6BAA6B,EAAE,aAAa,WAAW,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM;wBAClG,QAAQ;oBACV;gBACF;gBACA,MAAM;oBACJ,SAAS;oBACT,MAAM;wBACJ,QAAQ,GAAG,aAAa,WAAW,CAAC,OAAO,CAAC;wBAC5C,KAAK;wBACL,QAAQ;oBACV;gBACF;gBACA,OAAO;oBACL,SAAS;oBACT,MAAM;wBACJ,MAAM,GAAG,aAAa,WAAW,CAAC,UAAU,CAAC;wBAC7C,KAAK;wBACL,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN,SAAS;oBACT,MAAM;wBACJ,MAAM,GAAG,aAAa,WAAW,CAAC,aAAa,CAAC;wBAChD,KAAK;wBACL,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN,SAAS;oBACT,MAAM;wBACJ,UAAU;wBACV,OAAO;wBACP,QAAQ;oBACV;gBACF;gBACA,WAAW;oBACT,SAAS;oBACT,MAAM;wBACJ,UAAU;wBACV,OAAO;wBACP,QAAQ;oBACV;gBACF;gBACA,iBAAiB;oBACf,SAAS;oBACT,MAAM;wBACJ,UAAU;wBACV,OAAO;wBACP,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN,SAAS;oBACT,MAAM;wBACJ,UAAU;wBACV,WAAW;wBACX,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN,SAAS;oBACT,MAAM;wBACJ,YAAY,GAAG,aAAa,WAAW,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM;wBAC5E,KAAK,CAAC,QAAQ,EAAE,aAAa,WAAW,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,KAAK,WAAW,CAAC;wBACxF,QAAQ;oBACV;gBACF;gBACA,KAAK;oBACH,SAAS;oBACT,MAAM;wBACJ,OAAO,GAAG,aAAa,WAAW,CAAC,MAAM,CAAC;wBAC1C,QAAQ;wBACR,QAAQ;oBACV;gBACF;YACF;YAEA,OAAO,aAAa,CAAC,cAAc,IAAI;gBAAE,SAAS;gBAAM,MAAM;oBAAE,QAAQ;gBAAY;YAAE;QACxF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAa;QAC/C;IACF;IAEA,qBAAqB;IACrB,WAAW,MAAiC,EAAiB;QAC3D,MAAM,UAAyB;YAC7B,GAAG,MAAM;YACT,IAAI,aAAa,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;QAC3D;QAEA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACnB,IAAI,CAAC,YAAY;QACjB,OAAO;IACT;IAEA,cAAc,EAAU,EAAE,OAA+B,EAAW;QAClE,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACpD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG;YAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM;YAAE,GAAG,OAAO;QAAC;QAC7D,IAAI,CAAC,YAAY;QACjB,OAAO;IACT;IAEA,cAAc,EAAU,EAAW;QACjC,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACpD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO;QAC5B,IAAI,CAAC,YAAY;QACjB,OAAO;IACT;IAEA,cAA+B;QAC7B,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA,MAAM,eAAe,SAAiB,EAAE,KAAa,EAAE,IAAS,EAAwB;QACtF,MAAM,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACjD,IAAI,CAAC,WAAW,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,MAAM,CAAC,QAAQ,CAAC,QAAQ;YACpE,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAsD;QACxF;QAEA,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,OAAO;gBAAE,SAAS;gBAAM,MAAM;oBAAE,SAAS,QAAQ,IAAI;oBAAE;oBAAO,WAAW;gBAAK;YAAE;QAClF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAa;QAC/C;IACF;IAEA,wBAAgC;QAC9B,OAAO,KAAK,SAAS,CAAC;YACpB,cAAc,IAAI,CAAC,YAAY;YAC/B,UAAU,IAAI,CAAC,QAAQ;YACvB,YAAY,IAAI;QAClB,GAAG,MAAM;IACX;IAEA,YAAkB;QAChB,IAAI,CAAC,YAAY,GAAG;eAAI;SAAuB;QAC/C,IAAI,CAAC,QAAQ,GAAG,EAAE;QAClB,aAAa,UAAU,CAAC,IAAI,CAAC,UAAU;QACvC,aAAa,UAAU,CAAC,IAAI,CAAC,WAAW;IAC1C;AACF;AAEO,MAAM,yBAAyB,IAAI", "debugId": null}}, {"offset": {"line": 2579, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/test/augment-prompt-generator/src/components/AdvancedSettings.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { createPortal } from 'react-dom';\nimport { AI_MODELS, PromptOptimizationOptions } from '@/lib/ai-service';\nimport { apiIntegrationsService, APIIntegration } from '@/lib/api-integrations';\n\nimport { ProjectInput } from '@/lib/types';\nimport CustomSelect from './CustomSelect';\n\ninterface AdvancedSettingsProps {\n  isOpen: boolean;\n  onClose: () => void;\n  settings: PromptOptimizationOptions;\n  onSettingsChange: (settings: PromptOptimizationOptions) => void;\n  recommendedModel?: string;\n  projectInput?: ProjectInput;\n  generatedPrompt?: string;\n}\n\nexport default function AdvancedSettings({\n  isOpen,\n  onClose,\n  settings,\n  onSettingsChange,\n  recommendedModel,\n  projectInput,\n  generatedPrompt\n}: AdvancedSettingsProps) {\n  const [localSettings, setLocalSettings] = useState<PromptOptimizationOptions>(settings);\n  const [activeTab, setActiveTab] = useState<'ai' | 'integrations'>('ai');\n\n  // Integration states\n  const [integrations, setIntegrations] = useState<APIIntegration[]>([]);\n  const [selectedIntegration, setSelectedIntegration] = useState<APIIntegration | null>(null);\n  const [isConfiguring, setIsConfiguring] = useState(false);\n  const [isTesting, setIsTesting] = useState(false);\n  const [isSending, setIsSending] = useState(false);\n  const [testResults, setTestResults] = useState<Record<string, { success: boolean; data?: unknown; error?: string }>>({});\n  const [configForm, setConfigForm] = useState<Record<string, string>>({});\n\n\n\n  useEffect(() => {\n    if (isOpen && activeTab === 'integrations') {\n      loadIntegrationData();\n    }\n\n  }, [isOpen, activeTab]);\n\n  const loadIntegrationData = () => {\n    setIntegrations(apiIntegrationsService.getIntegrations());\n  };\n\n\n\n  const handleEnableIntegration = async (integration: APIIntegration) => {\n    setSelectedIntegration(integration);\n    setIsConfiguring(true);\n    setConfigForm({});\n  };\n\n  const handleSaveConfig = async () => {\n    if (!selectedIntegration) return;\n\n    const success = apiIntegrationsService.enableIntegration(selectedIntegration.id, configForm);\n    if (success) {\n      loadIntegrationData();\n      setIsConfiguring(false);\n      setSelectedIntegration(null);\n      setConfigForm({});\n    }\n  };\n\n  const handleDisableIntegration = (id: string) => {\n    apiIntegrationsService.disableIntegration(id);\n    loadIntegrationData();\n  };\n\n  const handleTestIntegration = async (integration: APIIntegration) => {\n    setIsTesting(true);\n    const result = await apiIntegrationsService.testIntegration(integration.id);\n    setTestResults({ ...testResults, [integration.id]: result });\n    setIsTesting(false);\n  };\n\n  const handleSendToIntegration = async (integration: APIIntegration) => {\n    if (!projectInput || !generatedPrompt) return;\n\n    setIsSending(true);\n    const result = await apiIntegrationsService.sendToIntegration(\n      integration.id,\n      projectInput,\n      generatedPrompt\n    );\n    setTestResults({ ...testResults, [`${integration.id}_send`]: result });\n    setIsSending(false);\n  };\n\n  const modelOptions = Object.entries(AI_MODELS).map(([, model]) => ({\n    value: model.id,\n    label: model.name,\n    description: `${model.provider} • ${model.description}`\n  }));\n\n  const optimizationOptions = [\n    { value: 'basic', label: 'Basic', description: 'Clear, structured prompt with essentials' },\n    { value: 'enhanced', label: 'Enhanced', description: 'Comprehensive with detailed specifications' },\n    { value: 'expert', label: 'Expert', description: 'Advanced architectural considerations' }\n  ];\n\n  const audienceOptions = [\n    { value: 'developer', label: 'Developer', description: 'Technical implementation focus' },\n    { value: 'business', label: 'Business', description: 'Business value and outcomes' },\n    { value: 'technical', label: 'Technical', description: 'System requirements and integration' },\n    { value: 'general', label: 'General', description: 'Balanced technical and business' }\n  ];\n\n  const handleSave = () => {\n    onSettingsChange(localSettings);\n    onClose();\n  };\n\n  const handleReset = () => {\n    const defaultSettings: PromptOptimizationOptions = {\n      model: recommendedModel || 'deepseek/deepseek-chat-v3-0324:free',\n      optimizationLevel: 'enhanced',\n      includeExamples: true,\n      includeConstraints: true,\n      includeMetrics: false,\n      targetAudience: 'developer'\n    };\n    setLocalSettings(defaultSettings);\n  };\n\n\n\n  if (!isOpen) return null;\n\n  const selectedModel = Object.values(AI_MODELS).find(m => m.id === localSettings.model);\n\n  return (\n    <>\n      {/* Main Modal */}\n      <div className=\"fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-2 sm:p-4\">\n        <div className=\"bg-black border border-white/20 rounded-xl shadow-2xl p-4 sm:p-6 w-full max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-y-auto\" style={{\n          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8)'\n        }}>\n        <div className=\"flex justify-between items-center mb-4 sm:mb-6\">\n          <h3 className=\"text-lg sm:text-xl font-semibold text-white\">AI Settings & Integrations</h3>\n          <button\n            onClick={onClose}\n            className=\"bg-gray-800 text-white px-3 py-1 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm flex-shrink-0\"\n          >\n            Close\n          </button>\n        </div>\n\n        {/* Tabs - AI Settings, Integrations */}\n        <div className=\"flex gap-2 sm:gap-3 mb-4 sm:mb-6 overflow-x-auto\">\n          <button\n            onClick={() => setActiveTab('ai')}\n            className={`px-3 sm:px-4 py-2 rounded-md transition-all duration-300 whitespace-nowrap text-sm sm:text-base flex-shrink-0 ${\n              activeTab === 'ai'\n                ? 'bg-white text-black'\n                : 'bg-gray-800 text-gray-400 hover:bg-gray-700'\n            }`}\n          >\n            🤖 AI Settings\n          </button>\n          <button\n            onClick={() => setActiveTab('integrations')}\n            className={`px-3 sm:px-4 py-2 rounded-md transition-all duration-300 whitespace-nowrap text-sm sm:text-base flex-shrink-0 ${\n              activeTab === 'integrations'\n                ? 'bg-white text-black'\n                : 'bg-gray-800 text-gray-400 hover:bg-gray-700'\n            }`}\n          >\n            🔗 Integrations\n          </button>\n        </div>\n\n        {activeTab === 'ai' && (\n          <div className=\"space-y-4 sm:space-y-6\">\n            {/* AI Model Selection */}\n            <div>\n              <label className=\"block text-sm font-medium text-white mb-2\">\n                AI Model\n                {recommendedModel && localSettings.model === recommendedModel && (\n                  <span className=\"ml-2 text-xs bg-white text-black px-2 py-1 rounded\">Recommended</span>\n                )}\n              </label>\n              <CustomSelect\n                value={localSettings.model}\n                onChange={(value) => setLocalSettings(prev => ({ ...prev, model: value }))}\n                options={modelOptions}\n                placeholder=\"Select AI model\"\n              />\n              {selectedModel && (\n                <div className=\"mt-2 p-3 bg-gray-900 border border-white/10 rounded-lg\">\n                  <div className=\"text-sm text-white font-medium mb-1\">{selectedModel.name}</div>\n                  <div className=\"text-xs text-gray-400 mb-2\">{selectedModel.description}</div>\n                  <div className=\"flex flex-wrap gap-1 mb-2\">\n                    {selectedModel.strengths.map((strength, index) => (\n                      <span key={index} className=\"bg-gray-700 text-gray-200 text-xs px-2 py-1 rounded\">\n                        {strength}\n                      </span>\n                    ))}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    Max tokens: {selectedModel.maxTokens.toLocaleString()} •\n                    Cost: ${selectedModel.costPer1kTokens}/1k tokens\n                  </div>\n                </div>\n              )}\n            </div>\n\n          {/* Optimization Level */}\n          <div>\n            <label className=\"block text-sm font-medium text-white mb-2\">Optimization Level</label>\n            <CustomSelect\n              value={localSettings.optimizationLevel}\n              onChange={(value) => setLocalSettings(prev => ({ ...prev, optimizationLevel: value as 'basic' | 'enhanced' | 'expert' }))}\n              options={optimizationOptions}\n              placeholder=\"Select optimization level\"\n            />\n          </div>\n\n          {/* Target Audience */}\n          <div>\n            <label className=\"block text-sm font-medium text-white mb-2\">Target Audience</label>\n            <CustomSelect\n              value={localSettings.targetAudience}\n              onChange={(value) => setLocalSettings(prev => ({ ...prev, targetAudience: value as 'developer' | 'business' | 'technical' | 'general' }))}\n              options={audienceOptions}\n              placeholder=\"Select target audience\"\n            />\n          </div>\n\n          {/* Additional Options */}\n          <div>\n            <label className=\"block text-sm font-medium text-white mb-3\">Additional Options</label>\n            <div className=\"space-y-3\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={localSettings.includeExamples}\n                  onChange={(e) => setLocalSettings(prev => ({ ...prev, includeExamples: e.target.checked }))}\n                  className=\"mr-3 accent-white\"\n                />\n                <div>\n                  <span className=\"text-white text-sm font-medium\">Include Examples</span>\n                  <p className=\"text-xs text-gray-400\">Add relevant code examples and snippets</p>\n                </div>\n              </label>\n\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={localSettings.includeConstraints}\n                  onChange={(e) => setLocalSettings(prev => ({ ...prev, includeConstraints: e.target.checked }))}\n                  className=\"mr-3 accent-white\"\n                />\n                <div>\n                  <span className=\"text-white text-sm font-medium\">Include Constraints</span>\n                  <p className=\"text-xs text-gray-400\">Specify technical limitations and requirements</p>\n                </div>\n              </label>\n\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={localSettings.includeMetrics}\n                  onChange={(e) => setLocalSettings(prev => ({ ...prev, includeMetrics: e.target.checked }))}\n                  className=\"mr-3 accent-white\"\n                />\n                <div>\n                  <span className=\"text-white text-sm font-medium\">Include Success Metrics</span>\n                  <p className=\"text-xs text-gray-400\">Add KPIs and measurable outcomes</p>\n                </div>\n              </label>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex flex-col sm:flex-row justify-between pt-4 sm:pt-6 border-t border-white/10 space-y-3 sm:space-y-0\">\n            <button\n              onClick={handleReset}\n              className=\"group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-medium text-white bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl border border-white/10 transition-all duration-300 hover:border-white/20 hover:from-gray-700/90 hover:to-gray-800/90 focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-black\"\n              style={{\n                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'\n              }}\n            >\n              <span className=\"flex items-center justify-center space-x-2\">\n                <svg className=\"w-4 h-4 transition-transform duration-300 group-hover:rotate-180\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n                </svg>\n                <span>Reset to Defaults</span>\n              </span>\n            </button>\n            <div className=\"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3\">\n              <button\n                onClick={onClose}\n                className=\"group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-medium text-gray-300 bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl border border-white/10 transition-all duration-300 hover:border-white/20 hover:from-gray-700/90 hover:to-gray-800/90 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-black\"\n                style={{\n                  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'\n                }}\n              >\n                <span className=\"flex items-center justify-center space-x-2\">\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                  <span>Cancel</span>\n                </span>\n              </button>\n              <button\n                onClick={handleSave}\n                className=\"group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-semibold text-black bg-gradient-to-br from-white to-gray-100 backdrop-blur-xl border border-white/20 transition-all duration-300 hover:from-gray-100 hover:to-gray-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-black transform hover:scale-[1.02]\"\n                style={{\n                  boxShadow: '0 6px 20px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)'\n                }}\n              >\n                <span className=\"flex items-center justify-center space-x-2\">\n                  <svg className=\"w-4 h-4 transition-transform duration-300 group-hover:scale-110\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                  <span>Save Settings</span>\n                </span>\n              </button>\n            </div>\n          </div>\n          </div>\n        )}\n\n        {activeTab === 'integrations' && (\n          <div className=\"space-y-4 sm:space-y-6\">\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\">\n              {integrations.map((integration) => (\n                <div\n                  key={integration.id}\n                  className={`p-3 sm:p-4 rounded-lg border transition-all duration-300 ${\n                    integration.isEnabled\n                      ? 'bg-gray-800 border-white'\n                      : 'bg-gray-900 border-white/10 hover:border-white/20'\n                  }`}\n                >\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <div className=\"flex items-start space-x-2 sm:space-x-3 flex-1 min-w-0\">\n                      <div className=\"text-xl sm:text-2xl flex-shrink-0\">{integration.icon}</div>\n                      <div className=\"min-w-0 flex-1\">\n                        <h4 className=\"text-white font-medium text-sm sm:text-base truncate\">{integration.name}</h4>\n                        <p className=\"text-gray-400 text-xs sm:text-sm line-clamp-2\">{integration.description}</p>\n                      </div>\n                    </div>\n                    <div className={`w-3 h-3 rounded-full ${\n                      integration.isEnabled ? 'bg-white' : 'bg-gray-500'\n                    }`} />\n                  </div>\n\n                  <div className=\"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2\">\n                    {!integration.isEnabled ? (\n                      <button\n                        onClick={() => handleEnableIntegration(integration)}\n                        className=\"flex-1 bg-gray-800 text-white py-2 px-3 rounded-md hover:bg-gray-700 transition-all duration-300 text-xs sm:text-sm\"\n                      >\n                        Enable\n                      </button>\n                    ) : (\n                      <>\n                        <button\n                          onClick={() => handleTestIntegration(integration)}\n                          disabled={isTesting}\n                          className=\"flex-1 bg-gray-700 text-white py-2 px-3 rounded-md hover:bg-gray-600 transition-all duration-300 text-xs sm:text-sm disabled:opacity-50\"\n                        >\n                          {isTesting ? 'Testing...' : 'Test'}\n                        </button>\n                        {projectInput && generatedPrompt && (\n                          <button\n                            onClick={() => handleSendToIntegration(integration)}\n                            disabled={isSending}\n                            className=\"flex-1 bg-gray-800 text-white py-2 px-3 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm disabled:opacity-50\"\n                          >\n                            {isSending ? 'Sending...' : 'Send'}\n                          </button>\n                        )}\n                        <button\n                          onClick={() => handleDisableIntegration(integration.id)}\n                          className=\"bg-gray-800 text-white py-2 px-3 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm\"\n                        >\n                          Disable\n                        </button>\n                      </>\n                    )}\n                  </div>\n\n                  {testResults[integration.id] && (\n                    <div className=\"mt-3 p-2 bg-gray-800 border border-white/10 rounded text-xs\">\n                      <div className={`font-medium ${\n                        testResults[integration.id].success ? 'text-white' : 'text-gray-300'\n                      }`}>\n                        {testResults[integration.id].success ? 'Success' : 'Failed'}\n                      </div>\n                      <div className=\"text-gray-400 mt-1\">\n                        {testResults[integration.id].error || 'Connection successful'}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n\n\n          </div>\n        )}\n\n\n        </div>\n      </div>\n\n      {/* Configuration Modal - Rendered using Portal for proper positioning */}\n      {isConfiguring && selectedIntegration && createPortal(\n        <div\n          className=\"fixed inset-0 bg-black/90 flex items-center justify-center p-4\"\n          style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            zIndex: 10000,\n            margin: 0,\n            padding: '1rem'\n          }}\n          onClick={(e) => {\n            if (e.target === e.currentTarget) {\n              setIsConfiguring(false);\n            }\n          }}\n        >\n          <div\n            className=\"bg-black border border-white/20 rounded-xl shadow-2xl p-6 w-full max-w-md mx-auto my-auto\"\n            style={{\n              maxHeight: '90vh',\n              overflow: 'auto',\n              position: 'relative',\n              transform: 'none'\n            }}\n            onClick={(e) => e.stopPropagation()}\n          >\n            <h3 className=\"text-lg font-semibold text-white mb-4\">\n              Configure {selectedIntegration.name}\n            </h3>\n\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-white mb-2\">\n                  API Token\n                </label>\n                <input\n                  type=\"password\"\n                  value={configForm.token || ''}\n                  onChange={(e) => setConfigForm({ ...configForm, token: e.target.value })}\n                  className=\"w-full px-3 py-2 bg-gray-900 border border-white/10 rounded-md text-white placeholder-gray-400 focus:border-white/30 focus:outline-none\"\n                  placeholder=\"Enter your API token\"\n                />\n              </div>\n            </div>\n\n            <div className=\"flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 mt-4 sm:mt-6\">\n              <button\n                onClick={() => setIsConfiguring(false)}\n                className=\"group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-medium text-gray-300 bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl border border-white/10 transition-all duration-300 hover:border-white/20 hover:from-gray-700/90 hover:to-gray-800/90 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-black\"\n                style={{\n                  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'\n                }}\n              >\n                <span className=\"flex items-center justify-center space-x-2\">\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                  <span>Cancel</span>\n                </span>\n              </button>\n              <button\n                onClick={handleSaveConfig}\n                className=\"group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-semibold text-black bg-gradient-to-br from-white to-gray-100 backdrop-blur-xl border border-white/20 transition-all duration-300 hover:from-gray-100 hover:to-gray-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-black transform hover:scale-[1.02]\"\n                style={{\n                  boxShadow: '0 6px 20px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)'\n                }}\n              >\n                <span className=\"flex items-center justify-center space-x-2\">\n                  <svg className=\"w-4 h-4 transition-transform duration-300 group-hover:scale-110\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                  <span>Save</span>\n                </span>\n              </button>\n            </div>\n          </div>\n        </div>,\n        document.body\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAGA;;;;;;;;AAYe,SAAS,iBAAiB,EACvC,MAAM,EACN,OAAO,EACP,QAAQ,EACR,gBAAgB,EAChB,gBAAgB,EAChB,YAAY,EACZ,eAAe,EACO;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAElE,qBAAqB;IACrB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACrE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IACtF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwE,CAAC;IACtH,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAItE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,UAAU,cAAc,gBAAgB;gBAC1C;YACF;QAEF;qCAAG;QAAC;QAAQ;KAAU;IAEtB,MAAM,sBAAsB;QAC1B,gBAAgB,oIAAA,CAAA,yBAAsB,CAAC,eAAe;IACxD;IAIA,MAAM,0BAA0B,OAAO;QACrC,uBAAuB;QACvB,iBAAiB;QACjB,cAAc,CAAC;IACjB;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,qBAAqB;QAE1B,MAAM,UAAU,oIAAA,CAAA,yBAAsB,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,EAAE;QACjF,IAAI,SAAS;YACX;YACA,iBAAiB;YACjB,uBAAuB;YACvB,cAAc,CAAC;QACjB;IACF;IAEA,MAAM,2BAA2B,CAAC;QAChC,oIAAA,CAAA,yBAAsB,CAAC,kBAAkB,CAAC;QAC1C;IACF;IAEA,MAAM,wBAAwB,OAAO;QACnC,aAAa;QACb,MAAM,SAAS,MAAM,oIAAA,CAAA,yBAAsB,CAAC,eAAe,CAAC,YAAY,EAAE;QAC1E,eAAe;YAAE,GAAG,WAAW;YAAE,CAAC,YAAY,EAAE,CAAC,EAAE;QAAO;QAC1D,aAAa;IACf;IAEA,MAAM,0BAA0B,OAAO;QACrC,IAAI,CAAC,gBAAgB,CAAC,iBAAiB;QAEvC,aAAa;QACb,MAAM,SAAS,MAAM,oIAAA,CAAA,yBAAsB,CAAC,iBAAiB,CAC3D,YAAY,EAAE,EACd,cACA;QAEF,eAAe;YAAE,GAAG,WAAW;YAAE,CAAC,GAAG,YAAY,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QAAO;QACpE,aAAa;IACf;IAEA,MAAM,eAAe,OAAO,OAAO,CAAC,8HAAA,CAAA,YAAS,EAAE,GAAG,CAAC,CAAC,GAAG,MAAM,GAAK,CAAC;YACjE,OAAO,MAAM,EAAE;YACf,OAAO,MAAM,IAAI;YACjB,aAAa,GAAG,MAAM,QAAQ,CAAC,GAAG,EAAE,MAAM,WAAW,EAAE;QACzD,CAAC;IAED,MAAM,sBAAsB;QAC1B;YAAE,OAAO;YAAS,OAAO;YAAS,aAAa;QAA2C;QAC1F;YAAE,OAAO;YAAY,OAAO;YAAY,aAAa;QAA6C;QAClG;YAAE,OAAO;YAAU,OAAO;YAAU,aAAa;QAAwC;KAC1F;IAED,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAa,OAAO;YAAa,aAAa;QAAiC;QACxF;YAAE,OAAO;YAAY,OAAO;YAAY,aAAa;QAA8B;QACnF;YAAE,OAAO;YAAa,OAAO;YAAa,aAAa;QAAsC;QAC7F;YAAE,OAAO;YAAW,OAAO;YAAW,aAAa;QAAkC;KACtF;IAED,MAAM,aAAa;QACjB,iBAAiB;QACjB;IACF;IAEA,MAAM,cAAc;QAClB,MAAM,kBAA6C;YACjD,OAAO,oBAAoB;YAC3B,mBAAmB;YACnB,iBAAiB;YACjB,oBAAoB;YACpB,gBAAgB;YAChB,gBAAgB;QAClB;QACA,iBAAiB;IACnB;IAIA,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,gBAAgB,OAAO,MAAM,CAAC,8HAAA,CAAA,YAAS,EAAE,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,cAAc,KAAK;IAErF,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAiI,OAAO;wBACrJ,WAAW;oBACb;;sCACA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA8C;;;;;;8CAC5D,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,8GAA8G,EACxH,cAAc,OACV,wBACA,+CACJ;8CACH;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,8GAA8G,EACxH,cAAc,iBACV,wBACA,+CACJ;8CACH;;;;;;;;;;;;wBAKF,cAAc,sBACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;gDAA4C;gDAE1D,oBAAoB,cAAc,KAAK,KAAK,kCAC3C,6LAAC;oDAAK,WAAU;8DAAqD;;;;;;;;;;;;sDAGzE,6LAAC,qIAAA,CAAA,UAAY;4CACX,OAAO,cAAc,KAAK;4CAC1B,UAAU,CAAC,QAAU,iBAAiB,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,OAAO;oDAAM,CAAC;4CACxE,SAAS;4CACT,aAAY;;;;;;wCAEb,+BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAuC,cAAc,IAAI;;;;;;8DACxE,6LAAC;oDAAI,WAAU;8DAA8B,cAAc,WAAW;;;;;;8DACtE,6LAAC;oDAAI,WAAU;8DACZ,cAAc,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBACtC,6LAAC;4DAAiB,WAAU;sEACzB;2DADQ;;;;;;;;;;8DAKf,6LAAC;oDAAI,WAAU;;wDAAwB;wDACxB,cAAc,SAAS,CAAC,cAAc;wDAAG;wDAC9C,cAAc,eAAe;wDAAC;;;;;;;;;;;;;;;;;;;8CAOhD,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA4C;;;;;;sDAC7D,6LAAC,qIAAA,CAAA,UAAY;4CACX,OAAO,cAAc,iBAAiB;4CACtC,UAAU,CAAC,QAAU,iBAAiB,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,mBAAmB;oDAAyC,CAAC;4CACvH,SAAS;4CACT,aAAY;;;;;;;;;;;;8CAKhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA4C;;;;;;sDAC7D,6LAAC,qIAAA,CAAA,UAAY;4CACX,OAAO,cAAc,cAAc;4CACnC,UAAU,CAAC,QAAU,iBAAiB,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,gBAAgB;oDAA4D,CAAC;4CACvI,SAAS;4CACT,aAAY;;;;;;;;;;;;8CAKhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA4C;;;;;;sDAC7D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,SAAS,cAAc,eAAe;4DACtC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,iBAAiB,EAAE,MAAM,CAAC,OAAO;oEAAC,CAAC;4DACzF,WAAU;;;;;;sEAEZ,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAiC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAIzC,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,SAAS,cAAc,kBAAkB;4DACzC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,oBAAoB,EAAE,MAAM,CAAC,OAAO;oEAAC,CAAC;4DAC5F,WAAU;;;;;;sEAEZ,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAiC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAIzC,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,SAAS,cAAc,cAAc;4DACrC,UAAU,CAAC,IAAM,iBAAiB,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,gBAAgB,EAAE,MAAM,CAAC,OAAO;oEAAC,CAAC;4DACxF,WAAU;;;;;;sEAEZ,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAiC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;4CACT,WAAU;4CACV,OAAO;gDACL,WAAW;4CACb;sDAEA,cAAA,6LAAC;gDAAK,WAAU;;kEACd,6LAAC;wDAAI,WAAU;wDAAmE,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC1H,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;kEAEvE,6LAAC;kEAAK;;;;;;;;;;;;;;;;;sDAGV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS;oDACT,WAAU;oDACV,OAAO;wDACL,WAAW;oDACb;8DAEA,cAAA,6LAAC;wDAAK,WAAU;;0EACd,6LAAC;gEAAI,WAAU;gEAAU,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACjE,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;0EAEvE,6LAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,6LAAC;oDACC,SAAS;oDACT,WAAU;oDACV,OAAO;wDACL,WAAW;oDACb;8DAEA,cAAA,6LAAC;wDAAK,WAAU;;0EACd,6LAAC;gEAAI,WAAU;gEAAkE,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACzH,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;0EAEvE,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQf,cAAc,gCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC;wCAEC,WAAW,CAAC,yDAAyD,EACnE,YAAY,SAAS,GACjB,6BACA,qDACJ;;0DAEF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAqC,YAAY,IAAI;;;;;;0EACpE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAwD,YAAY,IAAI;;;;;;kFACtF,6LAAC;wEAAE,WAAU;kFAAiD,YAAY,WAAW;;;;;;;;;;;;;;;;;;kEAGzF,6LAAC;wDAAI,WAAW,CAAC,qBAAqB,EACpC,YAAY,SAAS,GAAG,aAAa,eACrC;;;;;;;;;;;;0DAGJ,6LAAC;gDAAI,WAAU;0DACZ,CAAC,YAAY,SAAS,iBACrB,6LAAC;oDACC,SAAS,IAAM,wBAAwB;oDACvC,WAAU;8DACX;;;;;yEAID;;sEACE,6LAAC;4DACC,SAAS,IAAM,sBAAsB;4DACrC,UAAU;4DACV,WAAU;sEAET,YAAY,eAAe;;;;;;wDAE7B,gBAAgB,iCACf,6LAAC;4DACC,SAAS,IAAM,wBAAwB;4DACvC,UAAU;4DACV,WAAU;sEAET,YAAY,eAAe;;;;;;sEAGhC,6LAAC;4DACC,SAAS,IAAM,yBAAyB,YAAY,EAAE;4DACtD,WAAU;sEACX;;;;;;;;;;;;;4CAON,WAAW,CAAC,YAAY,EAAE,CAAC,kBAC1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,YAAY,EAC3B,WAAW,CAAC,YAAY,EAAE,CAAC,CAAC,OAAO,GAAG,eAAe,iBACrD;kEACC,WAAW,CAAC,YAAY,EAAE,CAAC,CAAC,OAAO,GAAG,YAAY;;;;;;kEAErD,6LAAC;wDAAI,WAAU;kEACZ,WAAW,CAAC,YAAY,EAAE,CAAC,CAAC,KAAK,IAAI;;;;;;;;;;;;;uCAhEvC,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;YAiF9B,iBAAiB,qCAAuB,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,gBAClD,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,QAAQ;oBACR,QAAQ;oBACR,SAAS;gBACX;gBACA,SAAS,CAAC;oBACR,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;wBAChC,iBAAiB;oBACnB;gBACF;0BAEA,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,WAAW;wBACX,UAAU;wBACV,UAAU;wBACV,WAAW;oBACb;oBACA,SAAS,CAAC,IAAM,EAAE,eAAe;;sCAEjC,6LAAC;4BAAG,WAAU;;gCAAwC;gCACzC,oBAAoB,IAAI;;;;;;;sCAGrC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA4C;;;;;;kDAG7D,6LAAC;wCACC,MAAK;wCACL,OAAO,WAAW,KAAK,IAAI;wCAC3B,UAAU,CAAC,IAAM,cAAc;gDAAE,GAAG,UAAU;gDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAC;wCACtE,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;sCAKlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;oCACV,OAAO;wCACL,WAAW;oCACb;8CAEA,cAAA,6LAAC;wCAAK,WAAU;;0DACd,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,6LAAC;0DAAK;;;;;;;;;;;;;;;;;8CAGV,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAO;wCACL,WAAW;oCACb;8CAEA,cAAA,6LAAC;wCAAK,WAAU;;0DACd,6LAAC;gDAAI,WAAU;gDAAkE,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACzH,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sBAMhB,SAAS,IAAI;;;AAIrB;GApewB;KAAA", "debugId": null}}, {"offset": {"line": 3634, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/test/augment-prompt-generator/src/components/TypewriterTitle.tsx"], "sourcesContent": ["\"use client\";\n\n/**\n * @author: @dorian_baffier\n * @description: Typewriter\n * @version: 1.0.0\n * @date: 2025-06-26\n * @license: MIT\n * @website: https://kokonutui.com\n * @github: https://github.com/kokonut-labs/kokonutui\n */\n\nimport { motion, useAnimate } from \"motion/react\";\nimport { useEffect, useState } from \"react\";\n\ninterface TypewriterSequence {\n    text: string;\n    deleteAfter?: boolean;\n    pauseAfter?: number;\n}\n\ninterface TypewriterTitleProps {\n    sequences?: TypewriterSequence[];\n    typingSpeed?: number;\n    startDelay?: number;\n    autoLoop?: boolean;\n    loopDelay?: number;\n    className?: string;\n}\n\nexport default function TypewriterTitle({\n    sequences = [\n        { text: \"AI Prompt Generator\", deleteAfter: true },\n        { text: \"Professional Prompts\", deleteAfter: true },\n        { text: \"Augment Agent Ready\", deleteAfter: false },\n    ],\n    typingSpeed = 75,\n    startDelay = 500,\n    autoLoop = true,\n    loopDelay = 3000,\n    className = \"\",\n}: TypewriterTitleProps) {\n    const [scope, animate] = useAnimate();\n    const [hasRun, setHasRun] = useState(false);\n\n    useEffect(() => {\n        // Only start the animation once when component mounts\n        if (hasRun) return;\n        let isActive = true;\n\n        const typeText = async () => {\n            const titleElement =\n                scope.current?.querySelector(\"[data-typewriter]\");\n            if (!titleElement) return;\n\n            // Mark as having run to prevent restarts from user interactions\n            setHasRun(true);\n\n            // Reset the text content\n            if (scope.current) {\n                await animate(scope.current, { opacity: 1 });\n            }\n            titleElement.textContent = \"\";\n\n            // Wait for initial delay\n            await new Promise((resolve) => setTimeout(resolve, startDelay));\n\n            // Main animation loop - runs continuously if autoLoop is enabled\n            while (isActive) {\n                // Process each sequence\n                for (const sequence of sequences) {\n                    if (!isActive) break;\n\n                    // Type out the sequence text\n                    for (let i = 0; i < sequence.text.length; i++) {\n                        if (!isActive) break;\n                        titleElement.textContent = sequence.text.slice(\n                            0,\n                            i + 1\n                        );\n                        await new Promise((resolve) =>\n                            setTimeout(resolve, typingSpeed)\n                        );\n                    }\n\n                    // Pause after typing if specified\n                    if (sequence.pauseAfter) {\n                        await new Promise((resolve) =>\n                            setTimeout(resolve, sequence.pauseAfter)\n                        );\n                    }\n\n                    // Delete the text if specified\n                    if (sequence.deleteAfter) {\n                        // Small pause before deleting\n                        await new Promise((resolve) =>\n                            setTimeout(resolve, 500)\n                        );\n\n                        for (let i = sequence.text.length; i > 0; i--) {\n                            if (!isActive) break;\n                            titleElement.textContent = sequence.text.slice(\n                                0,\n                                i\n                            );\n                            await new Promise((resolve) =>\n                                setTimeout(resolve, typingSpeed / 2)\n                            );\n                        }\n                    }\n                }\n\n                // If autoLoop is disabled, break after first cycle\n                if (!autoLoop || !isActive) break;\n\n                // Wait before starting next loop\n                await new Promise((resolve) => setTimeout(resolve, loopDelay));\n\n                // Reset for next cycle\n                if (isActive) {\n                    titleElement.textContent = \"\";\n                }\n            }\n        };\n\n        typeText();\n\n        // Cleanup function to stop the animation when component unmounts\n        return () => {\n            isActive = false;\n        };\n    }, []); // Empty dependency array - only run on mount\n\n    return (\n        <div className={`relative w-full max-w-4xl mx-auto py-8 ${className}`}>\n            <div\n                className=\"relative text-center z-10 flex flex-col items-center justify-center\"\n                ref={scope}\n            >\n                <motion.div\n                    className=\"text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-mono text-white tracking-tight flex items-center gap-2\"\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                >\n                    <span\n                        data-typewriter\n                        className=\"inline-block border-r-2 border-white animate-cursor pr-1\"\n                    >\n                        {sequences[0].text}\n                    </span>\n                </motion.div>\n            </div>\n        </div>\n    );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;CAQC,GAED;AAAA;AACA;;;AAbA;;;AA8Be,SAAS,gBAAgB,EACpC,YAAY;IACR;QAAE,MAAM;QAAuB,aAAa;IAAK;IACjD;QAAE,MAAM;QAAwB,aAAa;IAAK;IAClD;QAAE,MAAM;QAAuB,aAAa;IAAM;CACrD,EACD,cAAc,EAAE,EAChB,aAAa,GAAG,EAChB,WAAW,IAAI,EACf,YAAY,IAAI,EAChB,YAAY,EAAE,EACK;;IACnB,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAA,GAAA,0LAAA,CAAA,aAAU,AAAD;IAClC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACN,sDAAsD;YACtD,IAAI,QAAQ;YACZ,IAAI,WAAW;YAEf,MAAM;sDAAW;oBACb,MAAM,eACF,MAAM,OAAO,EAAE,cAAc;oBACjC,IAAI,CAAC,cAAc;oBAEnB,gEAAgE;oBAChE,UAAU;oBAEV,yBAAyB;oBACzB,IAAI,MAAM,OAAO,EAAE;wBACf,MAAM,QAAQ,MAAM,OAAO,EAAE;4BAAE,SAAS;wBAAE;oBAC9C;oBACA,aAAa,WAAW,GAAG;oBAE3B,yBAAyB;oBACzB,MAAM,IAAI;8DAAQ,CAAC,UAAY,WAAW,SAAS;;oBAEnD,iEAAiE;oBACjE,MAAO,SAAU;wBACb,wBAAwB;wBACxB,KAAK,MAAM,YAAY,UAAW;4BAC9B,IAAI,CAAC,UAAU;4BAEf,6BAA6B;4BAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAI,CAAC,MAAM,EAAE,IAAK;gCAC3C,IAAI,CAAC,UAAU;gCACf,aAAa,WAAW,GAAG,SAAS,IAAI,CAAC,KAAK,CAC1C,GACA,IAAI;gCAER,MAAM,IAAI;0EAAQ,CAAC,UACf,WAAW,SAAS;;4BAE5B;4BAEA,kCAAkC;4BAClC,IAAI,SAAS,UAAU,EAAE;gCACrB,MAAM,IAAI;0EAAQ,CAAC,UACf,WAAW,SAAS,SAAS,UAAU;;4BAE/C;4BAEA,+BAA+B;4BAC/B,IAAI,SAAS,WAAW,EAAE;gCACtB,8BAA8B;gCAC9B,MAAM,IAAI;0EAAQ,CAAC,UACf,WAAW,SAAS;;gCAGxB,IAAK,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,EAAE,IAAI,GAAG,IAAK;oCAC3C,IAAI,CAAC,UAAU;oCACf,aAAa,WAAW,GAAG,SAAS,IAAI,CAAC,KAAK,CAC1C,GACA;oCAEJ,MAAM,IAAI;8EAAQ,CAAC,UACf,WAAW,SAAS,cAAc;;gCAE1C;4BACJ;wBACJ;wBAEA,mDAAmD;wBACnD,IAAI,CAAC,YAAY,CAAC,UAAU;wBAE5B,iCAAiC;wBACjC,MAAM,IAAI;kEAAQ,CAAC,UAAY,WAAW,SAAS;;wBAEnD,uBAAuB;wBACvB,IAAI,UAAU;4BACV,aAAa,WAAW,GAAG;wBAC/B;oBACJ;gBACJ;;YAEA;YAEA,iEAAiE;YACjE;6CAAO;oBACH,WAAW;gBACf;;QACJ;oCAAG,EAAE,GAAG,6CAA6C;IAErD,qBACI,6LAAC;QAAI,WAAW,CAAC,uCAAuC,EAAE,WAAW;kBACjE,cAAA,6LAAC;YACG,WAAU;YACV,KAAK;sBAEL,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACP,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;0BAEtB,cAAA,6LAAC;oBACG,iBAAe;oBACf,WAAU;8BAET,SAAS,CAAC,EAAE,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;AAM1C;GA5HwB;;QAYK,0LAAA,CAAA,aAAU;;;KAZf", "debugId": null}}, {"offset": {"line": 3804, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/test/augment-prompt-generator/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 3823, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/test/augment-prompt-generator/src/components/ShimmerText.tsx"], "sourcesContent": ["\"use client\";\n\n/**\n * @author: @dorian_baffier\n * @description: Shimmer Text - Safari iOS Compatible Version\n * @version: 1.1.0\n * @date: 2025-06-26\n * @license: MIT\n * @website: https://kokonutui.com\n * @github: https://github.com/kokonut-labs/kokonutui\n */\n\nimport { cn } from \"@/lib/utils\";\nimport { motion } from \"motion/react\";\nimport { useEffect, useState } from \"react\";\n\ninterface ShimmerTextProps {\n    text: string;\n    className?: string;\n}\n\nexport default function ShimmerText({\n    text = \"Text Shimmer\",\n    className,\n}: ShimmerTextProps) {\n    const [isSafari, setIsSafari] = useState(false);\n\n    useEffect(() => {\n        // Detect Safari browser for fallback\n        const userAgent = navigator.userAgent.toLowerCase();\n        const isSafariBrowser = userAgent.includes('safari') && !userAgent.includes('chrome');\n        setIsSafari(isSafariBrowser);\n    }, []);\n\n    // Safari fallback - simple white text with opacity animation\n    if (isSafari) {\n        return (\n            <motion.span\n                className={cn(\"text-white font-medium\", className)}\n                initial={{ opacity: 0.7 }}\n                animate={{ opacity: [0.7, 1, 0.7] }}\n                transition={{\n                    duration: 2.5,\n                    ease: \"easeInOut\",\n                    repeat: Number.POSITIVE_INFINITY,\n                }}\n            >\n                {text}\n            </motion.span>\n        );\n    }\n\n    // Standard shimmer effect for other browsers\n    return (\n        <motion.span\n            className={cn(\n                \"relative font-medium text-white\",\n                className\n            )}\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.5 }}\n            style={{\n                background: 'linear-gradient(90deg, #ffffff 0%, #d1d5db 50%, #ffffff 100%)',\n                backgroundSize: '200% 100%',\n                WebkitBackgroundClip: 'text',\n                backgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                color: 'transparent',\n                // Fallback for browsers that don't support background-clip: text\n                fallbacks: [\n                    { color: '#ffffff' }\n                ]\n            }}\n        >\n            <motion.span\n                animate={{\n                    backgroundPosition: ['200% center', '-200% center'],\n                }}\n                transition={{\n                    duration: 2.5,\n                    ease: \"linear\",\n                    repeat: Number.POSITIVE_INFINITY,\n                }}\n                style={{\n                    background: 'inherit',\n                    backgroundSize: 'inherit',\n                    WebkitBackgroundClip: 'inherit',\n                    backgroundClip: 'inherit',\n                    WebkitTextFillColor: 'inherit',\n                    color: 'inherit',\n                }}\n            >\n                {text}\n            </motion.span>\n            {/* Fallback text for Safari iOS */}\n            <span\n                className=\"absolute inset-0 text-white opacity-0\"\n                style={{\n                    opacity: isSafari ? 1 : 0,\n                    WebkitTextFillColor: 'white',\n                    color: 'white'\n                }}\n            >\n                {text}\n            </span>\n        </motion.span>\n    );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;CAQC,GAED;AACA;AACA;;;AAdA;;;;AAqBe,SAAS,YAAY,EAChC,OAAO,cAAc,EACrB,SAAS,EACM;;IACf,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACN,qCAAqC;YACrC,MAAM,YAAY,UAAU,SAAS,CAAC,WAAW;YACjD,MAAM,kBAAkB,UAAU,QAAQ,CAAC,aAAa,CAAC,UAAU,QAAQ,CAAC;YAC5E,YAAY;QAChB;gCAAG,EAAE;IAEL,6DAA6D;IAC7D,IAAI,UAAU;QACV,qBACI,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;YACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YACxC,SAAS;gBAAE,SAAS;YAAI;YACxB,SAAS;gBAAE,SAAS;oBAAC;oBAAK;oBAAG;iBAAI;YAAC;YAClC,YAAY;gBACR,UAAU;gBACV,MAAM;gBACN,QAAQ,OAAO,iBAAiB;YACpC;sBAEC;;;;;;IAGb;IAEA,6CAA6C;IAC7C,qBACI,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;QACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,mCACA;QAEJ,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAI;QAC5B,OAAO;YACH,YAAY;YACZ,gBAAgB;YAChB,sBAAsB;YACtB,gBAAgB;YAChB,qBAAqB;YACrB,OAAO;YACP,iEAAiE;YACjE,WAAW;gBACP;oBAAE,OAAO;gBAAU;aACtB;QACL;;0BAEA,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gBACR,SAAS;oBACL,oBAAoB;wBAAC;wBAAe;qBAAe;gBACvD;gBACA,YAAY;oBACR,UAAU;oBACV,MAAM;oBACN,QAAQ,OAAO,iBAAiB;gBACpC;gBACA,OAAO;oBACH,YAAY;oBACZ,gBAAgB;oBAChB,sBAAsB;oBACtB,gBAAgB;oBAChB,qBAAqB;oBACrB,OAAO;gBACX;0BAEC;;;;;;0BAGL,6LAAC;gBACG,WAAU;gBACV,OAAO;oBACH,SAAS,WAAW,IAAI;oBACxB,qBAAqB;oBACrB,OAAO;gBACX;0BAEC;;;;;;;;;;;;AAIjB;GAvFwB;KAAA", "debugId": null}}, {"offset": {"line": 3967, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/test/augment-prompt-generator/src/components/AITextLoading.tsx"], "sourcesContent": ["\"use client\";\n\n/**\n * @author: @kokonutui\n * @description: AI Text Loading\n * @version: 1.0.0\n * @date: 2025-06-26\n * @license: MIT\n * @website: https://kokonutui.com\n * @github: https://github.com/kokonut-labs/kokonutui\n */\n\nimport { cn } from \"@/lib/utils\";\nimport { motion, AnimatePresence } from \"motion/react\";\nimport { useEffect, useState } from \"react\";\n\ninterface AITextLoadingProps {\n    texts?: string[];\n    className?: string;\n    interval?: number;\n}\n\nexport default function AITextLoading({\n    texts = [\n        \"Thinking...\",\n        \"Processing...\",\n        \"Analyzing...\",\n        \"Computing...\",\n        \"Almost...\",\n    ],\n    className,\n    interval = 1500,\n}: AITextLoadingProps) {\n    const [currentTextIndex, setCurrentTextIndex] = useState(0);\n\n    useEffect(() => {\n        const timer = setInterval(() => {\n            setCurrentTextIndex((prevIndex) => (prevIndex + 1) % texts.length);\n        }, interval);\n\n        return () => clearInterval(timer);\n    }, [interval, texts.length]);\n\n    return (\n        <div className=\"flex items-center justify-center p-8\">\n            <motion.div\n                className=\"relative px-4 py-2 w-full\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ duration: 0.4 }}\n            >\n                <AnimatePresence mode=\"wait\">\n                    <motion.div\n                        key={currentTextIndex}\n                        initial={{ opacity: 0, y: 20 }}\n                        animate={{\n                            opacity: 1,\n                            y: 0,\n                            backgroundPosition: [\"200% center\", \"-200% center\"],\n                        }}\n                        exit={{ opacity: 0, y: -20 }}\n                        transition={{\n                            opacity: { duration: 0.3 },\n                            y: { duration: 0.3 },\n                            backgroundPosition: {\n                                duration: 2.5,\n                                ease: \"linear\",\n                                repeat: Infinity,\n                            },\n                        }}\n                        className={cn(\n                            \"flex justify-center text-3xl font-bold bg-gradient-to-r from-neutral-950 via-neutral-400 to-neutral-950 dark:from-white dark:via-neutral-600 dark:to-white bg-[length:200%_100%] bg-clip-text text-transparent whitespace-nowrap min-w-max\",\n                            className\n                        )}\n                    >\n                        {texts[currentTextIndex]}\n                    </motion.div>\n                </AnimatePresence>\n            </motion.div>\n        </div>\n    );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;CAQC,GAED;AACA;AAAA;AACA;;;AAdA;;;;AAsBe,SAAS,cAAc,EAClC,QAAQ;IACJ;IACA;IACA;IACA;IACA;CACH,EACD,SAAS,EACT,WAAW,IAAI,EACE;;IACjB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACN,MAAM,QAAQ;iDAAY;oBACtB;yDAAoB,CAAC,YAAc,CAAC,YAAY,CAAC,IAAI,MAAM,MAAM;;gBACrE;gDAAG;YAEH;2CAAO,IAAM,cAAc;;QAC/B;kCAAG;QAAC;QAAU,MAAM,MAAM;KAAC;IAE3B,qBACI,6LAAC;QAAI,WAAU;kBACX,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACP,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,YAAY;gBAAE,UAAU;YAAI;sBAE5B,cAAA,6LAAC,4LAAA,CAAA,kBAAe;gBAAC,MAAK;0BAClB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAEP,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBACL,SAAS;wBACT,GAAG;wBACH,oBAAoB;4BAAC;4BAAe;yBAAe;oBACvD;oBACA,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,YAAY;wBACR,SAAS;4BAAE,UAAU;wBAAI;wBACzB,GAAG;4BAAE,UAAU;wBAAI;wBACnB,oBAAoB;4BAChB,UAAU;4BACV,MAAM;4BACN,QAAQ;wBACZ;oBACJ;oBACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,8OACA;8BAGH,KAAK,CAAC,iBAAiB;mBAtBnB;;;;;;;;;;;;;;;;;;;;AA4B7B;GA3DwB;KAAA", "debugId": null}}, {"offset": {"line": 4096, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/test/augment-prompt-generator/src/lib/unified-templates.ts"], "sourcesContent": ["import { ProjectTemplate, ProjectType, Platform, Complexity } from './types';\n\nexport interface IndustryTemplate {\n  id: string;\n  name: string;\n  industry: string;\n  description: string;\n  projectType: ProjectType;\n  platform: Platform;\n  complexity: Complexity;\n  technologies: string[];\n  features: string[];\n  businessGoals: string[];\n  targetAudience: string;\n  timeline: string;\n  budget: string;\n  successMetrics: string[];\n  risks: string[];\n  template: string;\n}\n\nexport const UNIFIED_INDUSTRY_TEMPLATES: IndustryTemplate[] = [\n  // Financial Technology\n  {\n    id: 'fintech-trading-platform',\n    name: 'FinTech Trading Platform',\n    industry: 'Financial Technology',\n    description: 'Real-time trading platform with advanced analytics and risk management',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'WebSocket', 'PostgreSQL', 'Redis', 'Docker'],\n    features: ['Real-time trading', 'Portfolio management', 'Risk analytics', 'Compliance reporting'],\n    businessGoals: ['Increase trading volume', 'Reduce latency', 'Ensure regulatory compliance'],\n    targetAudience: 'Professional traders and financial institutions',\n    timeline: '12-18 months',\n    budget: '$500K - $2M',\n    successMetrics: ['Sub-100ms latency', '99.9% uptime', 'Full regulatory compliance'],\n    risks: ['Regulatory changes', 'Market volatility', 'Security breaches'],\n    template: `Build a comprehensive FinTech trading platform with real-time capabilities and regulatory compliance.\n\nKey Requirements:\n- Real-time market data processing and trading execution\n- Advanced risk management and portfolio analytics\n- Regulatory compliance and audit trails\n- High-performance architecture with low latency\n- Secure user authentication and authorization\n- Integration with market data providers and clearing houses\n\nTechnical Specifications:\n- Microservices architecture for scalability\n- WebSocket connections for real-time updates\n- Redis for caching and session management\n- PostgreSQL for transactional data\n- Docker containerization for deployment\n- Comprehensive API documentation\n\nSuccess Criteria:\n- Sub-100ms trade execution latency\n- 99.9% uptime during market hours\n- Full regulatory compliance (MiFID II, GDPR)\n- Support for 10,000+ concurrent users`\n  },\n\n  {\n    id: 'digital-banking-app',\n    name: 'Digital Banking Application',\n    industry: 'Financial Technology',\n    description: 'Mobile-first digital banking platform with AI-powered financial insights',\n    projectType: 'mobile-application',\n    platform: 'mobile',\n    complexity: 'advanced',\n    technologies: ['React Native', 'Node.js', 'PostgreSQL', 'AWS', 'TensorFlow', 'Plaid'],\n    features: ['Account management', 'Mobile payments', 'AI budgeting', 'Investment tracking'],\n    businessGoals: ['Increase customer engagement', 'Reduce operational costs', 'Expand digital services'],\n    targetAudience: 'Banking customers and financial service users',\n    timeline: '10-15 months',\n    budget: '$400K - $1.5M',\n    successMetrics: ['90% customer adoption', '50% cost reduction', '4.8+ app store rating'],\n    risks: ['Regulatory compliance', 'Security vulnerabilities', 'Competition from neobanks'],\n    template: `Create a next-generation digital banking application with AI-powered financial management.\n\nKey Requirements:\n- Secure account management and transaction history\n- Real-time mobile payments and transfers\n- AI-powered budgeting and spending insights\n- Investment portfolio tracking and recommendations\n- Biometric authentication and fraud detection\n- Integration with third-party financial services\n\nTechnical Specifications:\n- Cross-platform mobile development\n- End-to-end encryption for all transactions\n- Real-time fraud detection algorithms\n- Open banking API integrations\n- Cloud-native architecture with auto-scaling\n- Comprehensive security audit and penetration testing\n\nSuccess Criteria:\n- PCI DSS compliance certification\n- 99.99% transaction success rate\n- <3 second app load times\n- Zero critical security vulnerabilities`\n  },\n\n  {\n    id: 'cryptocurrency-exchange',\n    name: 'Cryptocurrency Exchange Platform',\n    industry: 'Financial Technology',\n    description: 'Secure cryptocurrency trading platform with advanced order matching',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Go', 'PostgreSQL', 'Redis', 'Kubernetes', 'WebSocket'],\n    features: ['Crypto trading', 'Order matching', 'Wallet management', 'KYC/AML compliance'],\n    businessGoals: ['Capture crypto market share', 'Ensure regulatory compliance', 'Maximize trading volume'],\n    targetAudience: 'Cryptocurrency traders and investors',\n    timeline: '12-18 months',\n    budget: '$600K - $2.5M',\n    successMetrics: ['$100M+ daily volume', '99.99% uptime', 'Full regulatory compliance'],\n    risks: ['Regulatory uncertainty', 'Security attacks', 'Market manipulation'],\n    template: `Develop a secure and scalable cryptocurrency exchange with institutional-grade features.\n\nKey Requirements:\n- High-performance order matching engine\n- Multi-cryptocurrency wallet management\n- Advanced trading features (limit, market, stop orders)\n- KYC/AML compliance and reporting\n- Cold storage security for digital assets\n- Real-time market data and charting tools\n\nTechnical Specifications:\n- Microservices architecture with Go backend\n- High-frequency trading support (>100k TPS)\n- Multi-signature wallet security\n- Real-time WebSocket market data feeds\n- Kubernetes orchestration for scalability\n- Comprehensive audit logging and monitoring\n\nSuccess Criteria:\n- Handle 1M+ transactions per day\n- 99.99% platform availability\n- SOC 2 Type II compliance\n- Zero successful security breaches`\n  },\n\n  // Healthcare & Medical (10+ templates)\n  {\n    id: 'telemedicine-platform',\n    name: 'Telemedicine Platform',\n    industry: 'Healthcare & Medical',\n    description: 'HIPAA-compliant telemedicine platform for remote patient consultations',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'WebRTC', 'PostgreSQL', 'AWS', 'Socket.io'],\n    features: ['Video consultations', 'Patient records', 'Prescription management', 'Appointment scheduling'],\n    businessGoals: ['Improve patient access', 'Reduce healthcare costs', 'Ensure HIPAA compliance'],\n    targetAudience: 'Healthcare providers and patients',\n    timeline: '8-12 months',\n    budget: '$200K - $800K',\n    successMetrics: ['Patient satisfaction >4.5', 'Consultation completion >95%', 'Zero HIPAA violations'],\n    risks: ['Regulatory compliance', 'Data security breaches', 'Video quality issues'],\n    template: `Create a HIPAA-compliant telemedicine platform enabling secure remote healthcare delivery.\n\nKey Requirements:\n- End-to-end encrypted video consultations\n- Electronic health records (EHR) integration\n- Prescription management and e-prescribing\n- Secure patient-provider messaging\n- Appointment scheduling and calendar integration\n- Payment processing and insurance verification\n\nTechnical Specifications:\n- WebRTC for real-time video communication\n- HIPAA-compliant cloud infrastructure\n- Role-based access control (RBAC)\n- Audit logging for all patient interactions\n- Integration with existing EHR systems\n- Mobile-responsive design for accessibility\n\nSuccess Criteria:\n- HIPAA compliance certification\n- 99.9% uptime for critical services\n- <2 second video connection establishment\n- Support for 1,000+ concurrent consultations`\n  },\n\n  {\n    id: 'patient-portal',\n    name: 'Patient Portal System',\n    industry: 'Healthcare & Medical',\n    description: 'Comprehensive patient portal for accessing medical records and managing healthcare',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'intermediate',\n    technologies: ['Vue.js', 'Laravel', 'MySQL', 'Redis', 'Docker'],\n    features: ['Medical records access', 'Appointment booking', 'Lab results', 'Billing management'],\n    businessGoals: ['Improve patient engagement', 'Reduce administrative burden', 'Enhance care coordination'],\n    targetAudience: 'Patients and healthcare administrators',\n    timeline: '6-9 months',\n    budget: '$150K - $400K',\n    successMetrics: ['Patient adoption >70%', 'Phone calls reduced 40%', 'Patient satisfaction >4.0'],\n    risks: ['Low user adoption', 'Integration complexity', 'Data privacy concerns'],\n    template: `Develop a user-friendly patient portal that empowers patients to manage their healthcare digitally.\n\nKey Requirements:\n- Secure access to medical records and test results\n- Online appointment scheduling and management\n- Prescription refill requests and medication tracking\n- Secure messaging with healthcare providers\n- Billing and insurance information management\n- Health education resources and reminders\n\nTechnical Specifications:\n- Multi-factor authentication for security\n- Integration with EHR and practice management systems\n- Mobile-first responsive design\n- Automated appointment reminders via SMS/email\n- Document upload and sharing capabilities\n- Accessibility compliance (WCAG 2.1)\n\nSuccess Criteria:\n- 70% patient adoption within 6 months\n- 40% reduction in administrative phone calls\n- 99.5% uptime for patient-facing services\n- Full HIPAA compliance and security audit passed`\n  },\n\n  {\n    id: 'medical-inventory-system',\n    name: 'Medical Inventory Management',\n    industry: 'Healthcare & Medical',\n    description: 'Smart inventory management system for medical supplies and pharmaceuticals',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'intermediate',\n    technologies: ['Angular', 'Spring Boot', 'PostgreSQL', 'Redis', 'Kubernetes'],\n    features: ['Real-time inventory tracking', 'Automated reordering', 'Expiration monitoring', 'Compliance reporting'],\n    businessGoals: ['Reduce waste', 'Ensure supply availability', 'Maintain regulatory compliance'],\n    targetAudience: 'Hospital administrators and pharmacy staff',\n    timeline: '5-8 months',\n    budget: '$100K - $300K',\n    successMetrics: ['Inventory waste reduction 25%', 'Stockout incidents <2%', 'Compliance score >98%'],\n    risks: ['Integration with existing systems', 'Staff training requirements', 'Regulatory changes'],\n    template: `Build an intelligent medical inventory management system that optimizes supply chain operations.\n\nKey Requirements:\n- Real-time tracking of medical supplies and pharmaceuticals\n- Automated reorder points and purchase order generation\n- Expiration date monitoring and alerts\n- Barcode/RFID scanning for inventory updates\n- Regulatory compliance reporting (FDA, DEA)\n- Integration with supplier systems and ERP\n\nTechnical Specifications:\n- Microservices architecture for scalability\n- Real-time dashboard with inventory analytics\n- Mobile app for warehouse staff\n- API integrations with supplier systems\n- Automated compliance reporting\n- Role-based access control for different user types\n\nSuccess Criteria:\n- 25% reduction in inventory carrying costs\n- 99% inventory accuracy\n- Zero expired medication incidents\n- Full regulatory compliance maintained`\n  },\n\n  {\n    id: 'hospital-management-system',\n    name: 'Hospital Management System',\n    industry: 'Healthcare & Medical',\n    description: 'Comprehensive hospital management system for operations and patient care',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'Redis', 'Docker', 'Elasticsearch'],\n    features: ['Patient management', 'Staff scheduling', 'Billing system', 'Inventory tracking'],\n    businessGoals: ['Streamline operations', 'Improve patient care', 'Reduce operational costs'],\n    targetAudience: 'Hospital administrators, doctors, and nursing staff',\n    timeline: '12-18 months',\n    budget: '$300K - $1M',\n    successMetrics: ['30% efficiency improvement', 'Patient satisfaction >4.5', 'Cost reduction 20%'],\n    risks: ['System integration complexity', 'Staff training', 'Data migration challenges'],\n    template: `Create a comprehensive hospital management system that integrates all aspects of hospital operations.\n\nKey Requirements:\n- Patient admission, discharge, and transfer (ADT) management\n- Electronic medical records (EMR) integration\n- Staff scheduling and resource allocation\n- Billing and insurance claim processing\n- Pharmacy and inventory management\n- Laboratory and radiology information systems\n\nTechnical Specifications:\n- Modular architecture with microservices\n- Real-time data synchronization across departments\n- Integration with medical devices and equipment\n- Advanced reporting and analytics dashboard\n- Mobile applications for staff\n- Disaster recovery and backup systems\n\nSuccess Criteria:\n- 99.9% system availability\n- 30% reduction in administrative tasks\n- Full integration with existing medical systems\n- HIPAA compliance and security certification`\n  },\n\n  {\n    id: 'mental-health-app',\n    name: 'Mental Health Support App',\n    industry: 'Healthcare & Medical',\n    description: 'Mobile mental health platform with therapy sessions and wellness tracking',\n    projectType: 'mobile-application',\n    platform: 'mobile',\n    complexity: 'intermediate',\n    technologies: ['React Native', 'Node.js', 'MongoDB', 'AWS', 'WebRTC'],\n    features: ['Therapy sessions', 'Mood tracking', 'Meditation guides', 'Crisis support'],\n    businessGoals: ['Improve mental health access', 'Reduce therapy costs', 'Provide 24/7 support'],\n    targetAudience: 'Individuals seeking mental health support',\n    timeline: '6-10 months',\n    budget: '$150K - $500K',\n    successMetrics: ['User engagement >80%', 'Therapy completion >70%', 'Crisis response <5min'],\n    risks: ['Regulatory compliance', 'Crisis management', 'User safety concerns'],\n    template: `Develop a comprehensive mental health support application with professional therapy integration.\n\nKey Requirements:\n- Secure video therapy sessions with licensed professionals\n- Daily mood and wellness tracking with insights\n- Guided meditation and mindfulness exercises\n- Crisis intervention and emergency support\n- Peer support communities and forums\n- Integration with wearable devices for health monitoring\n\nTechnical Specifications:\n- End-to-end encryption for all communications\n- HIPAA-compliant data storage and processing\n- AI-powered mood analysis and recommendations\n- Real-time crisis detection and alert systems\n- Offline functionality for core features\n- Accessibility features for diverse user needs\n\nSuccess Criteria:\n- 80% user retention after 3 months\n- 24/7 crisis support availability\n- Integration with 5+ major therapy providers\n- 4.5+ app store rating`\n  },\n\n  // Education & E-learning (10+ templates)\n  {\n    id: 'lms-platform',\n    name: 'Learning Management System',\n    industry: 'Education & E-learning',\n    description: 'Comprehensive LMS for educational institutions with advanced analytics',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Django', 'PostgreSQL', 'Redis', 'AWS', 'TensorFlow'],\n    features: ['Course management', 'Student tracking', 'Assessment tools', 'Analytics dashboard'],\n    businessGoals: ['Improve learning outcomes', 'Increase student engagement', 'Reduce administrative burden'],\n    targetAudience: 'Educational institutions, teachers, and students',\n    timeline: '8-12 months',\n    budget: '$200K - $700K',\n    successMetrics: ['90% course completion', '85% student satisfaction', '40% admin time reduction'],\n    risks: ['User adoption challenges', 'Content migration complexity', 'Scalability issues'],\n    template: `Build a comprehensive learning management system that enhances educational delivery and outcomes.\n\nKey Requirements:\n- Course creation and content management tools\n- Student enrollment and progress tracking\n- Interactive assessments and grading systems\n- Discussion forums and collaboration tools\n- Advanced analytics and reporting\n- Integration with existing educational systems\n\nTechnical Specifications:\n- Scalable cloud architecture supporting 10,000+ concurrent users\n- Mobile-responsive design for all devices\n- AI-powered learning analytics and recommendations\n- Video streaming and content delivery network (CDN)\n- Single sign-on (SSO) integration\n- Accessibility compliance (WCAG 2.1 AA)\n\nSuccess Criteria:\n- Support for 50,000+ students\n- 99.9% platform uptime\n- <3 second page load times\n- Full FERPA compliance for student data`\n  },\n\n  {\n    id: 'online-tutoring-platform',\n    name: 'Online Tutoring Platform',\n    industry: 'Education & E-learning',\n    description: 'AI-powered tutoring platform connecting students with qualified tutors',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'intermediate',\n    technologies: ['Vue.js', 'Node.js', 'MongoDB', 'WebRTC', 'Stripe', 'Socket.io'],\n    features: ['Tutor matching', 'Video sessions', 'Payment processing', 'Progress tracking'],\n    businessGoals: ['Improve learning outcomes', 'Increase tutor utilization', 'Expand market reach'],\n    targetAudience: 'Students, parents, and tutors',\n    timeline: '6-9 months',\n    budget: '$150K - $400K',\n    successMetrics: ['90% session completion', 'Tutor utilization >75%', 'Student improvement 25%'],\n    risks: ['Quality control', 'Payment disputes', 'Tutor availability'],\n    template: `Create an intelligent tutoring platform that matches students with the best tutors for their needs.\n\nKey Requirements:\n- AI-powered tutor-student matching algorithm\n- High-quality video conferencing for tutoring sessions\n- Integrated whiteboard and screen sharing tools\n- Secure payment processing and tutor payouts\n- Session recording and review capabilities\n- Progress tracking and performance analytics\n\nTechnical Specifications:\n- Real-time video communication with WebRTC\n- Machine learning for tutor recommendation engine\n- Secure payment processing with escrow system\n- Mobile-responsive design for all devices\n- Integration with calendar and scheduling systems\n- Multi-language support for global reach\n\nSuccess Criteria:\n- 90% successful tutor-student matches\n- Average session rating >4.5\n- 75% student retention after 3 months\n- Platform supports 1,000+ concurrent sessions`\n  },\n\n  // Real Estate (10+ templates)\n  {\n    id: 'property-management-platform',\n    name: 'Property Management Platform',\n    industry: 'Real Estate',\n    description: 'Comprehensive property management system for landlords and property managers',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'intermediate',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'Stripe', 'AWS', 'Twilio'],\n    features: ['Property listings', 'Tenant management', 'Rent collection', 'Maintenance tracking'],\n    businessGoals: ['Streamline operations', 'Improve tenant satisfaction', 'Increase rental income'],\n    targetAudience: 'Property managers, landlords, and tenants',\n    timeline: '6-10 months',\n    budget: '$150K - $500K',\n    successMetrics: ['95% rent collection rate', 'Tenant satisfaction >4.0', '30% time savings'],\n    risks: ['Regulatory compliance', 'Payment processing issues', 'Data security'],\n    template: `Build a comprehensive property management platform that automates rental operations.\n\nKey Requirements:\n- Property portfolio management and listings\n- Tenant screening and application processing\n- Automated rent collection and payment processing\n- Maintenance request tracking and vendor management\n- Financial reporting and accounting integration\n- Communication tools for landlord-tenant interactions\n\nTechnical Specifications:\n- Multi-tenant architecture for property managers\n- Secure payment processing with ACH and credit cards\n- Mobile applications for tenants and maintenance staff\n- Integration with accounting software (QuickBooks, Xero)\n- Document management and e-signature capabilities\n- Automated late payment notifications and collections\n\nSuccess Criteria:\n- 95% on-time rent collection rate\n- 50% reduction in administrative tasks\n- 99.5% payment processing uptime\n- Full compliance with local rental regulations`\n  },\n\n  {\n    id: 'real-estate-marketplace',\n    name: 'Real Estate Marketplace',\n    industry: 'Real Estate',\n    description: 'AI-powered real estate marketplace with virtual tours and market analytics',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['Next.js', 'Python', 'PostgreSQL', 'Elasticsearch', 'AWS', 'TensorFlow'],\n    features: ['Property search', 'Virtual tours', 'Market analytics', 'Agent matching'],\n    businessGoals: ['Increase property sales', 'Improve user experience', 'Expand market share'],\n    targetAudience: 'Home buyers, sellers, and real estate agents',\n    timeline: '10-15 months',\n    budget: '$300K - $1M',\n    successMetrics: ['1M+ property views/month', 'Conversion rate >3%', 'Agent satisfaction >4.5'],\n    risks: ['Market competition', 'Data accuracy', 'Technology adoption'],\n    template: `Create an innovative real estate marketplace with AI-powered features and immersive experiences.\n\nKey Requirements:\n- Advanced property search with AI-powered recommendations\n- 360-degree virtual tours and augmented reality features\n- Real-time market analytics and price predictions\n- Agent-buyer matching and communication tools\n- Mortgage calculator and financing options\n- Neighborhood insights and demographic data\n\nTechnical Specifications:\n- Machine learning for property valuation and recommendations\n- High-performance search with Elasticsearch\n- CDN for fast image and video delivery\n- Integration with MLS (Multiple Listing Service) data\n- Mobile-first responsive design\n- Real-time chat and video calling capabilities\n\nSuccess Criteria:\n- 1 million monthly active users\n- 3% buyer-to-sale conversion rate\n- <2 second property search response time\n- Integration with 50+ MLS systems nationwide`\n  },\n\n  // Gaming & Entertainment (10+ templates)\n  {\n    id: 'multiplayer-game-platform',\n    name: 'Multiplayer Gaming Platform',\n    industry: 'Gaming & Entertainment',\n    description: 'Real-time multiplayer gaming platform with social features and tournaments',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'Socket.io', 'Redis', 'MongoDB', 'WebRTC'],\n    features: ['Real-time gameplay', 'Tournament system', 'Social features', 'Leaderboards'],\n    businessGoals: ['Increase player engagement', 'Monetize through tournaments', 'Build gaming community'],\n    targetAudience: 'Gamers and esports enthusiasts',\n    timeline: '8-12 months',\n    budget: '$200K - $800K',\n    successMetrics: ['100K+ active players', 'Average session >30min', 'Tournament participation >20%'],\n    risks: ['Server scalability', 'Cheating prevention', 'Player retention'],\n    template: `Develop a high-performance multiplayer gaming platform with competitive features.\n\nKey Requirements:\n- Real-time multiplayer game engine with low latency\n- Tournament and league management system\n- Player profiles and social networking features\n- In-game chat and voice communication\n- Anti-cheat detection and prevention systems\n- Spectator mode and live streaming integration\n\nTechnical Specifications:\n- WebSocket-based real-time communication\n- Distributed server architecture for global reach\n- Redis for session management and caching\n- Machine learning for cheat detection\n- CDN for game asset delivery\n- Mobile-responsive design for cross-platform play\n\nSuccess Criteria:\n- <50ms latency for real-time gameplay\n- Support for 10,000+ concurrent players\n- 99.9% server uptime during peak hours\n- Zero tolerance for cheating with 99% detection rate`\n  },\n\n  {\n    id: 'streaming-platform',\n    name: 'Video Streaming Platform',\n    industry: 'Gaming & Entertainment',\n    description: 'Live streaming platform for content creators with monetization features',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'WebRTC', 'AWS', 'Redis', 'Elasticsearch'],\n    features: ['Live streaming', 'Chat system', 'Monetization tools', 'Content discovery'],\n    businessGoals: ['Attract content creators', 'Increase viewer engagement', 'Generate revenue'],\n    targetAudience: 'Content creators and viewers',\n    timeline: '10-15 months',\n    budget: '$400K - $1.5M',\n    successMetrics: ['1M+ monthly viewers', 'Creator retention >80%', 'Revenue growth 50%/year'],\n    risks: ['Content moderation', 'Bandwidth costs', 'Competition from major platforms'],\n    template: `Build a comprehensive live streaming platform that empowers content creators.\n\nKey Requirements:\n- High-quality live video streaming with adaptive bitrate\n- Real-time chat and interaction features\n- Creator monetization tools (subscriptions, donations, ads)\n- Content discovery and recommendation engine\n- Mobile streaming applications for creators\n- Advanced analytics and creator dashboard\n\nTechnical Specifications:\n- WebRTC and HLS for video streaming\n- Global CDN for low-latency delivery\n- Real-time chat with moderation tools\n- Machine learning for content recommendations\n- Payment processing for creator monetization\n- Scalable architecture supporting millions of viewers\n\nSuccess Criteria:\n- Support for 100,000+ concurrent viewers per stream\n- <3 second stream start time globally\n- 99.9% streaming uptime\n- Creator payout processing within 24 hours`\n  },\n\n  // Merged from Example Projects - Task Management\n  {\n    id: 'task-management-app',\n    name: 'TaskMaster Pro',\n    industry: 'Productivity & Collaboration',\n    description: 'Comprehensive task management application for teams and individuals',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'intermediate',\n    technologies: ['Next.js', 'Tailwind CSS', 'Supabase', 'NextAuth.js', 'Vercel'],\n    features: ['Task assignment', 'Deadline tracking', 'Progress visualization', 'Team collaboration'],\n    businessGoals: ['Improve team productivity', 'Enhance project visibility', 'Reduce missed deadlines'],\n    targetAudience: 'Project managers, teams, and individual professionals',\n    timeline: '4-6 months',\n    budget: '$80K - $250K',\n    successMetrics: ['90% task completion rate', 'Team productivity increase 25%', 'User adoption >80%'],\n    risks: ['User adoption challenges', 'Integration complexity', 'Competition from established tools'],\n    template: `Build a comprehensive task management application that helps teams organize, track, and collaborate on projects.\n\nKey Requirements:\n- User authentication and team management\n- Task creation, assignment, and tracking\n- Project dashboard with progress visualization\n- Real-time collaboration and comments\n- File attachments and document sharing\n- Email notifications and reminders\n- Time tracking and reporting\n- Integration with calendar apps\n\nTechnical Specifications:\n- Modern React-based frontend with Next.js\n- Real-time updates using WebSocket connections\n- Role-based permissions and workspace management\n- Mobile-responsive design for all devices\n- Integration with popular productivity tools\n- Automated backup and data recovery\n\nSuccess Criteria:\n- Support for teams of up to 500 members\n- 99.5% uptime for critical features\n- <2 second page load times\n- Integration with 10+ popular productivity apps`\n  },\n\n  // Merged from Example Projects - Fitness Tracking\n  {\n    id: 'fitness-tracking-app',\n    name: 'FitTracker Mobile',\n    industry: 'Health & Fitness',\n    description: 'Mobile fitness tracking app with wearable integration and social features',\n    projectType: 'mobile-application',\n    platform: 'mobile',\n    complexity: 'advanced',\n    technologies: ['React Native', 'Firebase', 'Firebase Auth', 'Jest', 'HealthKit'],\n    features: ['Workout tracking', 'Nutrition logging', 'Progress photos', 'Social challenges'],\n    businessGoals: ['Improve user health outcomes', 'Increase app engagement', 'Build fitness community'],\n    targetAudience: 'Fitness enthusiasts and health-conscious individuals',\n    timeline: '8-12 months',\n    budget: '$200K - $600K',\n    successMetrics: ['Daily active users >50K', 'Workout completion >80%', 'User retention >70%'],\n    risks: ['Wearable integration complexity', 'Health data privacy', 'User motivation'],\n    template: `Create a comprehensive fitness tracking mobile app with advanced features and social integration.\n\nKey Requirements:\n- User profiles and personalized goal setting\n- Comprehensive workout tracking and exercise library\n- Nutrition logging with calorie and macro counting\n- Progress photos and body measurements tracking\n- Social features including challenges and leaderboards\n- Integration with popular fitness wearables and health apps\n- Offline workout mode with data synchronization\n- Push notifications for motivation and reminders\n\nTechnical Specifications:\n- Cross-platform mobile development with React Native\n- Integration with HealthKit (iOS) and Google Fit (Android)\n- Real-time data synchronization with cloud backend\n- Machine learning for personalized recommendations\n- Social networking features with privacy controls\n- Offline-first architecture with background sync\n\nSuccess Criteria:\n- Integration with 10+ major fitness wearables\n- 80% workout completion rate\n- 4.5+ app store rating\n- Support for offline usage in 90% of features`\n  },\n\n  // Merged from Example Projects - Data Visualization\n  {\n    id: 'data-visualization-platform',\n    name: 'DataViz Dashboard',\n    industry: 'Business Intelligence & Analytics',\n    description: 'Interactive data visualization platform with advanced analytics capabilities',\n    projectType: 'data-analysis',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Python/FastAPI', 'PostgreSQL', 'Material-UI', 'AWS', 'D3.js'],\n    features: ['Data import', 'Interactive charts', 'Real-time processing', 'Collaborative sharing'],\n    businessGoals: ['Enable data-driven decisions', 'Improve analytics accessibility', 'Reduce reporting time'],\n    targetAudience: 'Data analysts, business users, and executives',\n    timeline: '10-14 months',\n    budget: '$300K - $900K',\n    successMetrics: ['Processing 1M+ rows', 'Report generation <30sec', 'User adoption >85%'],\n    risks: ['Data security', 'Performance with large datasets', 'User training requirements'],\n    template: `Develop an advanced data visualization platform that transforms raw data into actionable insights.\n\nKey Requirements:\n- Support for multiple data sources (CSV, JSON, databases, APIs)\n- Interactive chart and graph creation with drag-and-drop interface\n- Real-time data processing and streaming capabilities\n- Collaborative dashboard sharing and permissions\n- Advanced statistical analysis and machine learning integration\n- Custom visualization components and templates\n- Automated report generation and scheduling\n- RESTful API for data integration and embedding\n\nTechnical Specifications:\n- High-performance backend with Python/FastAPI\n- Scalable data processing with Apache Spark\n- Interactive visualizations using D3.js and custom components\n- Real-time data streaming with WebSocket connections\n- Cloud-native architecture with auto-scaling\n- Advanced caching for improved performance\n\nSuccess Criteria:\n- Process datasets with millions of rows in real-time\n- Generate complex reports in under 30 seconds\n- Support for 1,000+ concurrent users\n- 99.9% data accuracy and integrity`\n  },\n\n  // Healthcare & Medical (10 templates)\n  {\n    id: 'hospital-management-system',\n    name: 'Hospital Management System',\n    industry: 'Healthcare & Medical',\n    description: 'Comprehensive HIPAA-compliant hospital management platform with patient records and scheduling',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'HL7 FHIR', 'Redis'],\n    features: ['Patient records', 'Appointment scheduling', 'Billing system', 'Inventory management', 'Staff management'],\n    businessGoals: ['Improve patient care', 'Reduce administrative costs', 'Ensure HIPAA compliance', 'Streamline operations'],\n    targetAudience: 'Hospitals, clinics, and healthcare administrators',\n    timeline: '12-18 months',\n    budget: '$400K - $1.2M',\n    successMetrics: ['HIPAA compliance certification', '50% reduction in paperwork', '99.9% uptime'],\n    risks: ['Data breaches', 'Regulatory compliance', 'Staff training requirements'],\n    template: `Build a comprehensive hospital management system with full HIPAA compliance and integrated patient care workflows.\n\nKey Requirements:\n- Electronic Health Records (EHR) with HL7 FHIR compliance\n- Patient appointment scheduling and management\n- Billing and insurance claim processing\n- Medical inventory and pharmacy management\n- Staff scheduling and role-based access control\n- Integration with medical devices and lab systems\n- Telemedicine capabilities for remote consultations\n- Automated reporting and analytics dashboard\n\nTechnical Specifications:\n- HIPAA-compliant cloud infrastructure\n- End-to-end encryption for all patient data\n- Role-based access control with audit trails\n- Integration with existing hospital systems\n- Real-time notifications and alerts\n- Mobile-responsive design for tablets and smartphones\n- Backup and disaster recovery systems\n- API integrations with insurance providers\n\nSuccess Criteria:\n- Full HIPAA compliance certification\n- 99.9% system uptime during critical hours\n- 50% reduction in administrative paperwork\n- Integration with 95% of existing hospital systems`\n  },\n\n  {\n    id: 'mental-health-platform',\n    name: 'Mental Health Support Platform',\n    industry: 'Healthcare & Medical',\n    description: 'Digital mental health platform with therapy matching, progress tracking, and crisis intervention',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'intermediate',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'Stripe', 'Twilio', 'TensorFlow'],\n    features: ['Therapist matching', 'Video sessions', 'Progress tracking', 'Crisis intervention', 'Resource library'],\n    businessGoals: ['Improve mental health access', 'Reduce therapy wait times', 'Provide 24/7 support'],\n    targetAudience: 'Individuals seeking mental health support and licensed therapists',\n    timeline: '8-12 months',\n    budget: '$200K - $600K',\n    successMetrics: ['90% user satisfaction', '70% therapy completion rate', '24/7 crisis response'],\n    risks: ['Privacy concerns', 'Therapist availability', 'Crisis management protocols'],\n    template: `Create a comprehensive mental health platform that connects users with licensed therapists and provides ongoing support.\n\nKey Requirements:\n- AI-powered therapist matching based on specialties and preferences\n- Secure video conferencing for therapy sessions\n- Progress tracking with mood journals and assessments\n- 24/7 crisis intervention with emergency protocols\n- Comprehensive resource library with self-help tools\n- Insurance integration and billing management\n- Mobile app for on-the-go access\n- Community support groups and forums\n\nTechnical Specifications:\n- HIPAA-compliant video conferencing\n- Encrypted messaging and file sharing\n- AI algorithms for therapist-client matching\n- Real-time crisis detection and alert systems\n- Integration with electronic health records\n- Payment processing with insurance claims\n- Multi-platform mobile and web applications\n- Advanced analytics for treatment outcomes\n\nSuccess Criteria:\n- 90% user satisfaction rating\n- 70% therapy session completion rate\n- Sub-5 minute crisis response time\n- Integration with major insurance providers`\n  },\n\n  {\n    id: 'medical-imaging-ai',\n    name: 'AI Medical Imaging Platform',\n    industry: 'Healthcare & Medical',\n    description: 'AI-powered medical imaging analysis platform for radiology and diagnostic imaging',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['Python', 'TensorFlow', 'React', 'FastAPI', 'PostgreSQL', 'DICOM'],\n    features: ['Image analysis', 'AI diagnostics', 'Report generation', 'DICOM integration', 'Radiologist workflow'],\n    businessGoals: ['Improve diagnostic accuracy', 'Reduce analysis time', 'Support radiologists'],\n    targetAudience: 'Radiologists, hospitals, and diagnostic imaging centers',\n    timeline: '15-24 months',\n    budget: '$800K - $2.5M',\n    successMetrics: ['95% diagnostic accuracy', '60% faster analysis', 'FDA approval'],\n    risks: ['Regulatory approval', 'AI model accuracy', 'Integration complexity'],\n    template: `Develop an AI-powered medical imaging platform that assists radiologists in diagnostic analysis and reporting.\n\nKey Requirements:\n- Advanced AI models for medical image analysis (X-ray, CT, MRI, ultrasound)\n- DICOM standard compliance for medical imaging\n- Automated report generation with confidence scores\n- Integration with existing radiology workflows (PACS/RIS)\n- Real-time collaboration tools for radiologists\n- Quality assurance and peer review systems\n- Mobile access for emergency consultations\n- Comprehensive audit trails and version control\n\nTechnical Specifications:\n- Deep learning models trained on medical datasets\n- GPU-accelerated image processing infrastructure\n- DICOM viewer with advanced visualization tools\n- RESTful APIs for PACS/RIS integration\n- Cloud-based storage with HIPAA compliance\n- Real-time image streaming and processing\n- Advanced security with role-based access\n- Scalable architecture for high-volume processing\n\nSuccess Criteria:\n- 95% diagnostic accuracy compared to expert radiologists\n- 60% reduction in image analysis time\n- FDA 510(k) clearance for clinical use\n- Integration with 90% of major PACS systems`\n  },\n\n  {\n    id: 'pharmacy-management',\n    name: 'Pharmacy Management System',\n    industry: 'Healthcare & Medical',\n    description: 'Complete pharmacy management solution with inventory, prescriptions, and insurance processing',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'intermediate',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'Stripe', 'Twilio', 'AWS'],\n    features: ['Prescription management', 'Inventory tracking', 'Insurance processing', 'Patient profiles', 'Drug interaction alerts'],\n    businessGoals: ['Streamline pharmacy operations', 'Reduce medication errors', 'Improve customer service'],\n    targetAudience: 'Pharmacies, pharmacists, and pharmacy technicians',\n    timeline: '6-10 months',\n    budget: '$150K - $500K',\n    successMetrics: ['99% prescription accuracy', '50% faster processing', '95% insurance approval'],\n    risks: ['Regulatory compliance', 'Drug database accuracy', 'Insurance integration'],\n    template: `Build a comprehensive pharmacy management system that streamlines prescription processing and inventory management.\n\nKey Requirements:\n- Electronic prescription processing and verification\n- Real-time inventory management with automatic reordering\n- Insurance claim processing and prior authorization\n- Patient profile management with medication history\n- Drug interaction and allergy checking\n- Automated refill reminders and notifications\n- Point-of-sale integration with payment processing\n- Regulatory compliance reporting (DEA, state boards)\n\nTechnical Specifications:\n- Integration with electronic health records (EHR)\n- Real-time drug database updates (First Databank, Lexicomp)\n- Secure prescription transmission (SCRIPT standard)\n- Barcode scanning for medication verification\n- Cloud-based backup and disaster recovery\n- Mobile app for prescription management\n- Advanced reporting and analytics dashboard\n- Multi-location support for pharmacy chains\n\nSuccess Criteria:\n- 99% prescription accuracy with error checking\n- 50% reduction in prescription processing time\n- 95% insurance claim approval rate\n- Full compliance with pharmacy regulations`\n  },\n\n  {\n    id: 'fitness-wellness-app',\n    name: 'Fitness & Wellness Tracking App',\n    industry: 'Healthcare & Medical',\n    description: 'Comprehensive fitness and wellness platform with personalized coaching and health monitoring',\n    projectType: 'mobile-application',\n    platform: 'mobile',\n    complexity: 'intermediate',\n    technologies: ['React Native', 'Node.js', 'MongoDB', 'AWS', 'HealthKit', 'Google Fit'],\n    features: ['Workout tracking', 'Nutrition logging', 'Health monitoring', 'Personal coaching', 'Social features'],\n    businessGoals: ['Promote healthy lifestyles', 'Increase user engagement', 'Provide personalized guidance'],\n    targetAudience: 'Fitness enthusiasts, health-conscious individuals, and personal trainers',\n    timeline: '6-9 months',\n    budget: '$100K - $400K',\n    successMetrics: ['80% daily active users', '70% goal completion', '4.5+ app rating'],\n    risks: ['User retention', 'Data accuracy', 'Competition from established apps'],\n    template: `Create a comprehensive fitness and wellness platform that motivates users to achieve their health goals.\n\nKey Requirements:\n- Comprehensive workout tracking with exercise library\n- Nutrition logging with barcode scanning and meal planning\n- Health metrics monitoring (weight, heart rate, sleep, steps)\n- AI-powered personal coaching and recommendations\n- Social features with challenges and community support\n- Integration with wearable devices and health apps\n- Progress tracking with detailed analytics and insights\n- Customizable workout plans and nutrition programs\n\nTechnical Specifications:\n- Cross-platform mobile development (iOS/Android)\n- Integration with HealthKit (iOS) and Google Fit (Android)\n- Real-time synchronization across devices\n- Machine learning for personalized recommendations\n- Social networking features with privacy controls\n- Push notifications for motivation and reminders\n- Offline mode for workout tracking\n- Advanced analytics and reporting dashboard\n\nSuccess Criteria:\n- 80% daily active user retention\n- 70% user goal completion rate\n- 4.5+ average app store rating\n- Integration with 95% of popular fitness wearables`\n  },\n\n  // Financial Services & Fintech (8 templates)\n  {\n    id: 'cryptocurrency-exchange',\n    name: 'Cryptocurrency Exchange Platform',\n    industry: 'Financial Services & Fintech',\n    description: 'Secure cryptocurrency trading platform with advanced order matching and wallet management',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'Redis', 'WebSocket', 'Blockchain APIs'],\n    features: ['Crypto trading', 'Wallet management', 'Order matching', 'Security features', 'Market analysis'],\n    businessGoals: ['Enable crypto trading', 'Ensure security', 'Provide liquidity', 'Comply with regulations'],\n    targetAudience: 'Cryptocurrency traders and investors',\n    timeline: '12-18 months',\n    budget: '$600K - $2M',\n    successMetrics: ['$1M+ daily volume', '99.9% uptime', 'Zero security breaches'],\n    risks: ['Regulatory changes', 'Security threats', 'Market volatility'],\n    template: `Build a secure and scalable cryptocurrency exchange platform with advanced trading features.\n\nKey Requirements:\n- Multi-cryptocurrency support (Bitcoin, Ethereum, altcoins)\n- Advanced order matching engine with high throughput\n- Secure wallet management with cold storage integration\n- Real-time market data and charting tools\n- KYC/AML compliance and identity verification\n- Two-factor authentication and security measures\n- API for algorithmic trading and third-party integrations\n- Liquidity management and market making tools\n\nTechnical Specifications:\n- High-performance order matching engine (100k+ orders/sec)\n- Multi-signature wallet security with hardware security modules\n- Real-time WebSocket connections for market data\n- Microservices architecture for scalability\n- Advanced monitoring and alerting systems\n- Compliance reporting and audit trails\n- DDoS protection and security hardening\n- Multi-region deployment for global access\n\nSuccess Criteria:\n- Process $1M+ in daily trading volume\n- 99.9% platform uptime during market hours\n- Zero security breaches or fund losses\n- Full regulatory compliance in target jurisdictions`\n  },\n\n  {\n    id: 'robo-advisor-platform',\n    name: 'Robo-Advisor Investment Platform',\n    industry: 'Financial Services & Fintech',\n    description: 'AI-powered investment advisory platform with automated portfolio management and rebalancing',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Python', 'TensorFlow', 'PostgreSQL', 'AWS', 'Alpaca API'],\n    features: ['Portfolio management', 'Risk assessment', 'Automated rebalancing', 'Tax optimization', 'Goal tracking'],\n    businessGoals: ['Democratize investing', 'Reduce management fees', 'Provide personalized advice'],\n    targetAudience: 'Individual investors and financial advisors',\n    timeline: '10-15 months',\n    budget: '$400K - $1.2M',\n    successMetrics: ['$100M+ assets under management', '8%+ annual returns', '0.5% management fee'],\n    risks: ['Market volatility', 'Regulatory compliance', 'Algorithm performance'],\n    template: `Create an AI-powered robo-advisor platform that provides automated investment management and financial planning.\n\nKey Requirements:\n- AI-driven portfolio construction and optimization\n- Automated rebalancing based on market conditions\n- Risk tolerance assessment and goal-based investing\n- Tax-loss harvesting and optimization strategies\n- Integration with brokerage accounts and custodians\n- Comprehensive financial planning tools\n- Real-time performance tracking and reporting\n- Educational content and investment insights\n\nTechnical Specifications:\n- Machine learning algorithms for portfolio optimization\n- Real-time market data integration and analysis\n- Automated trading execution with best execution\n- Advanced risk management and compliance monitoring\n- Secure account aggregation and data synchronization\n- Mobile-first responsive design\n- Comprehensive API for third-party integrations\n- Advanced analytics and performance attribution\n\nSuccess Criteria:\n- Manage $100M+ in assets under management\n- Achieve 8%+ average annual returns net of fees\n- Maintain 0.5% or lower management fee structure\n- 95% client satisfaction and retention rate`\n  },\n\n  {\n    id: 'peer-to-peer-lending',\n    name: 'Peer-to-Peer Lending Platform',\n    industry: 'Financial Services & Fintech',\n    description: 'P2P lending marketplace connecting borrowers with investors for personal and business loans',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'Stripe', 'Plaid', 'TensorFlow'],\n    features: ['Loan marketplace', 'Credit scoring', 'Automated investing', 'Risk assessment', 'Payment processing'],\n    businessGoals: ['Connect borrowers and lenders', 'Reduce lending costs', 'Improve access to credit'],\n    targetAudience: 'Individual borrowers, investors, and small businesses',\n    timeline: '12-16 months',\n    budget: '$500K - $1.5M',\n    successMetrics: ['$50M+ loan origination', '5% default rate', '12% investor returns'],\n    risks: ['Credit risk', 'Regulatory compliance', 'Economic downturns'],\n    template: `Build a comprehensive peer-to-peer lending platform that efficiently matches borrowers with investors.\n\nKey Requirements:\n- Borrower application and verification system\n- AI-powered credit scoring and risk assessment\n- Investor dashboard with automated investing options\n- Loan marketplace with filtering and search capabilities\n- Integrated payment processing and loan servicing\n- Regulatory compliance and reporting tools\n- Mobile applications for borrowers and investors\n- Advanced analytics and performance tracking\n\nTechnical Specifications:\n- Machine learning models for credit risk assessment\n- Integration with credit bureaus and financial data providers\n- Automated loan servicing and payment processing\n- Real-time loan performance monitoring\n- Secure document upload and verification\n- Advanced fraud detection and prevention\n- Comprehensive reporting and compliance tools\n- Scalable architecture for high transaction volume\n\nSuccess Criteria:\n- Originate $50M+ in loans annually\n- Maintain sub-5% default rate across loan portfolio\n- Achieve 12%+ average annual returns for investors\n- Full compliance with lending regulations`\n  },\n\n  {\n    id: 'expense-management-app',\n    name: 'Corporate Expense Management',\n    industry: 'Financial Services & Fintech',\n    description: 'AI-powered expense management platform with receipt scanning and automated approval workflows',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'intermediate',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'OCR API', 'Stripe'],\n    features: ['Receipt scanning', 'Expense tracking', 'Approval workflows', 'Reimbursement processing', 'Analytics'],\n    businessGoals: ['Streamline expense reporting', 'Reduce processing time', 'Improve compliance'],\n    targetAudience: 'Businesses, employees, and finance teams',\n    timeline: '6-10 months',\n    budget: '$200K - $600K',\n    successMetrics: ['80% faster processing', '95% receipt accuracy', '90% user adoption'],\n    risks: ['OCR accuracy', 'Integration complexity', 'User adoption'],\n    template: `Create an intelligent expense management platform that automates expense reporting and approval processes.\n\nKey Requirements:\n- AI-powered receipt scanning and data extraction\n- Mobile app for expense capture and submission\n- Customizable approval workflows and policies\n- Integration with accounting systems (QuickBooks, SAP, etc.)\n- Real-time expense tracking and budget monitoring\n- Automated mileage tracking and calculation\n- Corporate credit card integration and reconciliation\n- Comprehensive reporting and analytics dashboard\n\nTechnical Specifications:\n- OCR technology for receipt data extraction\n- Machine learning for expense categorization\n- Real-time synchronization across devices\n- Integration with major accounting platforms\n- Automated policy compliance checking\n- Advanced reporting with custom dashboards\n- Mobile-first responsive design\n- Secure document storage and retrieval\n\nSuccess Criteria:\n- 80% reduction in expense processing time\n- 95% accuracy in receipt data extraction\n- 90% employee adoption within 6 months\n- Integration with 95% of popular accounting systems`\n  },\n\n  {\n    id: 'insurance-claims-platform',\n    name: 'Digital Insurance Claims Platform',\n    industry: 'Financial Services & Fintech',\n    description: 'AI-powered insurance claims processing platform with automated assessment and fraud detection',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Python', 'TensorFlow', 'PostgreSQL', 'AWS', 'Computer Vision'],\n    features: ['Claims processing', 'Damage assessment', 'Fraud detection', 'Customer portal', 'Agent dashboard'],\n    businessGoals: ['Accelerate claims processing', 'Reduce fraud', 'Improve customer satisfaction'],\n    targetAudience: 'Insurance companies, claims adjusters, and policyholders',\n    timeline: '12-18 months',\n    budget: '$600K - $1.8M',\n    successMetrics: ['70% faster processing', '90% fraud detection', '95% customer satisfaction'],\n    risks: ['AI accuracy', 'Regulatory compliance', 'Integration complexity'],\n    template: `Develop an AI-powered insurance claims platform that automates assessment and accelerates processing.\n\nKey Requirements:\n- AI-powered damage assessment using computer vision\n- Automated fraud detection and risk scoring\n- Customer self-service portal for claim submission\n- Claims adjuster dashboard with workflow management\n- Integration with existing insurance systems\n- Real-time claim status tracking and notifications\n- Mobile app for photo capture and documentation\n- Comprehensive reporting and analytics tools\n\nTechnical Specifications:\n- Computer vision models for damage assessment\n- Machine learning algorithms for fraud detection\n- Real-time image processing and analysis\n- Integration with core insurance systems\n- Automated workflow orchestration\n- Advanced security and data protection\n- Scalable cloud infrastructure\n- Mobile-optimized user interfaces\n\nSuccess Criteria:\n- 70% reduction in claims processing time\n- 90% accuracy in fraud detection\n- 95% customer satisfaction rating\n- Integration with major insurance carriers`\n  },\n\n  // E-commerce & Retail (8 templates)\n  {\n    id: 'marketplace-platform',\n    name: 'Multi-Vendor Marketplace',\n    industry: 'E-commerce & Retail',\n    description: 'Comprehensive multi-vendor marketplace with seller management and advanced analytics',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'Stripe', 'AWS', 'Elasticsearch'],\n    features: ['Vendor management', 'Product catalog', 'Order processing', 'Payment gateway', 'Analytics dashboard'],\n    businessGoals: ['Create marketplace ecosystem', 'Generate commission revenue', 'Scale vendor network'],\n    targetAudience: 'Online sellers, buyers, and marketplace operators',\n    timeline: '10-15 months',\n    budget: '$400K - $1.2M',\n    successMetrics: ['1000+ active vendors', '$10M+ GMV', '95% uptime'],\n    risks: ['Vendor quality control', 'Payment disputes', 'Competition'],\n    template: `Build a comprehensive multi-vendor marketplace platform that connects sellers with buyers globally.\n\nKey Requirements:\n- Vendor onboarding and management system\n- Advanced product catalog with search and filtering\n- Integrated payment processing with split payments\n- Order management and fulfillment tracking\n- Review and rating system for vendors and products\n- Commission management and payout automation\n- Mobile-responsive design with PWA capabilities\n- Advanced analytics and reporting dashboard\n\nTechnical Specifications:\n- Microservices architecture for scalability\n- Elasticsearch for advanced product search\n- Real-time inventory management across vendors\n- Automated commission calculation and distribution\n- Multi-currency and multi-language support\n- Advanced fraud detection and prevention\n- CDN integration for fast global content delivery\n- Comprehensive API for third-party integrations\n\nSuccess Criteria:\n- Onboard 1000+ active vendors within first year\n- Achieve $10M+ gross merchandise value (GMV)\n- Maintain 95% platform uptime\n- Process 100,000+ orders monthly`\n  },\n\n  {\n    id: 'fashion-ecommerce',\n    name: 'Fashion E-commerce Platform',\n    industry: 'E-commerce & Retail',\n    description: 'AI-powered fashion e-commerce with virtual try-on and personalized recommendations',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'TensorFlow', 'PostgreSQL', 'AWS', 'AR.js'],\n    features: ['Virtual try-on', 'Style recommendations', 'Size matching', 'Social shopping', 'Inventory management'],\n    businessGoals: ['Reduce returns', 'Increase conversions', 'Enhance shopping experience'],\n    targetAudience: 'Fashion-conscious consumers and clothing retailers',\n    timeline: '12-16 months',\n    budget: '$500K - $1.5M',\n    successMetrics: ['30% reduction in returns', '25% increase in conversion', '4.5+ user rating'],\n    risks: ['AR technology adoption', 'Size accuracy', 'Fashion trend changes'],\n    template: `Create an innovative fashion e-commerce platform with AI-powered features and virtual try-on capabilities.\n\nKey Requirements:\n- AI-powered virtual try-on using augmented reality\n- Personalized style recommendations based on preferences\n- Advanced size matching and fit prediction\n- Social shopping features with style sharing\n- Comprehensive inventory management system\n- Integration with fashion brands and suppliers\n- Mobile-first design with AR capabilities\n- Advanced search with visual similarity matching\n\nTechnical Specifications:\n- Computer vision for virtual try-on and fit analysis\n- Machine learning for personalized recommendations\n- Augmented reality integration for mobile devices\n- Real-time inventory synchronization\n- Advanced image processing and optimization\n- Social media integration for style sharing\n- Progressive web app for mobile experience\n- Analytics dashboard for fashion insights\n\nSuccess Criteria:\n- 30% reduction in return rates through better fit\n- 25% increase in conversion rates\n- 4.5+ average user rating in app stores\n- Integration with 100+ fashion brands`\n  },\n\n  {\n    id: 'grocery-delivery-app',\n    name: 'Grocery Delivery Platform',\n    industry: 'E-commerce & Retail',\n    description: 'On-demand grocery delivery platform with real-time tracking and inventory management',\n    projectType: 'mobile-application',\n    platform: 'mobile',\n    complexity: 'intermediate',\n    technologies: ['React Native', 'Node.js', 'PostgreSQL', 'Stripe', 'Google Maps', 'Firebase'],\n    features: ['Product browsing', 'Real-time tracking', 'Delivery scheduling', 'Payment processing', 'Inventory sync'],\n    businessGoals: ['Provide convenient shopping', 'Optimize delivery routes', 'Increase customer retention'],\n    targetAudience: 'Busy consumers, families, and grocery stores',\n    timeline: '8-12 months',\n    budget: '$250K - $750K',\n    successMetrics: ['30-minute delivery', '95% on-time delivery', '4.8+ app rating'],\n    risks: ['Delivery logistics', 'Inventory accuracy', 'Driver availability'],\n    template: `Build a comprehensive grocery delivery platform that provides fast and reliable service to customers.\n\nKey Requirements:\n- Intuitive product browsing with categories and search\n- Real-time inventory synchronization with stores\n- Flexible delivery scheduling and time slots\n- Live order tracking with GPS integration\n- Multiple payment options and secure processing\n- Driver management and route optimization\n- Customer support and order management\n- Loyalty programs and promotional campaigns\n\nTechnical Specifications:\n- Cross-platform mobile development (iOS/Android)\n- Real-time GPS tracking and route optimization\n- Integration with grocery store POS systems\n- Automated inventory management and updates\n- Push notifications for order status updates\n- Advanced analytics for demand forecasting\n- Driver app with navigation and order management\n- Admin dashboard for operations management\n\nSuccess Criteria:\n- Achieve 30-minute average delivery time\n- Maintain 95% on-time delivery rate\n- 4.8+ average app store rating\n- Process 10,000+ orders monthly`\n  },\n\n  {\n    id: 'subscription-box-platform',\n    name: 'Subscription Box Service',\n    industry: 'E-commerce & Retail',\n    description: 'Customizable subscription box platform with curation algorithms and customer preferences',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'intermediate',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'Stripe', 'AWS', 'Machine Learning'],\n    features: ['Subscription management', 'Product curation', 'Customer preferences', 'Billing automation', 'Analytics'],\n    businessGoals: ['Build recurring revenue', 'Increase customer lifetime value', 'Personalize experiences'],\n    targetAudience: 'Subscription box enthusiasts and niche product consumers',\n    timeline: '6-10 months',\n    budget: '$200K - $600K',\n    successMetrics: ['90% retention rate', '$100 average LTV', '95% satisfaction'],\n    risks: ['Customer churn', 'Inventory management', 'Shipping costs'],\n    template: `Create a personalized subscription box platform that delivers curated products based on customer preferences.\n\nKey Requirements:\n- Flexible subscription management with pause/skip options\n- AI-powered product curation based on preferences\n- Customer preference profiling and feedback system\n- Automated billing and payment processing\n- Inventory management with supplier integration\n- Shipping and logistics management\n- Customer portal for subscription customization\n- Analytics dashboard for business insights\n\nTechnical Specifications:\n- Machine learning algorithms for product recommendations\n- Automated subscription billing and dunning management\n- Integration with shipping carriers and tracking\n- Customer feedback and rating system\n- Inventory forecasting and procurement automation\n- Mobile-responsive customer portal\n- Advanced analytics and cohort analysis\n- Integration with e-commerce platforms\n\nSuccess Criteria:\n- Achieve 90% monthly customer retention rate\n- $100+ average customer lifetime value\n- 95% customer satisfaction rating\n- Process 50,000+ subscription boxes monthly`\n  },\n\n  {\n    id: 'b2b-wholesale-platform',\n    name: 'B2B Wholesale Marketplace',\n    industry: 'E-commerce & Retail',\n    description: 'B2B wholesale platform connecting manufacturers with retailers and distributors',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'Stripe', 'AWS', 'ERP Integration'],\n    features: ['Bulk ordering', 'Price negotiation', 'Credit management', 'Logistics coordination', 'Analytics'],\n    businessGoals: ['Connect B2B buyers and sellers', 'Streamline wholesale processes', 'Increase trade volume'],\n    targetAudience: 'Manufacturers, wholesalers, retailers, and distributors',\n    timeline: '12-18 months',\n    budget: '$500K - $1.5M',\n    successMetrics: ['$50M+ trade volume', '500+ active buyers', '99% order accuracy'],\n    risks: ['Credit risk', 'Logistics complexity', 'Market competition'],\n    template: `Build a comprehensive B2B wholesale marketplace that facilitates large-scale trade between businesses.\n\nKey Requirements:\n- Advanced product catalog with bulk pricing tiers\n- Quote request and negotiation system\n- Credit management and payment terms\n- Bulk order processing and fulfillment\n- Logistics coordination and shipping management\n- Supplier verification and quality assurance\n- Integration with ERP and accounting systems\n- Advanced analytics and market insights\n\nTechnical Specifications:\n- Enterprise-grade security and compliance\n- Integration with major ERP systems (SAP, Oracle)\n- Advanced search and filtering for B2B products\n- Automated credit checking and approval workflows\n- Real-time inventory management across suppliers\n- Comprehensive reporting and analytics dashboard\n- API integrations for third-party logistics\n- Multi-currency and international trade support\n\nSuccess Criteria:\n- Facilitate $50M+ in annual trade volume\n- Onboard 500+ active business buyers\n- Achieve 99% order accuracy and fulfillment\n- Process 10,000+ B2B transactions monthly`\n  },\n\n  // Education & EdTech (8 templates)\n  {\n    id: 'online-learning-platform',\n    name: 'Online Learning Platform',\n    industry: 'Education & EdTech',\n    description: 'Comprehensive online learning platform with interactive courses and progress tracking',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'WebRTC', 'TensorFlow'],\n    features: ['Course creation', 'Video streaming', 'Interactive assessments', 'Progress tracking', 'Certification'],\n    businessGoals: ['Democratize education', 'Scale learning delivery', 'Improve learning outcomes'],\n    targetAudience: 'Students, educators, and educational institutions',\n    timeline: '10-15 months',\n    budget: '$400K - $1.2M',\n    successMetrics: ['100K+ active learners', '85% course completion', '4.7+ rating'],\n    risks: ['Content quality', 'Technology adoption', 'Competition'],\n    template: `Create a comprehensive online learning platform that delivers engaging educational experiences at scale.\n\nKey Requirements:\n- Intuitive course creation tools for educators\n- High-quality video streaming with adaptive bitrate\n- Interactive assessments and quizzes with instant feedback\n- Comprehensive progress tracking and analytics\n- Certification and badge system for achievements\n- Discussion forums and peer collaboration tools\n- Mobile-responsive design with offline capabilities\n- Integration with existing educational systems (LTI)\n\nTechnical Specifications:\n- Scalable video delivery with CDN integration\n- Real-time collaboration tools for group projects\n- AI-powered content recommendations\n- Advanced analytics for learning insights\n- Secure payment processing for course purchases\n- Multi-language support and accessibility features\n- API integrations with educational tools\n- Comprehensive admin dashboard for institutions\n\nSuccess Criteria:\n- Onboard 100,000+ active learners\n- Achieve 85% average course completion rate\n- Maintain 4.7+ average course rating\n- Support 1,000+ concurrent video streams`\n  },\n\n  {\n    id: 'student-information-system',\n    name: 'Student Information System',\n    industry: 'Education & EdTech',\n    description: 'Comprehensive SIS for K-12 schools with gradebook, attendance, and parent communication',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'Twilio', 'Chart.js'],\n    features: ['Student records', 'Gradebook', 'Attendance tracking', 'Parent portal', 'Reporting'],\n    businessGoals: ['Streamline school administration', 'Improve parent engagement', 'Enhance student outcomes'],\n    targetAudience: 'K-12 schools, teachers, students, and parents',\n    timeline: '12-18 months',\n    budget: '$300K - $900K',\n    successMetrics: ['99% data accuracy', '90% parent engagement', '50% admin time savings'],\n    risks: ['Data privacy', 'System integration', 'User training'],\n    template: `Build a comprehensive student information system that manages all aspects of K-12 school operations.\n\nKey Requirements:\n- Complete student record management with academic history\n- Digital gradebook with standards-based grading\n- Automated attendance tracking with notifications\n- Parent portal with real-time access to student progress\n- Comprehensive reporting and analytics dashboard\n- Integration with state reporting systems\n- Mobile app for teachers and parents\n- Secure communication tools between stakeholders\n\nTechnical Specifications:\n- FERPA-compliant data security and privacy\n- Integration with existing school systems (HR, Finance)\n- Real-time synchronization across all modules\n- Advanced reporting with custom dashboard creation\n- Automated notifications via email and SMS\n- Role-based access control for different user types\n- Backup and disaster recovery systems\n- API integrations with educational tools\n\nSuccess Criteria:\n- Achieve 99% data accuracy across all records\n- 90% parent engagement through portal usage\n- 50% reduction in administrative processing time\n- Full compliance with educational data regulations`\n  },\n\n  {\n    id: 'language-learning-app',\n    name: 'AI Language Learning App',\n    industry: 'Education & EdTech',\n    description: 'AI-powered language learning app with speech recognition and personalized curriculum',\n    projectType: 'mobile-application',\n    platform: 'mobile',\n    complexity: 'advanced',\n    technologies: ['React Native', 'Python', 'TensorFlow', 'PostgreSQL', 'AWS', 'Speech API'],\n    features: ['Speech recognition', 'Adaptive learning', 'Gamification', 'Progress tracking', 'Cultural content'],\n    businessGoals: ['Make language learning accessible', 'Improve learning efficiency', 'Increase user engagement'],\n    targetAudience: 'Language learners of all ages and proficiency levels',\n    timeline: '12-16 months',\n    budget: '$500K - $1.5M',\n    successMetrics: ['1M+ downloads', '70% retention rate', 'B2+ proficiency achievement'],\n    risks: ['Speech recognition accuracy', 'Content localization', 'User motivation'],\n    template: `Develop an AI-powered language learning app that adapts to individual learning styles and pace.\n\nKey Requirements:\n- Advanced speech recognition for pronunciation practice\n- AI-driven adaptive learning curriculum\n- Gamification elements with achievements and streaks\n- Comprehensive progress tracking and analytics\n- Cultural context and real-world conversation practice\n- Offline mode for learning without internet\n- Social features for language exchange\n- Integration with language proficiency standards (CEFR)\n\nTechnical Specifications:\n- Machine learning models for personalized learning paths\n- Advanced speech processing and pronunciation analysis\n- Real-time progress adaptation based on performance\n- Gamification engine with rewards and challenges\n- Cross-platform mobile development (iOS/Android)\n- Offline content synchronization and storage\n- Social networking features for peer interaction\n- Analytics dashboard for learning insights\n\nSuccess Criteria:\n- Achieve 1M+ app downloads within first year\n- Maintain 70% user retention after 30 days\n- Help users achieve B2+ proficiency level\n- Support 20+ languages with native speaker quality`\n  },\n\n  {\n    id: 'virtual-classroom-platform',\n    name: 'Virtual Classroom Platform',\n    industry: 'Education & EdTech',\n    description: 'Interactive virtual classroom with real-time collaboration and engagement tools',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'WebRTC', 'Socket.io', 'PostgreSQL', 'AWS'],\n    features: ['Video conferencing', 'Screen sharing', 'Interactive whiteboard', 'Breakout rooms', 'Recording'],\n    businessGoals: ['Enable remote learning', 'Increase engagement', 'Reduce technology barriers'],\n    targetAudience: 'Educators, students, and educational institutions',\n    timeline: '8-12 months',\n    budget: '$300K - $900K',\n    successMetrics: ['500+ concurrent users', '95% uptime', '4.5+ user satisfaction'],\n    risks: ['Bandwidth limitations', 'Technology adoption', 'Security concerns'],\n    template: `Create an interactive virtual classroom platform that replicates and enhances in-person learning experiences.\n\nKey Requirements:\n- High-quality video conferencing with screen sharing\n- Interactive whiteboard with real-time collaboration\n- Breakout rooms for small group activities\n- Session recording and playback capabilities\n- Chat and messaging with moderation tools\n- Attendance tracking and engagement analytics\n- Integration with learning management systems\n- Mobile support for tablets and smartphones\n\nTechnical Specifications:\n- WebRTC for peer-to-peer video communication\n- Real-time collaboration using WebSocket connections\n- Scalable architecture supporting 500+ concurrent users\n- Advanced audio/video processing and optimization\n- Cloud recording with automatic transcription\n- Comprehensive security and privacy controls\n- API integrations with popular LMS platforms\n- Responsive design for multiple device types\n\nSuccess Criteria:\n- Support 500+ concurrent users per session\n- Maintain 95% platform uptime during peak hours\n- Achieve 4.5+ user satisfaction rating\n- Process 10,000+ virtual classroom sessions monthly`\n  },\n\n  {\n    id: 'skill-assessment-platform',\n    name: 'Skills Assessment Platform',\n    industry: 'Education & EdTech',\n    description: 'AI-powered skills assessment platform with adaptive testing and competency mapping',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Python', 'TensorFlow', 'PostgreSQL', 'AWS', 'Proctoring API'],\n    features: ['Adaptive testing', 'Skill mapping', 'Proctoring', 'Analytics', 'Certification'],\n    businessGoals: ['Validate skills accurately', 'Reduce assessment time', 'Provide actionable insights'],\n    targetAudience: 'Educational institutions, employers, and certification bodies',\n    timeline: '10-14 months',\n    budget: '$400K - $1.2M',\n    successMetrics: ['95% assessment accuracy', '50% time reduction', '90% user satisfaction'],\n    risks: ['Cheating prevention', 'Algorithm bias', 'Technical complexity'],\n    template: `Build an AI-powered skills assessment platform that accurately measures competencies and provides actionable insights.\n\nKey Requirements:\n- Adaptive testing algorithms that adjust difficulty in real-time\n- Comprehensive skill mapping and competency frameworks\n- Advanced proctoring with AI-powered monitoring\n- Detailed analytics and performance insights\n- Automated certification and badge generation\n- Integration with HR systems and job platforms\n- Multi-format questions (multiple choice, coding, simulation)\n- Accessibility features for diverse learners\n\nTechnical Specifications:\n- Machine learning models for adaptive question selection\n- Computer vision for proctoring and identity verification\n- Advanced analytics engine for skill gap analysis\n- Secure test delivery with anti-cheating measures\n- Real-time performance monitoring and alerts\n- Integration APIs for third-party systems\n- Comprehensive reporting and dashboard tools\n- Multi-language support and localization\n\nSuccess Criteria:\n- Achieve 95% assessment accuracy compared to expert evaluation\n- Reduce assessment time by 50% through adaptive testing\n- Maintain 90% user satisfaction rating\n- Process 100,000+ assessments annually`\n  },\n\n  // Real Estate & PropTech (6 templates)\n  {\n    id: 'real-estate-marketplace',\n    name: 'Real Estate Marketplace',\n    industry: 'Real Estate & PropTech',\n    description: 'Comprehensive real estate marketplace with virtual tours and AI-powered property matching',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'Google Maps', 'Three.js'],\n    features: ['Property listings', 'Virtual tours', 'Search filters', 'Agent profiles', 'Market analytics'],\n    businessGoals: ['Connect buyers and sellers', 'Streamline property discovery', 'Provide market insights'],\n    targetAudience: 'Home buyers, sellers, real estate agents, and investors',\n    timeline: '10-15 months',\n    budget: '$400K - $1.2M',\n    successMetrics: ['100K+ property listings', '1M+ monthly visitors', '15% conversion rate'],\n    risks: ['Data accuracy', 'Market competition', 'Technology adoption'],\n    template: `Create a comprehensive real estate marketplace that revolutionizes property discovery and transactions.\n\nKey Requirements:\n- Advanced property search with AI-powered matching\n- Immersive virtual tours and 360-degree photography\n- Comprehensive property details with market analytics\n- Agent profiles with ratings and transaction history\n- Mortgage calculator and financing options\n- Neighborhood insights and demographic data\n- Mobile app for property viewing and notifications\n- Integration with MLS and real estate databases\n\nTechnical Specifications:\n- AI algorithms for property recommendation and matching\n- 3D virtual tour technology with WebGL/Three.js\n- Advanced mapping with Google Maps integration\n- Real-time property data synchronization\n- Image optimization and CDN for fast loading\n- Advanced search with filters and sorting options\n- Mobile-responsive design with native app features\n- Analytics dashboard for market trends and insights\n\nSuccess Criteria:\n- List 100,000+ active properties across major markets\n- Achieve 1M+ monthly unique visitors\n- Maintain 15% lead-to-sale conversion rate\n- Support virtual tours for 80% of listings`\n  },\n\n  {\n    id: 'property-investment-platform',\n    name: 'Property Investment Platform',\n    industry: 'Real Estate & PropTech',\n    description: 'Real estate investment platform with crowdfunding and portfolio management tools',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'Stripe', 'AWS', 'TensorFlow'],\n    features: ['Investment opportunities', 'Portfolio tracking', 'Due diligence', 'Returns calculation', 'Investor dashboard'],\n    businessGoals: ['Democratize real estate investing', 'Provide passive income opportunities', 'Reduce investment barriers'],\n    targetAudience: 'Individual investors, accredited investors, and real estate sponsors',\n    timeline: '12-18 months',\n    budget: '$500K - $1.5M',\n    successMetrics: ['$100M+ invested', '12% average returns', '95% investor satisfaction'],\n    risks: ['Regulatory compliance', 'Market volatility', 'Due diligence accuracy'],\n    template: `Build a comprehensive real estate investment platform that enables fractional ownership and passive investing.\n\nKey Requirements:\n- Curated investment opportunities with detailed analysis\n- Fractional ownership and crowdfunding capabilities\n- Comprehensive due diligence and property evaluation\n- Real-time portfolio tracking and performance analytics\n- Automated distribution of rental income and profits\n- Investor education and market insights\n- Regulatory compliance and investor accreditation\n- Mobile app for investment monitoring\n\nTechnical Specifications:\n- SEC-compliant investment processing and documentation\n- AI-powered property valuation and risk assessment\n- Automated distribution and tax reporting systems\n- Real-time portfolio performance tracking\n- Integration with property management systems\n- Advanced analytics for investment insights\n- Secure document storage and e-signature capabilities\n- Comprehensive investor dashboard and reporting\n\nSuccess Criteria:\n- Facilitate $100M+ in real estate investments\n- Achieve 12% average annual returns for investors\n- Maintain 95% investor satisfaction rating\n- Process 1,000+ investment transactions annually`\n  },\n\n  {\n    id: 'smart-building-management',\n    name: 'Smart Building Management System',\n    industry: 'Real Estate & PropTech',\n    description: 'IoT-enabled building management platform with energy optimization and predictive maintenance',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS IoT', 'TensorFlow', 'MQTT'],\n    features: ['IoT monitoring', 'Energy optimization', 'Predictive maintenance', 'Tenant portal', 'Analytics dashboard'],\n    businessGoals: ['Reduce operating costs', 'Improve tenant satisfaction', 'Optimize energy usage'],\n    targetAudience: 'Property managers, building owners, and commercial tenants',\n    timeline: '12-16 months',\n    budget: '$600K - $1.8M',\n    successMetrics: ['30% energy savings', '50% maintenance cost reduction', '95% tenant satisfaction'],\n    risks: ['IoT integration complexity', 'Data security', 'Hardware compatibility'],\n    template: `Develop a smart building management system that optimizes operations through IoT and AI technologies.\n\nKey Requirements:\n- Comprehensive IoT sensor integration for monitoring\n- AI-powered energy optimization and demand management\n- Predictive maintenance with equipment health monitoring\n- Tenant portal for service requests and building information\n- Real-time analytics dashboard for building performance\n- Integration with existing building automation systems\n- Mobile app for facility managers and maintenance teams\n- Advanced reporting and compliance tools\n\nTechnical Specifications:\n- IoT device management with MQTT protocol\n- Machine learning models for predictive analytics\n- Real-time data processing and alerting systems\n- Integration with HVAC, lighting, and security systems\n- Cloud-based architecture with edge computing\n- Advanced visualization and dashboard tools\n- API integrations with third-party building systems\n- Comprehensive security and access control\n\nSuccess Criteria:\n- Achieve 30% reduction in energy consumption\n- Reduce maintenance costs by 50% through predictive analytics\n- Maintain 95% tenant satisfaction rating\n- Monitor 10,000+ IoT devices across multiple buildings`\n  },\n\n  {\n    id: 'rental-management-platform',\n    name: 'Rental Property Management',\n    industry: 'Real Estate & PropTech',\n    description: 'Comprehensive rental property management platform with tenant screening and maintenance tracking',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'intermediate',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'Stripe', 'Twilio', 'AWS'],\n    features: ['Property listings', 'Tenant screening', 'Rent collection', 'Maintenance requests', 'Financial reporting'],\n    businessGoals: ['Streamline property management', 'Improve tenant relations', 'Maximize rental income'],\n    targetAudience: 'Property managers, landlords, and tenants',\n    timeline: '8-12 months',\n    budget: '$250K - $750K',\n    successMetrics: ['95% rent collection', '24-hour maintenance response', '90% tenant retention'],\n    risks: ['Tenant screening accuracy', 'Payment processing issues', 'Maintenance coordination'],\n    template: `Create a comprehensive rental property management platform that automates operations and improves tenant experiences.\n\nKey Requirements:\n- Online property listings with virtual tours\n- Automated tenant screening with credit and background checks\n- Digital lease signing and document management\n- Automated rent collection with late fee processing\n- Maintenance request system with vendor coordination\n- Financial reporting and expense tracking\n- Tenant portal for payments and communication\n- Mobile app for property managers and tenants\n\nTechnical Specifications:\n- Integration with credit reporting agencies\n- Automated payment processing with ACH and credit cards\n- Document management with e-signature capabilities\n- Real-time communication tools for tenants and managers\n- Advanced reporting and analytics dashboard\n- Integration with accounting systems (QuickBooks, etc.)\n- Mobile-responsive design with native app features\n- Comprehensive security and data protection\n\nSuccess Criteria:\n- Achieve 95% on-time rent collection rate\n- Respond to maintenance requests within 24 hours\n- Maintain 90% tenant retention rate\n- Manage 10,000+ rental units across multiple properties`\n  },\n\n  {\n    id: 'construction-project-management',\n    name: 'Construction Project Management',\n    industry: 'Real Estate & PropTech',\n    description: 'Digital construction management platform with project tracking and collaboration tools',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'AutoCAD API', 'Drone API'],\n    features: ['Project planning', 'Progress tracking', 'Document management', 'Team collaboration', 'Budget monitoring'],\n    businessGoals: ['Improve project efficiency', 'Reduce construction delays', 'Enhance collaboration'],\n    targetAudience: 'Construction companies, project managers, and contractors',\n    timeline: '10-14 months',\n    budget: '$400K - $1.2M',\n    successMetrics: ['20% faster completion', '15% cost savings', '95% on-time delivery'],\n    risks: ['Project complexity', 'Team adoption', 'Integration challenges'],\n    template: `Build a comprehensive construction project management platform that streamlines workflows and improves collaboration.\n\nKey Requirements:\n- Comprehensive project planning with Gantt charts and timelines\n- Real-time progress tracking with photo documentation\n- Document management with version control and approvals\n- Team collaboration tools with role-based access\n- Budget monitoring and cost tracking with alerts\n- Integration with CAD software and building plans\n- Mobile app for field workers and site managers\n- Reporting and analytics for project insights\n\nTechnical Specifications:\n- Integration with AutoCAD and BIM software\n- Real-time collaboration with WebSocket connections\n- Document versioning and approval workflows\n- Mobile-first design for field use\n- Advanced project analytics and reporting\n- Integration with accounting and ERP systems\n- Drone integration for aerial progress monitoring\n- Comprehensive security and access control\n\nSuccess Criteria:\n- Achieve 20% faster project completion times\n- Reduce project costs by 15% through better planning\n- Maintain 95% on-time project delivery rate\n- Manage 500+ concurrent construction projects`\n  },\n\n  // Manufacturing & IoT (6 templates)\n  {\n    id: 'smart-factory-platform',\n    name: 'Smart Factory Management Platform',\n    industry: 'Manufacturing & IoT',\n    description: 'IoT-enabled smart factory platform with predictive maintenance and production optimization',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS IoT', 'TensorFlow', 'InfluxDB'],\n    features: ['Production monitoring', 'Predictive maintenance', 'Quality control', 'Inventory management', 'Analytics dashboard'],\n    businessGoals: ['Increase production efficiency', 'Reduce downtime', 'Improve product quality'],\n    targetAudience: 'Manufacturing companies, plant managers, and operations teams',\n    timeline: '12-18 months',\n    budget: '$600K - $2M',\n    successMetrics: ['25% efficiency increase', '40% downtime reduction', '99.5% quality rate'],\n    risks: ['IoT integration complexity', 'Legacy system compatibility', 'Data security'],\n    template: `Build a comprehensive smart factory platform that leverages IoT and AI to optimize manufacturing operations.\n\nKey Requirements:\n- Real-time production monitoring with IoT sensors\n- AI-powered predictive maintenance for equipment\n- Automated quality control with computer vision\n- Intelligent inventory management and supply chain optimization\n- Energy consumption monitoring and optimization\n- Worker safety monitoring and alert systems\n- Integration with existing ERP and MES systems\n- Advanced analytics dashboard for operational insights\n\nTechnical Specifications:\n- IoT device management with industrial protocols (OPC-UA, Modbus)\n- Machine learning models for predictive analytics\n- Time-series database for sensor data storage\n- Real-time data processing and alerting systems\n- Computer vision for quality inspection\n- Edge computing for low-latency processing\n- Comprehensive security for industrial networks\n- API integrations with manufacturing systems\n\nSuccess Criteria:\n- Achieve 25% increase in production efficiency\n- Reduce unplanned downtime by 40%\n- Maintain 99.5% product quality rate\n- Monitor 10,000+ IoT devices across production lines`\n  },\n\n  {\n    id: 'supply-chain-optimization',\n    name: 'Supply Chain Optimization Platform',\n    industry: 'Manufacturing & IoT',\n    description: 'AI-powered supply chain platform with demand forecasting and logistics optimization',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Python', 'TensorFlow', 'PostgreSQL', 'AWS', 'Blockchain'],\n    features: ['Demand forecasting', 'Inventory optimization', 'Supplier management', 'Logistics tracking', 'Risk assessment'],\n    businessGoals: ['Optimize inventory levels', 'Reduce supply chain costs', 'Improve delivery times'],\n    targetAudience: 'Supply chain managers, procurement teams, and logistics coordinators',\n    timeline: '10-15 months',\n    budget: '$500K - $1.5M',\n    successMetrics: ['30% inventory reduction', '20% cost savings', '95% on-time delivery'],\n    risks: ['Demand volatility', 'Supplier reliability', 'Integration complexity'],\n    template: `Create an AI-powered supply chain optimization platform that enhances efficiency and reduces costs.\n\nKey Requirements:\n- Advanced demand forecasting using machine learning\n- Intelligent inventory optimization with safety stock calculations\n- Comprehensive supplier management and performance tracking\n- Real-time logistics tracking and route optimization\n- Risk assessment and mitigation strategies\n- Blockchain integration for supply chain transparency\n- Integration with ERP and procurement systems\n- Advanced analytics and reporting dashboard\n\nTechnical Specifications:\n- Machine learning models for demand prediction\n- Optimization algorithms for inventory and logistics\n- Real-time tracking with GPS and IoT integration\n- Blockchain for supply chain traceability\n- API integrations with suppliers and logistics providers\n- Advanced analytics and visualization tools\n- Mobile app for field operations and tracking\n- Comprehensive security and data protection\n\nSuccess Criteria:\n- Reduce inventory holding costs by 30%\n- Achieve 20% overall supply chain cost savings\n- Maintain 95% on-time delivery performance\n- Process 100,000+ supply chain transactions monthly`\n  },\n\n  {\n    id: 'industrial-iot-platform',\n    name: 'Industrial IoT Management Platform',\n    industry: 'Manufacturing & IoT',\n    description: 'Comprehensive IIoT platform for device management, data analytics, and remote monitoring',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'InfluxDB', 'AWS IoT', 'TensorFlow', 'MQTT'],\n    features: ['Device management', 'Data visualization', 'Remote monitoring', 'Predictive analytics', 'Alert systems'],\n    businessGoals: ['Enable digital transformation', 'Improve operational visibility', 'Reduce maintenance costs'],\n    targetAudience: 'Industrial companies, IoT engineers, and operations managers',\n    timeline: '12-16 months',\n    budget: '$500K - $1.5M',\n    successMetrics: ['50K+ connected devices', '99.9% uptime', '60% maintenance savings'],\n    risks: ['Device compatibility', 'Network reliability', 'Data security'],\n    template: `Develop a comprehensive Industrial IoT platform that connects, monitors, and optimizes industrial operations.\n\nKey Requirements:\n- Scalable device management for thousands of IoT sensors\n- Real-time data visualization with customizable dashboards\n- Remote monitoring and control capabilities\n- Predictive analytics for equipment health and performance\n- Automated alert and notification systems\n- Edge computing for low-latency processing\n- Integration with existing industrial systems\n- Comprehensive security and access control\n\nTechnical Specifications:\n- Support for multiple IoT protocols (MQTT, CoAP, OPC-UA)\n- Time-series database for efficient data storage\n- Real-time data streaming and processing\n- Machine learning models for predictive maintenance\n- Edge computing deployment for critical applications\n- Advanced visualization with 3D plant models\n- API integrations for third-party systems\n- Enterprise-grade security and compliance\n\nSuccess Criteria:\n- Connect and manage 50,000+ IoT devices\n- Achieve 99.9% platform uptime and reliability\n- Reduce maintenance costs by 60% through predictive analytics\n- Process 1M+ sensor readings per minute`\n  },\n\n  {\n    id: 'quality-management-system',\n    name: 'Digital Quality Management System',\n    industry: 'Manufacturing & IoT',\n    description: 'Comprehensive QMS with automated inspections, compliance tracking, and corrective actions',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'intermediate',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'Computer Vision', 'OCR'],\n    features: ['Quality inspections', 'Compliance tracking', 'Corrective actions', 'Document control', 'Audit management'],\n    businessGoals: ['Ensure product quality', 'Maintain compliance', 'Reduce defects'],\n    targetAudience: 'Quality managers, inspectors, and compliance teams',\n    timeline: '8-12 months',\n    budget: '$300K - $900K',\n    successMetrics: ['99% compliance rate', '50% defect reduction', '90% audit success'],\n    risks: ['Regulatory changes', 'Process complexity', 'User adoption'],\n    template: `Build a comprehensive digital quality management system that ensures compliance and continuous improvement.\n\nKey Requirements:\n- Digital quality inspection workflows with mobile support\n- Automated compliance tracking and reporting\n- Corrective and preventive action (CAPA) management\n- Document control with version management\n- Audit management and preparation tools\n- Statistical process control (SPC) with real-time monitoring\n- Integration with manufacturing execution systems\n- Training management and competency tracking\n\nTechnical Specifications:\n- Computer vision for automated quality inspections\n- OCR technology for document digitization\n- Real-time statistical analysis and control charts\n- Workflow automation for quality processes\n- Integration with ERP and manufacturing systems\n- Mobile app for field inspections and data collection\n- Advanced reporting and analytics dashboard\n- Compliance templates for industry standards (ISO, FDA)\n\nSuccess Criteria:\n- Achieve 99% regulatory compliance rate\n- Reduce product defects by 50%\n- Pass 90% of external audits on first attempt\n- Process 10,000+ quality inspections monthly`\n  },\n\n  {\n    id: 'asset-tracking-system',\n    name: 'Industrial Asset Tracking System',\n    industry: 'Manufacturing & IoT',\n    description: 'RFID/IoT-enabled asset tracking platform with maintenance scheduling and lifecycle management',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'intermediate',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'RFID', 'GPS'],\n    features: ['Asset tracking', 'Maintenance scheduling', 'Lifecycle management', 'Location monitoring', 'Reporting'],\n    businessGoals: ['Improve asset utilization', 'Reduce asset loss', 'Optimize maintenance'],\n    targetAudience: 'Asset managers, maintenance teams, and operations staff',\n    timeline: '6-10 months',\n    budget: '$200K - $600K',\n    successMetrics: ['99% asset visibility', '30% utilization increase', '25% maintenance savings'],\n    risks: ['RFID implementation', 'Data accuracy', 'System integration'],\n    template: `Create a comprehensive asset tracking system that provides real-time visibility and optimizes asset management.\n\nKey Requirements:\n- Real-time asset tracking with RFID and GPS technology\n- Comprehensive asset database with specifications and history\n- Automated maintenance scheduling based on usage and time\n- Asset lifecycle management from procurement to disposal\n- Location monitoring and geofencing capabilities\n- Mobile app for asset scanning and updates\n- Integration with ERP and maintenance systems\n- Advanced reporting and analytics dashboard\n\nTechnical Specifications:\n- RFID reader integration for automated tracking\n- GPS tracking for mobile and outdoor assets\n- Barcode and QR code scanning capabilities\n- Real-time location updates and alerts\n- Integration with CMMS and ERP systems\n- Mobile app for field asset management\n- Advanced analytics for asset optimization\n- Comprehensive security and access control\n\nSuccess Criteria:\n- Achieve 99% asset visibility and tracking accuracy\n- Increase asset utilization by 30%\n- Reduce maintenance costs by 25%\n- Track 100,000+ assets across multiple facilities`\n  },\n\n  // Media & Entertainment (6 templates)\n  {\n    id: 'streaming-platform',\n    name: 'Video Streaming Platform',\n    industry: 'Media & Entertainment',\n    description: 'Scalable video streaming platform with content management and personalized recommendations',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'AWS', 'CDN', 'TensorFlow', 'FFmpeg'],\n    features: ['Video streaming', 'Content management', 'User profiles', 'Recommendations', 'Analytics'],\n    businessGoals: ['Deliver high-quality streaming', 'Increase user engagement', 'Monetize content'],\n    targetAudience: 'Content creators, viewers, and media companies',\n    timeline: '12-18 months',\n    budget: '$600K - $2M',\n    successMetrics: ['1M+ concurrent streams', '80% user retention', '4K streaming quality'],\n    risks: ['Bandwidth costs', 'Content licensing', 'Competition'],\n    template: `Build a scalable video streaming platform that delivers high-quality content with personalized experiences.\n\nKey Requirements:\n- Adaptive bitrate streaming for optimal quality\n- Comprehensive content management system\n- User profiles with viewing history and preferences\n- AI-powered content recommendations\n- Multi-device support (web, mobile, TV, gaming consoles)\n- Live streaming capabilities with real-time chat\n- Content protection and DRM integration\n- Advanced analytics and viewer insights\n\nTechnical Specifications:\n- CDN integration for global content delivery\n- Video transcoding and optimization pipeline\n- Machine learning for personalized recommendations\n- Real-time streaming with low latency\n- Scalable architecture supporting millions of users\n- Advanced video player with adaptive streaming\n- Content protection with digital rights management\n- Comprehensive analytics and reporting dashboard\n\nSuccess Criteria:\n- Support 1M+ concurrent video streams\n- Achieve 80% user retention after 30 days\n- Deliver 4K streaming quality with minimal buffering\n- Process 100TB+ of video content monthly`\n  },\n\n  {\n    id: 'music-streaming-app',\n    name: 'Music Streaming Application',\n    industry: 'Media & Entertainment',\n    description: 'AI-powered music streaming platform with social features and artist collaboration tools',\n    projectType: 'mobile-application',\n    platform: 'mobile',\n    complexity: 'advanced',\n    technologies: ['React Native', 'Node.js', 'PostgreSQL', 'AWS', 'TensorFlow', 'Spotify API'],\n    features: ['Music streaming', 'Playlist creation', 'Social sharing', 'Artist profiles', 'Discovery'],\n    businessGoals: ['Build music community', 'Support artists', 'Increase user engagement'],\n    targetAudience: 'Music lovers, artists, and content creators',\n    timeline: '10-15 months',\n    budget: '$400K - $1.2M',\n    successMetrics: ['10M+ songs catalog', '5M+ active users', '90% user satisfaction'],\n    risks: ['Music licensing', 'Artist acquisition', 'Platform competition'],\n    template: `Create an innovative music streaming platform that connects artists with fans and builds music communities.\n\nKey Requirements:\n- High-quality audio streaming with offline capabilities\n- AI-powered music discovery and recommendations\n- Social features with playlist sharing and collaboration\n- Artist profiles with direct fan engagement\n- Podcast and audio content support\n- Live streaming for concerts and events\n- Music creation tools and collaboration features\n- Advanced search and music discovery\n\nTechnical Specifications:\n- High-quality audio streaming with lossless options\n- Machine learning for music recommendation algorithms\n- Real-time social features and messaging\n- Integration with music distribution platforms\n- Cross-platform mobile development (iOS/Android)\n- Offline music storage and synchronization\n- Advanced audio processing and equalization\n- Comprehensive analytics for artists and labels\n\nSuccess Criteria:\n- Build catalog of 10M+ licensed songs\n- Achieve 5M+ monthly active users\n- Maintain 90% user satisfaction rating\n- Support 100,000+ independent artists`\n  },\n\n  {\n    id: 'content-creation-platform',\n    name: 'Content Creation Platform',\n    industry: 'Media & Entertainment',\n    description: 'All-in-one content creation platform with editing tools and collaboration features',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'WebGL', 'FFmpeg', 'AWS', 'WebRTC'],\n    features: ['Video editing', 'Audio editing', 'Collaboration tools', 'Asset library', 'Publishing'],\n    businessGoals: ['Democratize content creation', 'Enable collaboration', 'Streamline workflows'],\n    targetAudience: 'Content creators, video editors, and creative teams',\n    timeline: '12-16 months',\n    budget: '$500K - $1.5M',\n    successMetrics: ['1M+ projects created', '500K+ active creators', '4.8+ user rating'],\n    risks: ['Performance optimization', 'Browser compatibility', 'Feature complexity'],\n    template: `Build a comprehensive content creation platform that empowers creators with professional-grade tools.\n\nKey Requirements:\n- Browser-based video and audio editing with timeline interface\n- Real-time collaboration with multiple editors\n- Comprehensive asset library with stock media\n- AI-powered editing assistance and automation\n- Multi-format export and publishing options\n- Cloud storage with version control\n- Template library for quick content creation\n- Integration with social media platforms\n\nTechnical Specifications:\n- WebGL-based video rendering and effects\n- Real-time collaboration using WebRTC\n- Cloud-based media processing and storage\n- AI algorithms for automated editing suggestions\n- Progressive web app for offline editing\n- Advanced timeline interface with precision controls\n- Multi-format media support and conversion\n- Comprehensive project management and sharing\n\nSuccess Criteria:\n- Enable creation of 1M+ video projects\n- Onboard 500,000+ active content creators\n- Achieve 4.8+ average user rating\n- Process 10PB+ of media content annually`\n  },\n\n  {\n    id: 'podcast-platform',\n    name: 'Podcast Hosting Platform',\n    industry: 'Media & Entertainment',\n    description: 'Complete podcast hosting and distribution platform with analytics and monetization tools',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'intermediate',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'Stripe', 'RSS'],\n    features: ['Podcast hosting', 'Distribution', 'Analytics', 'Monetization', 'Audience engagement'],\n    businessGoals: ['Support podcast creators', 'Enable monetization', 'Grow podcast ecosystem'],\n    targetAudience: 'Podcast creators, listeners, and media companies',\n    timeline: '8-12 months',\n    budget: '$250K - $750K',\n    successMetrics: ['100K+ podcasts hosted', '10M+ downloads monthly', '95% uptime'],\n    risks: ['Content moderation', 'Bandwidth costs', 'Platform competition'],\n    template: `Create a comprehensive podcast platform that supports creators from recording to monetization.\n\nKey Requirements:\n- Easy podcast upload and hosting with unlimited storage\n- Automatic distribution to major podcast platforms\n- Comprehensive analytics with listener demographics\n- Monetization tools including ads and subscriptions\n- Audience engagement features with comments and ratings\n- Recording and editing tools for content creation\n- RSS feed management and customization\n- Mobile app for podcast management and listening\n\nTechnical Specifications:\n- Scalable audio hosting with global CDN\n- Automated distribution to Apple Podcasts, Spotify, etc.\n- Advanced analytics with real-time listener tracking\n- Payment processing for subscriptions and donations\n- Audio processing and optimization pipeline\n- RSS feed generation and management\n- Mobile-responsive design with native app features\n- Comprehensive creator dashboard and tools\n\nSuccess Criteria:\n- Host 100,000+ active podcasts\n- Deliver 10M+ podcast downloads monthly\n- Maintain 95% platform uptime\n- Generate $1M+ in creator revenue annually`\n  },\n\n  {\n    id: 'live-streaming-platform',\n    name: 'Live Streaming Platform',\n    industry: 'Media & Entertainment',\n    description: 'Interactive live streaming platform with real-time chat and monetization features',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'WebRTC', 'Socket.io', 'AWS', 'Stripe'],\n    features: ['Live streaming', 'Real-time chat', 'Virtual gifts', 'Subscriptions', 'Analytics'],\n    businessGoals: ['Enable live content creation', 'Build creator economy', 'Increase engagement'],\n    targetAudience: 'Live streamers, content creators, and viewers',\n    timeline: '10-14 months',\n    budget: '$400K - $1.2M',\n    successMetrics: ['100K+ concurrent viewers', '50K+ active streamers', '$5M+ creator earnings'],\n    risks: ['Streaming quality', 'Content moderation', 'Monetization balance'],\n    template: `Build an interactive live streaming platform that empowers creators and engages audiences in real-time.\n\nKey Requirements:\n- High-quality live video streaming with low latency\n- Real-time chat with moderation tools\n- Virtual gifts and tipping system for monetization\n- Subscription and membership features\n- Stream recording and highlight creation\n- Multi-platform streaming (simultaneous broadcast)\n- Creator dashboard with analytics and earnings\n- Mobile app for streaming and viewing\n\nTechnical Specifications:\n- WebRTC for low-latency live streaming\n- Real-time messaging with Socket.io\n- Scalable architecture supporting 100K+ concurrent viewers\n- Payment processing for virtual gifts and subscriptions\n- Content delivery network for global streaming\n- Advanced moderation tools and AI content filtering\n- Mobile streaming with camera and screen capture\n- Comprehensive analytics and revenue tracking\n\nSuccess Criteria:\n- Support 100,000+ concurrent viewers during peak times\n- Onboard 50,000+ active streamers\n- Generate $5M+ in creator earnings annually\n- Achieve sub-3 second streaming latency`\n  },\n\n  // Travel & Hospitality (5 templates)\n  {\n    id: 'hotel-booking-platform',\n    name: 'Hotel Booking Platform',\n    industry: 'Travel & Hospitality',\n    description: 'Comprehensive hotel booking platform with real-time availability and dynamic pricing',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'Stripe', 'Google Maps', 'Redis'],\n    features: ['Hotel search', 'Real-time booking', 'Dynamic pricing', 'Reviews', 'Mobile app'],\n    businessGoals: ['Increase bookings', 'Optimize pricing', 'Improve guest experience'],\n    targetAudience: 'Travelers, hotels, and hospitality businesses',\n    timeline: '10-15 months',\n    budget: '$400K - $1.2M',\n    successMetrics: ['1M+ bookings annually', '95% booking accuracy', '4.5+ user rating'],\n    risks: ['Inventory management', 'Payment processing', 'Competition'],\n    template: `Create a comprehensive hotel booking platform that connects travelers with accommodations worldwide.\n\nKey Requirements:\n- Advanced hotel search with filters and map integration\n- Real-time availability and instant booking confirmation\n- Dynamic pricing based on demand and seasonality\n- Comprehensive hotel profiles with photos and amenities\n- Guest review and rating system\n- Multi-currency and multi-language support\n- Mobile app for booking management and check-in\n- Integration with hotel management systems\n\nTechnical Specifications:\n- Real-time inventory management with Redis caching\n- Payment processing with multiple gateways\n- Advanced search with Elasticsearch\n- Map integration with Google Maps API\n- Mobile-responsive design with PWA capabilities\n- Integration with hotel PMS and channel managers\n- Advanced analytics and revenue optimization\n- Comprehensive security and fraud prevention\n\nSuccess Criteria:\n- Process 1M+ hotel bookings annually\n- Achieve 95% booking accuracy and confirmation\n- Maintain 4.5+ average user rating\n- Partner with 100,000+ hotels globally`\n  },\n\n  {\n    id: 'travel-planning-app',\n    name: 'AI Travel Planning App',\n    industry: 'Travel & Hospitality',\n    description: 'AI-powered travel planning app with personalized itineraries and local recommendations',\n    projectType: 'mobile-application',\n    platform: 'mobile',\n    complexity: 'advanced',\n    technologies: ['React Native', 'Python', 'TensorFlow', 'PostgreSQL', 'Google Maps', 'AWS'],\n    features: ['Itinerary planning', 'Local recommendations', 'Budget tracking', 'Social sharing', 'Offline maps'],\n    businessGoals: ['Personalize travel experiences', 'Increase user engagement', 'Monetize recommendations'],\n    targetAudience: 'Travelers, tourists, and travel enthusiasts',\n    timeline: '8-12 months',\n    budget: '$300K - $900K',\n    successMetrics: ['5M+ app downloads', '80% trip completion', '4.7+ app rating'],\n    risks: ['Data accuracy', 'Local content quality', 'User adoption'],\n    template: `Build an AI-powered travel planning app that creates personalized itineraries and enhances travel experiences.\n\nKey Requirements:\n- AI-powered itinerary generation based on preferences\n- Local recommendations for restaurants, attractions, and activities\n- Budget tracking and expense management\n- Social features for trip sharing and collaboration\n- Offline maps and navigation capabilities\n- Real-time travel updates and notifications\n- Integration with booking platforms and services\n- Photo sharing and travel journal features\n\nTechnical Specifications:\n- Machine learning for personalized recommendations\n- Integration with travel APIs (flights, hotels, activities)\n- Offline map storage and GPS navigation\n- Real-time data synchronization across devices\n- Social networking features with privacy controls\n- Advanced analytics for travel insights\n- Cross-platform mobile development (iOS/Android)\n- Comprehensive travel database and content management\n\nSuccess Criteria:\n- Achieve 5M+ app downloads within first year\n- 80% of planned trips completed using the app\n- Maintain 4.7+ average app store rating\n- Generate 1M+ personalized itineraries`\n  },\n\n  // Food & Restaurant (5 templates)\n  {\n    id: 'restaurant-management-system',\n    name: 'Restaurant Management System',\n    industry: 'Food & Restaurant',\n    description: 'Complete restaurant management platform with POS, inventory, and staff scheduling',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'intermediate',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'Stripe', 'Twilio', 'AWS'],\n    features: ['POS system', 'Inventory management', 'Staff scheduling', 'Customer management', 'Analytics'],\n    businessGoals: ['Streamline operations', 'Reduce costs', 'Improve customer service'],\n    targetAudience: 'Restaurant owners, managers, and staff',\n    timeline: '8-12 months',\n    budget: '$250K - $750K',\n    successMetrics: ['30% cost reduction', '95% order accuracy', '90% staff satisfaction'],\n    risks: ['Hardware integration', 'Staff training', 'System reliability'],\n    template: `Create a comprehensive restaurant management system that optimizes operations and enhances customer experiences.\n\nKey Requirements:\n- Point-of-sale system with order management\n- Real-time inventory tracking and automatic reordering\n- Staff scheduling and time tracking\n- Customer relationship management with loyalty programs\n- Table reservation and waitlist management\n- Kitchen display system for order coordination\n- Financial reporting and analytics dashboard\n- Mobile app for staff and customer interactions\n\nTechnical Specifications:\n- Integration with payment processors and hardware\n- Real-time order synchronization across devices\n- Inventory management with supplier integration\n- Staff scheduling with labor cost optimization\n- Customer data management with privacy compliance\n- Kitchen workflow optimization and timing\n- Advanced reporting and business intelligence\n- Mobile-responsive design with tablet support\n\nSuccess Criteria:\n- Reduce operational costs by 30%\n- Achieve 95% order accuracy and customer satisfaction\n- Improve staff satisfaction to 90%\n- Process 100,000+ orders monthly`\n  },\n\n  {\n    id: 'food-delivery-platform',\n    name: 'Food Delivery Platform',\n    industry: 'Food & Restaurant',\n    description: 'Multi-restaurant food delivery platform with real-time tracking and driver management',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'Stripe', 'Google Maps', 'Socket.io'],\n    features: ['Restaurant listings', 'Order management', 'Real-time tracking', 'Driver coordination', 'Payment processing'],\n    businessGoals: ['Connect restaurants with customers', 'Optimize delivery routes', 'Increase order volume'],\n    targetAudience: 'Restaurants, customers, and delivery drivers',\n    timeline: '10-15 months',\n    budget: '$400K - $1.2M',\n    successMetrics: ['30-minute delivery', '95% on-time delivery', '1M+ orders monthly'],\n    risks: ['Driver availability', 'Delivery logistics', 'Restaurant partnerships'],\n    template: `Build a comprehensive food delivery platform that connects restaurants, customers, and drivers efficiently.\n\nKey Requirements:\n- Restaurant onboarding and menu management\n- Customer app with search, ordering, and payment\n- Real-time order tracking with GPS integration\n- Driver app with route optimization and earnings tracking\n- Restaurant dashboard for order management\n- Dynamic pricing and delivery fee calculation\n- Customer support and dispute resolution\n- Analytics dashboard for all stakeholders\n\nTechnical Specifications:\n- Real-time order processing and status updates\n- GPS tracking and route optimization algorithms\n- Payment processing with split payments to restaurants\n- Push notifications for order status updates\n- Machine learning for delivery time estimation\n- Integration with restaurant POS systems\n- Advanced analytics and reporting dashboard\n- Scalable architecture for high order volume\n\nSuccess Criteria:\n- Achieve 30-minute average delivery time\n- Maintain 95% on-time delivery rate\n- Process 1M+ food orders monthly\n- Partner with 10,000+ restaurants`\n  },\n\n  {\n    id: 'recipe-sharing-platform',\n    name: 'Recipe Sharing Community',\n    industry: 'Food & Restaurant',\n    description: 'Social recipe sharing platform with meal planning and grocery integration',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'intermediate',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'Stripe', 'Computer Vision'],\n    features: ['Recipe sharing', 'Meal planning', 'Grocery lists', 'Social features', 'Nutrition tracking'],\n    businessGoals: ['Build cooking community', 'Monetize through partnerships', 'Promote healthy eating'],\n    targetAudience: 'Home cooks, food enthusiasts, and health-conscious individuals',\n    timeline: '6-10 months',\n    budget: '$200K - $600K',\n    successMetrics: ['1M+ registered users', '100K+ recipes shared', '4.6+ user rating'],\n    risks: ['Content quality', 'User engagement', 'Monetization strategy'],\n    template: `Create a vibrant recipe sharing community that helps people discover, plan, and cook delicious meals.\n\nKey Requirements:\n- Recipe creation and sharing with photo uploads\n- Advanced search and filtering by ingredients, diet, cuisine\n- Meal planning calendar with automated grocery lists\n- Social features with following, likes, and comments\n- Nutrition tracking and dietary restriction support\n- Integration with grocery delivery services\n- Video recipe tutorials and cooking tips\n- Personal recipe collections and favorites\n\nTechnical Specifications:\n- Image recognition for recipe ingredient detection\n- Advanced search with ingredient-based filtering\n- Social networking features with user profiles\n- Integration with grocery APIs for shopping lists\n- Nutrition calculation and dietary analysis\n- Video streaming for cooking tutorials\n- Mobile-responsive design with PWA capabilities\n- Content moderation and quality control systems\n\nSuccess Criteria:\n- Build community of 1M+ registered users\n- Facilitate sharing of 100,000+ unique recipes\n- Achieve 4.6+ average user rating\n- Generate 10M+ monthly recipe views`\n  },\n\n  // Logistics & Supply Chain (4 templates)\n  {\n    id: 'fleet-management-system',\n    name: 'Fleet Management System',\n    industry: 'Logistics & Supply Chain',\n    description: 'Comprehensive fleet management platform with GPS tracking and maintenance scheduling',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'GPS API', 'IoT'],\n    features: ['Vehicle tracking', 'Route optimization', 'Maintenance scheduling', 'Driver management', 'Fuel monitoring'],\n    businessGoals: ['Optimize fleet operations', 'Reduce fuel costs', 'Improve safety'],\n    targetAudience: 'Fleet managers, logistics companies, and transportation businesses',\n    timeline: '10-14 months',\n    budget: '$400K - $1.2M',\n    successMetrics: ['25% fuel savings', '30% route optimization', '99% vehicle uptime'],\n    risks: ['GPS accuracy', 'Driver adoption', 'Hardware integration'],\n    template: `Build a comprehensive fleet management system that optimizes vehicle operations and reduces costs.\n\nKey Requirements:\n- Real-time GPS tracking and vehicle monitoring\n- Route optimization and traffic-aware navigation\n- Preventive maintenance scheduling and alerts\n- Driver behavior monitoring and safety scoring\n- Fuel consumption tracking and cost analysis\n- Electronic logging device (ELD) compliance\n- Mobile app for drivers and field operations\n- Advanced analytics and reporting dashboard\n\nTechnical Specifications:\n- Integration with GPS and telematics devices\n- Real-time data processing and alerts\n- Machine learning for route optimization\n- IoT integration for vehicle diagnostics\n- Compliance reporting for transportation regulations\n- Mobile app with offline capabilities\n- Advanced analytics and predictive maintenance\n- Comprehensive security and data protection\n\nSuccess Criteria:\n- Achieve 25% reduction in fuel costs\n- Improve route efficiency by 30%\n- Maintain 99% vehicle uptime through predictive maintenance\n- Manage 10,000+ vehicles across multiple fleets`\n  },\n\n  {\n    id: 'warehouse-management-system',\n    name: 'Warehouse Management System',\n    industry: 'Logistics & Supply Chain',\n    description: 'Advanced WMS with automated inventory tracking and order fulfillment optimization',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'RFID', 'Barcode'],\n    features: ['Inventory tracking', 'Order fulfillment', 'Warehouse optimization', 'Staff management', 'Reporting'],\n    businessGoals: ['Optimize warehouse operations', 'Reduce fulfillment time', 'Improve accuracy'],\n    targetAudience: 'Warehouse managers, logistics coordinators, and fulfillment centers',\n    timeline: '8-12 months',\n    budget: '$300K - $900K',\n    successMetrics: ['99.5% inventory accuracy', '50% faster fulfillment', '30% space optimization'],\n    risks: ['System integration', 'Staff training', 'Inventory complexity'],\n    template: `Create an advanced warehouse management system that automates operations and optimizes fulfillment processes.\n\nKey Requirements:\n- Real-time inventory tracking with RFID and barcode scanning\n- Automated order picking and fulfillment workflows\n- Warehouse layout optimization and slotting\n- Staff task management and productivity tracking\n- Integration with ERP and e-commerce platforms\n- Returns processing and quality control\n- Mobile devices for warehouse operations\n- Advanced reporting and analytics dashboard\n\nTechnical Specifications:\n- Integration with barcode and RFID systems\n- Real-time inventory synchronization\n- Automated workflow orchestration\n- Mobile app for warehouse staff\n- Integration with shipping carriers and systems\n- Advanced analytics for warehouse optimization\n- API integrations with e-commerce platforms\n- Comprehensive security and access control\n\nSuccess Criteria:\n- Achieve 99.5% inventory accuracy\n- Reduce order fulfillment time by 50%\n- Optimize warehouse space utilization by 30%\n- Process 1M+ orders annually`\n  },\n\n  // Energy & Utilities (3 templates)\n  {\n    id: 'smart-grid-management',\n    name: 'Smart Grid Management Platform',\n    industry: 'Energy & Utilities',\n    description: 'IoT-enabled smart grid platform with energy optimization and demand response',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'InfluxDB', 'AWS IoT', 'TensorFlow', 'SCADA'],\n    features: ['Grid monitoring', 'Energy optimization', 'Demand response', 'Outage management', 'Analytics'],\n    businessGoals: ['Optimize energy distribution', 'Reduce outages', 'Enable renewable integration'],\n    targetAudience: 'Utility companies, grid operators, and energy managers',\n    timeline: '15-24 months',\n    budget: '$800K - $2.5M',\n    successMetrics: ['20% efficiency gain', '50% outage reduction', '99.9% grid reliability'],\n    risks: ['Critical infrastructure security', 'Regulatory compliance', 'System complexity'],\n    template: `Develop a smart grid management platform that optimizes energy distribution and integrates renewable sources.\n\nKey Requirements:\n- Real-time grid monitoring with IoT sensors and smart meters\n- AI-powered energy demand forecasting and optimization\n- Automated demand response and load balancing\n- Outage detection and restoration management\n- Renewable energy integration and storage management\n- Customer energy usage analytics and billing\n- Cybersecurity and critical infrastructure protection\n- Regulatory compliance and reporting tools\n\nTechnical Specifications:\n- Integration with SCADA and grid control systems\n- Time-series database for energy data storage\n- Machine learning for demand prediction and optimization\n- Real-time data processing and alerting\n- Cybersecurity frameworks for critical infrastructure\n- Advanced visualization and control dashboards\n- API integrations with energy markets and systems\n- Comprehensive backup and disaster recovery\n\nSuccess Criteria:\n- Achieve 20% improvement in grid efficiency\n- Reduce power outages by 50%\n- Maintain 99.9% grid reliability\n- Integrate 50% renewable energy sources`\n  },\n\n  {\n    id: 'renewable-energy-platform',\n    name: 'Renewable Energy Management',\n    industry: 'Energy & Utilities',\n    description: 'Comprehensive platform for managing solar, wind, and other renewable energy assets',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Python', 'InfluxDB', 'AWS', 'TensorFlow', 'Weather API'],\n    features: ['Asset monitoring', 'Performance optimization', 'Predictive maintenance', 'Energy trading', 'Analytics'],\n    businessGoals: ['Maximize energy production', 'Reduce maintenance costs', 'Optimize trading'],\n    targetAudience: 'Renewable energy companies, asset managers, and energy traders',\n    timeline: '12-18 months',\n    budget: '$500K - $1.5M',\n    successMetrics: ['95% asset uptime', '15% production increase', '30% maintenance savings'],\n    risks: ['Weather dependency', 'Equipment reliability', 'Market volatility'],\n    template: `Build a comprehensive renewable energy management platform that maximizes production and optimizes operations.\n\nKey Requirements:\n- Real-time monitoring of solar, wind, and other renewable assets\n- Weather-based production forecasting and optimization\n- Predictive maintenance for renewable energy equipment\n- Energy trading and market participation tools\n- Performance analytics and benchmarking\n- Integration with grid systems and energy markets\n- Mobile app for field technicians and asset managers\n- Environmental impact tracking and reporting\n\nTechnical Specifications:\n- Integration with renewable energy equipment and inverters\n- Weather data integration for production forecasting\n- Machine learning for performance optimization\n- Time-series database for energy production data\n- Trading algorithms for energy market participation\n- Advanced analytics and visualization tools\n- Mobile app for remote monitoring and maintenance\n- Comprehensive reporting and compliance tools\n\nSuccess Criteria:\n- Maintain 95% renewable asset uptime\n- Increase energy production by 15% through optimization\n- Reduce maintenance costs by 30%\n- Manage 1GW+ of renewable energy capacity`\n  },\n\n  // Agriculture & AgTech (3 templates)\n  {\n    id: 'precision-farming-platform',\n    name: 'Precision Farming Platform',\n    industry: 'Agriculture & AgTech',\n    description: 'IoT-enabled precision agriculture platform with crop monitoring and yield optimization',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Python', 'InfluxDB', 'AWS IoT', 'TensorFlow', 'Drone API'],\n    features: ['Crop monitoring', 'Soil analysis', 'Weather integration', 'Yield prediction', 'Equipment tracking'],\n    businessGoals: ['Increase crop yields', 'Reduce resource usage', 'Optimize farming operations'],\n    targetAudience: 'Farmers, agricultural consultants, and agribusiness companies',\n    timeline: '12-16 months',\n    budget: '$400K - $1.2M',\n    successMetrics: ['20% yield increase', '30% water savings', '25% cost reduction'],\n    risks: ['Weather dependency', 'Technology adoption', 'Data accuracy'],\n    template: `Create a precision farming platform that leverages IoT and AI to optimize agricultural operations and increase yields.\n\nKey Requirements:\n- IoT sensor networks for soil moisture, temperature, and nutrient monitoring\n- Drone integration for aerial crop monitoring and analysis\n- Weather data integration and microclimate monitoring\n- AI-powered yield prediction and crop health analysis\n- Irrigation and fertilizer optimization recommendations\n- Equipment tracking and maintenance scheduling\n- Mobile app for field operations and data collection\n- Integration with farm management systems\n\nTechnical Specifications:\n- IoT device management with agricultural sensors\n- Computer vision for crop health analysis from drone imagery\n- Machine learning for yield prediction and optimization\n- Weather API integration for localized forecasting\n- Time-series database for agricultural data storage\n- Mobile app with offline capabilities for field use\n- Advanced analytics and reporting dashboard\n- Integration with agricultural equipment and systems\n\nSuccess Criteria:\n- Achieve 20% increase in crop yields\n- Reduce water usage by 30% through precision irrigation\n- Lower farming costs by 25%\n- Monitor 100,000+ acres of farmland`\n  },\n\n  {\n    id: 'livestock-management-system',\n    name: 'Livestock Management System',\n    industry: 'Agriculture & AgTech',\n    description: 'Comprehensive livestock tracking and health monitoring platform with RFID and IoT sensors',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'intermediate',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'RFID', 'IoT'],\n    features: ['Animal tracking', 'Health monitoring', 'Breeding management', 'Feed optimization', 'Veterinary records'],\n    businessGoals: ['Improve animal health', 'Optimize breeding programs', 'Reduce veterinary costs'],\n    targetAudience: 'Livestock farmers, ranchers, and veterinarians',\n    timeline: '8-12 months',\n    budget: '$250K - $750K',\n    successMetrics: ['95% animal health tracking', '20% breeding efficiency', '30% vet cost reduction'],\n    risks: ['Animal welfare concerns', 'Technology durability', 'Data privacy'],\n    template: `Build a comprehensive livestock management system that monitors animal health and optimizes farm operations.\n\nKey Requirements:\n- RFID tagging and tracking for individual animal identification\n- Health monitoring with wearable sensors and alerts\n- Breeding program management with genetic tracking\n- Feed optimization and nutrition management\n- Veterinary records and treatment history\n- Milk production tracking for dairy operations\n- Mobile app for field operations and animal care\n- Integration with veterinary and feed supplier systems\n\nTechnical Specifications:\n- RFID reader integration for animal identification\n- IoT sensors for health and activity monitoring\n- Real-time alerts for health issues and breeding cycles\n- Genetic database for breeding optimization\n- Mobile app with barcode scanning capabilities\n- Integration with veterinary management systems\n- Advanced analytics for herd performance\n- Comprehensive reporting and compliance tools\n\nSuccess Criteria:\n- Track health status of 95% of livestock\n- Improve breeding efficiency by 20%\n- Reduce veterinary costs by 30%\n- Manage 50,000+ head of livestock`\n  },\n\n  // Sports & Fitness (3 templates)\n  {\n    id: 'sports-analytics-platform',\n    name: 'Sports Analytics Platform',\n    industry: 'Sports & Fitness',\n    description: 'Advanced sports analytics platform with player performance tracking and game analysis',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Python', 'TensorFlow', 'PostgreSQL', 'AWS', 'Computer Vision'],\n    features: ['Performance tracking', 'Game analysis', 'Player statistics', 'Video analysis', 'Predictive modeling'],\n    businessGoals: ['Improve team performance', 'Optimize player development', 'Gain competitive advantage'],\n    targetAudience: 'Sports teams, coaches, and performance analysts',\n    timeline: '10-15 months',\n    budget: '$400K - $1.2M',\n    successMetrics: ['15% performance improvement', '90% prediction accuracy', '100% coach adoption'],\n    risks: ['Data accuracy', 'Technology complexity', 'Coach acceptance'],\n    template: `Develop an advanced sports analytics platform that provides deep insights into player and team performance.\n\nKey Requirements:\n- Real-time player performance tracking with wearable sensors\n- Video analysis with computer vision for game breakdown\n- Advanced statistics and performance metrics\n- Predictive modeling for injury prevention and performance\n- Game strategy analysis and opponent scouting\n- Player development tracking and recommendations\n- Mobile app for coaches and players\n- Integration with existing sports management systems\n\nTechnical Specifications:\n- Computer vision for automated video analysis\n- Machine learning for performance prediction and optimization\n- Real-time data processing from wearable devices\n- Advanced statistical analysis and visualization\n- Video streaming and annotation tools\n- Mobile app with real-time performance monitoring\n- Integration with sports equipment and tracking systems\n- Comprehensive reporting and dashboard tools\n\nSuccess Criteria:\n- Achieve 15% improvement in team performance metrics\n- Maintain 90% accuracy in performance predictions\n- Achieve 100% adoption by coaching staff\n- Analyze 1,000+ hours of game footage monthly`\n  },\n\n  {\n    id: 'fitness-coaching-app',\n    name: 'AI Fitness Coaching App',\n    industry: 'Sports & Fitness',\n    description: 'AI-powered personal fitness coaching app with workout generation and progress tracking',\n    projectType: 'mobile-application',\n    platform: 'mobile',\n    complexity: 'advanced',\n    technologies: ['React Native', 'Python', 'TensorFlow', 'PostgreSQL', 'AWS', 'HealthKit'],\n    features: ['Workout generation', 'Form analysis', 'Progress tracking', 'Nutrition guidance', 'Social features'],\n    businessGoals: ['Personalize fitness experiences', 'Improve user engagement', 'Reduce trainer costs'],\n    targetAudience: 'Fitness enthusiasts, personal trainers, and gym members',\n    timeline: '8-12 months',\n    budget: '$300K - $900K',\n    successMetrics: ['1M+ app downloads', '80% user retention', '90% goal achievement'],\n    risks: ['AI accuracy', 'User motivation', 'Competition'],\n    template: `Create an AI-powered fitness coaching app that provides personalized workouts and real-time form feedback.\n\nKey Requirements:\n- AI-generated personalized workout plans based on goals and fitness level\n- Computer vision for exercise form analysis and correction\n- Progress tracking with detailed analytics and insights\n- Nutrition guidance and meal planning integration\n- Social features with challenges and community support\n- Integration with wearable devices and health apps\n- Video exercise library with professional demonstrations\n- Personal trainer marketplace and virtual coaching\n\nTechnical Specifications:\n- Machine learning for workout personalization\n- Computer vision for real-time form analysis\n- Integration with HealthKit (iOS) and Google Fit (Android)\n- Real-time video processing and feedback\n- Social networking features with privacy controls\n- Advanced analytics for fitness progress tracking\n- Cross-platform mobile development (iOS/Android)\n- Comprehensive exercise database and content management\n\nSuccess Criteria:\n- Achieve 1M+ app downloads within first year\n- Maintain 80% user retention after 30 days\n- Help 90% of users achieve their fitness goals\n- Process 10M+ workout sessions annually`\n  },\n\n  // Gaming & Interactive Media (3 templates)\n  {\n    id: 'esports-tournament-platform',\n    name: 'Esports Tournament Platform',\n    industry: 'Gaming & Interactive Media',\n    description: 'Comprehensive esports tournament platform with live streaming and prize management',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'WebRTC', 'Stripe', 'Socket.io'],\n    features: ['Tournament management', 'Live streaming', 'Player registration', 'Prize distribution', 'Analytics'],\n    businessGoals: ['Grow esports ecosystem', 'Monetize tournaments', 'Engage gaming community'],\n    targetAudience: 'Esports players, tournament organizers, and gaming enthusiasts',\n    timeline: '10-14 months',\n    budget: '$400K - $1.2M',\n    successMetrics: ['1000+ tournaments hosted', '100K+ registered players', '$1M+ prize pool'],\n    risks: ['Technical complexity', 'Player acquisition', 'Monetization challenges'],\n    template: `Build a comprehensive esports tournament platform that connects players, organizers, and audiences globally.\n\nKey Requirements:\n- Tournament creation and management tools for organizers\n- Player registration and team formation systems\n- Live streaming integration with chat and commentary\n- Automated bracket generation and match scheduling\n- Prize pool management and distribution\n- Anti-cheat integration and fair play monitoring\n- Mobile app for players and spectators\n- Analytics dashboard for tournament insights\n\nTechnical Specifications:\n- Real-time tournament bracket updates\n- Live streaming with low-latency video delivery\n- Payment processing for entry fees and prize distribution\n- Integration with popular gaming platforms and APIs\n- Real-time chat and social features\n- Advanced analytics for player and tournament performance\n- Mobile-responsive design with native app features\n- Comprehensive security and anti-fraud measures\n\nSuccess Criteria:\n- Host 1,000+ tournaments annually\n- Register 100,000+ active players\n- Distribute $1M+ in tournament prizes\n- Stream 10,000+ hours of live esports content`\n  },\n\n  // Non-profit & Social Impact (2 templates)\n  {\n    id: 'volunteer-management-platform',\n    name: 'Volunteer Management Platform',\n    industry: 'Non-profit & Social Impact',\n    description: 'Comprehensive volunteer management platform with scheduling and impact tracking',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'intermediate',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'Stripe', 'Twilio', 'AWS'],\n    features: ['Volunteer registration', 'Event scheduling', 'Impact tracking', 'Communication tools', 'Reporting'],\n    businessGoals: ['Increase volunteer engagement', 'Streamline operations', 'Measure social impact'],\n    targetAudience: 'Non-profit organizations, volunteers, and community groups',\n    timeline: '6-10 months',\n    budget: '$200K - $600K',\n    successMetrics: ['10K+ active volunteers', '90% event attendance', '50% admin time savings'],\n    risks: ['Volunteer retention', 'Technology adoption', 'Funding constraints'],\n    template: `Create a volunteer management platform that helps non-profits organize, engage, and track volunteer activities.\n\nKey Requirements:\n- Volunteer registration and profile management\n- Event creation and scheduling with automated notifications\n- Skill-based volunteer matching for optimal placement\n- Impact tracking and measurement tools\n- Communication tools for volunteer coordination\n- Training module management and certification tracking\n- Mobile app for volunteers and coordinators\n- Integration with fundraising and CRM systems\n\nTechnical Specifications:\n- User management with role-based access control\n- Automated email and SMS notifications\n- Calendar integration for event scheduling\n- Mobile-responsive design with PWA capabilities\n- Integration with popular CRM and fundraising platforms\n- Advanced reporting and analytics dashboard\n- Volunteer hour tracking and verification\n- Comprehensive security and data protection\n\nSuccess Criteria:\n- Engage 10,000+ active volunteers\n- Achieve 90% volunteer event attendance rate\n- Reduce administrative time by 50%\n- Track 1M+ volunteer hours annually`\n  },\n\n  {\n    id: 'donation-crowdfunding-platform',\n    name: 'Donation & Crowdfunding Platform',\n    industry: 'Non-profit & Social Impact',\n    description: 'Social impact crowdfunding platform with transparent donation tracking and impact reporting',\n    projectType: 'web-application',\n    platform: 'web',\n    complexity: 'advanced',\n    technologies: ['React', 'Node.js', 'PostgreSQL', 'Stripe', 'Blockchain', 'AWS'],\n    features: ['Campaign creation', 'Donation processing', 'Impact tracking', 'Social sharing', 'Transparency tools'],\n    businessGoals: ['Enable social impact funding', 'Increase donation transparency', 'Build donor trust'],\n    targetAudience: 'Non-profits, social entrepreneurs, and donors',\n    timeline: '8-12 months',\n    budget: '$300K - $900K',\n    successMetrics: ['$10M+ raised', '95% donor satisfaction', '1000+ successful campaigns'],\n    risks: ['Fraud prevention', 'Regulatory compliance', 'Platform trust'],\n    template: `Build a transparent crowdfunding platform that connects social impact projects with donors worldwide.\n\nKey Requirements:\n- Campaign creation tools with multimedia content support\n- Secure donation processing with multiple payment methods\n- Blockchain-based transparency for donation tracking\n- Impact reporting and progress updates\n- Social sharing and viral campaign features\n- Donor management and engagement tools\n- Mobile app for campaign management and donations\n- Integration with social media and marketing platforms\n\nTechnical Specifications:\n- Secure payment processing with fraud prevention\n- Blockchain integration for donation transparency\n- Social media integration for campaign promotion\n- Advanced analytics for campaign performance\n- Mobile-responsive design with native app features\n- Integration with email marketing and CRM systems\n- Comprehensive reporting and impact measurement\n- Multi-currency and international payment support\n\nSuccess Criteria:\n- Facilitate $10M+ in donations annually\n- Achieve 95% donor satisfaction rating\n- Launch 1,000+ successful fundraising campaigns\n- Maintain 99.9% payment processing reliability`\n  }\n];\n"], "names": [], "mappings": ";;;AAqBO,MAAM,6BAAiD;IAC5D,uBAAuB;IACvB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAa;YAAc;YAAS;SAAS;QAChF,UAAU;YAAC;YAAqB;YAAwB;YAAkB;SAAuB;QACjG,eAAe;YAAC;YAA2B;YAAkB;SAA+B;QAC5F,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAqB;YAAgB;SAA6B;QACnF,OAAO;YAAC;YAAsB;YAAqB;SAAoB;QACvE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;sCAsBuB,CAAC;IACrC;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAgB;YAAW;YAAc;YAAO;YAAc;SAAQ;QACrF,UAAU;YAAC;YAAsB;YAAmB;YAAgB;SAAsB;QAC1F,eAAe;YAAC;YAAgC;YAA4B;SAA0B;QACtG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAyB;YAAsB;SAAwB;QACxF,OAAO;YAAC;YAAyB;YAA4B;SAA4B;QACzF,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;wCAsByB,CAAC;IACvC;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAM;YAAc;YAAS;YAAc;SAAY;QAC/E,UAAU;YAAC;YAAkB;YAAkB;YAAqB;SAAqB;QACzF,eAAe;YAAC;YAA+B;YAAgC;SAA0B;QACzG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAuB;YAAiB;SAA6B;QACtF,OAAO;YAAC;YAA0B;YAAoB;SAAsB;QAC5E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;mCAsBoB,CAAC;IAClC;IAEA,uCAAuC;IACvC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAU;YAAc;YAAO;SAAY;QAC9E,UAAU;YAAC;YAAuB;YAAmB;YAA2B;SAAyB;QACzG,eAAe;YAAC;YAA0B;YAA2B;SAA0B;QAC/F,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA6B;YAAgC;SAAwB;QACtG,OAAO;YAAC;YAAyB;YAA0B;SAAuB;QAClF,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;6CAsB8B,CAAC;IAC5C;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAU;YAAW;YAAS;YAAS;SAAS;QAC/D,UAAU;YAAC;YAA0B;YAAuB;YAAe;SAAqB;QAChG,eAAe;YAAC;YAA8B;YAAgC;SAA4B;QAC1G,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAyB;YAA2B;SAA4B;QACjG,OAAO;YAAC;YAAqB;YAA0B;SAAwB;QAC/E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;iDAsBkC,CAAC;IAChD;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAW;YAAe;YAAc;YAAS;SAAa;QAC7E,UAAU;YAAC;YAAgC;YAAwB;YAAyB;SAAuB;QACnH,eAAe;YAAC;YAAgB;YAA8B;SAAiC;QAC/F,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAiC;YAA0B;SAAwB;QACpG,OAAO;YAAC;YAAqC;YAA+B;SAAqB;QACjG,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;uCAsBwB,CAAC;IACtC;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAS;YAAU;SAAgB;QACpF,UAAU;YAAC;YAAsB;YAAoB;YAAkB;SAAqB;QAC5F,eAAe;YAAC;YAAyB;YAAwB;SAA2B;QAC5F,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA8B;YAA6B;SAAqB;QACjG,OAAO;YAAC;YAAiC;YAAkB;SAA4B;QACvF,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;6CAsB8B,CAAC;IAC5C;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAgB;YAAW;YAAW;YAAO;SAAS;QACrE,UAAU;YAAC;YAAoB;YAAiB;YAAqB;SAAiB;QACtF,eAAe;YAAC;YAAgC;YAAwB;SAAuB;QAC/F,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAwB;YAA2B;SAAwB;QAC5F,OAAO;YAAC;YAAyB;YAAqB;SAAuB;QAC7E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;uBAsBQ,CAAC;IACtB;IAEA,yCAAyC;IACzC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAU;YAAc;YAAS;YAAO;SAAa;QAC7E,UAAU;YAAC;YAAqB;YAAoB;YAAoB;SAAsB;QAC9F,eAAe;YAAC;YAA6B;YAA+B;SAA+B;QAC3G,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAyB;YAA4B;SAA2B;QACjG,OAAO;YAAC;YAA4B;YAAgC;SAAqB;QACzF,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;wCAsByB,CAAC;IACvC;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAU;YAAW;YAAW;YAAU;YAAU;SAAY;QAC/E,UAAU;YAAC;YAAkB;YAAkB;YAAsB;SAAoB;QACzF,eAAe;YAAC;YAA6B;YAA8B;SAAsB;QACjG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA0B;YAA0B;SAA0B;QAC/F,OAAO;YAAC;YAAmB;YAAoB;SAAqB;QACpE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;8CAsB+B,CAAC;IAC7C;IAEA,8BAA8B;IAC9B;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAU;YAAO;SAAS;QAC3E,UAAU;YAAC;YAAqB;YAAqB;YAAmB;SAAuB;QAC/F,eAAe;YAAC;YAAyB;YAA+B;SAAyB;QACjG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA4B;YAA4B;SAAmB;QAC5F,OAAO;YAAC;YAAyB;YAA6B;SAAgB;QAC9E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;+CAsBgC,CAAC;IAC9C;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAW;YAAU;YAAc;YAAiB;YAAO;SAAa;QACvF,UAAU;YAAC;YAAmB;YAAiB;YAAoB;SAAiB;QACpF,eAAe;YAAC;YAA2B;YAA2B;SAAsB;QAC5F,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA4B;YAAuB;SAA0B;QAC9F,OAAO;YAAC;YAAsB;YAAiB;SAAsB;QACrE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;6CAsB8B,CAAC;IAC5C;IAEA,yCAAyC;IACzC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAa;YAAS;YAAW;SAAS;QAC7E,UAAU;YAAC;YAAsB;YAAqB;YAAmB;SAAe;QACxF,eAAe;YAAC;YAA8B;YAAgC;SAAyB;QACvG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAwB;YAA0B;SAAgC;QACnG,OAAO;YAAC;YAAsB;YAAuB;SAAmB;QACxE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;qDAsBsC,CAAC;IACpD;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAU;YAAO;YAAS;SAAgB;QAC7E,UAAU;YAAC;YAAkB;YAAe;YAAsB;SAAoB;QACtF,eAAe;YAAC;YAA4B;YAA8B;SAAmB;QAC7F,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAuB;YAA0B;SAA0B;QAC5F,OAAO;YAAC;YAAsB;YAAmB;SAAmC;QACpF,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;2CAsB4B,CAAC;IAC1C;IAEA,iDAAiD;IACjD;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAW;YAAgB;YAAY;YAAe;SAAS;QAC9E,UAAU;YAAC;YAAmB;YAAqB;YAA0B;SAAqB;QAClG,eAAe;YAAC;YAA6B;YAA8B;SAA0B;QACrG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA4B;YAAkC;SAAqB;QACpG,OAAO;YAAC;YAA4B;YAA0B;SAAqC;QACnG,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;gDAwBiC,CAAC;IAC/C;IAEA,kDAAkD;IAClD;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAgB;YAAY;YAAiB;YAAQ;SAAY;QAChF,UAAU;YAAC;YAAoB;YAAqB;YAAmB;SAAoB;QAC3F,eAAe;YAAC;YAAgC;YAA2B;SAA0B;QACrG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA2B;YAA2B;SAAsB;QAC7F,OAAO;YAAC;YAAmC;YAAuB;SAAkB;QACpF,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAwB+B,CAAC;IAC7C;IAEA,oDAAoD;IACpD;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAkB;YAAc;YAAe;YAAO;SAAQ;QACtF,UAAU;YAAC;YAAe;YAAsB;YAAwB;SAAwB;QAChG,eAAe;YAAC;YAAgC;YAAmC;SAAwB;QAC3G,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAuB;YAA4B;SAAqB;QACzF,OAAO;YAAC;YAAiB;YAAmC;SAA6B;QACzF,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;mCAwBoB,CAAC;IAClC;IAEA,sCAAsC;IACtC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAO;YAAY;SAAQ;QAC5E,UAAU;YAAC;YAAmB;YAA0B;YAAkB;YAAwB;SAAmB;QACrH,eAAe;YAAC;YAAwB;YAA+B;YAA2B;SAAwB;QAC1H,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAkC;YAA8B;SAAe;QAChG,OAAO;YAAC;YAAiB;YAAyB;SAA8B;QAChF,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;mDA0BoC,CAAC;IAClD;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAU;YAAU;SAAa;QAClF,UAAU;YAAC;YAAsB;YAAkB;YAAqB;YAAuB;SAAmB;QAClH,eAAe;YAAC;YAAgC;YAA6B;SAAuB;QACpG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAyB;YAA+B;SAAuB;QAChG,OAAO;YAAC;YAAoB;YAA0B;SAA8B;QACpF,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;4CA0B6B,CAAC;IAC3C;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAU;YAAc;YAAS;YAAW;YAAc;SAAQ;QACjF,UAAU;YAAC;YAAkB;YAAkB;YAAqB;YAAqB;SAAuB;QAChH,eAAe;YAAC;YAA+B;YAAwB;SAAuB;QAC9F,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA2B;YAAuB;SAAe;QAClF,OAAO;YAAC;YAAuB;YAAqB;SAAyB;QAC7E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;4CA0B6B,CAAC;IAC3C;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAU;YAAU;SAAM;QAC3E,UAAU;YAAC;YAA2B;YAAsB;YAAwB;YAAoB;SAA0B;QAClI,eAAe;YAAC;YAAkC;YAA4B;SAA2B;QACzG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA6B;YAAyB;SAAyB;QAChG,OAAO;YAAC;YAAyB;YAA0B;SAAwB;QACnF,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;2CA0B4B,CAAC;IAC1C;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAgB;YAAW;YAAW;YAAO;YAAa;SAAa;QACtF,UAAU;YAAC;YAAoB;YAAqB;YAAqB;YAAqB;SAAkB;QAChH,eAAe;YAAC;YAA8B;YAA4B;SAAgC;QAC1G,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA0B;YAAuB;SAAkB;QACpF,OAAO;YAAC;YAAkB;YAAiB;SAAoC;QAC/E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;mDA0BoC,CAAC;IAClD;IAEA,6CAA6C;IAC7C;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAS;YAAa;SAAkB;QACzF,UAAU;YAAC;YAAkB;YAAqB;YAAkB;YAAqB;SAAkB;QAC3G,eAAe;YAAC;YAAyB;YAAmB;YAAqB;SAA0B;QAC3G,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAqB;YAAgB;SAAyB;QAC/E,OAAO;YAAC;YAAsB;YAAoB;SAAoB;QACtE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;oDA0BqC,CAAC;IACnD;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAU;YAAc;YAAc;YAAO;SAAa;QAClF,UAAU;YAAC;YAAwB;YAAmB;YAAyB;YAAoB;SAAgB;QACnH,eAAe;YAAC;YAAyB;YAA0B;SAA8B;QACjG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAkC;YAAsB;SAAsB;QAC/F,OAAO;YAAC;YAAqB;YAAyB;SAAwB;QAC9E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;4CA0B6B,CAAC;IAC3C;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAU;YAAS;SAAa;QACjF,UAAU;YAAC;YAAoB;YAAkB;YAAuB;YAAmB;SAAqB;QAChH,eAAe;YAAC;YAAiC;YAAwB;SAA2B;QACpG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA0B;YAAmB;SAAuB;QACrF,OAAO;YAAC;YAAe;YAAyB;SAAqB;QACrE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;0CA0B2B,CAAC;IACzC;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAO;YAAW;SAAS;QAC5E,UAAU;YAAC;YAAoB;YAAoB;YAAsB;YAA4B;SAAY;QACjH,eAAe;YAAC;YAAgC;YAA0B;SAAqB;QAC/F,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAyB;YAAwB;SAAoB;QACtF,OAAO;YAAC;YAAgB;YAA0B;SAAgB;QAClE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;oDA0BqC,CAAC;IACnD;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAU;YAAc;YAAc;YAAO;SAAkB;QACvF,UAAU;YAAC;YAAqB;YAAqB;YAAmB;YAAmB;SAAkB;QAC7G,eAAe;YAAC;YAAgC;YAAgB;SAAgC;QAChG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAyB;YAAuB;SAA4B;QAC7F,OAAO;YAAC;YAAe;YAAyB;SAAyB;QACzE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;2CA0B4B,CAAC;IAC1C;IAEA,oCAAoC;IACpC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAU;YAAO;SAAgB;QAClF,UAAU;YAAC;YAAqB;YAAmB;YAAoB;YAAmB;SAAsB;QAChH,eAAe;YAAC;YAAgC;YAA+B;SAAuB;QACtG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAwB;YAAa;SAAa;QACnE,OAAO;YAAC;YAA0B;YAAoB;SAAc;QACpE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;iCA0BkB,CAAC;IAChC;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAc;YAAO;SAAQ;QAC9E,UAAU;YAAC;YAAkB;YAAyB;YAAiB;YAAmB;SAAuB;QACjH,eAAe;YAAC;YAAkB;YAAwB;SAA8B;QACxF,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA4B;YAA8B;SAAmB;QAC9F,OAAO;YAAC;YAA0B;YAAiB;SAAwB;QAC3E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;sCA0BuB,CAAC;IACrC;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAgB;YAAW;YAAc;YAAU;YAAe;SAAW;QAC5F,UAAU;YAAC;YAAoB;YAAsB;YAAuB;YAAsB;SAAiB;QACnH,eAAe;YAAC;YAA+B;YAA4B;SAA8B;QACzG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAsB;YAAwB;SAAkB;QACjF,OAAO;YAAC;YAAsB;YAAsB;SAAsB;QAC1E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;gCA0BiB,CAAC;IAC/B;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAU;YAAO;SAAmB;QACrF,UAAU;YAAC;YAA2B;YAAoB;YAAwB;YAAsB;SAAY;QACpH,eAAe;YAAC;YAA2B;YAAoC;SAA0B;QACzG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAsB;YAAoB;SAAmB;QAC9E,OAAO;YAAC;YAAkB;YAAwB;SAAiB;QACnE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;4CA0B6B,CAAC;IAC3C;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAU;YAAO;SAAkB;QACpF,UAAU;YAAC;YAAiB;YAAqB;YAAqB;YAA0B;SAAY;QAC5G,eAAe;YAAC;YAAkC;YAAkC;SAAwB;QAC5G,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAsB;YAAsB;SAAqB;QAClF,OAAO;YAAC;YAAe;YAAwB;SAAqB;QACpE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;0CA0B2B,CAAC;IACzC;IAEA,mCAAmC;IACnC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAO;YAAU;SAAa;QAC/E,UAAU;YAAC;YAAmB;YAAmB;YAA2B;YAAqB;SAAgB;QACjH,eAAe;YAAC;YAAyB;YAA2B;SAA4B;QAChG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAyB;YAAyB;SAAc;QACjF,OAAO;YAAC;YAAmB;YAAuB;SAAc;QAChE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;yCA0B0B,CAAC;IACxC;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAO;YAAU;SAAW;QAC7E,UAAU;YAAC;YAAmB;YAAa;YAAuB;YAAiB;SAAY;QAC/F,eAAe;YAAC;YAAoC;YAA6B;SAA2B;QAC5G,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAqB;YAAyB;SAAyB;QACxF,OAAO;YAAC;YAAgB;YAAsB;SAAgB;QAC9D,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;mDA0BoC,CAAC;IAClD;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAgB;YAAU;YAAc;YAAc;YAAO;SAAa;QACzF,UAAU;YAAC;YAAsB;YAAqB;YAAgB;YAAqB;SAAmB;QAC9G,eAAe;YAAC;YAAqC;YAA+B;SAA2B;QAC/G,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAiB;YAAsB;SAA8B;QACtF,OAAO;YAAC;YAA+B;YAAwB;SAAkB;QACjF,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;mDA0BoC,CAAC;IAClD;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAU;YAAa;YAAc;SAAM;QAC9E,UAAU;YAAC;YAAsB;YAAkB;YAA0B;YAAkB;SAAY;QAC3G,eAAe;YAAC;YAA0B;YAAuB;SAA6B;QAC9F,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAyB;YAAc;SAAyB;QACjF,OAAO;YAAC;YAAyB;YAAuB;SAAoB;QAC5E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;oDA0BqC,CAAC;IACnD;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAU;YAAc;YAAc;YAAO;SAAiB;QACtF,UAAU;YAAC;YAAoB;YAAiB;YAAc;YAAa;SAAgB;QAC3F,eAAe;YAAC;YAA8B;YAA0B;SAA8B;QACtG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA2B;YAAsB;SAAwB;QAC1F,OAAO;YAAC;YAAuB;YAAkB;SAAuB;QACxE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;uCA0BwB,CAAC;IACtC;IAEA,uCAAuC;IACvC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAO;YAAe;SAAW;QAClF,UAAU;YAAC;YAAqB;YAAiB;YAAkB;YAAkB;SAAmB;QACxG,eAAe;YAAC;YAA8B;YAAiC;SAA0B;QACzG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA2B;YAAwB;SAAsB;QAC1F,OAAO;YAAC;YAAiB;YAAsB;SAAsB;QACrE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;2CA0B4B,CAAC;IAC1C;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAU;YAAO;SAAa;QAC/E,UAAU;YAAC;YAA4B;YAAsB;YAAiB;YAAuB;SAAqB;QAC1H,eAAe;YAAC;YAAqC;YAAwC;SAA6B;QAC1H,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAmB;YAAuB;SAA4B;QACvF,OAAO;YAAC;YAAyB;YAAqB;SAAyB;QAC/E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;iDA0BkC,CAAC;IAChD;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAW;YAAc;SAAO;QACjF,UAAU;YAAC;YAAkB;YAAuB;YAA0B;YAAiB;SAAsB;QACrH,eAAe;YAAC;YAA0B;YAA+B;SAAwB;QACjG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAsB;YAAkC;SAA0B;QACnG,OAAO;YAAC;YAA8B;YAAiB;SAAyB;QAChF,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;uDA0BwC,CAAC;IACtD;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAU;YAAU;SAAM;QAC3E,UAAU;YAAC;YAAqB;YAAoB;YAAmB;YAAwB;SAAsB;QACrH,eAAe;YAAC;YAAkC;YAA4B;SAAyB;QACvG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAuB;YAAgC;SAAuB;QAC/F,OAAO;YAAC;YAA6B;YAA6B;SAA2B;QAC7F,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;wDA0ByC,CAAC;IACvD;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAO;YAAe;SAAY;QACnF,UAAU;YAAC;YAAoB;YAAqB;YAAuB;YAAsB;SAAoB;QACrH,eAAe;YAAC;YAA8B;YAA8B;SAAwB;QACpG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAyB;YAAoB;SAAuB;QACrF,OAAO;YAAC;YAAsB;YAAiB;SAAyB;QACxE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;8CA0B+B,CAAC;IAC7C;IAEA,oCAAoC;IACpC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAW;YAAc;SAAW;QACrF,UAAU;YAAC;YAAyB;YAA0B;YAAmB;YAAwB;SAAsB;QAC/H,eAAe;YAAC;YAAkC;YAAmB;SAA0B;QAC/F,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA2B;YAA0B;SAAqB;QAC3F,OAAO;YAAC;YAA8B;YAA+B;SAAgB;QACrF,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;qDA0BsC,CAAC;IACpD;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAU;YAAc;YAAc;YAAO;SAAa;QAClF,UAAU;YAAC;YAAsB;YAA0B;YAAuB;YAAsB;SAAkB;QAC1H,eAAe;YAAC;YAA6B;YAA6B;SAAyB;QACnG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA2B;YAAoB;SAAuB;QACvF,OAAO;YAAC;YAAqB;YAAwB;SAAyB;QAC9E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;oDA0BqC,CAAC;IACnD;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAY;YAAW;YAAc;SAAO;QAC/E,UAAU;YAAC;YAAqB;YAAsB;YAAqB;YAAwB;SAAgB;QACnH,eAAe;YAAC;YAAiC;YAAkC;SAA2B;QAC9G,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA0B;YAAgB;SAA0B;QACrF,OAAO;YAAC;YAAwB;YAAuB;SAAgB;QACvE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;wCA0ByB,CAAC;IACvC;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAO;YAAmB;SAAM;QACjF,UAAU;YAAC;YAAuB;YAAuB;YAAsB;YAAoB;SAAmB;QACtH,eAAe;YAAC;YAA0B;YAAuB;SAAiB;QAClF,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAuB;YAAwB;SAAoB;QACpF,OAAO;YAAC;YAAsB;YAAsB;SAAgB;QACpE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;6CA0B8B,CAAC;IAC5C;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAO;YAAQ;SAAM;QACtE,UAAU;YAAC;YAAkB;YAA0B;YAAwB;YAAuB;SAAY;QAClH,eAAe;YAAC;YAA6B;YAAqB;SAAuB;QACzF,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAwB;YAA4B;SAA0B;QAC/F,OAAO;YAAC;YAAuB;YAAiB;SAAqB;QACrE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;kDA0BmC,CAAC;IACjD;IAEA,sCAAsC;IACtC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAO;YAAO;YAAc;SAAS;QACxE,UAAU;YAAC;YAAmB;YAAsB;YAAiB;YAAmB;SAAY;QACpG,eAAe;YAAC;YAAkC;YAA4B;SAAmB;QACjG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA0B;YAAsB;SAAuB;QACxF,OAAO;YAAC;YAAmB;YAAqB;SAAc;QAC9D,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;yCA0B0B,CAAC;IACxC;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAgB;YAAW;YAAc;YAAO;YAAc;SAAc;QAC3F,UAAU;YAAC;YAAmB;YAAqB;YAAkB;YAAmB;SAAY;QACpG,eAAe;YAAC;YAAyB;YAAmB;SAA2B;QACvF,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAsB;YAAoB;SAAwB;QACnF,OAAO;YAAC;YAAmB;YAAsB;SAAuB;QACxE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;sCA0BuB,CAAC;IACrC;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAS;YAAU;YAAO;SAAS;QACtE,UAAU;YAAC;YAAiB;YAAiB;YAAuB;YAAiB;SAAa;QAClG,eAAe;YAAC;YAAgC;YAAwB;SAAuB;QAC/F,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAwB;YAAyB;SAAmB;QACrF,OAAO;YAAC;YAA4B;YAAyB;SAAqB;QAClF,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;yCA0B0B,CAAC;IACxC;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAO;YAAU;SAAM;QACxE,UAAU;YAAC;YAAmB;YAAgB;YAAa;YAAgB;SAAsB;QACjG,eAAe;YAAC;YAA4B;YAAuB;SAAyB;QAC5F,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAyB;YAA0B;SAAa;QACjF,OAAO;YAAC;YAAsB;YAAmB;SAAuB;QACxE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;2CA0B4B,CAAC;IAC1C;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAU;YAAa;YAAO;SAAS;QAC1E,UAAU;YAAC;YAAkB;YAAkB;YAAiB;YAAiB;SAAY;QAC7F,eAAe;YAAC;YAAgC;YAAyB;SAAsB;QAC/F,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA4B;YAAyB;SAAwB;QAC9F,OAAO;YAAC;YAAqB;YAAsB;SAAuB;QAC1E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;wCA0ByB,CAAC;IACvC;IAEA,qCAAqC;IACrC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAU;YAAe;SAAQ;QAClF,UAAU;YAAC;YAAgB;YAAqB;YAAmB;YAAW;SAAa;QAC3F,eAAe;YAAC;YAAqB;YAAoB;SAA2B;QACpF,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAyB;YAAwB;SAAmB;QACrF,OAAO;YAAC;YAAwB;YAAsB;SAAc;QACpE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;uCA0BwB,CAAC;IACtC;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAgB;YAAU;YAAc;YAAc;YAAe;SAAM;QAC1F,UAAU;YAAC;YAAsB;YAAyB;YAAmB;YAAkB;SAAe;QAC9G,eAAe;YAAC;YAAkC;YAA4B;SAA2B;QACzG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAqB;YAAuB;SAAkB;QAC/E,OAAO;YAAC;YAAiB;YAAyB;SAAgB;QAClE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;uCA0BwB,CAAC;IACtC;IAEA,kCAAkC;IAClC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAU;YAAU;SAAM;QAC3E,UAAU;YAAC;YAAc;YAAwB;YAAoB;YAAuB;SAAY;QACxG,eAAe;YAAC;YAAyB;YAAgB;SAA2B;QACpF,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAsB;YAAsB;SAAyB;QACtF,OAAO;YAAC;YAAwB;YAAkB;SAAqB;QACvE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;iCA0BkB,CAAC;IAChC;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAU;YAAe;SAAY;QACtF,UAAU;YAAC;YAAuB;YAAoB;YAAsB;YAAuB;SAAqB;QACxH,eAAe;YAAC;YAAsC;YAA4B;SAAwB;QAC1G,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAsB;YAAwB;SAAqB;QACpF,OAAO;YAAC;YAAuB;YAAsB;SAA0B;QAC/E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;kCA0BmB,CAAC;IACjC;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAO;YAAU;SAAkB;QACpF,UAAU;YAAC;YAAkB;YAAiB;YAAiB;YAAmB;SAAqB;QACvG,eAAe;YAAC;YAA2B;YAAiC;SAAyB;QACrG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAwB;YAAwB;SAAmB;QACpF,OAAO;YAAC;YAAmB;YAAmB;SAAwB;QACtE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;oCA0BqB,CAAC;IACnC;IAEA,yCAAyC;IACzC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAO;YAAW;SAAM;QACzE,UAAU;YAAC;YAAoB;YAAsB;YAA0B;YAAqB;SAAkB;QACtH,eAAe;YAAC;YAA6B;YAAqB;SAAiB;QACnF,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAoB;YAA0B;SAAqB;QACpF,OAAO;YAAC;YAAgB;YAAmB;SAAuB;QAClE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;gDA0BiC,CAAC;IAC/C;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAO;YAAQ;SAAU;QAC1E,UAAU;YAAC;YAAsB;YAAqB;YAA0B;YAAoB;SAAY;QAChH,eAAe;YAAC;YAAiC;YAA2B;SAAmB;QAC/F,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA4B;YAA0B;SAAyB;QAChG,OAAO;YAAC;YAAsB;YAAkB;SAAuB;QACvE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;6BA0Bc,CAAC;IAC5B;IAEA,mCAAmC;IACnC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAY;YAAW;YAAc;SAAQ;QAChF,UAAU;YAAC;YAAmB;YAAuB;YAAmB;YAAqB;SAAY;QACzG,eAAe;YAAC;YAAgC;YAAkB;SAA+B;QACjG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAuB;YAAwB;SAAyB;QACzF,OAAO;YAAC;YAAoC;YAAyB;SAAoB;QACzF,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;wCA0ByB,CAAC;IACvC;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAU;YAAY;YAAO;YAAc;SAAc;QACjF,UAAU;YAAC;YAAoB;YAA4B;YAA0B;YAAkB;SAAY;QACnH,eAAe;YAAC;YAA8B;YAA4B;SAAmB;QAC7F,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAoB;YAA2B;SAA0B;QAC1F,OAAO;YAAC;YAAsB;YAAyB;SAAoB;QAC3E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;0CA0B2B,CAAC;IACzC;IAEA,qCAAqC;IACrC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAU;YAAY;YAAW;YAAc;SAAY;QACnF,UAAU;YAAC;YAAmB;YAAiB;YAAuB;YAAoB;SAAqB;QAC/G,eAAe;YAAC;YAAwB;YAAyB;SAA8B;QAC/F,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAsB;YAAqB;SAAqB;QACjF,OAAO;YAAC;YAAsB;YAAuB;SAAgB;QACrE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;oCA0BqB,CAAC;IACnC;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAO;YAAQ;SAAM;QACtE,UAAU;YAAC;YAAmB;YAAqB;YAAuB;YAAqB;SAAqB;QACpH,eAAe;YAAC;YAAyB;YAA8B;SAA0B;QACjG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA8B;YAA2B;SAAyB;QACnG,OAAO;YAAC;YAA2B;YAAyB;SAAe;QAC3E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;kCA0BmB,CAAC;IACjC;IAEA,iCAAiC;IACjC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAU;YAAc;YAAc;YAAO;SAAkB;QACvF,UAAU;YAAC;YAAwB;YAAiB;YAAqB;YAAkB;SAAsB;QACjH,eAAe;YAAC;YAA4B;YAA+B;SAA6B;QACxG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA+B;YAA2B;SAAsB;QACjG,OAAO;YAAC;YAAiB;YAAyB;SAAmB;QACrE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;8CA0B+B,CAAC;IAC7C;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAgB;YAAU;YAAc;YAAc;YAAO;SAAY;QACxF,UAAU;YAAC;YAAsB;YAAiB;YAAqB;YAAsB;SAAkB;QAC/G,eAAe;YAAC;YAAmC;YAA2B;SAAuB;QACrG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAqB;YAAsB;SAAuB;QACnF,OAAO;YAAC;YAAe;YAAmB;SAAc;QACxD,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;wCA0ByB,CAAC;IACvC;IAEA,2CAA2C;IAC3C;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAU;YAAU;SAAY;QACjF,UAAU;YAAC;YAAyB;YAAkB;YAAuB;YAAsB;SAAY;QAC/G,eAAe;YAAC;YAA0B;YAAwB;SAA0B;QAC5F,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA4B;YAA4B;SAAkB;QAC3F,OAAO;YAAC;YAAwB;YAAsB;SAA0B;QAChF,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;8CA0B+B,CAAC;IAC7C;IAEA,2CAA2C;IAC3C;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAU;YAAU;SAAM;QAC3E,UAAU;YAAC;YAA0B;YAAoB;YAAmB;YAAuB;SAAY;QAC/G,eAAe;YAAC;YAAiC;YAAyB;SAAwB;QAClG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAA0B;YAAwB;SAAyB;QAC5F,OAAO;YAAC;YAAuB;YAAuB;SAAsB;QAC5E,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;oCA0BqB,CAAC;IACnC;IAEA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,aAAa;QACb,UAAU;QACV,YAAY;QACZ,cAAc;YAAC;YAAS;YAAW;YAAc;YAAU;YAAc;SAAM;QAC/E,UAAU;YAAC;YAAqB;YAAuB;YAAmB;YAAkB;SAAqB;QACjH,eAAe;YAAC;YAAgC;YAAkC;SAAoB;QACtG,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,gBAAgB;YAAC;YAAgB;YAA0B;SAA6B;QACxF,OAAO;YAAC;YAAoB;YAAyB;SAAiB;QACtE,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;+CA0BgC,CAAC;IAC9C;CACD", "debugId": null}}, {"offset": {"line": 8743, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/test/augment-prompt-generator/src/lib/templates.ts"], "sourcesContent": ["import { ProjectTemplate, ProjectType, Platform, Complexity } from './types';\n\nexport interface IndustryTemplate {\n  id: string;\n  name: string;\n  industry: string;\n  description: string;\n  projectType: ProjectType;\n  platform: Platform;\n  complexity: Complexity;\n  technologies: string[];\n  features: string[];\n  businessGoals: string[];\n  targetAudience: string;\n  timeline: string;\n  budget: string;\n  successMetrics: string[];\n  risks: string[];\n  template: string;\n}\n\n// Import unified templates\nimport { UNIFIED_INDUSTRY_TEMPLATES } from './unified-templates';\n\nexport const INDUSTRY_TEMPLATES: IndustryTemplate[] = UNIFIED_INDUSTRY_TEMPLATES;\n\nexport const PROJECT_TEMPLATES: Record<ProjectType, ProjectTemplate> = {\n  'web-application': {\n    type: 'web-application',\n    name: 'Web Application',\n    description: 'Modern web applications with frontend and backend components',\n    defaultTechnologies: [],\n    commonFeatures: [\n      'User authentication and authorization',\n      'Responsive design for all devices',\n      'Database integration',\n      'API endpoints',\n      'Form handling and validation',\n      'Search functionality',\n      'Real-time updates',\n      'File upload/download',\n      'Email notifications',\n      'Admin dashboard'\n    ],\n    promptTemplate: `I want to build a comprehensive {projectType} called \"{projectName}\" with the following concept: {projectIdea}\n\n**Project Requirements:**\n\n**Core Functionality:**\n{projectDescription}\n\n**Technical Specifications:**\n- Platform: {platform}\n- Technology Stack: {technologyStack}\n- Complexity Level: {complexity}\n- Key Features: {features}\n\n**Implementation Approach:**\nPlease help me build this project step by step with the following approach:\n\n1. **Project Setup & Architecture:**\n   - Initialize the project with the specified technology stack\n   - Set up the development environment and folder structure\n   - Configure essential tools (linting, formatting, testing)\n   - Design the overall system architecture\n\n2. **Database Design:**\n   - Design and implement the database schema\n   - Set up database connections and ORM/query layer\n   - Create necessary migrations and seed data\n\n3. **Backend Development:**\n   - Implement core API endpoints\n   - Set up authentication and authorization\n   - Add input validation and error handling\n   - Implement business logic and data processing\n\n4. **Frontend Development:**\n   - Create responsive UI components\n   - Implement user interfaces for all features\n   - Add client-side routing and state management\n   - Integrate with backend APIs\n\n5. **Testing & Quality Assurance:**\n   - Write comprehensive unit tests for all components\n   - Implement integration tests for API endpoints\n   - Add end-to-end tests for critical user flows\n   - Set up automated testing pipeline\n\n6. **Deployment & DevOps:**\n   - Configure production environment\n   - Set up CI/CD pipeline\n   - Implement monitoring and logging\n   - Deploy to production platform\n\n**Augment Agent Specific Instructions:**\n- Please use your codebase context engine to understand the project structure as we build\n- Break down complex tasks into manageable subtasks using your task management capabilities\n- For each major component, first analyze the codebase context before making changes\n- Suggest and implement best practices for the chosen technology stack\n- Provide code reviews and optimization suggestions throughout development\n- Help debug any issues that arise during implementation\n\n**Additional Requirements:**\n{additionalRequirements}\n\nPlease start by analyzing this request and creating a detailed implementation plan with specific tasks. Then begin with the project setup and architecture phase.`\n  },\n\n  'mobile-application': {\n    type: 'mobile-application',\n    name: 'Mobile Application',\n    description: 'Cross-platform or native mobile applications',\n    defaultTechnologies: [],\n    commonFeatures: [\n      'User onboarding and authentication',\n      'Push notifications',\n      'Offline functionality',\n      'Camera and photo integration',\n      'Location services',\n      'Social sharing',\n      'In-app purchases',\n      'Biometric authentication',\n      'Dark/light theme support',\n      'App store optimization'\n    ],\n    promptTemplate: `I want to develop a {projectType} called \"{projectName}\" with this concept: {projectIdea}\n\n**Mobile App Requirements:**\n\n**Core Functionality:**\n{projectDescription}\n\n**Technical Specifications:**\n- Target Platform: {platform}\n- Technology Stack: {technologyStack}\n- Complexity Level: {complexity}\n- Key Features: {features}\n\n**Mobile-Specific Implementation Plan:**\n\n1. **Project Setup & Configuration:**\n   - Initialize mobile project with chosen framework\n   - Configure development environment (iOS/Android SDKs)\n   - Set up device testing and simulators\n   - Configure app icons, splash screens, and metadata\n\n2. **UI/UX Development:**\n   - Design mobile-first user interface\n   - Implement navigation patterns (tabs, stack, drawer)\n   - Create responsive layouts for different screen sizes\n   - Add platform-specific design elements\n\n3. **Core Features Implementation:**\n   - Implement user authentication and session management\n   - Add data persistence and offline capabilities\n   - Integrate device APIs (camera, location, sensors)\n   - Implement push notification system\n\n4. **Backend Integration:**\n   - Set up API communication layer\n   - Implement data synchronization\n   - Add error handling and retry logic\n   - Configure analytics and crash reporting\n\n5. **Testing & Optimization:**\n   - Test on multiple devices and OS versions\n   - Optimize performance and memory usage\n   - Implement automated testing for critical flows\n   - Conduct user acceptance testing\n\n6. **Deployment Preparation:**\n   - Configure app store metadata and assets\n   - Set up code signing and certificates\n   - Prepare for app store submission\n   - Plan release and update strategy\n\n**Augment Agent Mobile Development Guidelines:**\n- Use codebase context to understand mobile project structure\n- Consider platform-specific best practices and guidelines\n- Implement proper state management for mobile apps\n- Focus on performance optimization and battery efficiency\n- Ensure accessibility compliance for mobile interfaces\n- Plan for different device capabilities and limitations\n\n**Additional Requirements:**\n{additionalRequirements}\n\nPlease create a detailed mobile development plan and start with the project setup phase.`\n  },\n\n  'api-backend': {\n    type: 'api-backend',\n    name: 'API/Backend Service',\n    description: 'RESTful APIs and backend services',\n    defaultTechnologies: [],\n    commonFeatures: [\n      'RESTful API endpoints',\n      'Authentication and authorization',\n      'Database operations (CRUD)',\n      'Input validation and sanitization',\n      'Error handling and logging',\n      'Rate limiting and throttling',\n      'API documentation',\n      'Caching mechanisms',\n      'Background job processing',\n      'Monitoring and health checks'\n    ],\n    promptTemplate: `I need to build a robust {projectType} called \"{projectName}\" for: {projectIdea}\n\n**Backend Service Requirements:**\n\n**Core Functionality:**\n{projectDescription}\n\n**Technical Specifications:**\n- Platform: {platform}\n- Technology Stack: {technologyStack}\n- Complexity Level: {complexity}\n- Key Features: {features}\n\n**Backend Development Roadmap:**\n\n1. **API Architecture & Setup:**\n   - Design RESTful API structure and endpoints\n   - Set up project with chosen backend framework\n   - Configure development environment and tools\n   - Implement basic server setup and middleware\n\n2. **Database Layer:**\n   - Design normalized database schema\n   - Set up database connections and pooling\n   - Implement data models and relationships\n   - Create migration scripts and seed data\n\n3. **Authentication & Security:**\n   - Implement user authentication system\n   - Set up authorization and role-based access\n   - Add input validation and sanitization\n   - Configure security headers and CORS\n\n4. **Core API Development:**\n   - Implement CRUD operations for all entities\n   - Add business logic and data processing\n   - Create comprehensive error handling\n   - Implement logging and monitoring\n\n5. **Performance & Scalability:**\n   - Add caching layers (Redis, in-memory)\n   - Implement rate limiting and throttling\n   - Optimize database queries and indexing\n   - Set up background job processing\n\n6. **Documentation & Testing:**\n   - Generate comprehensive API documentation\n   - Write unit tests for all endpoints\n   - Implement integration testing\n   - Add load testing and performance benchmarks\n\n7. **Deployment & DevOps:**\n   - Configure production environment\n   - Set up CI/CD pipeline\n   - Implement monitoring and alerting\n   - Deploy to cloud infrastructure\n\n**Augment Agent Backend Best Practices:**\n- Use codebase context to understand existing API patterns\n- Implement proper error handling and status codes\n- Follow RESTful conventions and API design principles\n- Ensure database queries are optimized and secure\n- Add comprehensive logging for debugging and monitoring\n- Plan for horizontal scaling and load distribution\n\n**Additional Requirements:**\n{additionalRequirements}\n\nPlease analyze this backend project and create a detailed implementation plan with specific development tasks.`\n  },\n\n  'desktop-application': {\n    type: 'desktop-application',\n    name: 'Desktop Application',\n    description: 'Cross-platform desktop applications',\n    defaultTechnologies: [],\n    commonFeatures: [\n      'Native OS integration',\n      'File system access',\n      'System tray/menu bar integration',\n      'Auto-updater functionality',\n      'Offline functionality',\n      'Multi-window support',\n      'Keyboard shortcuts',\n      'Drag and drop support',\n      'System notifications',\n      'Cross-platform compatibility'\n    ],\n    promptTemplate: `I want to create a {projectType} called \"{projectName}\" with this concept: {projectIdea}\n\n**Desktop Application Requirements:**\n\n**Core Functionality:**\n{projectDescription}\n\n**Technical Specifications:**\n- Target Platform: {platform}\n- Technology Stack: {technologyStack}\n- Complexity Level: {complexity}\n- Key Features: {features}\n\n**Desktop Development Plan:**\n\n1. **Application Setup & Architecture:**\n   - Initialize desktop project with chosen framework\n   - Configure build tools and development environment\n   - Set up application structure and main window\n   - Configure app metadata and icons\n\n2. **UI/UX Development:**\n   - Design native-feeling user interface\n   - Implement responsive layouts for different screen sizes\n   - Add platform-specific UI elements and behaviors\n   - Create consistent theming and styling\n\n3. **Core Features Implementation:**\n   - Implement main application functionality\n   - Add file system integration and data persistence\n   - Create menu systems and keyboard shortcuts\n   - Implement inter-window communication\n\n4. **System Integration:**\n   - Add OS-specific features and integrations\n   - Implement system tray/menu bar functionality\n   - Configure auto-updater and installation\n   - Add system notifications and alerts\n\n5. **Testing & Optimization:**\n   - Test on multiple operating systems\n   - Optimize performance and memory usage\n   - Implement automated testing for core features\n   - Test installation and update processes\n\n6. **Distribution & Deployment:**\n   - Configure code signing and certificates\n   - Set up build pipeline for multiple platforms\n   - Prepare distribution packages\n   - Plan release and update strategy\n\n**Augment Agent Desktop Guidelines:**\n- Focus on native OS integration and user experience\n- Implement proper state management for desktop apps\n- Consider platform-specific design guidelines\n- Plan for offline functionality and data synchronization\n- Ensure proper resource management and performance\n\n**Additional Requirements:**\n{additionalRequirements}\n\nPlease create a detailed desktop development plan and start with the application setup.`\n  },\n\n  'data-analysis': {\n    type: 'data-analysis',\n    name: 'Data Analysis Tool',\n    description: 'Data processing and analysis applications',\n    defaultTechnologies: [],\n    commonFeatures: [\n      'Data import/export (CSV, JSON, Excel)',\n      'Data cleaning and preprocessing',\n      'Statistical analysis and calculations',\n      'Data visualization and charts',\n      'Interactive dashboards',\n      'Report generation',\n      'Data filtering and querying',\n      'Machine learning integration',\n      'Real-time data processing',\n      'Automated data pipelines'\n    ],\n    promptTemplate: `I need to build a {projectType} called \"{projectName}\" for: {projectIdea}\n\n**Data Analysis Requirements:**\n\n**Core Functionality:**\n{projectDescription}\n\n**Technical Specifications:**\n- Platform: {platform}\n- Technology Stack: {technologyStack}\n- Complexity Level: {complexity}\n- Key Features: {features}\n\n**Data Analysis Development Roadmap:**\n\n1. **Project Setup & Data Architecture:**\n   - Set up development environment with data science tools\n   - Design data storage and processing architecture\n   - Configure data pipeline and workflow tools\n   - Set up version control for data and code\n\n2. **Data Ingestion & Processing:**\n   - Implement data import from various sources\n   - Create data cleaning and preprocessing pipelines\n   - Add data validation and quality checks\n   - Set up automated data processing workflows\n\n3. **Analysis & Computation:**\n   - Implement statistical analysis functions\n   - Add data transformation and aggregation\n   - Create custom analysis algorithms\n   - Integrate machine learning capabilities\n\n4. **Visualization & Reporting:**\n   - Build interactive data visualizations\n   - Create customizable dashboards\n   - Implement report generation features\n   - Add export functionality for results\n\n5. **User Interface & Experience:**\n   - Design intuitive data exploration interface\n   - Add interactive filtering and querying\n   - Implement real-time data updates\n   - Create user-friendly analysis workflows\n\n6. **Performance & Scalability:**\n   - Optimize data processing performance\n   - Implement caching and data indexing\n   - Add support for large datasets\n   - Configure distributed processing if needed\n\n**Augment Agent Data Science Best Practices:**\n- Use appropriate data structures and algorithms\n- Implement proper error handling for data operations\n- Add comprehensive logging and monitoring\n- Follow data science workflow best practices\n- Ensure reproducible analysis and results\n- Plan for data security and privacy compliance\n\n**Additional Requirements:**\n{additionalRequirements}\n\nPlease analyze this data project and create a detailed implementation plan.`\n  },\n\n  'machine-learning': {\n    type: 'machine-learning',\n    name: 'Machine Learning Project',\n    description: 'AI/ML applications and models',\n    defaultTechnologies: [],\n    commonFeatures: [\n      'Data preprocessing and cleaning',\n      'Model training and evaluation',\n      'Feature engineering',\n      'Model deployment',\n      'Performance monitoring',\n      'A/B testing framework',\n      'Data pipeline automation',\n      'Model versioning',\n      'Inference API',\n      'Real-time predictions'\n    ],\n    promptTemplate: `I want to build a machine learning project called \"{projectName}\" with the following concept: {projectIdea}\n\n**Project Requirements:**\n{projectDescription}\n\n**Technical Specifications:**\n- Platform: {platform}\n- Technology Stack: {technologyStack}\n- Complexity Level: {complexity}\n- Key Features: {features}\n\n**Implementation Approach:**\n1. **Data Pipeline Setup:**\n   - Design data collection and preprocessing pipeline\n   - Implement data validation and quality checks\n   - Set up feature engineering workflows\n   - Create data versioning system\n\n2. **Model Development:**\n   - Implement model training pipeline\n   - Set up experiment tracking and model versioning\n   - Create model evaluation and validation framework\n   - Implement hyperparameter tuning\n\n3. **Model Deployment:**\n   - Create model serving infrastructure\n   - Implement inference API endpoints\n   - Set up monitoring and alerting\n   - Plan for model updates and rollbacks\n\n**Additional Requirements:**\n{additionalRequirements}\n\nPlease create a comprehensive ML project plan.`\n  },\n\n  'devops-infrastructure': {\n    type: 'devops-infrastructure',\n    name: 'DevOps Infrastructure',\n    description: 'Infrastructure automation and deployment systems',\n    defaultTechnologies: [],\n    commonFeatures: [\n      'CI/CD pipelines',\n      'Infrastructure as Code',\n      'Container orchestration',\n      'Monitoring and alerting',\n      'Log aggregation',\n      'Security scanning',\n      'Backup and disaster recovery',\n      'Auto-scaling',\n      'Load balancing',\n      'Service mesh'\n    ],\n    promptTemplate: `I want to build a DevOps infrastructure project called \"{projectName}\" with the following concept: {projectIdea}\n\n**Project Requirements:**\n{projectDescription}\n\n**Technical Specifications:**\n- Platform: {platform}\n- Technology Stack: {technologyStack}\n- Complexity Level: {complexity}\n- Key Features: {features}\n\n**Implementation Approach:**\n1. **Infrastructure Setup:**\n   - Design cloud architecture\n   - Implement Infrastructure as Code\n   - Set up networking and security\n   - Configure monitoring and logging\n\n2. **CI/CD Pipeline:**\n   - Create automated build and test pipelines\n   - Implement deployment automation\n   - Set up environment management\n   - Configure rollback mechanisms\n\n3. **Operations:**\n   - Implement monitoring and alerting\n   - Set up log aggregation and analysis\n   - Create backup and disaster recovery\n   - Plan for scaling and optimization\n\n**Additional Requirements:**\n{additionalRequirements}\n\nPlease create a detailed DevOps infrastructure plan.`\n  },\n\n  'chrome-extension': {\n    type: 'chrome-extension',\n    name: 'Chrome Extension',\n    description: 'Browser extensions for Chrome and Chromium browsers',\n    defaultTechnologies: [],\n    commonFeatures: [\n      'Content script injection',\n      'Background service worker',\n      'Popup interface',\n      'Options page',\n      'Context menus',\n      'Browser API integration',\n      'Local storage',\n      'Cross-origin requests',\n      'Notifications',\n      'Tab management'\n    ],\n    promptTemplate: `I want to build a Chrome extension called \"{projectName}\" with the following concept: {projectIdea}\n\n**Project Requirements:**\n{projectDescription}\n\n**Technical Specifications:**\n- Platform: {platform}\n- Technology Stack: {technologyStack}\n- Complexity Level: {complexity}\n- Key Features: {features}\n\n**Implementation Approach:**\n1. **Extension Setup:**\n   - Create manifest.json configuration\n   - Set up project structure\n   - Configure permissions and APIs\n   - Implement background service worker\n\n2. **User Interface:**\n   - Design and implement popup interface\n   - Create options/settings page\n   - Implement content scripts\n   - Add context menu integration\n\n3. **Functionality:**\n   - Implement core extension features\n   - Add browser API integrations\n   - Set up data storage and sync\n   - Create user preferences system\n\n**Additional Requirements:**\n{additionalRequirements}\n\nPlease create a detailed Chrome extension development plan.`\n  },\n\n  'cli-tool': {\n    type: 'cli-tool',\n    name: 'CLI Tool',\n    description: 'Command-line interface applications and utilities',\n    defaultTechnologies: [],\n    commonFeatures: [\n      'Command parsing',\n      'Interactive prompts',\n      'File system operations',\n      'Configuration management',\n      'Progress indicators',\n      'Error handling',\n      'Help documentation',\n      'Plugin system',\n      'Auto-completion',\n      'Logging and debugging'\n    ],\n    promptTemplate: `I want to build a CLI tool called \"{projectName}\" with the following concept: {projectIdea}\n\n**Project Requirements:**\n{projectDescription}\n\n**Technical Specifications:**\n- Platform: {platform}\n- Technology Stack: {technologyStack}\n- Complexity Level: {complexity}\n- Key Features: {features}\n\n**Implementation Approach:**\n1. **CLI Framework Setup:**\n   - Set up command parsing and routing\n   - Implement help system and documentation\n   - Create configuration management\n   - Add input validation and error handling\n\n2. **Core Functionality:**\n   - Implement main command features\n   - Add interactive prompts and menus\n   - Create file system operations\n   - Implement progress indicators\n\n3. **User Experience:**\n   - Add auto-completion support\n   - Implement logging and debugging\n   - Create installation and update system\n   - Add plugin/extension support\n\n**Additional Requirements:**\n{additionalRequirements}\n\nPlease create a detailed CLI tool development plan.`\n  },\n\n  'library-package': {\n    type: 'library-package',\n    name: 'Library/Package',\n    description: 'Reusable libraries and packages for developers',\n    defaultTechnologies: [],\n    commonFeatures: [\n      'Clean API design',\n      'Comprehensive documentation',\n      'Unit testing',\n      'Type definitions',\n      'Build system',\n      'Package publishing',\n      'Version management',\n      'Examples and demos',\n      'Performance optimization',\n      'Cross-platform support'\n    ],\n    promptTemplate: `I want to build a library/package called \"{projectName}\" with the following concept: {projectIdea}\n\n**Project Requirements:**\n{projectDescription}\n\n**Technical Specifications:**\n- Platform: {platform}\n- Technology Stack: {technologyStack}\n- Complexity Level: {complexity}\n- Key Features: {features}\n\n**Implementation Approach:**\n1. **Library Architecture:**\n   - Design clean and intuitive API\n   - Set up modular code structure\n   - Implement core functionality\n   - Add type definitions and interfaces\n\n2. **Development Workflow:**\n   - Set up build and bundling system\n   - Implement comprehensive testing\n   - Create documentation and examples\n   - Add linting and code quality tools\n\n3. **Distribution:**\n   - Configure package publishing\n   - Set up version management\n   - Create installation guides\n   - Implement usage examples and demos\n\n**Additional Requirements:**\n{additionalRequirements}\n\nPlease create a detailed library development plan.`\n  }\n};\n\n// Add more templates for other project types\nexport const getProjectTemplate = (projectType: ProjectType): ProjectTemplate => {\n  return PROJECT_TEMPLATES[projectType] || PROJECT_TEMPLATES['web-application'];\n};\n"], "names": [], "mappings": ";;;;;AAqBA,2BAA2B;AAC3B;;AAEO,MAAM,qBAAyC,qIAAA,CAAA,6BAA0B;AAEzE,MAAM,oBAA0D;IACrE,mBAAmB;QACjB,MAAM;QACN,MAAM;QACN,aAAa;QACb,qBAAqB,EAAE;QACvB,gBAAgB;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iKA8D4I,CAAC;IAChK;IAEA,sBAAsB;QACpB,MAAM;QACN,MAAM;QACN,aAAa;QACb,qBAAqB,EAAE;QACvB,gBAAgB;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wFA8DmE,CAAC;IACvF;IAEA,eAAe;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,qBAAqB,EAAE;QACvB,gBAAgB;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8GAoEyF,CAAC;IAC7G;IAEA,uBAAuB;QACrB,MAAM;QACN,MAAM;QACN,aAAa;QACb,qBAAqB,EAAE;QACvB,gBAAgB;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uFA6DkE,CAAC;IACtF;IAEA,iBAAiB;QACf,MAAM;QACN,MAAM;QACN,aAAa;QACb,qBAAqB,EAAE;QACvB,gBAAgB;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2EA8DsD,CAAC;IAC1E;IAEA,oBAAoB;QAClB,MAAM;QACN,MAAM;QACN,aAAa;QACb,qBAAqB,EAAE;QACvB,gBAAgB;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAiCyB,CAAC;IAC7C;IAEA,yBAAyB;QACvB,MAAM;QACN,MAAM;QACN,aAAa;QACb,qBAAqB,EAAE;QACvB,gBAAgB;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oDAiC+B,CAAC;IACnD;IAEA,oBAAoB;QAClB,MAAM;QACN,MAAM;QACN,aAAa;QACb,qBAAqB,EAAE;QACvB,gBAAgB;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2DAiCsC,CAAC;IAC1D;IAEA,YAAY;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,qBAAqB,EAAE;QACvB,gBAAgB;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mDAiC8B,CAAC;IAClD;IAEA,mBAAmB;QACjB,MAAM;QACN,MAAM;QACN,aAAa;QACb,qBAAqB,EAAE;QACvB,gBAAgB;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAiC6B,CAAC;IACjD;AACF;AAGO,MAAM,qBAAqB,CAAC;IACjC,OAAO,iBAAiB,CAAC,YAAY,IAAI,iBAAiB,CAAC,kBAAkB;AAC/E", "debugId": null}}, {"offset": {"line": 9436, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/test/augment-prompt-generator/src/components/AdvancedTemplateBrowser.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { INDUSTRY_TEMPLATES, IndustryTemplate } from '@/lib/templates';\nimport { ProjectInput } from '@/lib/types';\n\ninterface AdvancedTemplateBrowserProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onLoadTemplate: (projectInput: Partial<ProjectInput>) => void;\n}\n\nexport default function AdvancedTemplateBrowser({\n  isOpen,\n  onClose,\n  onLoadTemplate\n}: AdvancedTemplateBrowserProps) {\n  const [selectedIndustry, setSelectedIndustry] = useState<string>('all');\n  const [selectedComplexity, setSelectedComplexity] = useState<string>('all');\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedTemplate, setSelectedTemplate] = useState<IndustryTemplate | null>(null);\n\n  const industries = Array.from(new Set(INDUSTRY_TEMPLATES.map(t => t.industry)));\n  const complexities = ['basic', 'intermediate', 'advanced'];\n\n  const filteredIndustryTemplates = INDUSTRY_TEMPLATES.filter(template => {\n    const matchesIndustry = selectedIndustry === 'all' || template.industry === selectedIndustry;\n    const matchesComplexity = selectedComplexity === 'all' || template.complexity === selectedComplexity;\n    const matchesSearch = searchQuery === '' || \n      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n      template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||\n      template.industry.toLowerCase().includes(searchQuery.toLowerCase());\n    \n    return matchesIndustry && matchesComplexity && matchesSearch;\n  });\n\n  const handleLoadIndustryTemplate = (template: IndustryTemplate) => {\n    const projectInput: Partial<ProjectInput> = {\n      projectType: template.projectType,\n      projectName: template.name,\n      projectIdea: `${template.description}\\n\\nBusiness Goals:\\n${template.businessGoals.map(goal => `• ${goal}`).join('\\n')}\\n\\nTarget Audience: ${template.targetAudience}\\n\\nKey Features:\\n${template.features.map(feature => `• ${feature}`).join('\\n')}`,\n      platform: template.platform,\n      complexity: template.complexity,\n      technologies: template.technologies.slice(0, 4).map(tech => ({\n        category: 'frontend' as const,\n        name: tech,\n        description: `${tech} for ${template.industry.toLowerCase()} solutions`\n      })),\n      additionalRequirements: `Timeline: ${template.timeline}\\nBudget: ${template.budget}\\n\\nSuccess Metrics:\\n${template.successMetrics.map(metric => `• ${metric}`).join('\\n')}\\n\\nRisk Considerations:\\n${template.risks.map(risk => `• ${risk}`).join('\\n')}`\n    };\n    onLoadTemplate(projectInput);\n    onClose();\n  };\n\n  const getComplexityColor = (complexity: string) => {\n    switch (complexity) {\n      case 'basic': return 'bg-green-600 text-green-200';\n      case 'intermediate': return 'bg-yellow-600 text-yellow-200';\n      case 'advanced': return 'bg-red-600 text-red-200';\n      default: return 'bg-gray-600 text-gray-200';\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-2 sm:p-4\">\n      <div className=\"bg-black border border-white/20 rounded-xl shadow-2xl p-4 sm:p-6 max-w-7xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-hidden\" style={{\n        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8)'\n      }}>\n        <div className=\"flex justify-between items-center mb-4 sm:mb-6\">\n          <h3 className=\"text-lg sm:text-xl font-semibold text-white\">Industry Templates</h3>\n          <button\n            onClick={onClose}\n            className=\"bg-gray-800 text-white px-3 py-1 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm flex-shrink-0\"\n          >\n            Close\n          </button>\n        </div>\n\n        {/* Industry Templates Header */}\n        <div className=\"mb-6\">\n          <h3 className=\"text-xl font-semibold text-white mb-2\">Industry Templates</h3>\n          <p className=\"text-gray-400 text-sm\">Choose from comprehensive industry-specific project templates with detailed specifications and best practices.</p>\n        </div>\n\n        {/* Industry Templates Content */}\n            {/* Filters */}\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-4 sm:mb-6\">\n              <input\n                type=\"text\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                placeholder=\"Search templates...\"\n                className=\"px-3 py-2 rounded-md bg-gray-900 text-white placeholder-gray-400 border border-white/10 focus:border-white/30 focus:outline-none text-sm sm:text-base\"\n              />\n              <select\n                value={selectedIndustry}\n                onChange={(e) => setSelectedIndustry(e.target.value)}\n                className=\"px-3 py-2 rounded-md bg-gray-900 text-white border border-white/10 focus:border-white/30 focus:outline-none text-sm sm:text-base\"\n              >\n                <option value=\"all\">All Industries</option>\n                {industries.map(industry => (\n                  <option key={industry} value={industry}>{industry}</option>\n                ))}\n              </select>\n              <select\n                value={selectedComplexity}\n                onChange={(e) => setSelectedComplexity(e.target.value)}\n                className=\"px-3 py-2 rounded-md bg-gray-900 text-white border border-white/10 focus:border-white/30 focus:outline-none text-sm sm:text-base sm:col-span-2 lg:col-span-1\"\n              >\n                <option value=\"all\">All Complexity Levels</option>\n                {complexities.map(complexity => (\n                  <option key={complexity} value={complexity} className=\"capitalize\">{complexity}</option>\n                ))}\n              </select>\n            </div>\n\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 max-h-[55vh] sm:max-h-[65vh] overflow-y-auto\">\n              {/* Templates List */}\n              <div className={`space-y-3 sm:space-y-4 ${selectedTemplate ? 'hidden lg:block' : ''}`}>\n                {filteredIndustryTemplates.map((template) => (\n                  <div\n                    key={template.id}\n                    className={`bg-gray-900 border border-white/10 rounded-lg p-3 sm:p-4 transition-all duration-300 cursor-pointer ${\n                      selectedTemplate?.id === template.id\n                        ? 'ring-2 ring-white bg-gray-800'\n                        : 'hover:bg-gray-800'\n                    }`}\n                    onClick={() => setSelectedTemplate(template)}\n                  >\n                    <div className=\"flex justify-between items-start mb-2\">\n                      <h4 className=\"text-white font-medium text-sm sm:text-base pr-2 flex-1 min-w-0\">{template.name}</h4>\n                      <span className={`px-2 py-1 rounded text-xs capitalize flex-shrink-0 ${getComplexityColor(template.complexity)}`}>\n                        {template.complexity}\n                      </span>\n                    </div>\n                    <p className=\"text-gray-300 text-xs sm:text-sm mb-2\">{template.industry}</p>\n                    <p className=\"text-gray-400 text-xs sm:text-sm mb-3 line-clamp-2\">{template.description}</p>\n                    <div className=\"flex justify-between items-center text-xs text-gray-500\">\n                      <span className=\"truncate pr-2\">{template.timeline}</span>\n                      <span className=\"truncate\">{template.budget}</span>\n                    </div>\n                  </div>\n                ))}\n                {filteredIndustryTemplates.length === 0 && (\n                  <div className=\"text-center text-gray-400 py-12\">\n                    <div className=\"text-4xl mb-4\">🔍</div>\n                    <h4 className=\"text-lg font-semibold mb-2\">No Templates Found</h4>\n                    <p className=\"text-sm\">Try adjusting your filters or search query</p>\n                  </div>\n                )}\n              </div>\n\n              {/* Template Details */}\n              <div className={`bg-gray-900 border border-white/10 rounded-lg p-3 sm:p-4 ${selectedTemplate ? 'lg:col-span-1' : 'hidden lg:block'} ${selectedTemplate ? 'flex flex-col' : ''}`}>\n                {selectedTemplate ? (\n                  <div className=\"flex flex-col h-full\">\n                    {/* Back Button for Mobile */}\n                    <button\n                      onClick={() => setSelectedTemplate(null)}\n                      className=\"lg:hidden flex items-center space-x-2 text-gray-400 hover:text-white transition-colors mb-4 flex-shrink-0\"\n                    >\n                      <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n                      </svg>\n                      <span className=\"text-sm\">Back to Templates</span>\n                    </button>\n\n                    {/* Scrollable Content */}\n                    <div className=\"flex-1 overflow-y-auto space-y-3 sm:space-y-4 mb-4\">\n                      <div>\n                        <h4 className=\"text-white font-medium text-base sm:text-lg mb-2\">{selectedTemplate.name}</h4>\n                        <p className=\"text-gray-300 text-xs sm:text-sm mb-2\">{selectedTemplate.industry}</p>\n                        <p className=\"text-gray-400 text-xs sm:text-sm\">{selectedTemplate.description}</p>\n                      </div>\n\n                      <div>\n                        <h5 className=\"text-white font-medium mb-2 text-sm sm:text-base\">Key Features</h5>\n                        <div className=\"space-y-1\">\n                          {selectedTemplate.features.map((feature, index) => (\n                            <div key={index} className=\"text-gray-400 text-xs sm:text-sm\">• {feature}</div>\n                          ))}\n                        </div>\n                      </div>\n\n                      <div>\n                        <h5 className=\"text-white font-medium mb-2 text-sm sm:text-base\">Technologies</h5>\n                        <div className=\"flex flex-wrap gap-1 sm:gap-2\">\n                          {selectedTemplate.technologies.map((tech, index) => (\n                            <span key={index} className=\"bg-gray-700 text-gray-200 text-xs px-2 py-1 rounded\">\n                              {tech}\n                            </span>\n                          ))}\n                        </div>\n                      </div>\n\n                      <div>\n                        <h5 className=\"text-white font-medium mb-2 text-sm sm:text-base\">Business Goals</h5>\n                        <div className=\"space-y-1\">\n                          {selectedTemplate.businessGoals.map((goal, index) => (\n                            <div key={index} className=\"text-gray-400 text-xs sm:text-sm\">• {goal}</div>\n                          ))}\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 text-sm\">\n                        <div className=\"p-2 sm:p-3 rounded-lg bg-white/5 border border-white/10\">\n                          <span className=\"text-gray-400 text-xs sm:text-sm\">Timeline:</span>\n                          <div className=\"text-white text-sm sm:text-base font-medium\">{selectedTemplate.timeline}</div>\n                        </div>\n                        <div className=\"p-2 sm:p-3 rounded-lg bg-white/5 border border-white/10\">\n                          <span className=\"text-gray-400 text-xs sm:text-sm\">Budget:</span>\n                          <div className=\"text-white text-sm sm:text-base font-medium\">{selectedTemplate.budget}</div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Fixed Button at Bottom */}\n                    <div className=\"flex-shrink-0 pt-3 border-t border-white/10\">\n                      <button\n                        onClick={() => handleLoadIndustryTemplate(selectedTemplate)}\n                        className=\"w-full bg-white text-black py-3 px-4 rounded-lg hover:bg-gray-200 transition-all duration-300 font-semibold text-sm sm:text-base shadow-lg hover:shadow-xl transform hover:scale-[1.02]\"\n                      >\n                        Use This Template\n                      </button>\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"text-center text-gray-400 py-8 sm:py-12\">\n                    <div className=\"text-3xl sm:text-4xl mb-3 sm:mb-4\">📋</div>\n                    <h4 className=\"text-base sm:text-lg font-semibold mb-2\">Select a Template</h4>\n                    <p className=\"text-xs sm:text-sm\">Choose a template from the list to see detailed information</p>\n                  </div>\n                )}\n              </div>\n            </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AASe,SAAS,wBAAwB,EAC9C,MAAM,EACN,OAAO,EACP,cAAc,EACe;;IAC7B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAElF,MAAM,aAAa,MAAM,IAAI,CAAC,IAAI,IAAI,0HAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;IAC5E,MAAM,eAAe;QAAC;QAAS;QAAgB;KAAW;IAE1D,MAAM,4BAA4B,0HAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,CAAA;QAC1D,MAAM,kBAAkB,qBAAqB,SAAS,SAAS,QAAQ,KAAK;QAC5E,MAAM,oBAAoB,uBAAuB,SAAS,SAAS,UAAU,KAAK;QAClF,MAAM,gBAAgB,gBAAgB,MACpC,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACnE,SAAS,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAElE,OAAO,mBAAmB,qBAAqB;IACjD;IAEA,MAAM,6BAA6B,CAAC;QAClC,MAAM,eAAsC;YAC1C,aAAa,SAAS,WAAW;YACjC,aAAa,SAAS,IAAI;YAC1B,aAAa,GAAG,SAAS,WAAW,CAAC,qBAAqB,EAAE,SAAS,aAAa,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,qBAAqB,EAAE,SAAS,cAAc,CAAC,mBAAmB,EAAE,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO;YACxP,UAAU,SAAS,QAAQ;YAC3B,YAAY,SAAS,UAAU;YAC/B,cAAc,SAAS,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC3D,UAAU;oBACV,MAAM;oBACN,aAAa,GAAG,KAAK,KAAK,EAAE,SAAS,QAAQ,CAAC,WAAW,GAAG,UAAU,CAAC;gBACzE,CAAC;YACD,wBAAwB,CAAC,UAAU,EAAE,SAAS,QAAQ,CAAC,UAAU,EAAE,SAAS,MAAM,CAAC,sBAAsB,EAAE,SAAS,cAAc,CAAC,GAAG,CAAC,CAAA,SAAU,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,MAAM,0BAA0B,EAAE,SAAS,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO;QAC7P;QACA,eAAe;QACf;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;YAAiI,OAAO;gBACrJ,WAAW;YACb;;8BACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA8C;;;;;;sCAC5D,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAMH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKnC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,aAAY;4BACZ,WAAU;;;;;;sCAEZ,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4BACnD,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAM;;;;;;gCACnB,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;wCAAsB,OAAO;kDAAW;uCAA5B;;;;;;;;;;;sCAGjB,6LAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;4BACrD,WAAU;;8CAEV,6LAAC;oCAAO,OAAM;8CAAM;;;;;;gCACnB,aAAa,GAAG,CAAC,CAAA,2BAChB,6LAAC;wCAAwB,OAAO;wCAAY,WAAU;kDAAc;uCAAvD;;;;;;;;;;;;;;;;;8BAKnB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAW,CAAC,uBAAuB,EAAE,mBAAmB,oBAAoB,IAAI;;gCAClF,0BAA0B,GAAG,CAAC,CAAC,yBAC9B,6LAAC;wCAEC,WAAW,CAAC,oGAAoG,EAC9G,kBAAkB,OAAO,SAAS,EAAE,GAChC,kCACA,qBACJ;wCACF,SAAS,IAAM,oBAAoB;;0DAEnC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAmE,SAAS,IAAI;;;;;;kEAC9F,6LAAC;wDAAK,WAAW,CAAC,mDAAmD,EAAE,mBAAmB,SAAS,UAAU,GAAG;kEAC7G,SAAS,UAAU;;;;;;;;;;;;0DAGxB,6LAAC;gDAAE,WAAU;0DAAyC,SAAS,QAAQ;;;;;;0DACvE,6LAAC;gDAAE,WAAU;0DAAsD,SAAS,WAAW;;;;;;0DACvF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAiB,SAAS,QAAQ;;;;;;kEAClD,6LAAC;wDAAK,WAAU;kEAAY,SAAS,MAAM;;;;;;;;;;;;;uCAlBxC,SAAS,EAAE;;;;;gCAsBnB,0BAA0B,MAAM,KAAK,mBACpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;;;;;;;;sCAM7B,6LAAC;4BAAI,WAAW,CAAC,yDAAyD,EAAE,mBAAmB,kBAAkB,kBAAkB,CAAC,EAAE,mBAAmB,kBAAkB,IAAI;sCAC5K,iCACC,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;kDAI5B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAoD,iBAAiB,IAAI;;;;;;kEACvF,6LAAC;wDAAE,WAAU;kEAAyC,iBAAiB,QAAQ;;;;;;kEAC/E,6LAAC;wDAAE,WAAU;kEAAoC,iBAAiB,WAAW;;;;;;;;;;;;0DAG/E,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAmD;;;;;;kEACjE,6LAAC;wDAAI,WAAU;kEACZ,iBAAiB,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACvC,6LAAC;gEAAgB,WAAU;;oEAAmC;oEAAG;;+DAAvD;;;;;;;;;;;;;;;;0DAKhB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAmD;;;;;;kEACjE,6LAAC;wDAAI,WAAU;kEACZ,iBAAiB,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,sBACxC,6LAAC;gEAAiB,WAAU;0EACzB;+DADQ;;;;;;;;;;;;;;;;0DAOjB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAmD;;;;;;kEACjE,6LAAC;wDAAI,WAAU;kEACZ,iBAAiB,aAAa,CAAC,GAAG,CAAC,CAAC,MAAM,sBACzC,6LAAC;gEAAgB,WAAU;;oEAAmC;oEAAG;;+DAAvD;;;;;;;;;;;;;;;;0DAKhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAmC;;;;;;0EACnD,6LAAC;gEAAI,WAAU;0EAA+C,iBAAiB,QAAQ;;;;;;;;;;;;kEAEzF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAmC;;;;;;0EACnD,6LAAC;gEAAI,WAAU;0EAA+C,iBAAiB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;kDAM3F,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,SAAS,IAAM,2BAA2B;4CAC1C,WAAU;sDACX;;;;;;;;;;;;;;;;qDAML,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAoC;;;;;;kDACnD,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtD;GApOwB;KAAA", "debugId": null}}, {"offset": {"line": 10081, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/test/augment-prompt-generator/src/components/PromptGeneratorApp.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ProjectInput, ProjectType, Platform, Technology, Complexity } from '@/lib/types';\nimport { TECHNOLOGIES } from '@/lib/technologies';\n\nimport { aiService, PromptOptimizationOptions, AI_MODELS } from '@/lib/ai-service';\n\n\n\n\nimport AnimatedBackground from './AnimatedBackground';\nimport CustomSelect from './CustomSelect';\nimport AdvancedSettings from './AdvancedSettings';\nimport TypewriterTitle from './TypewriterTitle';\nimport ShimmerText from './ShimmerText';\nimport AITextLoading from './AITextLoading';\n\n// UUID generation fallback for environments without crypto.randomUUID\nconst generateUUID = (): string => {\n  if (typeof crypto !== 'undefined' && crypto.randomUUID) {\n    return crypto.randomUUID();\n  }\n  // Fallback UUID generation\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\n    return v.toString(16);\n  });\n};\n\n\n\n\nimport AdvancedTemplateBrowser from './AdvancedTemplateBrowser';\n\ninterface PromptHistory {\n  id: string;\n  timestamp: Date;\n  projectInput: ProjectInput;\n  generatedPrompt: string;\n  isFavorite: boolean;\n}\n\nexport default function PromptGeneratorApp() {\n  const [projectInput, setProjectInput] = useState<ProjectInput>({\n    projectName: '',\n    projectIdea: '',\n    projectType: 'web-application',\n    platform: 'web',\n    technologies: [],\n    complexity: 'intermediate',\n    features: [],\n    additionalRequirements: ''\n  });\n\n  const [generatedPrompt, setGeneratedPrompt] = useState<string>('');\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [generationProgress, setGenerationProgress] = useState(0);\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [touched, setTouched] = useState<Record<string, boolean>>({});\n  const [saveStatus, setSaveStatus] = useState<'saved' | 'saving' | 'error'>('saved');\n  const [promptHistory, setPromptHistory] = useState<PromptHistory[]>([]);\n  const [showHistory, setShowHistory] = useState(false);\n  const [suggestions, setSuggestions] = useState<string[]>([]);\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  const [showUserGuide, setShowUserGuide] = useState(false);\n  const [toast, setToast] = useState<{message: string, type: 'success' | 'error' | 'info'} | null>(null);\n  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);\n\n  const [showAdvancedTemplates, setShowAdvancedTemplates] = useState(false);\n  const [aiSettings, setAiSettings] = useState<PromptOptimizationOptions>({\n    model: 'deepseek/deepseek-chat-v3-0324:free',\n    optimizationLevel: 'enhanced',\n    includeExamples: true,\n    includeConstraints: true,\n    includeMetrics: false,\n    targetAudience: 'developer'\n  });\n  const [generationMetadata, setGenerationMetadata] = useState<{ model: string; tokensUsed: number; cost: number; optimizationLevel: string } | null>(null);\n\n  // Dropdown options\n  const projectTypeOptions = [\n    { value: 'web-application', label: 'Web Application', description: 'Frontend web apps, SPAs, and websites' },\n    { value: 'mobile-application', label: 'Mobile Application', description: 'iOS, Android, and cross-platform apps' },\n    { value: 'desktop-application', label: 'Desktop Application', description: 'Native desktop software and tools' },\n    { value: 'api-backend', label: 'API/Backend Service', description: 'REST APIs, GraphQL, and backend services' },\n    { value: 'data-analysis', label: 'Data Analysis Tool', description: 'Analytics, reporting, and data processing' },\n    { value: 'machine-learning', label: 'Machine Learning Project', description: 'AI/ML models and applications' },\n    { value: 'devops-infrastructure', label: 'DevOps Infrastructure', description: 'CI/CD, deployment, and automation' },\n    { value: 'chrome-extension', label: 'Chrome Extension', description: 'Browser extensions and add-ons' },\n    { value: 'cli-tool', label: 'CLI Tool', description: 'Command-line utilities and scripts' },\n    { value: 'library-package', label: 'Library/Package', description: 'Reusable libraries and npm packages' }\n  ];\n\n  const platformOptions = [\n    { value: 'web', label: 'Web', description: 'Browser-based applications' },\n    { value: 'mobile', label: 'Mobile', description: 'iOS and Android platforms' },\n    { value: 'desktop', label: 'Desktop', description: 'Windows, macOS, and Linux' },\n    { value: 'server', label: 'Server', description: 'Backend and cloud services' },\n    { value: 'cross-platform', label: 'Cross-Platform', description: 'Multiple platforms' }\n  ];\n\n  // Toast notification function\n  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'info') => {\n    setToast({ message, type });\n    setTimeout(() => setToast(null), 3000);\n  };\n\n  // Smart suggestions\n  const getProjectNameSuggestions = (input: string, projectType: ProjectType) => {\n    const suggestions: Record<ProjectType, string[]> = {\n      'web-application': ['TaskMaster Pro', 'DataViz Dashboard', 'EcoTracker', 'ShopSmart', 'ConnectHub'],\n      'mobile-application': ['FitTrack Mobile', 'LocalEats App', 'StudyBuddy', 'WeatherWise', 'PhotoShare'],\n      'desktop-application': ['CodeEditor Pro', 'MediaManager', 'TaskPlanner', 'FileSync', 'NoteTaker'],\n      'api-backend': ['UserAuth API', 'Payment Gateway', 'Analytics Service', 'Notification Hub', 'Data Processor'],\n      'data-analysis': ['Sales Analytics', 'Customer Insights', 'Market Research Tool', 'Performance Dashboard', 'Trend Analyzer'],\n      'machine-learning': ['Recommendation Engine', 'Image Classifier', 'Sentiment Analyzer', 'Fraud Detector', 'Chatbot AI'],\n      'devops-infrastructure': ['CI/CD Pipeline', 'Monitoring Stack', 'Container Platform', 'Auto Scaler', 'Log Aggregator'],\n      'chrome-extension': ['Productivity Booster', 'Tab Manager', 'Password Helper', 'Web Scraper', 'Time Tracker'],\n      'cli-tool': ['File Processor', 'Code Generator', 'Deployment Tool', 'Data Migrator', 'System Monitor'],\n      'library-package': ['UI Components', 'Utility Library', 'API Client', 'Data Validator', 'Chart Library']\n    };\n\n    return suggestions[projectType]?.filter(s =>\n      s.toLowerCase().includes(input.toLowerCase())\n    ) || [];\n  };\n\n  // Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.ctrlKey || event.metaKey) {\n        switch (event.key) {\n          case 'Enter':\n            event.preventDefault();\n            if (!isGenerating) {\n              handleGenerate();\n            }\n            break;\n          case 's':\n            event.preventDefault();\n            // Auto-save is already handled\n            break;\n          case 'k':\n            event.preventDefault();\n            document.querySelector<HTMLInputElement>('input[type=\"text\"]')?.focus();\n            break;\n          case 'h':\n            event.preventDefault();\n            setShowHistory(true);\n            break;\n\n          case 't':\n            event.preventDefault();\n            setShowAdvancedTemplates(true);\n            break;\n          case ',':\n            event.preventDefault();\n            setShowAdvancedSettings(true);\n            break;\n          case 'a':\n            event.preventDefault();\n            setShowAdvancedSettings(true);\n            break;\n\n          case 'b':\n            event.preventDefault();\n            setShowAdvancedTemplates(true);\n            break;\n          case 'Escape':\n            event.preventDefault();\n            setShowHistory(false);\n            setShowAdvancedTemplates(false);\n            setShowAdvancedSettings(false);\n\n            setShowAdvancedTemplates(false);\n            break;\n        }\n      }\n\n      if (event.key === 'Escape') {\n        setShowHistory(false);\n        setShowAdvancedTemplates(false);\n        setShowAdvancedSettings(false);\n\n        setShowAdvancedTemplates(false);\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [isGenerating]);\n\n  // Auto-load saved data on component mount\n  useEffect(() => {\n    const savedData = localStorage.getItem('promptGenerator_draft');\n    if (savedData) {\n      try {\n        const parsed = JSON.parse(savedData);\n        setProjectInput(parsed);\n      } catch (error) {\n        console.error('Failed to load saved data:', error);\n      }\n    }\n\n    const savedHistory = localStorage.getItem('promptHistory');\n    if (savedHistory) {\n      try {\n        const parsed = JSON.parse(savedHistory);\n        setPromptHistory(parsed.map((item: { timestamp: string; [key: string]: unknown }) => ({\n          ...item,\n          timestamp: new Date(item.timestamp)\n        })));\n      } catch (error) {\n        console.error('Failed to load history:', error);\n      }\n    }\n  }, []);\n\n  // History management functions\n  const saveToHistory = (input: ProjectInput, prompt: string) => {\n    const historyItem: PromptHistory = {\n      id: generateUUID(),\n      timestamp: new Date(),\n      projectInput: input,\n      generatedPrompt: prompt,\n      isFavorite: false,\n    };\n\n    const newHistory = [historyItem, ...promptHistory.slice(0, 19)]; // Keep last 20\n    setPromptHistory(newHistory);\n    localStorage.setItem('promptHistory', JSON.stringify(newHistory));\n  };\n\n  const toggleFavorite = (id: string) => {\n    const newHistory = promptHistory.map(item =>\n      item.id === id ? { ...item, isFavorite: !item.isFavorite } : item\n    );\n    setPromptHistory(newHistory);\n    localStorage.setItem('promptHistory', JSON.stringify(newHistory));\n  };\n\n  const loadFromHistory = (historyItem: PromptHistory) => {\n    setProjectInput(historyItem.projectInput);\n    setGeneratedPrompt(historyItem.generatedPrompt);\n    setShowHistory(false);\n  };\n\n\n\n  const loadAdvancedTemplate = (templateData: Partial<ProjectInput>) => {\n    setProjectInput(prev => ({\n      ...prev,\n      ...templateData\n    }));\n    showToast(`Loaded advanced template: ${templateData.projectName}`, 'success');\n\n\n  };\n\n  // Validation functions\n  const validateField = (name: string, value: string) => {\n    const newErrors = { ...errors };\n\n    switch (name) {\n      case 'projectName':\n        if (!value.trim()) {\n          newErrors.projectName = 'Project name is required';\n        } else if (value.length < 3) {\n          newErrors.projectName = 'Project name must be at least 3 characters';\n        } else if (value.length > 50) {\n          newErrors.projectName = 'Project name must be less than 50 characters';\n        } else {\n          delete newErrors.projectName;\n        }\n        break;\n      case 'projectIdea':\n        if (!value.trim()) {\n          newErrors.projectIdea = 'Project concept is required';\n        } else if (value.length < 20) {\n          newErrors.projectIdea = 'Please provide more details (minimum 20 characters)';\n        } else if (value.length > 1000) {\n          newErrors.projectIdea = 'Project concept must be less than 1000 characters';\n        } else {\n          delete newErrors.projectIdea;\n        }\n        break;\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const validateForm = () => {\n    const nameValid = validateField('projectName', projectInput.projectName);\n    const ideaValid = validateField('projectIdea', projectInput.projectIdea);\n    return nameValid && ideaValid;\n  };\n\n  const handleInputChange = (field: keyof ProjectInput, value: ProjectInput[keyof ProjectInput]) => {\n    setProjectInput(prev => ({ ...prev, [field]: value }));\n    setTouched(prev => ({ ...prev, [field]: true }));\n\n    if (field === 'projectName' || field === 'projectIdea') {\n      validateField(field, value as string);\n    }\n\n    // Auto-save functionality\n    setSaveStatus('saving');\n    setTimeout(() => {\n      try {\n        const updatedInput = { ...projectInput, [field]: value };\n        localStorage.setItem('promptGenerator_draft', JSON.stringify(updatedInput));\n        setSaveStatus('saved');\n      } catch (error) {\n        setSaveStatus('error');\n        showToast('Failed to auto-save', 'error');\n      }\n    }, 1000);\n  };\n\n  const handleGenerate = async () => {\n    // Validate form before generating\n    if (!validateForm()) {\n      setTouched({ projectName: true, projectIdea: true });\n      return;\n    }\n\n    setIsGenerating(true);\n    setGenerationProgress(0);\n\n    // Simulate progress updates\n    const progressInterval = setInterval(() => {\n      setGenerationProgress(prev => Math.min(prev + 10, 90));\n    }, 200);\n\n    try {\n      // Get AI model recommendation if not set\n      const recommendedModel = aiService.getModelRecommendation(projectInput);\n      const currentSettings = { ...aiSettings };\n      if (!currentSettings.model || currentSettings.model === 'auto') {\n        currentSettings.model = recommendedModel;\n      }\n\n      // Use AI service for generation\n      const result = await aiService.generatePrompt(projectInput, currentSettings);\n\n      setGenerationProgress(100);\n      setGeneratedPrompt(result.prompt);\n      setGenerationMetadata(result.metadata);\n\n      // Save to history with metadata\n      saveToHistory(projectInput, result.prompt);\n\n\n\n      // Show success with model info\n      const modelName = Object.values(AI_MODELS).find(m => m.id === currentSettings.model)?.name || 'AI';\n      showToast(`Prompt generated with ${modelName}`, 'success');\n\n    } catch (error) {\n      console.error('Error generating prompt:', error);\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n      setErrors({ general: `Error generating prompt: ${errorMessage}` });\n      showToast('Failed to generate prompt', 'error');\n\n\n    } finally {\n      clearInterval(progressInterval);\n      setIsGenerating(false);\n      setTimeout(() => setGenerationProgress(0), 1000);\n    }\n  };\n\n  // Enhanced export functions\n  const exportOptions = {\n    copyToClipboard: async (text: string) => {\n      try {\n        await navigator.clipboard.writeText(text);\n        showToast('Copied to clipboard!', 'success');\n\n      } catch {\n        showToast('Failed to copy to clipboard', 'error');\n      }\n    },\n\n    downloadAsFile: (text: string, filename: string) => {\n      try {\n        const blob = new Blob([text], { type: 'text/plain' });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `${filename}.txt`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        showToast('File downloaded successfully!', 'success');\n\n      } catch {\n        showToast('Failed to download file', 'error');\n      }\n    },\n\n    shareViaAPI: async (text: string) => {\n      if (navigator.share) {\n        try {\n          await navigator.share({\n            title: 'AI Generated Prompt for Augment Agent',\n            text: text,\n          });\n          showToast('Shared successfully!', 'success');\n\n        } catch (error) {\n          if (error instanceof Error && error.name !== 'AbortError') {\n            showToast('Failed to share', 'error');\n          }\n        }\n      } else {\n        showToast('Sharing not supported on this device', 'info');\n      }\n    }\n  };\n\n  const copyToClipboard = async () => {\n    await exportOptions.copyToClipboard(generatedPrompt);\n  };\n\n\n\n  const clearForm = () => {\n    setProjectInput({\n      projectName: '',\n      projectIdea: '',\n      projectType: 'web-application',\n      platform: 'web',\n      technologies: [],\n      complexity: 'intermediate',\n      features: [],\n      additionalRequirements: ''\n    });\n    setGeneratedPrompt('');\n    setErrors({});\n    setTouched({});\n    showToast('Form cleared', 'info');\n  };\n\n  const handleTechnologyToggle = (tech: Technology) => {\n    setProjectInput(prev => ({\n      ...prev,\n      technologies: prev.technologies.some(t => t.name === tech.name)\n        ? prev.technologies.filter(t => t.name !== tech.name)\n        : [...prev.technologies, tech]\n    }));\n  };\n\n  return (\n    <div className=\"min-h-screen bg-black py-4 sm:py-8 px-2 sm:px-4 relative\">\n      <AnimatedBackground />\n      <div className=\"max-w-6xl mx-auto relative z-10\">\n        <header className=\"text-center mb-4 sm:mb-8 px-2\">\n          <TypewriterTitle\n            sequences={[\n              { text: \"AI Prompt Generator\", deleteAfter: true, pauseAfter: 1200 },\n              { text: \"Professional Prompts\", deleteAfter: true, pauseAfter: 1200 },\n              { text: \"DeepSeek V3 Powered\", deleteAfter: true, pauseAfter: 1200 },\n              { text: \"Augment Agent Ready\", deleteAfter: true, pauseAfter: 2000 }\n            ]}\n            typingSpeed={60}\n            autoLoop={true}\n            loopDelay={3000}\n            className=\"mb-4\"\n          />\n          <ShimmerText\n            text=\"AI-Powered Prompt Generation with DeepSeek V3 for Augment Agent\"\n            className=\"text-sm sm:text-base lg:text-lg\"\n          />\n        </header>\n\n        <div className=\"space-y-4 sm:space-y-8\">\n          {/* Input Form */}\n          <div className=\"rounded-xl shadow-2xl p-3 sm:p-4 lg:p-6 transition-all duration-300 ease-in-out max-w-2xl mx-auto\" style={{\n            boxShadow: '0 20px 40px -12px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.03)'\n          }}>\n            <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center mb-3 space-y-2 sm:space-y-0\">\n              <ShimmerText text=\"Project Details\" className=\"text-lg sm:text-xl font-semibold\" />\n              <div className=\"flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2\">\n                {/* Status indicators removed for cleaner UI - auto-save still works in background */}\n\n                <button\n                  onClick={clearForm}\n                  className=\"bg-black/5 text-white px-3 py-2 sm:px-2 sm:py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg min-h-[44px] sm:min-h-auto\"\n                  style={{\n                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'\n                  }}\n                >\n                  Clear\n                </button>\n                <button\n                  onClick={() => setShowHistory(true)}\n                  className=\"bg-black/5 text-white px-3 py-2 sm:px-2 sm:py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg min-h-[44px] sm:min-h-auto\"\n                  style={{\n                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'\n                  }}\n                >\n                  History ({promptHistory.length})\n                </button>\n                <button\n                  onClick={() => setShowAdvancedTemplates(true)}\n                  className=\"bg-gray-800 text-white px-3 py-2 sm:px-2 sm:py-1 rounded-md hover:bg-gray-700 transition-all duration-300 text-xs hover:shadow-lg min-h-[44px] sm:min-h-auto\"\n                  style={{\n                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'\n                  }}\n                >\n                  Industry Templates\n                </button>\n                <button\n                  onClick={() => setShowAdvancedSettings(true)}\n                  className=\"bg-black/5 text-white px-3 py-2 sm:px-2 sm:py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg min-h-[44px] sm:min-h-auto\"\n                  style={{\n                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'\n                  }}\n                >\n                  AI Settings & Integrations\n                </button>\n\n              </div>\n            </div>\n\n            {/* Error/Success Messages */}\n            {errors.general && (\n              <div className=\"mb-3 p-2 bg-gray-800 border border-gray-600 rounded-md\">\n                <p className=\"text-gray-200 text-xs\">{errors.general}</p>\n              </div>\n            )}\n            {errors.success && (\n              <div className=\"mb-3 p-2 bg-gray-800 border border-gray-600 rounded-md\">\n                <p className=\"text-gray-200 text-xs\">{errors.success}</p>\n              </div>\n            )}\n\n            {/* Project Name */}\n            <div className=\"mb-3 sm:mb-2 relative\">\n              <label className=\"block text-sm sm:text-xs font-medium mb-2 sm:mb-1\">\n                <ShimmerText text=\"Project Name *\" className=\"text-sm sm:text-xs\" />\n              </label>\n              <input\n                type=\"text\"\n                value={projectInput.projectName}\n                onChange={(e) => {\n                  handleInputChange('projectName', e.target.value);\n                  const newSuggestions = getProjectNameSuggestions(e.target.value, projectInput.projectType);\n                  setSuggestions(newSuggestions);\n                  setShowSuggestions(e.target.value.length > 0 && newSuggestions.length > 0);\n                }}\n                onBlur={() => {\n                  setTouched(prev => ({ ...prev, projectName: true }));\n                  setTimeout(() => setShowSuggestions(false), 200);\n                }}\n                onFocus={() => {\n                  const newSuggestions = getProjectNameSuggestions(projectInput.projectName, projectInput.projectType);\n                  setSuggestions(newSuggestions);\n                  setShowSuggestions(projectInput.projectName.length > 0 && newSuggestions.length > 0);\n                }}\n                className={`w-full px-3 py-3 sm:px-2 sm:py-1.5 rounded-md focus:outline-none focus:ring-1 text-base sm:text-sm text-white bg-black/5 backdrop-blur-md placeholder-gray-400 transition-all duration-300 hover:bg-black/10 min-h-[44px] sm:min-h-auto ${\n                  errors.projectName && touched.projectName\n                    ? 'ring-red-400 border-red-400'\n                    : 'focus:ring-white/20'\n                }`}\n                style={{\n                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'\n                }}\n                placeholder=\"e.g., TaskMaster Pro\"\n              />\n\n              {/* Suggestions Dropdown */}\n              {showSuggestions && suggestions.length > 0 && (\n                <div className=\"absolute z-10 w-full mt-1 bg-black/20 backdrop-blur-xl rounded-md shadow-lg border border-white/10 max-h-40 overflow-y-auto\">\n                  {suggestions.map((suggestion, index) => (\n                    <button\n                      key={index}\n                      type=\"button\"\n                      onClick={() => {\n                        handleInputChange('projectName', suggestion);\n                        setShowSuggestions(false);\n                      }}\n                      className=\"w-full text-left px-3 py-2 text-sm text-white hover:bg-black/20 transition-all duration-200 first:rounded-t-md last:rounded-b-md\"\n                    >\n                      {suggestion}\n                    </button>\n                  ))}\n                </div>\n              )}\n\n              {errors.projectName && touched.projectName && (\n                <p className=\"text-gray-300 text-xs mt-1\">{errors.projectName}</p>\n              )}\n              <div className=\"text-xs text-gray-500 mt-1\">\n                {projectInput.projectName.length}/50 characters\n              </div>\n            </div>\n\n            {/* Project Idea */}\n            <div className=\"mb-2\">\n              <label className=\"block text-xs font-medium mb-1\">\n                <ShimmerText text=\"Project Concept *\" className=\"text-xs\" />\n              </label>\n              <textarea\n                value={projectInput.projectIdea}\n                onChange={(e) => handleInputChange('projectIdea', e.target.value)}\n                onBlur={() => setTouched(prev => ({ ...prev, projectIdea: true }))}\n                className={`w-full px-2 py-1.5 rounded-md focus:outline-none focus:ring-1 h-16 text-sm text-white bg-black/5 backdrop-blur-md placeholder-gray-400 transition-all duration-300 hover:bg-black/10 ${\n                  errors.projectIdea && touched.projectIdea\n                    ? 'ring-red-400 border-red-400'\n                    : 'focus:ring-white/20'\n                }`}\n                style={{\n                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'\n                }}\n                placeholder=\"Describe your project idea, goals, and target users...\"\n              />\n              {errors.projectIdea && touched.projectIdea && (\n                <p className=\"text-gray-300 text-xs mt-1\">{errors.projectIdea}</p>\n              )}\n              <div className=\"text-xs text-gray-500 mt-1 space-y-1\">\n                <div className=\"flex justify-between\">\n                  <span>{projectInput.projectIdea.length}/1000 characters</span>\n                  <span className={`${\n                    projectInput.projectIdea.length < 50 ? 'text-gray-400' :\n                    projectInput.projectIdea.length < 100 ? 'text-gray-300' :\n                    'text-white'\n                  }`}>\n                    {projectInput.projectIdea.length < 50 ? 'Too short' :\n                     projectInput.projectIdea.length < 100 ? 'Good start' :\n                     'Great detail!'}\n                  </span>\n                </div>\n                {projectInput.projectIdea.length < 100 && (\n                  <div className=\"text-gray-300 text-xs\">\n                    💡 Tip: Include target users, main features, and business goals for better AI prompts\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Project Type */}\n            <div className=\"mb-2\">\n              <label className=\"block text-xs font-medium mb-1\">\n                <ShimmerText text=\"Project Type\" className=\"text-xs\" />\n              </label>\n              <CustomSelect\n                value={projectInput.projectType}\n                onChange={(value) => handleInputChange('projectType', value as ProjectType)}\n                options={projectTypeOptions}\n                placeholder=\"Select project type\"\n              />\n            </div>\n\n            {/* Platform */}\n            <div className=\"mb-2\">\n              <label className=\"block text-xs font-medium mb-1\">\n                <ShimmerText text=\"Target Platform\" className=\"text-xs\" />\n              </label>\n              <CustomSelect\n                value={projectInput.platform}\n                onChange={(value) => handleInputChange('platform', value as Platform)}\n                options={platformOptions}\n                placeholder=\"Select target platform\"\n              />\n            </div>\n\n            {/* Complexity */}\n            <div className=\"mb-2\">\n              <label className=\"block text-xs font-medium mb-1\">\n                <ShimmerText text=\"Complexity Level\" className=\"text-xs\" />\n              </label>\n              <div className=\"flex space-x-2\">\n                {(['simple', 'intermediate', 'advanced'] as Complexity[]).map((level) => (\n                  <label\n                    key={level}\n                    className={`group relative flex-1 cursor-pointer transition-all duration-300 ${\n                      projectInput.complexity === level ? 'transform scale-[1.02]' : ''\n                    }`}\n                  >\n                    <input\n                      type=\"radio\"\n                      value={level}\n                      checked={projectInput.complexity === level}\n                      onChange={(e) => handleInputChange('complexity', e.target.value as Complexity)}\n                      className=\"sr-only\"\n                    />\n                    <div className={`\n                      px-4 py-3 rounded-lg text-center text-sm font-medium transition-all duration-300 border backdrop-blur-xl\n                      ${projectInput.complexity === level\n                        ? 'bg-gradient-to-br from-white/20 to-white/10 border-white/30 text-white shadow-lg ring-2 ring-white/20'\n                        : 'bg-gradient-to-br from-gray-800/90 to-gray-900/90 border-white/10 text-gray-300 hover:border-white/20 hover:from-gray-700/90 hover:to-gray-800/90 hover:text-white'\n                      }\n                    `}\n                    style={{\n                      boxShadow: projectInput.complexity === level\n                        ? '0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1)'\n                        : '0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'\n                    }}\n                    >\n                      <span className=\"capitalize\">{level}</span>\n                    </div>\n                  </label>\n                ))}\n              </div>\n            </div>\n\n            {/* Technology Selection */}\n            <div className=\"mb-2\">\n              <label className=\"block text-xs font-medium mb-1\">\n                <ShimmerText text=\"Technology Stack (Optional)\" className=\"text-xs\" />\n              </label>\n              <div className=\"space-y-2\">\n                {Object.entries(TECHNOLOGIES).map(([category, techs]) => (\n                  <div key={category}>\n                    <h4 className=\"font-medium text-white mb-1 capitalize text-xs\">\n                      {category.replace('-', ' ')}\n                    </h4>\n                    <div className=\"flex flex-wrap gap-1\">\n                      {techs.map((tech) => (\n                        <button\n                          key={tech.name}\n                          type=\"button\"\n                          onClick={() => handleTechnologyToggle(tech)}\n                          className={`px-2 py-0.5 text-xs rounded-full transition-all duration-300 ease-in-out ${\n                            projectInput.technologies.some(t => t.name === tech.name)\n                              ? 'bg-white/10 text-white backdrop-blur-md shadow-lg hover:bg-white/15'\n                              : 'bg-black/5 text-white backdrop-blur-md hover:bg-black/10 hover:shadow-lg'\n                          }`}\n                          style={{\n                            boxShadow: projectInput.technologies.some(t => t.name === tech.name)\n                              ? '0 2px 8px rgba(255, 255, 255, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.05)'\n                              : '0 1px 4px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'\n                          }}\n                        >\n                          {tech.name}\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Additional Requirements */}\n            <div className=\"mb-2\">\n              <label className=\"block text-xs font-medium mb-1\">\n                <ShimmerText text=\"Additional Requirements (Optional)\" className=\"text-xs\" />\n              </label>\n              <textarea\n                value={projectInput.additionalRequirements}\n                onChange={(e) => handleInputChange('additionalRequirements', e.target.value)}\n                className=\"w-full px-2 py-1.5 rounded-md focus:outline-none focus:ring-1 focus:ring-white/20 h-12 text-sm text-white bg-black/5 backdrop-blur-md placeholder-gray-400 transition-all duration-300 hover:bg-black/10\"\n                style={{\n                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'\n                }}\n                placeholder=\"Any specific requirements, constraints, or preferences...\"\n              />\n            </div>\n\n            {/* Generate Button - AI ONLY */}\n            <div className=\"space-y-1.5\">\n              <button\n                onClick={handleGenerate}\n                disabled={isGenerating || !projectInput.projectName || !projectInput.projectIdea}\n                className=\"w-full bg-black/10 text-white py-2 px-4 rounded-lg hover:bg-black/20 disabled:bg-gray-600/10 disabled:cursor-not-allowed transition-all duration-300 font-semibold text-sm backdrop-blur-md hover:shadow-xl\"\n                style={{\n                  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'\n                }}\n              >\n                {isGenerating ? (\n                  <div className=\"flex flex-col items-center space-y-4\">\n                    <AITextLoading\n                      texts={[\n                        \"Analyzing requirements...\",\n                        \"Structuring prompt...\",\n                        \"Optimizing for AI...\",\n                        \"Finalizing output...\",\n                        \"Almost ready...\"\n                      ]}\n                      interval={1800}\n                      className=\"text-lg\"\n                    />\n                    <div className=\"w-full bg-black/20 rounded-full h-2\">\n                      <div\n                        className=\"bg-gradient-to-r from-white to-gray-300 h-2 rounded-full transition-all duration-500 ease-out\"\n                        style={{ width: `${generationProgress}%` }}\n                      ></div>\n                    </div>\n                    <span className=\"text-xs text-gray-400\">{generationProgress}%</span>\n                  </div>\n                ) : (\n                  'Generate AI Prompt'\n                )}\n              </button>\n\n\n            </div>\n          </div>\n\n          {/* Generated Prompt Display */}\n          <div className=\"rounded-xl shadow-2xl p-3 transition-all duration-300 ease-in-out max-w-2xl mx-auto\" style={{\n            boxShadow: '0 20px 40px -12px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.03)'\n          }}>\n            <div className=\"flex justify-between items-center mb-2\">\n              <div>\n                <ShimmerText text=\"Generated Prompt\" className=\"text-xl font-semibold\" />\n                {generatedPrompt && (\n                  <div className=\"text-xs text-gray-400 mt-1 space-y-1\">\n                    <div>\n                      {generatedPrompt.split(' ').length} words • {Math.ceil(generatedPrompt.split(' ').length / 200)} min read\n                    </div>\n                    {generationMetadata && (\n                      <div className=\"flex items-center space-x-2\">\n                        <span>Generated with {Object.values(AI_MODELS).find(m => m.id === generationMetadata.model)?.name || 'AI'}</span>\n                        {generationMetadata.tokensUsed && (\n                          <span>• {generationMetadata.tokensUsed} tokens</span>\n                        )}\n                        <span className={`px-1.5 py-0.5 rounded text-xs ${\n                          generationMetadata.optimizationLevel === 'expert' ? 'bg-gray-700 text-gray-200' :\n                          generationMetadata.optimizationLevel === 'enhanced' ? 'bg-gray-600 text-gray-200' :\n                          'bg-gray-500 text-gray-200'\n                        }`}>\n                          {generationMetadata.optimizationLevel}\n                        </span>\n                      </div>\n                    )}\n                  </div>\n                )}\n              </div>\n              {generatedPrompt && (\n                <div className=\"flex space-x-2\">\n                  <button\n                    onClick={copyToClipboard}\n                    className=\"bg-black/5 text-white px-2 py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg\"\n                    style={{\n                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'\n                    }}\n                  >\n                    Copy\n                  </button>\n                  <button\n                    onClick={() => exportOptions.downloadAsFile(generatedPrompt, `augment-prompt-${Date.now()}`)}\n                    className=\"bg-black/5 text-white px-2 py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg\"\n                    style={{\n                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'\n                    }}\n                  >\n                    Download\n                  </button>\n                  {typeof navigator !== 'undefined' && 'share' in navigator && (\n                    <button\n                      onClick={() => exportOptions.shareViaAPI(generatedPrompt)}\n                      className=\"bg-black/5 text-white px-2 py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg\"\n                      style={{\n                        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'\n                      }}\n                    >\n                      Share\n                    </button>\n                  )}\n\n                </div>\n              )}\n            </div>\n\n            {generatedPrompt ? (\n              <div className=\"bg-black/5 backdrop-blur-md rounded-lg p-2 max-h-60 overflow-y-auto transition-all duration-300 hover:bg-black/10\" style={{\n                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'\n              }}>\n                <pre className=\"whitespace-pre-wrap text-xs text-white font-mono\">\n                  {generatedPrompt}\n                </pre>\n              </div>\n            ) : (\n              <div className=\"bg-black/3 backdrop-blur-lg rounded-lg p-4 text-center transition-all duration-300 hover:bg-black/8\" style={{\n                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'\n              }}>\n                <h3 className=\"text-lg font-semibold text-white mb-2\">AI-Powered Prompt Generation</h3>\n                <p className=\"text-gray-400 mb-2 text-sm\">Fill in your project details and let DeepSeek V3 create the perfect Augment Agent prompt for you.</p>\n                <div className=\"text-xs text-white font-medium\">\n                  Personalized • Optimized • Professional\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* User Guide Toggle Button */}\n        <div className=\"mt-8 sm:mt-16 text-center\">\n          <button\n            onClick={() => setShowUserGuide(!showUserGuide)}\n            className=\"bg-black/5 backdrop-blur-xl text-white px-6 py-3 sm:px-4 sm:py-2 rounded-xl hover:bg-black/10 transition-all duration-300 ease-in-out min-h-[44px] sm:min-h-auto text-base sm:text-sm font-medium\"\n            style={{\n              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.05)'\n            }}\n          >\n            📖 {showUserGuide ? 'Hide' : 'Show'} Complete User Guide\n          </button>\n        </div>\n\n        {/* Comprehensive Documentation Section */}\n        <div className={`mt-4 sm:mt-8 rounded-2xl shadow-2xl transition-all duration-500 ease-in-out overflow-hidden ${\n          showUserGuide ? 'max-h-none opacity-100 p-4 sm:p-6' : 'max-h-0 opacity-0 p-0'\n        }`} style={{\n          boxShadow: showUserGuide ? '0 25px 50px -12px rgba(0, 0, 0, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.05)' : 'none'\n        }}>\n          {showUserGuide && (\n            <>\n              <div className=\"flex justify-between items-center mb-4 sm:mb-6\">\n                <h2 className=\"text-2xl sm:text-3xl font-bold text-white\">Complete User Guide</h2>\n                <button\n                  onClick={() => setShowUserGuide(false)}\n                  className=\"bg-black/5 text-white px-3 py-2 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg min-h-[44px] sm:min-h-auto\"\n                  style={{\n                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'\n                  }}\n                >\n                  ✕ Close\n                </button>\n              </div>\n              <p className=\"text-center text-gray-400 mb-4 sm:mb-6 text-sm sm:text-lg\">\n                Learn how to use each feature effectively to generate the best AI-powered prompts for your projects\n              </p>\n\n              <div className=\"space-y-6\">\n            {/* Project Details Section */}\n            <div className=\"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out\" style={{\n              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'\n            }}>\n              <h3 className=\"text-2xl font-bold text-white mb-3\">Project Details</h3>\n\n              <div className=\"space-y-4\">\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Project Name</h4>\n                  <p className=\"text-gray-400\">\n                    Enter a descriptive name for your project. This helps the AI understand the scope and purpose.\n                    Examples: &ldquo;TaskMaster Pro&rdquo;, &ldquo;E-commerce Platform&rdquo;, &ldquo;Mobile Banking App&rdquo;\n                  </p>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Project Concept</h4>\n                  <p className=\"text-gray-400\">\n                    Describe your project idea in detail. Include the main purpose, target users, key features, and goals.\n                    The more specific you are, the better the AI can tailor the prompt to your needs.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Project Types Section */}\n            <div className=\"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out\" style={{\n              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'\n            }}>\n              <h3 className=\"text-2xl font-bold text-white mb-3\">Project Types</h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Web Application</h4>\n                  <p className=\"text-gray-400 text-sm\">\n                    Choose this for websites, web apps, or browser-based applications. Includes both frontend and backend development.\n                  </p>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Mobile Application</h4>\n                  <p className=\"text-gray-400 text-sm\">\n                    Select for iOS, Android, or cross-platform mobile apps. Covers native and hybrid development approaches.\n                  </p>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Desktop Application</h4>\n                  <p className=\"text-gray-400 text-sm\">\n                    For Windows, macOS, or Linux desktop software. Includes cross-platform and native desktop solutions.\n                  </p>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">API/Backend Service</h4>\n                  <p className=\"text-gray-400 text-sm\">\n                    Choose for server-side applications, REST APIs, microservices, or backend systems without a user interface.\n                  </p>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Data Analysis Tool</h4>\n                  <p className=\"text-gray-400 text-sm\">\n                    For data processing, analytics, visualization, or machine learning applications and tools.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Platform Options Section */}\n            <div className=\"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out\" style={{\n              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'\n            }}>\n              <h3 className=\"text-2xl font-bold text-white mb-3\">Platform Options</h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Web</h4>\n                  <p className=\"text-gray-400 text-sm\">\n                    Browser-based applications accessible via web browsers. Includes responsive design for all devices.\n                  </p>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Mobile</h4>\n                  <p className=\"text-gray-400 text-sm\">\n                    Applications designed specifically for smartphones and tablets, with mobile-optimized interfaces.\n                  </p>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Desktop</h4>\n                  <p className=\"text-gray-400 text-sm\">\n                    Native applications that run directly on desktop operating systems like Windows, macOS, or Linux.\n                  </p>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Server</h4>\n                  <p className=\"text-gray-400 text-sm\">\n                    Backend services, APIs, and server-side applications that run on servers or cloud infrastructure.\n                  </p>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Cloud</h4>\n                  <p className=\"text-gray-400 text-sm\">\n                    Cloud-native applications designed to leverage cloud services and distributed computing resources.\n                  </p>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Cross-platform</h4>\n                  <p className=\"text-gray-400 text-sm\">\n                    Applications that work across multiple platforms using frameworks like Electron, Flutter, or React Native.\n                  </p>\n                </div>\n              </div>\n            </div>\n            {/* Complexity Levels Section */}\n            <div className=\"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out\" style={{\n              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'\n            }}>\n              <h3 className=\"text-2xl font-bold text-white mb-3\">Complexity Levels</h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Simple</h4>\n                  <p className=\"text-gray-400 text-sm\">\n                    Basic functionality with minimal features. Quick to develop, perfect for MVPs, prototypes, or learning projects.\n                    Focuses on core features only.\n                  </p>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Intermediate</h4>\n                  <p className=\"text-gray-400 text-sm\">\n                    Moderate complexity with additional features, user authentication, database integration, and proper error handling.\n                    Suitable for most business applications.\n                  </p>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Advanced</h4>\n                  <p className=\"text-gray-400 text-sm\">\n                    Enterprise-level complexity with scalability, performance optimization, advanced security, microservices,\n                    and comprehensive testing strategies.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Technology Stack Section */}\n            <div className=\"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out\" style={{\n              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'\n            }}>\n              <h3 className=\"text-2xl font-bold text-white mb-3\">Technology Stack Guide</h3>\n              <p className=\"text-gray-400 mb-4\">\n                A technology stack is the combination of programming languages, frameworks, libraries, and tools used to build your application.\n                Selecting the right technologies is crucial for project success.\n              </p>\n\n              <div className=\"space-y-4\">\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Frontend Technologies</h4>\n                  <p className=\"text-gray-400 text-sm mb-2\">\n                    Frontend development creates the user interface and user experience. Choose technologies based on your project needs:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400\">\n                    <div><strong className=\"text-white\">React:</strong> Popular, component-based, large ecosystem</div>\n                    <div><strong className=\"text-white\">Vue.js:</strong> Progressive, easy to learn, flexible</div>\n                    <div><strong className=\"text-white\">Angular:</strong> Full framework, TypeScript-based, enterprise-ready</div>\n                    <div><strong className=\"text-white\">Next.js:</strong> React framework with SSR and static generation</div>\n                    <div><strong className=\"text-white\">Svelte:</strong> Compile-time optimized, smaller bundle sizes</div>\n                    <div><strong className=\"text-white\">TypeScript:</strong> Adds type safety to JavaScript</div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Backend Technologies</h4>\n                  <p className=\"text-gray-400 text-sm mb-2\">\n                    Backend development handles server-side logic, databases, and APIs:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400\">\n                    <div><strong className=\"text-white\">Node.js:</strong> JavaScript runtime, fast development</div>\n                    <div><strong className=\"text-white\">Python/Django:</strong> Rapid development, batteries included</div>\n                    <div><strong className=\"text-white\">Python/FastAPI:</strong> Modern, fast, automatic API docs</div>\n                    <div><strong className=\"text-white\">Go:</strong> High performance, excellent concurrency</div>\n                    <div><strong className=\"text-white\">Rust:</strong> Memory safety, extreme performance</div>\n                    <div><strong className=\"text-white\">Java/Spring:</strong> Enterprise-grade, mature ecosystem</div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Database Options</h4>\n                  <p className=\"text-gray-400 text-sm mb-2\">\n                    Choose databases based on your data structure and scalability needs:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400\">\n                    <div><strong className=\"text-white\">PostgreSQL:</strong> Advanced relational database, JSON support</div>\n                    <div><strong className=\"text-white\">MongoDB:</strong> NoSQL document database, flexible schema</div>\n                    <div><strong className=\"text-white\">SQLite:</strong> Lightweight, embedded, perfect for small apps</div>\n                    <div><strong className=\"text-white\">Redis:</strong> In-memory store, caching, real-time features</div>\n                    <div><strong className=\"text-white\">Supabase:</strong> Open-source Firebase alternative</div>\n                    <div><strong className=\"text-white\">Firebase:</strong> Google's platform, real-time features</div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Styling Technologies</h4>\n                  <p className=\"text-gray-400 text-sm mb-2\">\n                    Choose styling approaches based on your project needs and team preferences:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400\">\n                    <div><strong className=\"text-white\">Tailwind CSS:</strong> Utility-first CSS framework</div>\n                    <div><strong className=\"text-white\">CSS Modules:</strong> Scoped CSS with local class names</div>\n                    <div><strong className=\"text-white\">Styled Components:</strong> CSS-in-JS with component styling</div>\n                    <div><strong className=\"text-white\">SCSS/Sass:</strong> CSS preprocessor with variables and mixins</div>\n                    <div><strong className=\"text-white\">Material-UI:</strong> React components with Material Design</div>\n                    <div><strong className=\"text-white\">Chakra UI:</strong> Simple, modular React component library</div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Mobile Frameworks</h4>\n                  <p className=\"text-gray-400 text-sm mb-2\">\n                    Cross-platform and native mobile development frameworks:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400\">\n                    <div><strong className=\"text-white\">React Native:</strong> Cross-platform with React</div>\n                    <div><strong className=\"text-white\">Flutter:</strong> Google's UI toolkit for mobile</div>\n                    <div><strong className=\"text-white\">Swift:</strong> Native iOS development language</div>\n                    <div><strong className=\"text-white\">Kotlin:</strong> Modern Android development language</div>\n                    <div><strong className=\"text-white\">Expo:</strong> Platform for React Native development</div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Desktop Frameworks</h4>\n                  <p className=\"text-gray-400 text-sm mb-2\">\n                    Cross-platform and native desktop application frameworks:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400\">\n                    <div><strong className=\"text-white\">Electron:</strong> Web technologies for desktop apps</div>\n                    <div><strong className=\"text-white\">Tauri:</strong> Rust-based lightweight desktop framework</div>\n                    <div><strong className=\"text-white\">Qt:</strong> Cross-platform C++ application framework</div>\n                    <div><strong className=\"text-white\">WPF:</strong> Windows Presentation Foundation for .NET</div>\n                    <div><strong className=\"text-white\">JavaFX:</strong> Java platform for desktop applications</div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Deployment Platforms</h4>\n                  <p className=\"text-gray-400 text-sm mb-2\">\n                    Modern deployment and hosting solutions for applications:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400\">\n                    <div><strong className=\"text-white\">Vercel:</strong> Frontend deployment with edge functions</div>\n                    <div><strong className=\"text-white\">Netlify:</strong> JAMstack deployment and hosting</div>\n                    <div><strong className=\"text-white\">AWS:</strong> Amazon Web Services cloud platform</div>\n                    <div><strong className=\"text-white\">Google Cloud:</strong> Google's cloud computing services</div>\n                    <div><strong className=\"text-white\">Docker:</strong> Containerization platform</div>\n                    <div><strong className=\"text-white\">Kubernetes:</strong> Container orchestration system</div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Testing Frameworks</h4>\n                  <p className=\"text-gray-400 text-sm mb-2\">\n                    Testing tools and frameworks for quality assurance:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400\">\n                    <div><strong className=\"text-white\">Jest:</strong> JavaScript testing framework</div>\n                    <div><strong className=\"text-white\">Vitest:</strong> Fast unit testing framework</div>\n                    <div><strong className=\"text-white\">Cypress:</strong> End-to-end testing framework</div>\n                    <div><strong className=\"text-white\">Playwright:</strong> Cross-browser automation testing</div>\n                    <div><strong className=\"text-white\">React Testing Library:</strong> React component testing</div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Authentication Solutions</h4>\n                  <p className=\"text-gray-400 text-sm mb-2\">\n                    User authentication and authorization services:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400\">\n                    <div><strong className=\"text-white\">Auth0:</strong> Identity platform as a service</div>\n                    <div><strong className=\"text-white\">Firebase Auth:</strong> Google's authentication service</div>\n                    <div><strong className=\"text-white\">NextAuth.js:</strong> Authentication for Next.js</div>\n                    <div><strong className=\"text-white\">Supabase Auth:</strong> Open-source authentication</div>\n                    <div><strong className=\"text-white\">JWT:</strong> JSON Web Tokens for stateless auth</div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">State Management</h4>\n                  <p className=\"text-gray-400 text-sm mb-2\">\n                    Tools for managing application state across components and user sessions:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400\">\n                    <div><strong className=\"text-white\">Redux Toolkit:</strong> Modern Redux with simplified API</div>\n                    <div><strong className=\"text-white\">Zustand:</strong> Lightweight state management solution</div>\n                    <div><strong className=\"text-white\">Recoil:</strong> Experimental state management for React</div>\n                    <div><strong className=\"text-white\">MobX:</strong> Reactive state management through observables</div>\n                    <div><strong className=\"text-white\">Context API:</strong> React's built-in state management</div>\n                    <div><strong className=\"text-white\">Valtio:</strong> Proxy-based state management</div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">API Tools</h4>\n                  <p className=\"text-gray-400 text-sm mb-2\">\n                    Tools for API development, data fetching, and client-server communication:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400\">\n                    <div><strong className=\"text-white\">GraphQL:</strong> Query language for APIs</div>\n                    <div><strong className=\"text-white\">Apollo Client:</strong> Comprehensive GraphQL client</div>\n                    <div><strong className=\"text-white\">React Query:</strong> Data fetching and caching library</div>\n                    <div><strong className=\"text-white\">SWR:</strong> Data fetching with caching and revalidation</div>\n                    <div><strong className=\"text-white\">Axios:</strong> Promise-based HTTP client</div>\n                    <div><strong className=\"text-white\">tRPC:</strong> End-to-end typesafe APIs</div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Monitoring & Analytics</h4>\n                  <p className=\"text-gray-400 text-sm mb-2\">\n                    Tools for application monitoring, error tracking, and performance analysis:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400\">\n                    <div><strong className=\"text-white\">Sentry:</strong> Error tracking and performance monitoring</div>\n                    <div><strong className=\"text-white\">LogRocket:</strong> Session replay and logging</div>\n                    <div><strong className=\"text-white\">New Relic:</strong> Application performance monitoring</div>\n                    <div><strong className=\"text-white\">Datadog:</strong> Infrastructure and application monitoring</div>\n                    <div><strong className=\"text-white\">Prometheus:</strong> Open-source monitoring and alerting</div>\n                    <div><strong className=\"text-white\">Grafana:</strong> Analytics and monitoring dashboards</div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">CI/CD & DevOps</h4>\n                  <p className=\"text-gray-400 text-sm mb-2\">\n                    Continuous integration and deployment tools for automated workflows:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400\">\n                    <div><strong className=\"text-white\">GitHub Actions:</strong> CI/CD platform integrated with GitHub</div>\n                    <div><strong className=\"text-white\">GitLab CI:</strong> Built-in CI/CD for GitLab</div>\n                    <div><strong className=\"text-white\">Jenkins:</strong> Open-source automation server</div>\n                    <div><strong className=\"text-white\">CircleCI:</strong> Cloud-based CI/CD platform</div>\n                    <div><strong className=\"text-white\">Azure DevOps:</strong> Microsoft's DevOps platform</div>\n                    <div><strong className=\"text-white\">Travis CI:</strong> Hosted CI service for GitHub projects</div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Version Control</h4>\n                  <p className=\"text-gray-400 text-sm mb-2\">\n                    Version control systems and repository hosting platforms:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400\">\n                    <div><strong className=\"text-white\">Git:</strong> Distributed version control system</div>\n                    <div><strong className=\"text-white\">GitHub:</strong> Git hosting with collaboration features</div>\n                    <div><strong className=\"text-white\">GitLab:</strong> DevOps platform with Git repository</div>\n                    <div><strong className=\"text-white\">Bitbucket:</strong> Git solution for teams</div>\n                    <div><strong className=\"text-white\">Azure Repos:</strong> Git repositories in Azure DevOps</div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Package Managers</h4>\n                  <p className=\"text-gray-400 text-sm mb-2\">\n                    Tools for managing project dependencies and packages:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400\">\n                    <div><strong className=\"text-white\">npm:</strong> Node.js package manager</div>\n                    <div><strong className=\"text-white\">Yarn:</strong> Fast, reliable package manager</div>\n                    <div><strong className=\"text-white\">pnpm:</strong> Efficient package manager with shared dependencies</div>\n                    <div><strong className=\"text-white\">Bun:</strong> Fast all-in-one JavaScript runtime and package manager</div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Build Tools</h4>\n                  <p className=\"text-gray-400 text-sm mb-2\">\n                    Tools for bundling, compiling, and optimizing application code:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400\">\n                    <div><strong className=\"text-white\">Webpack:</strong> Module bundler for JavaScript applications</div>\n                    <div><strong className=\"text-white\">Vite:</strong> Fast build tool for modern web projects</div>\n                    <div><strong className=\"text-white\">Rollup:</strong> Module bundler for JavaScript libraries</div>\n                    <div><strong className=\"text-white\">Parcel:</strong> Zero-configuration build tool</div>\n                    <div><strong className=\"text-white\">esbuild:</strong> Extremely fast JavaScript bundler</div>\n                    <div><strong className=\"text-white\">Turbopack:</strong> Incremental bundler optimized for JavaScript and TypeScript</div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Code Quality</h4>\n                  <p className=\"text-gray-400 text-sm mb-2\">\n                    Tools for maintaining code quality, formatting, and automated code review:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400\">\n                    <div><strong className=\"text-white\">ESLint:</strong> JavaScript linting utility</div>\n                    <div><strong className=\"text-white\">Prettier:</strong> Code formatter</div>\n                    <div><strong className=\"text-white\">Husky:</strong> Git hooks for code quality</div>\n                    <div><strong className=\"text-white\">lint-staged:</strong> Run linters on staged files</div>\n                    <div><strong className=\"text-white\">SonarQube:</strong> Code quality and security analysis</div>\n                    <div><strong className=\"text-white\">CodeClimate:</strong> Automated code review and quality analytics</div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Documentation</h4>\n                  <p className=\"text-gray-400 text-sm mb-2\">\n                    Tools for creating and maintaining project documentation:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400\">\n                    <div><strong className=\"text-white\">Storybook:</strong> Tool for building UI components in isolation</div>\n                    <div><strong className=\"text-white\">Docusaurus:</strong> Documentation website generator</div>\n                    <div><strong className=\"text-white\">GitBook:</strong> Documentation platform</div>\n                    <div><strong className=\"text-white\">Notion:</strong> All-in-one workspace for documentation</div>\n                    <div><strong className=\"text-white\">Confluence:</strong> Team collaboration and documentation</div>\n                    <div><strong className=\"text-white\">JSDoc:</strong> API documentation generator for JavaScript</div>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-white mb-2\">Additional Requirements</h4>\n                  <p className=\"text-gray-400 text-sm\">\n                    Use this field to specify any special requirements, constraints, integrations, performance needs,\n                    security requirements, or specific features not covered in the main form. Be as detailed as possible\n                    to help the AI generate the most relevant prompt.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Tips Section */}\n            <div className=\"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out\" style={{\n              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'\n            }}>\n              <h3 className=\"text-2xl font-bold text-white mb-3\">Tips for Best Results</h3>\n\n              <div className=\"space-y-3\">\n                <div className=\"flex items-start space-x-2\">\n                  <span className=\"text-white font-bold\">1.</span>\n                  <p className=\"text-gray-400 text-sm\">\n                    <strong className=\"text-white\">Be Specific:</strong> The more detailed your project description, the better the AI can tailor the prompt to your exact needs.\n                  </p>\n                </div>\n\n                <div className=\"flex items-start space-x-2\">\n                  <span className=\"text-white font-bold\">2.</span>\n                  <p className=\"text-gray-400 text-sm\">\n                    <strong className=\"text-white\">Choose Appropriate Complexity:</strong> Match the complexity level to your timeline, budget, and technical requirements.\n                  </p>\n                </div>\n\n                <div className=\"flex items-start space-x-2\">\n                  <span className=\"text-white font-bold\">3.</span>\n                  <p className=\"text-gray-400 text-sm\">\n                    <strong className=\"text-white\">Select Relevant Technologies:</strong> If you're unsure about technologies, leave them blank and let the AI suggest appropriate options.\n                  </p>\n                </div>\n\n                <div className=\"flex items-start space-x-2\">\n                  <span className=\"text-white font-bold\">4.</span>\n                  <p className=\"text-gray-400 text-sm\">\n                    <strong className=\"text-white\">Use Templates:</strong> Click \"Project Templates\" to explore examples and see how to structure your project information effectively.\n                  </p>\n                </div>\n\n                <div className=\"flex items-start space-x-2\">\n                  <span className=\"text-white font-bold\">5.</span>\n                  <p className=\"text-gray-400 text-sm\">\n                    <strong className=\"text-white\">Include Context:</strong> Mention your target audience, business goals, and any existing systems you need to integrate with.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Keyboard Shortcuts Section */}\n            <div className=\"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out\" style={{\n              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'\n            }}>\n              <h3 className=\"text-2xl font-bold text-white mb-3\">Keyboard Shortcuts</h3>\n              <p className=\"text-gray-400 mb-4 text-sm\">\n                Use these keyboard shortcuts to work more efficiently:\n              </p>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-400 text-sm\">Generate Prompt</span>\n                    <kbd className=\"bg-black/20 text-white px-2 py-1 rounded text-xs\">Ctrl + Enter</kbd>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-400 text-sm\">Focus Project Name</span>\n                    <kbd className=\"bg-black/20 text-white px-2 py-1 rounded text-xs\">Ctrl + K</kbd>\n                  </div>\n\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-400 text-sm\">Open Templates</span>\n                    <kbd className=\"bg-black/20 text-white px-2 py-1 rounded text-xs\">Ctrl + T</kbd>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-400 text-sm\">AI Settings</span>\n                    <kbd className=\"bg-black/20 text-white px-2 py-1 rounded text-xs\">Ctrl + ,</kbd>\n                  </div>\n                </div>\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-400 text-sm\">Open History</span>\n                    <kbd className=\"bg-black/20 text-white px-2 py-1 rounded text-xs\">Ctrl + H</kbd>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-400 text-sm\">AI Settings & Integrations</span>\n                    <kbd className=\"bg-black/20 text-white px-2 py-1 rounded text-xs\">Ctrl + A</kbd>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-400 text-sm\">Team Dashboard</span>\n                    <kbd className=\"bg-black/20 text-white px-2 py-1 rounded text-xs\">Ctrl + M</kbd>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-400 text-sm\">Share Prompt</span>\n                    <kbd className=\"bg-black/20 text-white px-2 py-1 rounded text-xs\">Ctrl + S</kbd>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-400 text-sm\">Integrations</span>\n                    <kbd className=\"bg-black/20 text-white px-2 py-1 rounded text-xs\">Ctrl + I</kbd>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-400 text-sm\">Live Collaboration</span>\n                    <kbd className=\"bg-black/20 text-white px-2 py-1 rounded text-xs\">Ctrl + R</kbd>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-400 text-sm\">Industry Templates</span>\n                    <kbd className=\"bg-black/20 text-white px-2 py-1 rounded text-xs\">Ctrl + B</kbd>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-400 text-sm\">Close Modal</span>\n                    <kbd className=\"bg-black/20 text-white px-2 py-1 rounded text-xs\">Escape</kbd>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-gray-400 text-sm\">Auto-save</span>\n                    <span className=\"text-gray-300 text-xs\">Automatic</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n              </div>\n            </>\n          )}\n        </div>\n\n        {/* History Modal */}\n        {showHistory && (\n          <div className=\"fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4\">\n            <div className=\"bg-black border border-white/20 rounded-xl shadow-2xl p-6 max-w-4xl w-full max-h-[80vh] overflow-hidden\" style={{\n              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8)'\n            }}>\n              <div className=\"flex justify-between items-center mb-4\">\n                <h3 className=\"text-xl font-semibold text-white\">Prompt History</h3>\n                <button\n                  onClick={() => setShowHistory(false)}\n                  className=\"bg-gray-800 text-white px-3 py-1 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm\"\n                >\n                  Close\n                </button>\n              </div>\n\n              <div className=\"overflow-y-auto max-h-[60vh] space-y-3\">\n                {promptHistory.length === 0 ? (\n                  <p className=\"text-gray-400 text-center py-8\">No prompts generated yet</p>\n                ) : (\n                  promptHistory.map((item) => (\n                    <div key={item.id} className=\"bg-gray-900 border border-white/10 rounded-lg p-4 hover:bg-gray-800 transition-all duration-300\">\n                      <div className=\"flex justify-between items-start mb-2\">\n                        <div>\n                          <h4 className=\"text-white font-medium\">{item.projectInput.projectName}</h4>\n                          <p className=\"text-xs text-gray-400\">{item.timestamp.toLocaleString()}</p>\n                        </div>\n                        <div className=\"flex space-x-2\">\n                          <button\n                            onClick={() => toggleFavorite(item.id)}\n                            className={`px-2 py-1 rounded text-xs transition-all duration-300 ${\n                              item.isFavorite\n                                ? 'bg-white text-black'\n                                : 'bg-gray-700 text-gray-400 hover:bg-gray-600'\n                            }`}\n                          >\n                            {item.isFavorite ? '★' : '☆'}\n                          </button>\n                          <button\n                            onClick={() => loadFromHistory(item)}\n                            className=\"bg-gray-700 text-white px-2 py-1 rounded text-xs hover:bg-gray-600 transition-all duration-300\"\n                          >\n                            Load\n                          </button>\n                        </div>\n                      </div>\n                      <p className=\"text-sm text-gray-300 mb-2 line-clamp-2\">{item.projectInput.projectIdea}</p>\n                      <div className=\"flex flex-wrap gap-1 mb-2\">\n                        <span className=\"bg-gray-700 text-xs px-2 py-1 rounded text-gray-300\">{item.projectInput.projectType}</span>\n                        <span className=\"bg-gray-700 text-xs px-2 py-1 rounded text-gray-300\">{item.projectInput.platform}</span>\n                        <span className=\"bg-gray-700 text-xs px-2 py-1 rounded text-gray-300\">{item.projectInput.complexity}</span>\n                      </div>\n                    </div>\n                  ))\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n\n\n        {/* Advanced Settings Modal */}\n        <AdvancedSettings\n          isOpen={showAdvancedSettings}\n          onClose={() => setShowAdvancedSettings(false)}\n          settings={aiSettings}\n          onSettingsChange={setAiSettings}\n          recommendedModel={aiService.getModelRecommendation(projectInput)}\n          projectInput={projectInput}\n          generatedPrompt={generatedPrompt}\n        />\n\n\n\n\n\n        {/* Advanced Template Browser Modal */}\n        <AdvancedTemplateBrowser\n          isOpen={showAdvancedTemplates}\n          onClose={() => setShowAdvancedTemplates(false)}\n          onLoadTemplate={loadAdvancedTemplate}\n        />\n\n        {/* Toast Notification */}\n        {toast && (\n          <div className={`fixed top-4 right-4 z-50 px-4 py-2 rounded-lg transition-all duration-300 animate-slide-up ${\n            toast.type === 'success' ? 'bg-white border border-gray-300 text-black' :\n            toast.type === 'error' ? 'bg-gray-900 border border-gray-600 text-white' :\n            'bg-gray-800 border border-gray-600 text-white'\n          }`}>\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-sm font-medium\">{toast.message}</span>\n              <button\n                onClick={() => setToast(null)}\n                className=\"text-xs opacity-70 hover:opacity-100 transition-opacity\"\n              >\n                ✕\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AAKA;AACA;AACA;AACA;AACA;AACA;AAkBA;;;AAlCA;;;;;;;;;;AAkBA,sEAAsE;AACtE,MAAM,eAAe;IACnB,IAAI,OAAO,WAAW,eAAe,OAAO,UAAU,EAAE;QACtD,OAAO,OAAO,UAAU;IAC1B;IACA,2BAA2B;IAC3B,OAAO,uCAAuC,OAAO,CAAC,SAAS,SAAS,CAAC;QACvE,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK;QAC/B,MAAM,IAAI,MAAM,MAAM,IAAK,IAAI,MAAM;QACrC,OAAO,EAAE,QAAQ,CAAC;IACpB;AACF;;AAee,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QAC7D,aAAa;QACb,aAAa;QACb,aAAa;QACb,UAAU;QACV,cAAc,EAAE;QAChB,YAAY;QACZ,UAAU,EAAE;QACZ,wBAAwB;IAC1B;IAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IAC3E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgE;IACjG,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;QACtE,OAAO;QACP,mBAAmB;QACnB,iBAAiB;QACjB,oBAAoB;QACpB,gBAAgB;QAChB,gBAAgB;IAClB;IACA,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyF;IAEpJ,mBAAmB;IACnB,MAAM,qBAAqB;QACzB;YAAE,OAAO;YAAmB,OAAO;YAAmB,aAAa;QAAwC;QAC3G;YAAE,OAAO;YAAsB,OAAO;YAAsB,aAAa;QAAwC;QACjH;YAAE,OAAO;YAAuB,OAAO;YAAuB,aAAa;QAAoC;QAC/G;YAAE,OAAO;YAAe,OAAO;YAAuB,aAAa;QAA2C;QAC9G;YAAE,OAAO;YAAiB,OAAO;YAAsB,aAAa;QAA4C;QAChH;YAAE,OAAO;YAAoB,OAAO;YAA4B,aAAa;QAAgC;QAC7G;YAAE,OAAO;YAAyB,OAAO;YAAyB,aAAa;QAAoC;QACnH;YAAE,OAAO;YAAoB,OAAO;YAAoB,aAAa;QAAiC;QACtG;YAAE,OAAO;YAAY,OAAO;YAAY,aAAa;QAAqC;QAC1F;YAAE,OAAO;YAAmB,OAAO;YAAmB,aAAa;QAAsC;KAC1G;IAED,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAO,OAAO;YAAO,aAAa;QAA6B;QACxE;YAAE,OAAO;YAAU,OAAO;YAAU,aAAa;QAA4B;QAC7E;YAAE,OAAO;YAAW,OAAO;YAAW,aAAa;QAA4B;QAC/E;YAAE,OAAO;YAAU,OAAO;YAAU,aAAa;QAA6B;QAC9E;YAAE,OAAO;YAAkB,OAAO;YAAkB,aAAa;QAAqB;KACvF;IAED,8BAA8B;IAC9B,MAAM,YAAY,CAAC,SAAiB,OAAqC,MAAM;QAC7E,SAAS;YAAE;YAAS;QAAK;QACzB,WAAW,IAAM,SAAS,OAAO;IACnC;IAEA,oBAAoB;IACpB,MAAM,4BAA4B,CAAC,OAAe;QAChD,MAAM,cAA6C;YACjD,mBAAmB;gBAAC;gBAAkB;gBAAqB;gBAAc;gBAAa;aAAa;YACnG,sBAAsB;gBAAC;gBAAmB;gBAAiB;gBAAc;gBAAe;aAAa;YACrG,uBAAuB;gBAAC;gBAAkB;gBAAgB;gBAAe;gBAAY;aAAY;YACjG,eAAe;gBAAC;gBAAgB;gBAAmB;gBAAqB;gBAAoB;aAAiB;YAC7G,iBAAiB;gBAAC;gBAAmB;gBAAqB;gBAAwB;gBAAyB;aAAiB;YAC5H,oBAAoB;gBAAC;gBAAyB;gBAAoB;gBAAsB;gBAAkB;aAAa;YACvH,yBAAyB;gBAAC;gBAAkB;gBAAoB;gBAAsB;gBAAe;aAAiB;YACtH,oBAAoB;gBAAC;gBAAwB;gBAAe;gBAAmB;gBAAe;aAAe;YAC7G,YAAY;gBAAC;gBAAkB;gBAAkB;gBAAmB;gBAAiB;aAAiB;YACtG,mBAAmB;gBAAC;gBAAiB;gBAAmB;gBAAc;gBAAkB;aAAgB;QAC1G;QAEA,OAAO,WAAW,CAAC,YAAY,EAAE,OAAO,CAAA,IACtC,EAAE,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,QACvC,EAAE;IACT;IAEA,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;8DAAgB,CAAC;oBACrB,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,EAAE;wBAClC,OAAQ,MAAM,GAAG;4BACf,KAAK;gCACH,MAAM,cAAc;gCACpB,IAAI,CAAC,cAAc;oCACjB;gCACF;gCACA;4BACF,KAAK;gCACH,MAAM,cAAc;gCAEpB;4BACF,KAAK;gCACH,MAAM,cAAc;gCACpB,SAAS,aAAa,CAAmB,uBAAuB;gCAChE;4BACF,KAAK;gCACH,MAAM,cAAc;gCACpB,eAAe;gCACf;4BAEF,KAAK;gCACH,MAAM,cAAc;gCACpB,yBAAyB;gCACzB;4BACF,KAAK;gCACH,MAAM,cAAc;gCACpB,wBAAwB;gCACxB;4BACF,KAAK;gCACH,MAAM,cAAc;gCACpB,wBAAwB;gCACxB;4BAEF,KAAK;gCACH,MAAM,cAAc;gCACpB,yBAAyB;gCACzB;4BACF,KAAK;gCACH,MAAM,cAAc;gCACpB,eAAe;gCACf,yBAAyB;gCACzB,wBAAwB;gCAExB,yBAAyB;gCACzB;wBACJ;oBACF;oBAEA,IAAI,MAAM,GAAG,KAAK,UAAU;wBAC1B,eAAe;wBACf,yBAAyB;wBACzB,wBAAwB;wBAExB,yBAAyB;oBAC3B;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;gDAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;uCAAG;QAAC;KAAa;IAEjB,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,YAAY,aAAa,OAAO,CAAC;YACvC,IAAI,WAAW;gBACb,IAAI;oBACF,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,gBAAgB;gBAClB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC9C;YACF;YAEA,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,cAAc;gBAChB,IAAI;oBACF,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,iBAAiB,OAAO,GAAG;wDAAC,CAAC,OAAwD,CAAC;gCACpF,GAAG,IAAI;gCACP,WAAW,IAAI,KAAK,KAAK,SAAS;4BACpC,CAAC;;gBACH,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,2BAA2B;gBAC3C;YACF;QACF;uCAAG,EAAE;IAEL,+BAA+B;IAC/B,MAAM,gBAAgB,CAAC,OAAqB;QAC1C,MAAM,cAA6B;YACjC,IAAI;YACJ,WAAW,IAAI;YACf,cAAc;YACd,iBAAiB;YACjB,YAAY;QACd;QAEA,MAAM,aAAa;YAAC;eAAgB,cAAc,KAAK,CAAC,GAAG;SAAI,EAAE,eAAe;QAChF,iBAAiB;QACjB,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;IACvD;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,aAAa,cAAc,GAAG,CAAC,CAAA,OACnC,KAAK,EAAE,KAAK,KAAK;gBAAE,GAAG,IAAI;gBAAE,YAAY,CAAC,KAAK,UAAU;YAAC,IAAI;QAE/D,iBAAiB;QACjB,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;IACvD;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB,YAAY,YAAY;QACxC,mBAAmB,YAAY,eAAe;QAC9C,eAAe;IACjB;IAIA,MAAM,uBAAuB,CAAC;QAC5B,gBAAgB,CAAA,OAAQ,CAAC;gBACvB,GAAG,IAAI;gBACP,GAAG,YAAY;YACjB,CAAC;QACD,UAAU,CAAC,0BAA0B,EAAE,aAAa,WAAW,EAAE,EAAE;IAGrE;IAEA,uBAAuB;IACvB,MAAM,gBAAgB,CAAC,MAAc;QACnC,MAAM,YAAY;YAAE,GAAG,MAAM;QAAC;QAE9B,OAAQ;YACN,KAAK;gBACH,IAAI,CAAC,MAAM,IAAI,IAAI;oBACjB,UAAU,WAAW,GAAG;gBAC1B,OAAO,IAAI,MAAM,MAAM,GAAG,GAAG;oBAC3B,UAAU,WAAW,GAAG;gBAC1B,OAAO,IAAI,MAAM,MAAM,GAAG,IAAI;oBAC5B,UAAU,WAAW,GAAG;gBAC1B,OAAO;oBACL,OAAO,UAAU,WAAW;gBAC9B;gBACA;YACF,KAAK;gBACH,IAAI,CAAC,MAAM,IAAI,IAAI;oBACjB,UAAU,WAAW,GAAG;gBAC1B,OAAO,IAAI,MAAM,MAAM,GAAG,IAAI;oBAC5B,UAAU,WAAW,GAAG;gBAC1B,OAAO,IAAI,MAAM,MAAM,GAAG,MAAM;oBAC9B,UAAU,WAAW,GAAG;gBAC1B,OAAO;oBACL,OAAO,UAAU,WAAW;gBAC9B;gBACA;QACJ;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe;QACnB,MAAM,YAAY,cAAc,eAAe,aAAa,WAAW;QACvE,MAAM,YAAY,cAAc,eAAe,aAAa,WAAW;QACvE,OAAO,aAAa;IACtB;IAEA,MAAM,oBAAoB,CAAC,OAA2B;QACpD,gBAAgB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QACpD,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAK,CAAC;QAE9C,IAAI,UAAU,iBAAiB,UAAU,eAAe;YACtD,cAAc,OAAO;QACvB;QAEA,0BAA0B;QAC1B,cAAc;QACd,WAAW;YACT,IAAI;gBACF,MAAM,eAAe;oBAAE,GAAG,YAAY;oBAAE,CAAC,MAAM,EAAE;gBAAM;gBACvD,aAAa,OAAO,CAAC,yBAAyB,KAAK,SAAS,CAAC;gBAC7D,cAAc;YAChB,EAAE,OAAO,OAAO;gBACd,cAAc;gBACd,UAAU,uBAAuB;YACnC;QACF,GAAG;IACL;IAEA,MAAM,iBAAiB;QACrB,kCAAkC;QAClC,IAAI,CAAC,gBAAgB;YACnB,WAAW;gBAAE,aAAa;gBAAM,aAAa;YAAK;YAClD;QACF;QAEA,gBAAgB;QAChB,sBAAsB;QAEtB,4BAA4B;QAC5B,MAAM,mBAAmB,YAAY;YACnC,sBAAsB,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,IAAI;QACpD,GAAG;QAEH,IAAI;YACF,yCAAyC;YACzC,MAAM,mBAAmB,8HAAA,CAAA,YAAS,CAAC,sBAAsB,CAAC;YAC1D,MAAM,kBAAkB;gBAAE,GAAG,UAAU;YAAC;YACxC,IAAI,CAAC,gBAAgB,KAAK,IAAI,gBAAgB,KAAK,KAAK,QAAQ;gBAC9D,gBAAgB,KAAK,GAAG;YAC1B;YAEA,gCAAgC;YAChC,MAAM,SAAS,MAAM,8HAAA,CAAA,YAAS,CAAC,cAAc,CAAC,cAAc;YAE5D,sBAAsB;YACtB,mBAAmB,OAAO,MAAM;YAChC,sBAAsB,OAAO,QAAQ;YAErC,gCAAgC;YAChC,cAAc,cAAc,OAAO,MAAM;YAIzC,+BAA+B;YAC/B,MAAM,YAAY,OAAO,MAAM,CAAC,8HAAA,CAAA,YAAS,EAAE,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gBAAgB,KAAK,GAAG,QAAQ;YAC9F,UAAU,CAAC,sBAAsB,EAAE,WAAW,EAAE;QAElD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,UAAU;gBAAE,SAAS,CAAC,yBAAyB,EAAE,cAAc;YAAC;YAChE,UAAU,6BAA6B;QAGzC,SAAU;YACR,cAAc;YACd,gBAAgB;YAChB,WAAW,IAAM,sBAAsB,IAAI;QAC7C;IACF;IAEA,4BAA4B;IAC5B,MAAM,gBAAgB;QACpB,iBAAiB,OAAO;YACtB,IAAI;gBACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;gBACpC,UAAU,wBAAwB;YAEpC,EAAE,OAAM;gBACN,UAAU,+BAA+B;YAC3C;QACF;QAEA,gBAAgB,CAAC,MAAc;YAC7B,IAAI;gBACF,MAAM,OAAO,IAAI,KAAK;oBAAC;iBAAK,EAAE;oBAAE,MAAM;gBAAa;gBACnD,MAAM,MAAM,IAAI,eAAe,CAAC;gBAChC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,GAAG,SAAS,IAAI,CAAC;gBAC9B,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,IAAI,eAAe,CAAC;gBACpB,UAAU,iCAAiC;YAE7C,EAAE,OAAM;gBACN,UAAU,2BAA2B;YACvC;QACF;QAEA,aAAa,OAAO;YAClB,IAAI,UAAU,KAAK,EAAE;gBACnB,IAAI;oBACF,MAAM,UAAU,KAAK,CAAC;wBACpB,OAAO;wBACP,MAAM;oBACR;oBACA,UAAU,wBAAwB;gBAEpC,EAAE,OAAO,OAAO;oBACd,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,cAAc;wBACzD,UAAU,mBAAmB;oBAC/B;gBACF;YACF,OAAO;gBACL,UAAU,wCAAwC;YACpD;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,MAAM,cAAc,eAAe,CAAC;IACtC;IAIA,MAAM,YAAY;QAChB,gBAAgB;YACd,aAAa;YACb,aAAa;YACb,aAAa;YACb,UAAU;YACV,cAAc,EAAE;YAChB,YAAY;YACZ,UAAU,EAAE;YACZ,wBAAwB;QAC1B;QACA,mBAAmB;QACnB,UAAU,CAAC;QACX,WAAW,CAAC;QACZ,UAAU,gBAAgB;IAC5B;IAEA,MAAM,yBAAyB,CAAC;QAC9B,gBAAgB,CAAA,OAAQ,CAAC;gBACvB,GAAG,IAAI;gBACP,cAAc,KAAK,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,KAAK,IAAI,IAC1D,KAAK,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,KAAK,IAAI,IAClD;uBAAI,KAAK,YAAY;oBAAE;iBAAK;YAClC,CAAC;IACH;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,2IAAA,CAAA,UAAkB;;;;;0BACnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,wIAAA,CAAA,UAAe;gCACd,WAAW;oCACT;wCAAE,MAAM;wCAAuB,aAAa;wCAAM,YAAY;oCAAK;oCACnE;wCAAE,MAAM;wCAAwB,aAAa;wCAAM,YAAY;oCAAK;oCACpE;wCAAE,MAAM;wCAAuB,aAAa;wCAAM,YAAY;oCAAK;oCACnE;wCAAE,MAAM;wCAAuB,aAAa;wCAAM,YAAY;oCAAK;iCACpE;gCACD,aAAa;gCACb,UAAU;gCACV,WAAW;gCACX,WAAU;;;;;;0CAEZ,6LAAC,oIAAA,CAAA,UAAW;gCACV,MAAK;gCACL,WAAU;;;;;;;;;;;;kCAId,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;gCAAoG,OAAO;oCACxH,WAAW;gCACb;;kDACE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,UAAW;gDAAC,MAAK;gDAAkB,WAAU;;;;;;0DAC9C,6LAAC;gDAAI,WAAU;;kEAGb,6LAAC;wDACC,SAAS;wDACT,WAAU;wDACV,OAAO;4DACL,WAAW;wDACb;kEACD;;;;;;kEAGD,6LAAC;wDACC,SAAS,IAAM,eAAe;wDAC9B,WAAU;wDACV,OAAO;4DACL,WAAW;wDACb;;4DACD;4DACW,cAAc,MAAM;4DAAC;;;;;;;kEAEjC,6LAAC;wDACC,SAAS,IAAM,yBAAyB;wDACxC,WAAU;wDACV,OAAO;4DACL,WAAW;wDACb;kEACD;;;;;;kEAGD,6LAAC;wDACC,SAAS,IAAM,wBAAwB;wDACvC,WAAU;wDACV,OAAO;4DACL,WAAW;wDACb;kEACD;;;;;;;;;;;;;;;;;;oCAQJ,OAAO,OAAO,kBACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAyB,OAAO,OAAO;;;;;;;;;;;oCAGvD,OAAO,OAAO,kBACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAyB,OAAO,OAAO;;;;;;;;;;;kDAKxD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC,oIAAA,CAAA,UAAW;oDAAC,MAAK;oDAAiB,WAAU;;;;;;;;;;;0DAE/C,6LAAC;gDACC,MAAK;gDACL,OAAO,aAAa,WAAW;gDAC/B,UAAU,CAAC;oDACT,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC/C,MAAM,iBAAiB,0BAA0B,EAAE,MAAM,CAAC,KAAK,EAAE,aAAa,WAAW;oDACzF,eAAe;oDACf,mBAAmB,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,eAAe,MAAM,GAAG;gDAC1E;gDACA,QAAQ;oDACN,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,aAAa;wDAAK,CAAC;oDAClD,WAAW,IAAM,mBAAmB,QAAQ;gDAC9C;gDACA,SAAS;oDACP,MAAM,iBAAiB,0BAA0B,aAAa,WAAW,EAAE,aAAa,WAAW;oDACnG,eAAe;oDACf,mBAAmB,aAAa,WAAW,CAAC,MAAM,GAAG,KAAK,eAAe,MAAM,GAAG;gDACpF;gDACA,WAAW,CAAC,wOAAwO,EAClP,OAAO,WAAW,IAAI,QAAQ,WAAW,GACrC,gCACA,uBACJ;gDACF,OAAO;oDACL,WAAW;gDACb;gDACA,aAAY;;;;;;4CAIb,mBAAmB,YAAY,MAAM,GAAG,mBACvC,6LAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,6LAAC;wDAEC,MAAK;wDACL,SAAS;4DACP,kBAAkB,eAAe;4DACjC,mBAAmB;wDACrB;wDACA,WAAU;kEAET;uDARI;;;;;;;;;;4CAcZ,OAAO,WAAW,IAAI,QAAQ,WAAW,kBACxC,6LAAC;gDAAE,WAAU;0DAA8B,OAAO,WAAW;;;;;;0DAE/D,6LAAC;gDAAI,WAAU;;oDACZ,aAAa,WAAW,CAAC,MAAM;oDAAC;;;;;;;;;;;;;kDAKrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC,oIAAA,CAAA,UAAW;oDAAC,MAAK;oDAAoB,WAAU;;;;;;;;;;;0DAElD,6LAAC;gDACC,OAAO,aAAa,WAAW;gDAC/B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;gDAChE,QAAQ,IAAM,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,aAAa;wDAAK,CAAC;gDAChE,WAAW,CAAC,qLAAqL,EAC/L,OAAO,WAAW,IAAI,QAAQ,WAAW,GACrC,gCACA,uBACJ;gDACF,OAAO;oDACL,WAAW;gDACb;gDACA,aAAY;;;;;;4CAEb,OAAO,WAAW,IAAI,QAAQ,WAAW,kBACxC,6LAAC;gDAAE,WAAU;0DAA8B,OAAO,WAAW;;;;;;0DAE/D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAM,aAAa,WAAW,CAAC,MAAM;oEAAC;;;;;;;0EACvC,6LAAC;gEAAK,WAAW,GACf,aAAa,WAAW,CAAC,MAAM,GAAG,KAAK,kBACvC,aAAa,WAAW,CAAC,MAAM,GAAG,MAAM,kBACxC,cACA;0EACC,aAAa,WAAW,CAAC,MAAM,GAAG,KAAK,cACvC,aAAa,WAAW,CAAC,MAAM,GAAG,MAAM,eACxC;;;;;;;;;;;;oDAGJ,aAAa,WAAW,CAAC,MAAM,GAAG,qBACjC,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAQ7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC,oIAAA,CAAA,UAAW;oDAAC,MAAK;oDAAe,WAAU;;;;;;;;;;;0DAE7C,6LAAC,qIAAA,CAAA,UAAY;gDACX,OAAO,aAAa,WAAW;gDAC/B,UAAU,CAAC,QAAU,kBAAkB,eAAe;gDACtD,SAAS;gDACT,aAAY;;;;;;;;;;;;kDAKhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC,oIAAA,CAAA,UAAW;oDAAC,MAAK;oDAAkB,WAAU;;;;;;;;;;;0DAEhD,6LAAC,qIAAA,CAAA,UAAY;gDACX,OAAO,aAAa,QAAQ;gDAC5B,UAAU,CAAC,QAAU,kBAAkB,YAAY;gDACnD,SAAS;gDACT,aAAY;;;;;;;;;;;;kDAKhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC,oIAAA,CAAA,UAAW;oDAAC,MAAK;oDAAmB,WAAU;;;;;;;;;;;0DAEjD,6LAAC;gDAAI,WAAU;0DACZ,AAAC;oDAAC;oDAAU;oDAAgB;iDAAW,CAAkB,GAAG,CAAC,CAAC,sBAC7D,6LAAC;wDAEC,WAAW,CAAC,iEAAiE,EAC3E,aAAa,UAAU,KAAK,QAAQ,2BAA2B,IAC/D;;0EAEF,6LAAC;gEACC,MAAK;gEACL,OAAO;gEACP,SAAS,aAAa,UAAU,KAAK;gEACrC,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;gEAC/D,WAAU;;;;;;0EAEZ,6LAAC;gEAAI,WAAW,CAAC;;sBAEf,EAAE,aAAa,UAAU,KAAK,QAC1B,0GACA,qKACH;oBACH,CAAC;gEACD,OAAO;oEACL,WAAW,aAAa,UAAU,KAAK,QACnC,0EACA;gEACN;0EAEE,cAAA,6LAAC;oEAAK,WAAU;8EAAc;;;;;;;;;;;;uDAzB3B;;;;;;;;;;;;;;;;kDAiCb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC,oIAAA,CAAA,UAAW;oDAAC,MAAK;oDAA8B,WAAU;;;;;;;;;;;0DAE5D,6LAAC;gDAAI,WAAU;0DACZ,OAAO,OAAO,CAAC,6HAAA,CAAA,eAAY,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,MAAM,iBAClD,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EACX,SAAS,OAAO,CAAC,KAAK;;;;;;0EAEzB,6LAAC;gEAAI,WAAU;0EACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;wEAEC,MAAK;wEACL,SAAS,IAAM,uBAAuB;wEACtC,WAAW,CAAC,yEAAyE,EACnF,aAAa,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,KAAK,IAAI,IACpD,wEACA,4EACJ;wEACF,OAAO;4EACL,WAAW,aAAa,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,KAAK,IAAI,IAC/D,iFACA;wEACN;kFAEC,KAAK,IAAI;uEAdL,KAAK,IAAI;;;;;;;;;;;uDAPZ;;;;;;;;;;;;;;;;kDA+BhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DACf,cAAA,6LAAC,oIAAA,CAAA,UAAW;oDAAC,MAAK;oDAAqC,WAAU;;;;;;;;;;;0DAEnE,6LAAC;gDACC,OAAO,aAAa,sBAAsB;gDAC1C,UAAU,CAAC,IAAM,kBAAkB,0BAA0B,EAAE,MAAM,CAAC,KAAK;gDAC3E,WAAU;gDACV,OAAO;oDACL,WAAW;gDACb;gDACA,aAAY;;;;;;;;;;;;kDAKhB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,SAAS;4CACT,UAAU,gBAAgB,CAAC,aAAa,WAAW,IAAI,CAAC,aAAa,WAAW;4CAChF,WAAU;4CACV,OAAO;gDACL,WAAW;4CACb;sDAEC,6BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,sIAAA,CAAA,UAAa;wDACZ,OAAO;4DACL;4DACA;4DACA;4DACA;4DACA;yDACD;wDACD,UAAU;wDACV,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO,GAAG,mBAAmB,CAAC,CAAC;4DAAC;;;;;;;;;;;kEAG7C,6LAAC;wDAAK,WAAU;;4DAAyB;4DAAmB;;;;;;;;;;;;uDAG9D;;;;;;;;;;;;;;;;;0CASR,6LAAC;gCAAI,WAAU;gCAAsF,OAAO;oCAC1G,WAAW;gCACb;;kDACE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,UAAW;wDAAC,MAAK;wDAAmB,WAAU;;;;;;oDAC9C,iCACC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEACE,gBAAgB,KAAK,CAAC,KAAK,MAAM;oEAAC;oEAAU,KAAK,IAAI,CAAC,gBAAgB,KAAK,CAAC,KAAK,MAAM,GAAG;oEAAK;;;;;;;4DAEjG,oCACC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;4EAAK;4EAAgB,OAAO,MAAM,CAAC,8HAAA,CAAA,YAAS,EAAE,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,mBAAmB,KAAK,GAAG,QAAQ;;;;;;;oEACpG,mBAAmB,UAAU,kBAC5B,6LAAC;;4EAAK;4EAAG,mBAAmB,UAAU;4EAAC;;;;;;;kFAEzC,6LAAC;wEAAK,WAAW,CAAC,8BAA8B,EAC9C,mBAAmB,iBAAiB,KAAK,WAAW,8BACpD,mBAAmB,iBAAiB,KAAK,aAAa,8BACtD,6BACA;kFACC,mBAAmB,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;4CAOhD,iCACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS;wDACT,WAAU;wDACV,OAAO;4DACL,WAAW;wDACb;kEACD;;;;;;kEAGD,6LAAC;wDACC,SAAS,IAAM,cAAc,cAAc,CAAC,iBAAiB,CAAC,eAAe,EAAE,KAAK,GAAG,IAAI;wDAC3F,WAAU;wDACV,OAAO;4DACL,WAAW;wDACb;kEACD;;;;;;oDAGA,OAAO,cAAc,eAAe,WAAW,2BAC9C,6LAAC;wDACC,SAAS,IAAM,cAAc,WAAW,CAAC;wDACzC,WAAU;wDACV,OAAO;4DACL,WAAW;wDACb;kEACD;;;;;;;;;;;;;;;;;;oCASR,gCACC,6LAAC;wCAAI,WAAU;wCAAoH,OAAO;4CACxI,WAAW;wCACb;kDACE,cAAA,6LAAC;4CAAI,WAAU;sDACZ;;;;;;;;;;6DAIL,6LAAC;wCAAI,WAAU;wCAAsG,OAAO;4CAC1H,WAAW;wCACb;;0DACE,6LAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,6LAAC;gDAAI,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;;kCASxD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS,IAAM,iBAAiB,CAAC;4BACjC,WAAU;4BACV,OAAO;gCACL,WAAW;4BACb;;gCACD;gCACK,gBAAgB,SAAS;gCAAO;;;;;;;;;;;;kCAKxC,6LAAC;wBAAI,WAAW,CAAC,4FAA4F,EAC3G,gBAAgB,sCAAsC,yBACtD;wBAAE,OAAO;4BACT,WAAW,gBAAgB,kFAAkF;wBAC/G;kCACG,+BACC;;8CACE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,6LAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;4CACV,OAAO;gDACL,WAAW;4CACb;sDACD;;;;;;;;;;;;8CAIH,6LAAC;oCAAE,WAAU;8CAA4D;;;;;;8CAIzE,6LAAC;oCAAI,WAAU;;sDAEjB,6LAAC;4CAAI,WAAU;4CAAuG,OAAO;gDAC3H,WAAW;4CACb;;8DACE,6LAAC;oDAAG,WAAU;8DAAqC;;;;;;8DAEnD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;;;;;;;sEAM/B,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;;sDASnC,6LAAC;4CAAI,WAAU;4CAAuG,OAAO;gDAC3H,WAAW;4CACb;;8DACE,6LAAC;oDAAG,WAAU;8DAAqC;;;;;;8DAEnD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAKvC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAKvC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAKvC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAKvC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;sDAQ3C,6LAAC;4CAAI,WAAU;4CAAuG,OAAO;gDAC3H,WAAW;4CACb;;8DACE,6LAAC;oDAAG,WAAU;8DAAqC;;;;;;8DAEnD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAKvC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAKvC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAKvC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAKvC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAKvC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;sDAO3C,6LAAC;4CAAI,WAAU;4CAAuG,OAAO;gDAC3H,WAAW;4CACb;;8DACE,6LAAC;oDAAG,WAAU;8DAAqC;;;;;;8DAEnD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAMvC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAMvC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;sDAS3C,6LAAC;4CAAI,WAAU;4CAAuG,OAAO;gDAC3H,WAAW;4CACb;;8DACE,6LAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,6LAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAKlC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAe;;;;;;;sFACnD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAgB;;;;;;;sFACpD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAiB;;;;;;;sFACrD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAiB;;;;;;;sFACrD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAgB;;;;;;;sFACpD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAoB;;;;;;;;;;;;;;;;;;;sEAI5D,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAiB;;;;;;;sFACrD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAuB;;;;;;;sFAC3D,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAwB;;;;;;;sFAC5D,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAY;;;;;;;sFAChD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAc;;;;;;;sFAClD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAqB;;;;;;;;;;;;;;;;;;;sEAI7D,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAoB;;;;;;;sFACxD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAiB;;;;;;;sFACrD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAgB;;;;;;;sFACpD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAe;;;;;;;sFACnD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAkB;;;;;;;sFACtD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAkB;;;;;;;;;;;;;;;;;;;sEAI1D,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAsB;;;;;;;sFAC1D,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAqB;;;;;;;sFACzD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAA2B;;;;;;;sFAC/D,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAmB;;;;;;;sFACvD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAqB;;;;;;;sFACzD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAmB;;;;;;;;;;;;;;;;;;;sEAI3D,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAsB;;;;;;;sFAC1D,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAiB;;;;;;;sFACrD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAe;;;;;;;sFACnD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAgB;;;;;;;sFACpD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAc;;;;;;;;;;;;;;;;;;;sEAItD,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAkB;;;;;;;sFACtD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAe;;;;;;;sFACnD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAY;;;;;;;sFAChD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAa;;;;;;;sFACjD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAgB;;;;;;;;;;;;;;;;;;;sEAIxD,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAgB;;;;;;;sFACpD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAiB;;;;;;;sFACrD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAa;;;;;;;sFACjD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAsB;;;;;;;sFAC1D,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAgB;;;;;;;sFACpD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAoB;;;;;;;;;;;;;;;;;;;sEAI5D,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAc;;;;;;;sFAClD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAgB;;;;;;;sFACpD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAiB;;;;;;;sFACrD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAoB;;;;;;;sFACxD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAA+B;;;;;;;;;;;;;;;;;;;sEAIvE,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAe;;;;;;;sFACnD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAuB;;;;;;;sFAC3D,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAqB;;;;;;;sFACzD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAuB;;;;;;;sFAC3D,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAa;;;;;;;;;;;;;;;;;;;sEAIrD,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAuB;;;;;;;sFAC3D,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAiB;;;;;;;sFACrD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAgB;;;;;;;sFACpD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAc;;;;;;;sFAClD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAqB;;;;;;;sFACzD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAgB;;;;;;;;;;;;;;;;;;;sEAIxD,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAiB;;;;;;;sFACrD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAuB;;;;;;;sFAC3D,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAqB;;;;;;;sFACzD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAa;;;;;;;sFACjD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAe;;;;;;;sFACnD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAc;;;;;;;;;;;;;;;;;;;sEAItD,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAgB;;;;;;;sFACpD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAmB;;;;;;;sFACvD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAmB;;;;;;;sFACvD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAiB;;;;;;;sFACrD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAoB;;;;;;;sFACxD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAiB;;;;;;;;;;;;;;;;;;;sEAIzD,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAwB;;;;;;;sFAC5D,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAmB;;;;;;;sFACvD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAiB;;;;;;;sFACrD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAkB;;;;;;;sFACtD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAsB;;;;;;;sFAC1D,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAmB;;;;;;;;;;;;;;;;;;;sEAI3D,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAa;;;;;;;sFACjD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAgB;;;;;;;sFACpD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAgB;;;;;;;sFACpD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAmB;;;;;;;sFACvD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAqB;;;;;;;;;;;;;;;;;;;sEAI7D,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAa;;;;;;;sFACjD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAc;;;;;;;sFAClD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAc;;;;;;;sFAClD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAa;;;;;;;;;;;;;;;;;;;sEAIrD,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAiB;;;;;;;sFACrD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAc;;;;;;;sFAClD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAgB;;;;;;;sFACpD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAgB;;;;;;;sFACpD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAiB;;;;;;;sFACrD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAmB;;;;;;;;;;;;;;;;;;;sEAI3D,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAgB;;;;;;;sFACpD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAkB;;;;;;;sFACtD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAe;;;;;;;sFACnD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAqB;;;;;;;sFACzD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAmB;;;;;;;sFACvD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAqB;;;;;;;;;;;;;;;;;;;sEAI7D,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAmB;;;;;;;sFACvD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAoB;;;;;;;sFACxD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAiB;;;;;;;sFACrD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAgB;;;;;;;sFACpD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAoB;;;;;;;sFACxD,6LAAC;;8FAAI,6LAAC;oFAAO,WAAU;8FAAa;;;;;;gFAAe;;;;;;;;;;;;;;;;;;;sEAIvD,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAwC;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;sDAU3C,6LAAC;4CAAI,WAAU;4CAAuG,OAAO;gDAC3H,WAAW;4CACb;;8DACE,6LAAC;oDAAG,WAAU;8DAAqC;;;;;;8DAEnD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAuB;;;;;;8EACvC,6LAAC;oEAAE,WAAU;;sFACX,6LAAC;4EAAO,WAAU;sFAAa;;;;;;wEAAqB;;;;;;;;;;;;;sEAIxD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAuB;;;;;;8EACvC,6LAAC;oEAAE,WAAU;;sFACX,6LAAC;4EAAO,WAAU;sFAAa;;;;;;wEAAuC;;;;;;;;;;;;;sEAI1E,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAuB;;;;;;8EACvC,6LAAC;oEAAE,WAAU;;sFACX,6LAAC;4EAAO,WAAU;sFAAa;;;;;;wEAAsC;;;;;;;;;;;;;sEAIzE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAuB;;;;;;8EACvC,6LAAC;oEAAE,WAAU;;sFACX,6LAAC;4EAAO,WAAU;sFAAa;;;;;;wEAAuB;;;;;;;;;;;;;sEAI1D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAuB;;;;;;8EACvC,6LAAC;oEAAE,WAAU;;sFACX,6LAAC;4EAAO,WAAU;sFAAa;;;;;;wEAAyB;;;;;;;;;;;;;;;;;;;;;;;;;sDAOhE,6LAAC;4CAAI,WAAU;4CAAuG,OAAO;gDAC3H,WAAW;4CACb;;8DACE,6LAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAI1C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6LAAC;4EAAI,WAAU;sFAAmD;;;;;;;;;;;;8EAEpE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6LAAC;4EAAI,WAAU;sFAAmD;;;;;;;;;;;;8EAGpE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6LAAC;4EAAI,WAAU;sFAAmD;;;;;;;;;;;;8EAEpE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6LAAC;4EAAI,WAAU;sFAAmD;;;;;;;;;;;;;;;;;;sEAGtE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6LAAC;4EAAI,WAAU;sFAAmD;;;;;;;;;;;;8EAEpE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6LAAC;4EAAI,WAAU;sFAAmD;;;;;;;;;;;;8EAEpE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6LAAC;4EAAI,WAAU;sFAAmD;;;;;;;;;;;;8EAEpE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6LAAC;4EAAI,WAAU;sFAAmD;;;;;;;;;;;;8EAEpE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6LAAC;4EAAI,WAAU;sFAAmD;;;;;;;;;;;;8EAEpE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6LAAC;4EAAI,WAAU;sFAAmD;;;;;;;;;;;;8EAEpE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6LAAC;4EAAI,WAAU;sFAAmD;;;;;;;;;;;;8EAEpE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6LAAC;4EAAI,WAAU;sFAAmD;;;;;;;;;;;;8EAEpE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;sFACxC,6LAAC;4EAAK,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAWnD,6BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAA0G,OAAO;gCAC9H,WAAW;4BACb;;8CACE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDACX;;;;;;;;;;;;8CAKH,6LAAC;oCAAI,WAAU;8CACZ,cAAc,MAAM,KAAK,kBACxB,6LAAC;wCAAE,WAAU;kDAAiC;;;;;+CAE9C,cAAc,GAAG,CAAC,CAAC,qBACjB,6LAAC;4CAAkB,WAAU;;8DAC3B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA0B,KAAK,YAAY,CAAC,WAAW;;;;;;8EACrE,6LAAC;oEAAE,WAAU;8EAAyB,KAAK,SAAS,CAAC,cAAc;;;;;;;;;;;;sEAErE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,SAAS,IAAM,eAAe,KAAK,EAAE;oEACrC,WAAW,CAAC,sDAAsD,EAChE,KAAK,UAAU,GACX,wBACA,+CACJ;8EAED,KAAK,UAAU,GAAG,MAAM;;;;;;8EAE3B,6LAAC;oEACC,SAAS,IAAM,gBAAgB;oEAC/B,WAAU;8EACX;;;;;;;;;;;;;;;;;;8DAKL,6LAAC;oDAAE,WAAU;8DAA2C,KAAK,YAAY,CAAC,WAAW;;;;;;8DACrF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAuD,KAAK,YAAY,CAAC,WAAW;;;;;;sEACpG,6LAAC;4DAAK,WAAU;sEAAuD,KAAK,YAAY,CAAC,QAAQ;;;;;;sEACjG,6LAAC;4DAAK,WAAU;sEAAuD,KAAK,YAAY,CAAC,UAAU;;;;;;;;;;;;;2CA7B7F,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;kCA0C7B,6LAAC,yIAAA,CAAA,UAAgB;wBACf,QAAQ;wBACR,SAAS,IAAM,wBAAwB;wBACvC,UAAU;wBACV,kBAAkB;wBAClB,kBAAkB,8HAAA,CAAA,YAAS,CAAC,sBAAsB,CAAC;wBACnD,cAAc;wBACd,iBAAiB;;;;;;kCAQnB,6LAAC,gJAAA,CAAA,UAAuB;wBACtB,QAAQ;wBACR,SAAS,IAAM,yBAAyB;wBACxC,gBAAgB;;;;;;oBAIjB,uBACC,6LAAC;wBAAI,WAAW,CAAC,2FAA2F,EAC1G,MAAM,IAAI,KAAK,YAAY,+CAC3B,MAAM,IAAI,KAAK,UAAU,kDACzB,iDACA;kCACA,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAuB,MAAM,OAAO;;;;;;8CACpD,6LAAC;oCACC,SAAS,IAAM,SAAS;oCACxB,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA3gDwB;KAAA", "debugId": null}}]}