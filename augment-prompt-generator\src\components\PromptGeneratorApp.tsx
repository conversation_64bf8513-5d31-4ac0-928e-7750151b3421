'use client';

import { useState, useEffect, useCallback } from 'react';
import { ProjectInput, ProjectType, Platform, Technology, Complexity } from '@/lib/types';
import { TECHNOLOGIES } from '@/lib/technologies';

import { aiService, PromptOptimizationOptions, AI_MODELS } from '@/lib/ai-service';




import AnimatedBackground from './AnimatedBackground';
import CustomSelect from './CustomSelect';
import AdvancedSettings from './AdvancedSettings';
import TypewriterTitle from './TypewriterTitle';
import ShimmerText from './ShimmerText';
import AITextLoading from './AITextLoading';

// UUID generation fallback for environments without crypto.randomUUID
const generateUUID = (): string => {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }
  // Fallback UUID generation
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};




import AdvancedTemplateBrowser from './AdvancedTemplateBrowser';

interface PromptHistory {
  id: string;
  timestamp: Date;
  projectInput: ProjectInput;
  generatedPrompt: string;
  isFavorite: boolean;
}

export default function PromptGeneratorApp() {
  const [projectInput, setProjectInput] = useState<ProjectInput>({
    projectName: '',
    projectIdea: '',
    projectType: 'web-application',
    platform: 'web',
    technologies: [],
    complexity: 'intermediate',
    features: [],
    additionalRequirements: ''
  });

  const [generatedPrompt, setGeneratedPrompt] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const [promptHistory, setPromptHistory] = useState<PromptHistory[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showUserGuide, setShowUserGuide] = useState(false);
  const [toast, setToast] = useState<{message: string, type: 'success' | 'error' | 'info'} | null>(null);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);

  const [showAdvancedTemplates, setShowAdvancedTemplates] = useState(false);
  const [aiSettings, setAiSettings] = useState<PromptOptimizationOptions>({
    model: 'deepseek/deepseek-chat-v3-0324:free',
    optimizationLevel: 'enhanced',
    includeExamples: true,
    includeConstraints: true,
    includeMetrics: false,
    targetAudience: 'developer'
  });
  const [generationMetadata, setGenerationMetadata] = useState<{ model: string; tokensUsed: number; cost: number; optimizationLevel: string } | null>(null);

  // Dropdown options
  const projectTypeOptions = [
    { value: 'web-application', label: 'Web Application', description: 'Frontend web apps, SPAs, and websites' },
    { value: 'mobile-application', label: 'Mobile Application', description: 'iOS, Android, and cross-platform apps' },
    { value: 'desktop-application', label: 'Desktop Application', description: 'Native desktop software and tools' },
    { value: 'api-backend', label: 'API/Backend Service', description: 'REST APIs, GraphQL, and backend services' },
    { value: 'data-analysis', label: 'Data Analysis Tool', description: 'Analytics, reporting, and data processing' },
    { value: 'machine-learning', label: 'Machine Learning Project', description: 'AI/ML models and applications' },
    { value: 'devops-infrastructure', label: 'DevOps Infrastructure', description: 'CI/CD, deployment, and automation' },
    { value: 'chrome-extension', label: 'Chrome Extension', description: 'Browser extensions and add-ons' },
    { value: 'cli-tool', label: 'CLI Tool', description: 'Command-line utilities and scripts' },
    { value: 'library-package', label: 'Library/Package', description: 'Reusable libraries and npm packages' }
  ];

  const platformOptions = [
    { value: 'web', label: 'Web', description: 'Browser-based applications' },
    { value: 'mobile', label: 'Mobile', description: 'iOS and Android platforms' },
    { value: 'desktop', label: 'Desktop', description: 'Windows, macOS, and Linux' },
    { value: 'server', label: 'Server', description: 'Backend and cloud services' },
    { value: 'cross-platform', label: 'Cross-Platform', description: 'Multiple platforms' }
  ];

  // Toast notification function
  const showToast = useCallback((message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3000);
  }, []);

  // Smart suggestions
  const getProjectNameSuggestions = (input: string, projectType: ProjectType) => {
    const suggestions: Record<ProjectType, string[]> = {
      'web-application': ['TaskMaster Pro', 'DataViz Dashboard', 'EcoTracker', 'ShopSmart', 'ConnectHub'],
      'mobile-application': ['FitTrack Mobile', 'LocalEats App', 'StudyBuddy', 'WeatherWise', 'PhotoShare'],
      'desktop-application': ['CodeEditor Pro', 'MediaManager', 'TaskPlanner', 'FileSync', 'NoteTaker'],
      'api-backend': ['UserAuth API', 'Payment Gateway', 'Analytics Service', 'Notification Hub', 'Data Processor'],
      'data-analysis': ['Sales Analytics', 'Customer Insights', 'Market Research Tool', 'Performance Dashboard', 'Trend Analyzer'],
      'machine-learning': ['Recommendation Engine', 'Image Classifier', 'Sentiment Analyzer', 'Fraud Detector', 'Chatbot AI'],
      'devops-infrastructure': ['CI/CD Pipeline', 'Monitoring Stack', 'Container Platform', 'Auto Scaler', 'Log Aggregator'],
      'chrome-extension': ['Productivity Booster', 'Tab Manager', 'Password Helper', 'Web Scraper', 'Time Tracker'],
      'cli-tool': ['File Processor', 'Code Generator', 'Deployment Tool', 'Data Migrator', 'System Monitor'],
      'library-package': ['UI Components', 'Utility Library', 'API Client', 'Data Validator', 'Chart Library']
    };

    return suggestions[projectType]?.filter(s =>
      s.toLowerCase().includes(input.toLowerCase())
    ) || [];
  };



  // Auto-load saved data on component mount
  useEffect(() => {
    const savedData = localStorage.getItem('promptGenerator_draft');
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);
        setProjectInput(parsed);
      } catch (error) {
        console.error('Failed to load saved data:', error);
      }
    }

    const savedHistory = localStorage.getItem('promptHistory');
    if (savedHistory) {
      try {
        const parsed = JSON.parse(savedHistory);
        setPromptHistory(parsed.map((item: { timestamp: string; [key: string]: unknown }) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        })));
      } catch (error) {
        console.error('Failed to load history:', error);
      }
    }
  }, []);

  // History management functions
  const saveToHistory = useCallback((input: ProjectInput, prompt: string) => {
    const historyItem: PromptHistory = {
      id: generateUUID(),
      timestamp: new Date(),
      projectInput: input,
      generatedPrompt: prompt,
      isFavorite: false,
    };

    const newHistory = [historyItem, ...promptHistory.slice(0, 19)]; // Keep last 20
    setPromptHistory(newHistory);
    localStorage.setItem('promptHistory', JSON.stringify(newHistory));
  }, [promptHistory]);

  const toggleFavorite = (id: string) => {
    const newHistory = promptHistory.map(item =>
      item.id === id ? { ...item, isFavorite: !item.isFavorite } : item
    );
    setPromptHistory(newHistory);
    localStorage.setItem('promptHistory', JSON.stringify(newHistory));
  };

  const loadFromHistory = (historyItem: PromptHistory) => {
    setProjectInput(historyItem.projectInput);
    setGeneratedPrompt(historyItem.generatedPrompt);
    setShowHistory(false);
  };



  const loadAdvancedTemplate = (templateData: Partial<ProjectInput>) => {
    setProjectInput(prev => ({
      ...prev,
      ...templateData
    }));
    showToast(`Loaded advanced template: ${templateData.projectName}`, 'success');


  };

  // Validation functions
  const validateField = useCallback((name: string, value: string) => {
    const newErrors = { ...errors };

    switch (name) {
      case 'projectName':
        if (!value.trim()) {
          newErrors.projectName = 'Project name is required';
        } else if (value.length < 3) {
          newErrors.projectName = 'Project name must be at least 3 characters';
        } else if (value.length > 50) {
          newErrors.projectName = 'Project name must be less than 50 characters';
        } else {
          delete newErrors.projectName;
        }
        break;
      case 'projectIdea':
        if (!value.trim()) {
          newErrors.projectIdea = 'Project concept is required';
        } else if (value.length < 20) {
          newErrors.projectIdea = 'Please provide more details (minimum 20 characters)';
        } else if (value.length > 1000) {
          newErrors.projectIdea = 'Project concept must be less than 1000 characters';
        } else {
          delete newErrors.projectIdea;
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [errors]);

  const validateForm = useCallback(() => {
    const nameValid = validateField('projectName', projectInput.projectName);
    const ideaValid = validateField('projectIdea', projectInput.projectIdea);
    return nameValid && ideaValid;
  }, [projectInput.projectName, projectInput.projectIdea, validateField]);

  const handleInputChange = (field: keyof ProjectInput, value: ProjectInput[keyof ProjectInput]) => {
    setProjectInput(prev => ({ ...prev, [field]: value }));
    setTouched(prev => ({ ...prev, [field]: true }));

    if (field === 'projectName' || field === 'projectIdea') {
      validateField(field, value as string);
    }


  };

  const handleGenerate = useCallback(async () => {
    // Validate form before generating
    if (!validateForm()) {
      setTouched({ projectName: true, projectIdea: true });
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      setGenerationProgress(prev => Math.min(prev + 10, 90));
    }, 200);

    try {
      // Get AI model recommendation if not set
      const recommendedModel = aiService.getModelRecommendation(projectInput);
      const currentSettings = { ...aiSettings };
      if (!currentSettings.model || currentSettings.model === 'auto') {
        currentSettings.model = recommendedModel;
      }

      // Use AI service for generation
      const result = await aiService.generatePrompt(projectInput, currentSettings);

      setGenerationProgress(100);
      setGeneratedPrompt(result.prompt);
      setGenerationMetadata(result.metadata);

      // Save to history with metadata
      saveToHistory(projectInput, result.prompt);



      // Show success with model info
      const modelName = Object.values(AI_MODELS).find(m => m.id === currentSettings.model)?.name || 'AI';
      showToast(`Prompt generated with ${modelName}`, 'success');

    } catch (error) {
      console.error('Error generating prompt:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setErrors({ general: `Error generating prompt: ${errorMessage}` });
      showToast('Failed to generate prompt', 'error');


    } finally {
      clearInterval(progressInterval);
      setIsGenerating(false);
      setTimeout(() => setGenerationProgress(0), 1000);
    }
  }, [projectInput, validateForm, showToast, aiSettings, saveToHistory]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'Enter':
            event.preventDefault();
            if (!isGenerating) {
              handleGenerate();
            }
            break;
          case 's':
            event.preventDefault();
            // Auto-save is already handled
            break;
          case 'k':
            event.preventDefault();
            document.querySelector<HTMLInputElement>('input[type="text"]')?.focus();
            break;
          case 'h':
            event.preventDefault();
            setShowHistory(true);
            break;

          case 't':
            event.preventDefault();
            setShowAdvancedTemplates(true);
            break;
          case ',':
            event.preventDefault();
            setShowAdvancedSettings(true);
            break;
          case 'a':
            event.preventDefault();
            setShowAdvancedSettings(true);
            break;

          case 'b':
            event.preventDefault();
            setShowAdvancedTemplates(true);
            break;
          case 'Escape':
            event.preventDefault();
            setShowHistory(false);
            setShowAdvancedTemplates(false);
            setShowAdvancedSettings(false);

            setShowAdvancedTemplates(false);
            break;
        }
      }

      if (event.key === 'Escape') {
        setShowHistory(false);
        setShowAdvancedTemplates(false);
        setShowAdvancedSettings(false);

        setShowAdvancedTemplates(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isGenerating, handleGenerate]);

  // Enhanced export functions
  const exportOptions = {
    copyToClipboard: async (text: string) => {
      try {
        await navigator.clipboard.writeText(text);
        showToast('Copied to clipboard!', 'success');

      } catch {
        showToast('Failed to copy to clipboard', 'error');
      }
    },

    downloadAsFile: (text: string, filename: string) => {
      try {
        const blob = new Blob([text], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${filename}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        showToast('File downloaded successfully!', 'success');

      } catch {
        showToast('Failed to download file', 'error');
      }
    },

    shareViaAPI: async (text: string) => {
      if (navigator.share) {
        try {
          await navigator.share({
            title: 'AI Generated Prompt for Augment Agent',
            text: text,
          });
          showToast('Shared successfully!', 'success');

        } catch (error) {
          if (error instanceof Error && error.name !== 'AbortError') {
            showToast('Failed to share', 'error');
          }
        }
      } else {
        showToast('Sharing not supported on this device', 'info');
      }
    }
  };

  const copyToClipboard = async () => {
    await exportOptions.copyToClipboard(generatedPrompt);
  };



  const clearForm = () => {
    setProjectInput({
      projectName: '',
      projectIdea: '',
      projectType: 'web-application',
      platform: 'web',
      technologies: [],
      complexity: 'intermediate',
      features: [],
      additionalRequirements: ''
    });
    setGeneratedPrompt('');
    setErrors({});
    setTouched({});
    showToast('Form cleared', 'info');
  };

  const handleTechnologyToggle = (tech: Technology) => {
    setProjectInput(prev => ({
      ...prev,
      technologies: prev.technologies.some(t => t.name === tech.name)
        ? prev.technologies.filter(t => t.name !== tech.name)
        : [...prev.technologies, tech]
    }));
  };

  return (
    <div className="min-h-screen bg-black py-4 sm:py-8 px-2 sm:px-4 relative">
      <AnimatedBackground />
      <div className="max-w-6xl mx-auto relative z-10">
        <header className="text-center mb-4 sm:mb-8 px-2">
          <TypewriterTitle
            sequences={[
              { text: "AI Prompt Generator", deleteAfter: true, pauseAfter: 1200 },
              { text: "Professional Prompts", deleteAfter: true, pauseAfter: 1200 },
              { text: "DeepSeek V3 Powered", deleteAfter: true, pauseAfter: 1200 },
              { text: "Augment Agent Ready", deleteAfter: true, pauseAfter: 2000 }
            ]}
            typingSpeed={60}
            autoLoop={true}
            loopDelay={3000}
            className="mb-4"
          />
          <ShimmerText
            text="AI-Powered Prompt Generation with DeepSeek V3 for Augment Agent"
            className="text-sm sm:text-base lg:text-lg"
          />
        </header>

        <div className="space-y-4 sm:space-y-8">
          {/* Input Form */}
          <div className="rounded-xl shadow-2xl p-3 sm:p-4 lg:p-6 transition-all duration-300 ease-in-out max-w-2xl mx-auto" style={{
            boxShadow: '0 20px 40px -12px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
          }}>
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-3 space-y-2 sm:space-y-0">
              <ShimmerText text="Project Details" className="text-lg sm:text-xl font-semibold" />
              <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
                {/* Status indicators removed for cleaner UI - auto-save still works in background */}

                <button
                  onClick={clearForm}
                  className="bg-black/5 text-white px-3 py-2 sm:px-2 sm:py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg min-h-[44px] sm:min-h-auto"
                  style={{
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
                  }}
                >
                  Clear
                </button>
                <button
                  onClick={() => setShowHistory(true)}
                  className="bg-black/5 text-white px-3 py-2 sm:px-2 sm:py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg min-h-[44px] sm:min-h-auto"
                  style={{
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
                  }}
                >
                  History ({promptHistory.length})
                </button>
                <button
                  onClick={() => setShowAdvancedTemplates(true)}
                  className="bg-gray-800 text-white px-3 py-2 sm:px-2 sm:py-1 rounded-md hover:bg-gray-700 transition-all duration-300 text-xs hover:shadow-lg min-h-[44px] sm:min-h-auto"
                  style={{
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
                  }}
                >
                  Industry Templates
                </button>
                <button
                  onClick={() => setShowAdvancedSettings(true)}
                  className="bg-black/5 text-white px-3 py-2 sm:px-2 sm:py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg min-h-[44px] sm:min-h-auto"
                  style={{
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
                  }}
                >
                  AI Settings & Integrations
                </button>

              </div>
            </div>

            {/* Error/Success Messages */}
            {errors.general && (
              <div className="mb-3 p-2 bg-gray-800 border border-gray-600 rounded-md">
                <p className="text-gray-200 text-xs">{errors.general}</p>
              </div>
            )}
            {errors.success && (
              <div className="mb-3 p-2 bg-gray-800 border border-gray-600 rounded-md">
                <p className="text-gray-200 text-xs">{errors.success}</p>
              </div>
            )}

            {/* Project Name */}
            <div className="mb-3 sm:mb-2 relative">
              <label className="block text-sm sm:text-xs font-medium mb-2 sm:mb-1">
                <ShimmerText text="Project Name *" className="text-sm sm:text-xs" />
              </label>
              <input
                type="text"
                value={projectInput.projectName}
                onChange={(e) => {
                  handleInputChange('projectName', e.target.value);
                  const newSuggestions = getProjectNameSuggestions(e.target.value, projectInput.projectType);
                  setSuggestions(newSuggestions);
                  setShowSuggestions(e.target.value.length > 0 && newSuggestions.length > 0);
                }}
                onBlur={() => {
                  setTouched(prev => ({ ...prev, projectName: true }));
                  setTimeout(() => setShowSuggestions(false), 200);
                }}
                onFocus={() => {
                  const newSuggestions = getProjectNameSuggestions(projectInput.projectName, projectInput.projectType);
                  setSuggestions(newSuggestions);
                  setShowSuggestions(projectInput.projectName.length > 0 && newSuggestions.length > 0);
                }}
                className={`w-full px-3 py-3 sm:px-2 sm:py-1.5 rounded-md focus:outline-none focus:ring-1 text-base sm:text-sm text-white bg-black/5 backdrop-blur-md placeholder-gray-400 transition-all duration-300 hover:bg-black/10 min-h-[44px] sm:min-h-auto ${
                  errors.projectName && touched.projectName
                    ? 'ring-red-400 border-red-400'
                    : 'focus:ring-white/20'
                }`}
                style={{
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
                }}
                placeholder="e.g., TaskMaster Pro"
              />

              {/* Suggestions Dropdown */}
              {showSuggestions && suggestions.length > 0 && (
                <div className="absolute z-10 w-full mt-1 bg-black/20 backdrop-blur-xl rounded-md shadow-lg border border-white/10 max-h-40 overflow-y-auto">
                  {suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => {
                        handleInputChange('projectName', suggestion);
                        setShowSuggestions(false);
                      }}
                      className="w-full text-left px-3 py-2 text-sm text-white hover:bg-black/20 transition-all duration-200 first:rounded-t-md last:rounded-b-md"
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              )}

              {errors.projectName && touched.projectName && (
                <p className="text-gray-300 text-xs mt-1">{errors.projectName}</p>
              )}
              <div className="text-xs text-gray-500 mt-1">
                {projectInput.projectName.length}/50 characters
              </div>
            </div>

            {/* Project Idea */}
            <div className="mb-2">
              <label className="block text-xs font-medium mb-1">
                <ShimmerText text="Project Concept *" className="text-xs" />
              </label>
              <textarea
                value={projectInput.projectIdea}
                onChange={(e) => handleInputChange('projectIdea', e.target.value)}
                onBlur={() => setTouched(prev => ({ ...prev, projectIdea: true }))}
                className={`w-full px-2 py-1.5 rounded-md focus:outline-none focus:ring-1 h-16 text-sm text-white bg-black/5 backdrop-blur-md placeholder-gray-400 transition-all duration-300 hover:bg-black/10 ${
                  errors.projectIdea && touched.projectIdea
                    ? 'ring-red-400 border-red-400'
                    : 'focus:ring-white/20'
                }`}
                style={{
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
                }}
                placeholder="Describe your project idea, goals, and target users..."
              />
              {errors.projectIdea && touched.projectIdea && (
                <p className="text-gray-300 text-xs mt-1">{errors.projectIdea}</p>
              )}
              <div className="text-xs text-gray-500 mt-1 space-y-1">
                <div className="flex justify-between">
                  <span>{projectInput.projectIdea.length}/1000 characters</span>
                  <span className={`${
                    projectInput.projectIdea.length < 50 ? 'text-gray-400' :
                    projectInput.projectIdea.length < 100 ? 'text-gray-300' :
                    'text-white'
                  }`}>
                    {projectInput.projectIdea.length < 50 ? 'Too short' :
                     projectInput.projectIdea.length < 100 ? 'Good start' :
                     'Great detail!'}
                  </span>
                </div>
                {projectInput.projectIdea.length < 100 && (
                  <div className="text-gray-300 text-xs">
                    💡 Tip: Include target users, main features, and business goals for better AI prompts
                  </div>
                )}
              </div>
            </div>

            {/* Project Type */}
            <div className="mb-2">
              <label className="block text-xs font-medium mb-1">
                <ShimmerText text="Project Type" className="text-xs" />
              </label>
              <CustomSelect
                value={projectInput.projectType}
                onChange={(value) => handleInputChange('projectType', value as ProjectType)}
                options={projectTypeOptions}
                placeholder="Select project type"
              />
            </div>

            {/* Platform */}
            <div className="mb-2">
              <label className="block text-xs font-medium mb-1">
                <ShimmerText text="Target Platform" className="text-xs" />
              </label>
              <CustomSelect
                value={projectInput.platform}
                onChange={(value) => handleInputChange('platform', value as Platform)}
                options={platformOptions}
                placeholder="Select target platform"
              />
            </div>

            {/* Complexity */}
            <div className="mb-2">
              <label className="block text-xs font-medium mb-1">
                <ShimmerText text="Complexity Level" className="text-xs" />
              </label>
              <div className="flex space-x-2">
                {(['simple', 'intermediate', 'advanced'] as Complexity[]).map((level) => (
                  <label
                    key={level}
                    className={`group relative flex-1 cursor-pointer transition-all duration-300 ${
                      projectInput.complexity === level ? 'transform scale-[1.02]' : ''
                    }`}
                  >
                    <input
                      type="radio"
                      value={level}
                      checked={projectInput.complexity === level}
                      onChange={(e) => handleInputChange('complexity', e.target.value as Complexity)}
                      className="sr-only"
                    />
                    <div className={`
                      px-4 py-3 rounded-lg text-center text-sm font-medium transition-all duration-300 border backdrop-blur-xl
                      ${projectInput.complexity === level
                        ? 'bg-gradient-to-br from-white/20 to-white/10 border-white/30 text-white shadow-lg ring-2 ring-white/20'
                        : 'bg-gradient-to-br from-gray-800/90 to-gray-900/90 border-white/10 text-gray-300 hover:border-white/20 hover:from-gray-700/90 hover:to-gray-800/90 hover:text-white'
                      }
                    `}
                    style={{
                      boxShadow: projectInput.complexity === level
                        ? '0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                        : '0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
                    }}
                    >
                      <span className="capitalize">{level}</span>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Technology Selection */}
            <div className="mb-2">
              <label className="block text-xs font-medium mb-1">
                <ShimmerText text="Technology Stack (Optional)" className="text-xs" />
              </label>
              <div className="space-y-2">
                {Object.entries(TECHNOLOGIES).map(([category, techs]) => (
                  <div key={category}>
                    <h4 className="font-medium text-white mb-1 capitalize text-xs">
                      {category.replace('-', ' ')}
                    </h4>
                    <div className="flex flex-wrap gap-1">
                      {techs.map((tech) => (
                        <button
                          key={tech.name}
                          type="button"
                          onClick={() => handleTechnologyToggle(tech)}
                          className={`px-2 py-0.5 text-xs rounded-full transition-all duration-300 ease-in-out ${
                            projectInput.technologies.some(t => t.name === tech.name)
                              ? 'bg-white/10 text-white backdrop-blur-md shadow-lg hover:bg-white/15'
                              : 'bg-black/5 text-white backdrop-blur-md hover:bg-black/10 hover:shadow-lg'
                          }`}
                          style={{
                            boxShadow: projectInput.technologies.some(t => t.name === tech.name)
                              ? '0 2px 8px rgba(255, 255, 255, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
                              : '0 1px 4px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
                          }}
                        >
                          {tech.name}
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Additional Requirements */}
            <div className="mb-2">
              <label className="block text-xs font-medium mb-1">
                <ShimmerText text="Additional Requirements (Optional)" className="text-xs" />
              </label>
              <textarea
                value={projectInput.additionalRequirements}
                onChange={(e) => handleInputChange('additionalRequirements', e.target.value)}
                className="w-full px-2 py-1.5 rounded-md focus:outline-none focus:ring-1 focus:ring-white/20 h-12 text-sm text-white bg-black/5 backdrop-blur-md placeholder-gray-400 transition-all duration-300 hover:bg-black/10"
                style={{
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
                }}
                placeholder="Any specific requirements, constraints, or preferences..."
              />
            </div>

            {/* Generate Button - AI ONLY */}
            <div className="space-y-1.5">
              <button
                onClick={handleGenerate}
                disabled={isGenerating || !projectInput.projectName || !projectInput.projectIdea}
                className="w-full bg-black/10 text-white py-2 px-4 rounded-lg hover:bg-black/20 disabled:bg-gray-600/10 disabled:cursor-not-allowed transition-all duration-300 font-semibold text-sm backdrop-blur-md hover:shadow-xl"
                style={{
                  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
                }}
              >
                {isGenerating ? (
                  <div className="flex flex-col items-center space-y-4">
                    <AITextLoading
                      texts={[
                        "Analyzing requirements...",
                        "Structuring prompt...",
                        "Optimizing for AI...",
                        "Finalizing output...",
                        "Almost ready..."
                      ]}
                      interval={1800}
                      className="text-lg"
                    />
                    <div className="w-full bg-black/20 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-white to-gray-300 h-2 rounded-full transition-all duration-500 ease-out"
                        style={{ width: `${generationProgress}%` }}
                      ></div>
                    </div>
                    <span className="text-xs text-gray-400">{generationProgress}%</span>
                  </div>
                ) : (
                  'Generate AI Prompt'
                )}
              </button>


            </div>
          </div>

          {/* Generated Prompt Display */}
          <div className="rounded-xl shadow-2xl p-3 transition-all duration-300 ease-in-out max-w-2xl mx-auto" style={{
            boxShadow: '0 20px 40px -12px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
          }}>
            <div className="flex justify-between items-center mb-2">
              <div>
                <ShimmerText text="Generated Prompt" className="text-xl font-semibold" />
                {generatedPrompt && (
                  <div className="text-xs text-gray-400 mt-1 space-y-1">
                    <div>
                      {generatedPrompt.split(' ').length} words • {Math.ceil(generatedPrompt.split(' ').length / 200)} min read
                    </div>
                    {generationMetadata && (
                      <div className="flex items-center space-x-2">
                        <span>Generated with {Object.values(AI_MODELS).find(m => m.id === generationMetadata.model)?.name || 'AI'}</span>
                        {generationMetadata.tokensUsed && (
                          <span>• {generationMetadata.tokensUsed} tokens</span>
                        )}
                        <span className={`px-1.5 py-0.5 rounded text-xs ${
                          generationMetadata.optimizationLevel === 'expert' ? 'bg-gray-700 text-gray-200' :
                          generationMetadata.optimizationLevel === 'enhanced' ? 'bg-gray-600 text-gray-200' :
                          'bg-gray-500 text-gray-200'
                        }`}>
                          {generationMetadata.optimizationLevel}
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </div>
              {generatedPrompt && (
                <div className="flex space-x-2">
                  <button
                    onClick={copyToClipboard}
                    className="bg-black/5 text-white px-2 py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg"
                    style={{
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
                    }}
                  >
                    Copy
                  </button>
                  <button
                    onClick={() => exportOptions.downloadAsFile(generatedPrompt, `augment-prompt-${Date.now()}`)}
                    className="bg-black/5 text-white px-2 py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg"
                    style={{
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
                    }}
                  >
                    Download
                  </button>
                  {typeof navigator !== 'undefined' && 'share' in navigator && (
                    <button
                      onClick={() => exportOptions.shareViaAPI(generatedPrompt)}
                      className="bg-black/5 text-white px-2 py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg"
                      style={{
                        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
                      }}
                    >
                      Share
                    </button>
                  )}

                </div>
              )}
            </div>

            {generatedPrompt ? (
              <div className="bg-black/5 backdrop-blur-md rounded-lg p-2 max-h-60 overflow-y-auto transition-all duration-300 hover:bg-black/10" style={{
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
              }}>
                <pre className="whitespace-pre-wrap text-xs text-white font-mono">
                  {generatedPrompt}
                </pre>
              </div>
            ) : (
              <div className="bg-black/3 backdrop-blur-lg rounded-lg p-4 text-center transition-all duration-300 hover:bg-black/8" style={{
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
              }}>
                <h3 className="text-lg font-semibold text-white mb-2">AI-Powered Prompt Generation</h3>
                <p className="text-gray-400 mb-2 text-sm">Fill in your project details and let DeepSeek V3 create the perfect Augment Agent prompt for you.</p>
                <div className="text-xs text-white font-medium">
                  Personalized • Optimized • Professional
                </div>
              </div>
            )}
          </div>
        </div>

        {/* User Guide Toggle Button */}
        <div className="mt-8 sm:mt-16 text-center">
          <button
            onClick={() => setShowUserGuide(!showUserGuide)}
            className="bg-black/5 backdrop-blur-xl text-white px-6 py-3 sm:px-4 sm:py-2 rounded-xl hover:bg-black/10 transition-all duration-300 ease-in-out min-h-[44px] sm:min-h-auto text-base sm:text-sm font-medium"
            style={{
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
            }}
          >
            📖 {showUserGuide ? 'Hide' : 'Show'} Complete User Guide
          </button>
        </div>

        {/* Comprehensive Documentation Section */}
        <div className={`mt-4 sm:mt-8 rounded-2xl shadow-2xl transition-all duration-500 ease-in-out overflow-hidden ${
          showUserGuide ? 'max-h-none opacity-100 p-4 sm:p-6' : 'max-h-0 opacity-0 p-0'
        }`} style={{
          boxShadow: showUserGuide ? '0 25px 50px -12px rgba(0, 0, 0, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.05)' : 'none'
        }}>
          {showUserGuide && (
            <>
              <div className="flex justify-between items-center mb-4 sm:mb-6">
                <h2 className="text-2xl sm:text-3xl font-bold text-white">Complete User Guide</h2>
                <button
                  onClick={() => setShowUserGuide(false)}
                  className="bg-black/5 text-white px-3 py-2 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg min-h-[44px] sm:min-h-auto"
                  style={{
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
                  }}
                >
                  ✕ Close
                </button>
              </div>
              <p className="text-center text-gray-400 mb-4 sm:mb-6 text-sm sm:text-lg">
                Learn how to use each feature effectively to generate the best AI-powered prompts for your projects
              </p>

              <div className="space-y-6">
            {/* Project Details Section */}
            <div className="rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out" style={{
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
            }}>
              <h3 className="text-2xl font-bold text-white mb-3">Project Details</h3>

              <div className="space-y-4">
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Project Name</h4>
                  <p className="text-gray-400">
                    Enter a descriptive name for your project. This helps the AI understand the scope and purpose.
                    Examples: &ldquo;TaskMaster Pro&rdquo;, &ldquo;E-commerce Platform&rdquo;, &ldquo;Mobile Banking App&rdquo;
                  </p>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Project Concept</h4>
                  <p className="text-gray-400">
                    Describe your project idea in detail. Include the main purpose, target users, key features, and goals.
                    The more specific you are, the better the AI can tailor the prompt to your needs.
                  </p>
                </div>
              </div>
            </div>

            {/* Project Types Section */}
            <div className="rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out" style={{
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
            }}>
              <h3 className="text-2xl font-bold text-white mb-3">Project Types</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Web Application</h4>
                  <p className="text-gray-400 text-sm">
                    Choose this for websites, web apps, or browser-based applications. Includes both frontend and backend development.
                  </p>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Mobile Application</h4>
                  <p className="text-gray-400 text-sm">
                    Select for iOS, Android, or cross-platform mobile apps. Covers native and hybrid development approaches.
                  </p>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Desktop Application</h4>
                  <p className="text-gray-400 text-sm">
                    For Windows, macOS, or Linux desktop software. Includes cross-platform and native desktop solutions.
                  </p>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">API/Backend Service</h4>
                  <p className="text-gray-400 text-sm">
                    Choose for server-side applications, REST APIs, microservices, or backend systems without a user interface.
                  </p>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Data Analysis Tool</h4>
                  <p className="text-gray-400 text-sm">
                    For data processing, analytics, visualization, or machine learning applications and tools.
                  </p>
                </div>
              </div>
            </div>

            {/* Platform Options Section */}
            <div className="rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out" style={{
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
            }}>
              <h3 className="text-2xl font-bold text-white mb-3">Platform Options</h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Web</h4>
                  <p className="text-gray-400 text-sm">
                    Browser-based applications accessible via web browsers. Includes responsive design for all devices.
                  </p>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Mobile</h4>
                  <p className="text-gray-400 text-sm">
                    Applications designed specifically for smartphones and tablets, with mobile-optimized interfaces.
                  </p>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Desktop</h4>
                  <p className="text-gray-400 text-sm">
                    Native applications that run directly on desktop operating systems like Windows, macOS, or Linux.
                  </p>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Server</h4>
                  <p className="text-gray-400 text-sm">
                    Backend services, APIs, and server-side applications that run on servers or cloud infrastructure.
                  </p>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Cloud</h4>
                  <p className="text-gray-400 text-sm">
                    Cloud-native applications designed to leverage cloud services and distributed computing resources.
                  </p>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Cross-platform</h4>
                  <p className="text-gray-400 text-sm">
                    Applications that work across multiple platforms using frameworks like Electron, Flutter, or React Native.
                  </p>
                </div>
              </div>
            </div>
            {/* Complexity Levels Section */}
            <div className="rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out" style={{
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
            }}>
              <h3 className="text-2xl font-bold text-white mb-3">Complexity Levels</h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Simple</h4>
                  <p className="text-gray-400 text-sm">
                    Basic functionality with minimal features. Quick to develop, perfect for MVPs, prototypes, or learning projects.
                    Focuses on core features only.
                  </p>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Intermediate</h4>
                  <p className="text-gray-400 text-sm">
                    Moderate complexity with additional features, user authentication, database integration, and proper error handling.
                    Suitable for most business applications.
                  </p>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Advanced</h4>
                  <p className="text-gray-400 text-sm">
                    Enterprise-level complexity with scalability, performance optimization, advanced security, microservices,
                    and comprehensive testing strategies.
                  </p>
                </div>
              </div>
            </div>

            {/* Technology Stack Section */}
            <div className="rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out" style={{
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
            }}>
              <h3 className="text-2xl font-bold text-white mb-3">Technology Stack Guide</h3>
              <p className="text-gray-400 mb-4">
                A technology stack is the combination of programming languages, frameworks, libraries, and tools used to build your application.
                Selecting the right technologies is crucial for project success.
              </p>

              <div className="space-y-4">
                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Frontend Technologies</h4>
                  <p className="text-gray-400 text-sm mb-2">
                    Frontend development creates the user interface and user experience. Choose technologies based on your project needs:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                    <div><strong className="text-white">React:</strong> Popular, component-based, large ecosystem</div>
                    <div><strong className="text-white">Vue.js:</strong> Progressive, easy to learn, flexible</div>
                    <div><strong className="text-white">Angular:</strong> Full framework, TypeScript-based, enterprise-ready</div>
                    <div><strong className="text-white">Next.js:</strong> React framework with SSR and static generation</div>
                    <div><strong className="text-white">Svelte:</strong> Compile-time optimized, smaller bundle sizes</div>
                    <div><strong className="text-white">TypeScript:</strong> Adds type safety to JavaScript</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Backend Technologies</h4>
                  <p className="text-gray-400 text-sm mb-2">
                    Backend development handles server-side logic, databases, and APIs:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                    <div><strong className="text-white">Node.js:</strong> JavaScript runtime, fast development</div>
                    <div><strong className="text-white">Python/Django:</strong> Rapid development, batteries included</div>
                    <div><strong className="text-white">Python/FastAPI:</strong> Modern, fast, automatic API docs</div>
                    <div><strong className="text-white">Go:</strong> High performance, excellent concurrency</div>
                    <div><strong className="text-white">Rust:</strong> Memory safety, extreme performance</div>
                    <div><strong className="text-white">Java/Spring:</strong> Enterprise-grade, mature ecosystem</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Database Options</h4>
                  <p className="text-gray-400 text-sm mb-2">
                    Choose databases based on your data structure and scalability needs:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                    <div><strong className="text-white">PostgreSQL:</strong> Advanced relational database, JSON support</div>
                    <div><strong className="text-white">MongoDB:</strong> NoSQL document database, flexible schema</div>
                    <div><strong className="text-white">SQLite:</strong> Lightweight, embedded, perfect for small apps</div>
                    <div><strong className="text-white">Redis:</strong> In-memory store, caching, real-time features</div>
                    <div><strong className="text-white">Supabase:</strong> Open-source Firebase alternative</div>
                    <div><strong className="text-white">Firebase:</strong> Google&apos;s platform, real-time features</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Styling Technologies</h4>
                  <p className="text-gray-400 text-sm mb-2">
                    Choose styling approaches based on your project needs and team preferences:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                    <div><strong className="text-white">Tailwind CSS:</strong> Utility-first CSS framework</div>
                    <div><strong className="text-white">CSS Modules:</strong> Scoped CSS with local class names</div>
                    <div><strong className="text-white">Styled Components:</strong> CSS-in-JS with component styling</div>
                    <div><strong className="text-white">SCSS/Sass:</strong> CSS preprocessor with variables and mixins</div>
                    <div><strong className="text-white">Material-UI:</strong> React components with Material Design</div>
                    <div><strong className="text-white">Chakra UI:</strong> Simple, modular React component library</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Mobile Frameworks</h4>
                  <p className="text-gray-400 text-sm mb-2">
                    Cross-platform and native mobile development frameworks:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                    <div><strong className="text-white">React Native:</strong> Cross-platform with React</div>
                    <div><strong className="text-white">Flutter:</strong> Google&apos;s UI toolkit for mobile</div>
                    <div><strong className="text-white">Swift:</strong> Native iOS development language</div>
                    <div><strong className="text-white">Kotlin:</strong> Modern Android development language</div>
                    <div><strong className="text-white">Expo:</strong> Platform for React Native development</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Desktop Frameworks</h4>
                  <p className="text-gray-400 text-sm mb-2">
                    Cross-platform and native desktop application frameworks:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                    <div><strong className="text-white">Electron:</strong> Web technologies for desktop apps</div>
                    <div><strong className="text-white">Tauri:</strong> Rust-based lightweight desktop framework</div>
                    <div><strong className="text-white">Qt:</strong> Cross-platform C++ application framework</div>
                    <div><strong className="text-white">WPF:</strong> Windows Presentation Foundation for .NET</div>
                    <div><strong className="text-white">JavaFX:</strong> Java platform for desktop applications</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Deployment Platforms</h4>
                  <p className="text-gray-400 text-sm mb-2">
                    Modern deployment and hosting solutions for applications:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                    <div><strong className="text-white">Vercel:</strong> Frontend deployment with edge functions</div>
                    <div><strong className="text-white">Netlify:</strong> JAMstack deployment and hosting</div>
                    <div><strong className="text-white">AWS:</strong> Amazon Web Services cloud platform</div>
                    <div><strong className="text-white">Google Cloud:</strong> Google&apos;s cloud computing services</div>
                    <div><strong className="text-white">Docker:</strong> Containerization platform</div>
                    <div><strong className="text-white">Kubernetes:</strong> Container orchestration system</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Testing Frameworks</h4>
                  <p className="text-gray-400 text-sm mb-2">
                    Testing tools and frameworks for quality assurance:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                    <div><strong className="text-white">Jest:</strong> JavaScript testing framework</div>
                    <div><strong className="text-white">Vitest:</strong> Fast unit testing framework</div>
                    <div><strong className="text-white">Cypress:</strong> End-to-end testing framework</div>
                    <div><strong className="text-white">Playwright:</strong> Cross-browser automation testing</div>
                    <div><strong className="text-white">React Testing Library:</strong> React component testing</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Authentication Solutions</h4>
                  <p className="text-gray-400 text-sm mb-2">
                    User authentication and authorization services:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                    <div><strong className="text-white">Auth0:</strong> Identity platform as a service</div>
                    <div><strong className="text-white">Firebase Auth:</strong> Google&apos;s authentication service</div>
                    <div><strong className="text-white">NextAuth.js:</strong> Authentication for Next.js</div>
                    <div><strong className="text-white">Supabase Auth:</strong> Open-source authentication</div>
                    <div><strong className="text-white">JWT:</strong> JSON Web Tokens for stateless auth</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">State Management</h4>
                  <p className="text-gray-400 text-sm mb-2">
                    Tools for managing application state across components and user sessions:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                    <div><strong className="text-white">Redux Toolkit:</strong> Modern Redux with simplified API</div>
                    <div><strong className="text-white">Zustand:</strong> Lightweight state management solution</div>
                    <div><strong className="text-white">Recoil:</strong> Experimental state management for React</div>
                    <div><strong className="text-white">MobX:</strong> Reactive state management through observables</div>
                    <div><strong className="text-white">Context API:</strong> React&apos;s built-in state management</div>
                    <div><strong className="text-white">Valtio:</strong> Proxy-based state management</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">API Tools</h4>
                  <p className="text-gray-400 text-sm mb-2">
                    Tools for API development, data fetching, and client-server communication:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                    <div><strong className="text-white">GraphQL:</strong> Query language for APIs</div>
                    <div><strong className="text-white">Apollo Client:</strong> Comprehensive GraphQL client</div>
                    <div><strong className="text-white">React Query:</strong> Data fetching and caching library</div>
                    <div><strong className="text-white">SWR:</strong> Data fetching with caching and revalidation</div>
                    <div><strong className="text-white">Axios:</strong> Promise-based HTTP client</div>
                    <div><strong className="text-white">tRPC:</strong> End-to-end typesafe APIs</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Monitoring & Analytics</h4>
                  <p className="text-gray-400 text-sm mb-2">
                    Tools for application monitoring, error tracking, and performance analysis:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                    <div><strong className="text-white">Sentry:</strong> Error tracking and performance monitoring</div>
                    <div><strong className="text-white">LogRocket:</strong> Session replay and logging</div>
                    <div><strong className="text-white">New Relic:</strong> Application performance monitoring</div>
                    <div><strong className="text-white">Datadog:</strong> Infrastructure and application monitoring</div>
                    <div><strong className="text-white">Prometheus:</strong> Open-source monitoring and alerting</div>
                    <div><strong className="text-white">Grafana:</strong> Analytics and monitoring dashboards</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">CI/CD & DevOps</h4>
                  <p className="text-gray-400 text-sm mb-2">
                    Continuous integration and deployment tools for automated workflows:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                    <div><strong className="text-white">GitHub Actions:</strong> CI/CD platform integrated with GitHub</div>
                    <div><strong className="text-white">GitLab CI:</strong> Built-in CI/CD for GitLab</div>
                    <div><strong className="text-white">Jenkins:</strong> Open-source automation server</div>
                    <div><strong className="text-white">CircleCI:</strong> Cloud-based CI/CD platform</div>
                    <div><strong className="text-white">Azure DevOps:</strong> Microsoft&apos;s DevOps platform</div>
                    <div><strong className="text-white">Travis CI:</strong> Hosted CI service for GitHub projects</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Version Control</h4>
                  <p className="text-gray-400 text-sm mb-2">
                    Version control systems and repository hosting platforms:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                    <div><strong className="text-white">Git:</strong> Distributed version control system</div>
                    <div><strong className="text-white">GitHub:</strong> Git hosting with collaboration features</div>
                    <div><strong className="text-white">GitLab:</strong> DevOps platform with Git repository</div>
                    <div><strong className="text-white">Bitbucket:</strong> Git solution for teams</div>
                    <div><strong className="text-white">Azure Repos:</strong> Git repositories in Azure DevOps</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Package Managers</h4>
                  <p className="text-gray-400 text-sm mb-2">
                    Tools for managing project dependencies and packages:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                    <div><strong className="text-white">npm:</strong> Node.js package manager</div>
                    <div><strong className="text-white">Yarn:</strong> Fast, reliable package manager</div>
                    <div><strong className="text-white">pnpm:</strong> Efficient package manager with shared dependencies</div>
                    <div><strong className="text-white">Bun:</strong> Fast all-in-one JavaScript runtime and package manager</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Build Tools</h4>
                  <p className="text-gray-400 text-sm mb-2">
                    Tools for bundling, compiling, and optimizing application code:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                    <div><strong className="text-white">Webpack:</strong> Module bundler for JavaScript applications</div>
                    <div><strong className="text-white">Vite:</strong> Fast build tool for modern web projects</div>
                    <div><strong className="text-white">Rollup:</strong> Module bundler for JavaScript libraries</div>
                    <div><strong className="text-white">Parcel:</strong> Zero-configuration build tool</div>
                    <div><strong className="text-white">esbuild:</strong> Extremely fast JavaScript bundler</div>
                    <div><strong className="text-white">Turbopack:</strong> Incremental bundler optimized for JavaScript and TypeScript</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Code Quality</h4>
                  <p className="text-gray-400 text-sm mb-2">
                    Tools for maintaining code quality, formatting, and automated code review:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                    <div><strong className="text-white">ESLint:</strong> JavaScript linting utility</div>
                    <div><strong className="text-white">Prettier:</strong> Code formatter</div>
                    <div><strong className="text-white">Husky:</strong> Git hooks for code quality</div>
                    <div><strong className="text-white">lint-staged:</strong> Run linters on staged files</div>
                    <div><strong className="text-white">SonarQube:</strong> Code quality and security analysis</div>
                    <div><strong className="text-white">CodeClimate:</strong> Automated code review and quality analytics</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Documentation</h4>
                  <p className="text-gray-400 text-sm mb-2">
                    Tools for creating and maintaining project documentation:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400">
                    <div><strong className="text-white">Storybook:</strong> Tool for building UI components in isolation</div>
                    <div><strong className="text-white">Docusaurus:</strong> Documentation website generator</div>
                    <div><strong className="text-white">GitBook:</strong> Documentation platform</div>
                    <div><strong className="text-white">Notion:</strong> All-in-one workspace for documentation</div>
                    <div><strong className="text-white">Confluence:</strong> Team collaboration and documentation</div>
                    <div><strong className="text-white">JSDoc:</strong> API documentation generator for JavaScript</div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-semibold text-white mb-2">Additional Requirements</h4>
                  <p className="text-gray-400 text-sm">
                    Use this field to specify any special requirements, constraints, integrations, performance needs,
                    security requirements, or specific features not covered in the main form. Be as detailed as possible
                    to help the AI generate the most relevant prompt.
                  </p>
                </div>
              </div>
            </div>

            {/* Tips Section */}
            <div className="rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out" style={{
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
            }}>
              <h3 className="text-2xl font-bold text-white mb-3">Tips for Best Results</h3>

              <div className="space-y-3">
                <div className="flex items-start space-x-2">
                  <span className="text-white font-bold">1.</span>
                  <p className="text-gray-400 text-sm">
                    <strong className="text-white">Be Specific:</strong> The more detailed your project description, the better the AI can tailor the prompt to your exact needs.
                  </p>
                </div>

                <div className="flex items-start space-x-2">
                  <span className="text-white font-bold">2.</span>
                  <p className="text-gray-400 text-sm">
                    <strong className="text-white">Choose Appropriate Complexity:</strong> Match the complexity level to your timeline, budget, and technical requirements.
                  </p>
                </div>

                <div className="flex items-start space-x-2">
                  <span className="text-white font-bold">3.</span>
                  <p className="text-gray-400 text-sm">
                    <strong className="text-white">Select Relevant Technologies:</strong> If you&apos;re unsure about technologies, leave them blank and let the AI suggest appropriate options.
                  </p>
                </div>

                <div className="flex items-start space-x-2">
                  <span className="text-white font-bold">4.</span>
                  <p className="text-gray-400 text-sm">
                    <strong className="text-white">Use Templates:</strong> Click &quot;Project Templates&quot; to explore examples and see how to structure your project information effectively.
                  </p>
                </div>

                <div className="flex items-start space-x-2">
                  <span className="text-white font-bold">5.</span>
                  <p className="text-gray-400 text-sm">
                    <strong className="text-white">Include Context:</strong> Mention your target audience, business goals, and any existing systems you need to integrate with.
                  </p>
                </div>
              </div>
            </div>

            {/* Keyboard Shortcuts Section */}
            <div className="rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out" style={{
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
            }}>
              <h3 className="text-2xl font-bold text-white mb-3">Keyboard Shortcuts</h3>
              <p className="text-gray-400 mb-4 text-sm">
                Use these keyboard shortcuts to work more efficiently:
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">Generate Prompt</span>
                    <kbd className="bg-black/20 text-white px-2 py-1 rounded text-xs">Ctrl + Enter</kbd>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">Focus Project Name</span>
                    <kbd className="bg-black/20 text-white px-2 py-1 rounded text-xs">Ctrl + K</kbd>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">Open Templates</span>
                    <kbd className="bg-black/20 text-white px-2 py-1 rounded text-xs">Ctrl + T</kbd>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">AI Settings</span>
                    <kbd className="bg-black/20 text-white px-2 py-1 rounded text-xs">Ctrl + ,</kbd>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">Open History</span>
                    <kbd className="bg-black/20 text-white px-2 py-1 rounded text-xs">Ctrl + H</kbd>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">AI Settings & Integrations</span>
                    <kbd className="bg-black/20 text-white px-2 py-1 rounded text-xs">Ctrl + A</kbd>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">Team Dashboard</span>
                    <kbd className="bg-black/20 text-white px-2 py-1 rounded text-xs">Ctrl + M</kbd>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">Share Prompt</span>
                    <kbd className="bg-black/20 text-white px-2 py-1 rounded text-xs">Ctrl + S</kbd>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">Integrations</span>
                    <kbd className="bg-black/20 text-white px-2 py-1 rounded text-xs">Ctrl + I</kbd>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">Live Collaboration</span>
                    <kbd className="bg-black/20 text-white px-2 py-1 rounded text-xs">Ctrl + R</kbd>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">Industry Templates</span>
                    <kbd className="bg-black/20 text-white px-2 py-1 rounded text-xs">Ctrl + B</kbd>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">Close Modal</span>
                    <kbd className="bg-black/20 text-white px-2 py-1 rounded text-xs">Escape</kbd>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400 text-sm">Auto-save</span>
                    <span className="text-gray-300 text-xs">Automatic</span>
                  </div>
                </div>
              </div>
            </div>
              </div>
            </>
          )}
        </div>

        {/* History Modal */}
        {showHistory && (
          <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
            <div className="bg-black border border-white/20 rounded-xl shadow-2xl p-6 max-w-4xl w-full max-h-[80vh] overflow-hidden" style={{
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8)'
            }}>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-semibold text-white">Prompt History</h3>
                <button
                  onClick={() => setShowHistory(false)}
                  className="bg-gray-800 text-white px-3 py-1 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm"
                >
                  Close
                </button>
              </div>

              <div className="overflow-y-auto max-h-[60vh] space-y-3">
                {promptHistory.length === 0 ? (
                  <p className="text-gray-400 text-center py-8">No prompts generated yet</p>
                ) : (
                  promptHistory.map((item) => (
                    <div key={item.id} className="bg-gray-900 border border-white/10 rounded-lg p-4 hover:bg-gray-800 transition-all duration-300">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h4 className="text-white font-medium">{item.projectInput.projectName}</h4>
                          <p className="text-xs text-gray-400">{item.timestamp.toLocaleString()}</p>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => toggleFavorite(item.id)}
                            className={`px-2 py-1 rounded text-xs transition-all duration-300 ${
                              item.isFavorite
                                ? 'bg-white text-black'
                                : 'bg-gray-700 text-gray-400 hover:bg-gray-600'
                            }`}
                          >
                            {item.isFavorite ? '★' : '☆'}
                          </button>
                          <button
                            onClick={() => loadFromHistory(item)}
                            className="bg-gray-700 text-white px-2 py-1 rounded text-xs hover:bg-gray-600 transition-all duration-300"
                          >
                            Load
                          </button>
                        </div>
                      </div>
                      <p className="text-sm text-gray-300 mb-2 line-clamp-2">{item.projectInput.projectIdea}</p>
                      <div className="flex flex-wrap gap-1 mb-2">
                        <span className="bg-gray-700 text-xs px-2 py-1 rounded text-gray-300">{item.projectInput.projectType}</span>
                        <span className="bg-gray-700 text-xs px-2 py-1 rounded text-gray-300">{item.projectInput.platform}</span>
                        <span className="bg-gray-700 text-xs px-2 py-1 rounded text-gray-300">{item.projectInput.complexity}</span>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        )}



        {/* Advanced Settings Modal */}
        <AdvancedSettings
          isOpen={showAdvancedSettings}
          onClose={() => setShowAdvancedSettings(false)}
          settings={aiSettings}
          onSettingsChange={setAiSettings}
          recommendedModel={aiService.getModelRecommendation(projectInput)}
          projectInput={projectInput}
          generatedPrompt={generatedPrompt}
        />





        {/* Advanced Template Browser Modal */}
        <AdvancedTemplateBrowser
          isOpen={showAdvancedTemplates}
          onClose={() => setShowAdvancedTemplates(false)}
          onLoadTemplate={loadAdvancedTemplate}
        />

        {/* Toast Notification */}
        {toast && (
          <div className={`fixed top-4 right-4 z-50 px-4 py-2 rounded-lg transition-all duration-300 animate-slide-up ${
            toast.type === 'success' ? 'bg-white border border-gray-300 text-black' :
            toast.type === 'error' ? 'bg-gray-900 border border-gray-600 text-white' :
            'bg-gray-800 border border-gray-600 text-white'
          }`}>
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">{toast.message}</span>
              <button
                onClick={() => setToast(null)}
                className="text-xs opacity-70 hover:opacity-100 transition-opacity"
              >
                ✕
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
