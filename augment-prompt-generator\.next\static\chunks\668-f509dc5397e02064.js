"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[668],{10:(t,e,i)=>{i.d(e,{V:()=>h,f:()=>m});var r=i(4272);let n=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var s=i(614),o=i(1557);let a="number",l="color",u=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function h(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},s=[],o=0,h=e.replace(u,t=>(r.y.test(t)?(n.color.push(o),s.push(l),i.push(r.y.parse(t))):t.startsWith("var(")?(n.var.push(o),s.push("var"),i.push(t)):(n.number.push(o),s.push(a),i.push(parseFloat(t))),++o,"${}")).split("${}");return{values:i,split:h,indexes:n,types:s}}function d(t){return h(t).values}function c(t){let{split:e,types:i}=h(t),n=e.length;return t=>{let s="";for(let u=0;u<n;u++)if(s+=e[u],void 0!==t[u]){let e=i[u];e===a?s+=(0,o.a)(t[u]):e===l?s+=r.y.transform(t[u]):s+=t[u]}return s}}let p=t=>"number"==typeof t?0:r.y.test(t)?r.y.getAnimatableNone(t):t,m={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(s.S)?.length||0)+(t.match(n)?.length||0)>0},parse:d,createTransformer:c,getAnimatableNone:function(t){let e=d(t);return c(t)(e.map(p))}}},18:(t,e,i)=>{i.d(e,{U:()=>r,f:()=>n});let r=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],n=new Set(r)},98:(t,e,i)=>{i.d(e,{OQ:()=>h});var r=i(5626),n=i(2923),s=i(4261),o=i(9515);let a=t=>!isNaN(parseFloat(t)),l={current:void 0};class u{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=s.k.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=s.k.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=a(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new r.v);let i=this.events[t].add(e);return"change"===t?()=>{i(),o.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return l.current&&l.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=s.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,n.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function h(t,e){return new u(t,e)}},182:(t,e,i)=>{i.d(e,{P:()=>iM});var r,n,s=i(6340),o=i(419),a=i(7934);function l(t,e,i={}){let r=(0,o.K)(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(n=i.transitionOverride);let s=r?()=>Promise.all((0,a.$)(t,r,i)):()=>Promise.resolve(),h=t.variantChildren&&t.variantChildren.size?(r=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=n;return function(t,e,i=0,r=0,n=1,s){let o=[],a=(t.variantChildren.size-1)*r,h=1===n?(t=0)=>t*r:(t=0)=>a-t*r;return Array.from(t.variantChildren).sort(u).forEach((t,r)=>{t.notify("AnimationStart",e),o.push(l(t,e,{...s,delay:i+h(r)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+r,o,a,i)}:()=>Promise.resolve(),{when:d}=n;if(!d)return Promise.all([s(),h(i.delay)]);{let[t,e]="beforeChildren"===d?[s,h]:[h,s];return t().then(()=>e())}}function u(t,e){return t.sortNodePosition(e)}var h=i(5910);function d(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let r=0;r<i;r++)if(e[r]!==t[r])return!1;return!0}var c=i(5305),p=i(8312);let m=p._.length,f=[...p.U].reverse(),g=p.U.length;function v(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function y(){return{animate:v(!0),whileInView:v(),whileHover:v(),whileTap:v(),whileDrag:v(),whileFocus:v(),exit:v()}}class x{constructor(t){this.isMounted=!1,this.node=t}update(){}}class b extends x{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>l(t,e,i)));else if("string"==typeof e)r=l(t,e,i);else{let n="function"==typeof e?(0,o.K)(t,e,i.custom):e;r=Promise.all((0,a.$)(t,n,i))}return r.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=y(),r=!0,n=e=>(i,r)=>{let n=(0,o.K)(t,r,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...r}=n;i={...i,...r,...e}}return i};function u(a){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<m;t++){let r=p._[t],n=e.props[r];((0,c.w)(n)||!1===n)&&(i[r]=n)}return i}(t.parent)||{},v=[],y=new Set,x={},b=1/0;for(let e=0;e<g;e++){var w,k;let o=f[e],p=i[o],m=void 0!==l[o]?l[o]:u[o],g=(0,c.w)(m),T=o===a?p.isActive:null;!1===T&&(b=e);let P=m===u[o]&&m!==l[o]&&g;if(P&&r&&t.manuallyAnimateOnMount&&(P=!1),p.protectedKeys={...x},!p.isActive&&null===T||!m&&!p.prevProp||(0,s.N)(m)||"boolean"==typeof m)continue;let S=(w=p.prevProp,"string"==typeof(k=m)?k!==w:!!Array.isArray(k)&&!d(k,w)),A=S||o===a&&p.isActive&&!P&&g||e>b&&g,M=!1,V=Array.isArray(m)?m:[m],E=V.reduce(n(o),{});!1===T&&(E={});let{prevResolvedValues:C={}}=p,D={...C,...E},j=e=>{A=!0,y.has(e)&&(M=!0,y.delete(e)),p.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in D){let e=E[t],i=C[t];if(x.hasOwnProperty(t))continue;let r=!1;((0,h.p)(e)&&(0,h.p)(i)?d(e,i):e===i)?void 0!==e&&y.has(t)?j(t):p.protectedKeys[t]=!0:null!=e?j(t):y.add(t)}p.prevProp=m,p.prevResolvedValues=E,p.isActive&&(x={...x,...E}),r&&t.blockInitialAnimation&&(A=!1);let R=!(P&&S)||M;A&&R&&v.push(...V.map(t=>({animation:t,options:{type:o}})))}if(y.size){let e={};if("boolean"!=typeof l.initial){let i=(0,o.K)(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}y.forEach(i=>{let r=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=r??null}),v.push({animation:e})}let T=!!v.length;return r&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(T=!1),r=!1,T?e(v):Promise.resolve()}return{animateChanges:u,setActive:function(e,r){if(i[e].isActive===r)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,r)),i[e].isActive=r;let n=u(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=y(),r=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();(0,s.N)(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let w=0;class k extends x{constructor(){super(...arguments),this.id=w++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let r=this.node.animationState.setActive("exit",!t);e&&!t&&r.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}var T=i(9827);let P={x:!1,y:!1};var S=i(4158),A=i(9515),M=i(3210),V=i(4542),E=i(6051);function C(t,e,i,r={passive:!0}){return t.addEventListener(e,i,r),()=>t.removeEventListener(e,i)}let D=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function j(t){return{point:{x:t.pageX,y:t.pageY}}}let R=t=>e=>D(e)&&t(e,j(e));function L(t,e,i,r){return C(t,e,R(i),r)}var F=i(8588);function B(t){return t.max-t.min}function O(t,e,i,r=.5){t.origin=r,t.originPoint=(0,M.k)(e.min,e.max,t.origin),t.scale=B(i)/B(e),t.translate=(0,M.k)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function I(t,e,i,r){O(t.x,e.x,i.x,r?r.originX:void 0),O(t.y,e.y,i.y,r?r.originY:void 0)}function z(t,e,i){t.min=i.min+e.min,t.max=t.min+B(e)}function U(t,e,i){t.min=e.min-i.min,t.max=t.min+B(e)}function W(t,e,i){U(t.x,e.x,i.x),U(t.y,e.y,i.y)}var N=i(1786);function $(t){return[t("x"),t("y")]}var G=i(3757);let q=({current:t})=>t?t.ownerDocument.defaultView:null;function X(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}var K=i(6333),H=i(3191),Y=i(7215);let _=(t,e)=>Math.abs(t-e);class Q{constructor(t,e,{transformPagePoint:i,contextWindow:r,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=tt(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(_(t.x,e.x)**2+_(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:r}=t,{timestamp:n}=A.uv;this.history.push({...r,timestamp:n});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=Z(e,this.transformPagePoint),A.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=tt("pointercancel"===t.type?this.lastMoveEventInfo:Z(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),r&&r(t,s)},!D(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=r||window;let s=Z(j(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=A.uv;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,tt(s,this.history)),this.removeListeners=(0,H.F)(L(this.contextWindow,"pointermove",this.handlePointerMove),L(this.contextWindow,"pointerup",this.handlePointerUp),L(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,A.WG)(this.updatePoint)}}function Z(t,e){return e?{point:e(t.point)}:t}function J(t,e){return{x:t.x-e.x,y:t.y-e.y}}function tt({point:t},e){return{point:t,delta:J(t,te(e)),offset:J(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,r=null,n=te(t);for(;i>=0&&(r=t[i],!(n.timestamp-r.timestamp>(0,Y.f)(.1)));)i--;if(!r)return{x:0,y:0};let s=(0,Y.X)(n.timestamp-r.timestamp);if(0===s)return{x:0,y:0};let o={x:(n.x-r.x)/s,y:(n.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function te(t){return t[t.length-1]}var ti=i(5818),tr=i(1297);function tn(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function ts(t,e){let i=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,r]=[r,i]),{min:i,max:r}}function to(t,e,i){return{min:ta(t,e),max:ta(t,i)}}function ta(t,e){return"number"==typeof t?t:t[e]||0}let tl=new WeakMap;class tu{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=(0,N.ge)(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new Q(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(j(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:r,onDragStart:n}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(P[t])return null;else return P[t]=!0,()=>{P[t]=!1};return P.x||P.y?null:(P.x=P.y=!0,()=>{P.x=P.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),$(t=>{let e=this.getAxisMotionValue(t).get()||0;if(S.KN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[t];r&&(e=B(r)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&A.Gt.postRender(()=>n(t,e)),(0,K.g)(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(r&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>$(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:q(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:r}=e;this.startAnimation(r);let{onDragEnd:n}=this.getProps();n&&A.Gt.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:r}=this.getProps();if(!i||!th(t,r,this.currentDirection))return;let n=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},r){return void 0!==e&&t<e?t=r?(0,M.k)(e,t,r.min):Math.max(t,e):void 0!==i&&t>i&&(t=r?(0,M.k)(i,t,r.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),n.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;t&&X(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:r,right:n}){return{x:tn(t.x,i,n),y:tn(t.y,e,r)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:to(t,"left","right"),y:to(t,"top","bottom")}}(e),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&$(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!X(e))return!1;let r=e.current;(0,V.V)(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=(0,G.L)(r,n.root,this.visualElement.getTransformPagePoint()),o=(t=n.layout.layoutBox,{x:ts(t.x,s.x),y:ts(t.y,s.y)});if(i){let t=i((0,F.pA)(o));this.hasMutatedConstraints=!!t,t&&(o=(0,F.FY)(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all($(o=>{if(!th(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return(0,K.g)(this.visualElement,t),i.start((0,E.f)(t,i,0,e,this.visualElement,!1))}stopAnimation(){$(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){$(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){$(e=>{let{drag:i}=this.getProps();if(!th(e,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(e);if(r&&r.layout){let{min:i,max:s}=r.layout.layoutBox[e];n.set(t[e]-(0,M.k)(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!X(e)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};$(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();r[t]=function(t,e){let i=.5,r=B(t),n=B(e);return n>r?i=(0,ti.q)(e.min,e.max-r,t.min):r>n&&(i=(0,ti.q)(t.min,t.max-n,e.min)),(0,tr.q)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),$(e=>{if(!th(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:s}=this.constraints[e];i.set((0,M.k)(n,s,r[e]))})}addListeners(){if(!this.visualElement.current)return;tl.set(this.visualElement,this);let t=L(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();X(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),A.Gt.read(e);let n=C(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&($(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),r(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:s,dragMomentum:o}}}function th(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class td extends x{constructor(t){super(t),this.removeGroupControls=T.l,this.removeListeners=T.l,this.controls=new tu(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||T.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let tc=t=>(e,i)=>{t&&A.Gt.postRender(()=>t(e,i))};class tp extends x{constructor(){super(...arguments),this.removePointerDownListener=T.l}onPointerDown(t){this.session=new Q(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:q(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:tc(t),onStart:tc(e),onMove:i,onEnd:(t,e)=>{delete this.session,r&&A.Gt.postRender(()=>r(t,e))}}}mount(){this.removePointerDownListener=L(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var tm=i(5155);let{schedule:tf}=(0,i(8437).I)(queueMicrotask,!1);var tg=i(2115),tv=i(2082),ty=i(869);let tx=(0,tg.createContext)({}),tb={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function tw(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let tk={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!S.px.test(t))return t;else t=parseFloat(t);let i=tw(t,e.target.x),r=tw(t,e.target.y);return`${i}% ${r}%`}};var tT=i(10),tP=i(637);class tS extends tg.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=t;(0,tP.$)(tM),n&&(e.group&&e.group.add(n),i&&i.register&&r&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),tb.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:r,isPresent:n}=this.props,{projection:s}=i;return s&&(s.isPresent=n,r||t.layoutDependency!==e||void 0===e||t.isPresent!==n?s.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?s.promote():s.relegate()||A.Gt.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),tf.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function tA(t){let[e,i]=(0,tv.xQ)(),r=(0,tg.useContext)(ty.L);return(0,tm.jsx)(tS,{...t,layoutGroup:r,switchLayoutGroup:(0,tg.useContext)(tx),isPresent:e,safeToRemove:i})}let tM={borderRadius:{...tk,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:tk,borderTopRightRadius:tk,borderBottomLeftRadius:tk,borderBottomRightRadius:tk,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let r=tT.f.parse(t);if(r.length>5)return t;let n=tT.f.createTransformer(t),s=+("number"!=typeof r[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;r[0+s]/=o,r[1+s]/=a;let l=(0,M.k)(o,a,.5);return"number"==typeof r[2+s]&&(r[2+s]/=l),"number"==typeof r[3+s]&&(r[3+s]/=l),n(r)}}};var tV=i(4744),tE=i(9782),tC=i(5943),tD=i(8777),tj=i(4261),tR=i(3704),tL=i(98),tF=i(5626),tB=i(5580),tO=i(6926),tI=i(6668);let tz=(t,e)=>t.depth-e.depth;class tU{constructor(){this.children=[],this.isDirty=!1}add(t){(0,tI.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,tI.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(tz),this.isDirty=!1,this.children.forEach(t)}}var tW=i(4803);function tN(t){return(0,tW.S)(t)?t.get():t}var t$=i(7712);let tG=["TopLeft","TopRight","BottomLeft","BottomRight"],tq=tG.length,tX=t=>"string"==typeof t?parseFloat(t):t,tK=t=>"number"==typeof t||S.px.test(t);function tH(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let tY=tQ(0,.5,t$.yT),t_=tQ(.5,.95,T.l);function tQ(t,e,i){return r=>r<t?0:r>e?1:i((0,ti.q)(t,e,r))}function tZ(t,e){t.min=e.min,t.max=e.max}function tJ(t,e){tZ(t.x,e.x),tZ(t.y,e.y)}function t0(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}var t1=i(6147);function t5(t,e,i,r,n){return t-=e,t=(0,t1.hq)(t,1/i,r),void 0!==n&&(t=(0,t1.hq)(t,1/n,r)),t}function t2(t,e,[i,r,n],s,o){!function(t,e=0,i=1,r=.5,n,s=t,o=t){if(S.KN.test(e)&&(e=parseFloat(e),e=(0,M.k)(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=(0,M.k)(s.min,s.max,r);t===s&&(a-=e),t.min=t5(t.min,e,i,a,n),t.max=t5(t.max,e,i,a,n)}(t,e[i],e[r],e[n],e.scale,s,o)}let t8=["x","scaleX","originX"],t3=["y","scaleY","originY"];function t4(t,e,i,r){t2(t.x,e,t8,i?i.x:void 0,r?r.x:void 0),t2(t.y,e,t3,i?i.y:void 0,r?r.y:void 0)}function t7(t){return 0===t.translate&&1===t.scale}function t6(t){return t7(t.x)&&t7(t.y)}function t9(t,e){return t.min===e.min&&t.max===e.max}function et(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function ee(t,e){return et(t.x,e.x)&&et(t.y,e.y)}function ei(t){return B(t.x)/B(t.y)}function er(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class en{constructor(){this.members=[]}add(t){(0,tI.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,tI.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:r}=t.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}var es=i(2662);let eo={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},ea=["","X","Y","Z"],el={visibility:"hidden"},eu=0;function eh(t,e,i,r){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),r&&(r[t]=0))}function ed({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:r,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=eu++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,tV.Q.value&&(eo.nodes=eo.calculatedTargetDeltas=eo.calculatedProjections=0),this.nodes.forEach(em),this.nodes.forEach(ew),this.nodes.forEach(ek),this.nodes.forEach(ef),tV.Q.addProjectionMetrics&&tV.Q.addProjectionMetrics(eo)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new tU)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new tF.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=(0,tE.x)(e)&&!(0,tC.h)(e),this.instance=e;let{layoutId:i,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||i)&&(this.isLayoutDirty=!0),t){let i,r=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=tj.k.now(),r=({timestamp:n})=>{let s=n-i;s>=250&&((0,A.WG)(r),t(s-e))};return A.Gt.setup(r,!0),()=>(0,A.WG)(r)}(r,250),tb.hasAnimatedSinceResize&&(tb.hasAnimatedSinceResize=!1,this.nodes.forEach(eb))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||n.getDefaultTransition()||eV,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=n.getProps(),l=!this.targetLayout||!ee(this.targetLayout,r),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...(0,tD.r)(s,"layout"),onPlay:o,onComplete:a};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||eb(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,A.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(eT),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let r=(0,tO.P)(i);if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",A.Gt,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(ev);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(ey);this.isUpdating||this.nodes.forEach(ey),this.animationCommitId=this.animationId,this.isUpdating=!1,this.nodes.forEach(ex),this.nodes.forEach(ec),this.nodes.forEach(ep),this.clearAllSnapshots();let t=tj.k.now();A.uv.delta=(0,tr.q)(0,1e3/60,t-A.uv.timestamp),A.uv.timestamp=t,A.uv.isProcessing=!0,A.PP.update.process(A.uv),A.PP.preRender.process(A.uv),A.PP.render.process(A.uv),A.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,tf.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(eg),this.sharedNodes.forEach(eP)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,A.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){A.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||B(this.snapshot.measuredBox.x)||B(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=(0,N.ge)(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=r(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!t6(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,s=r!==this.prevTransformTemplateValue;t&&this.instance&&(e||(0,es.HD)(this.latestValues)||s)&&(n(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),eD((e=r).x),eD(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return(0,N.ge)();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(eR))){let{scroll:t}=this.root;t&&((0,t1.Ql)(e.x,t.offset.x),(0,t1.Ql)(e.y,t.offset.y))}return e}removeElementScroll(t){let e=(0,N.ge)();if(tJ(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let r=this.path[i],{scroll:n,options:s}=r;r!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&tJ(e,t),(0,t1.Ql)(e.x,n.offset.x),(0,t1.Ql)(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=(0,N.ge)();tJ(i,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];!e&&r.options.layoutScroll&&r.scroll&&r!==r.root&&(0,t1.Ww)(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),(0,es.HD)(r.latestValues)&&(0,t1.Ww)(i,r.latestValues)}return(0,es.HD)(this.latestValues)&&(0,t1.Ww)(i,this.latestValues),i}removeTransform(t){let e=(0,N.ge)();tJ(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!(0,es.HD)(i.latestValues))continue;(0,es.vk)(i.latestValues)&&i.updateSnapshot();let r=(0,N.ge)();tJ(r,i.measurePageBox()),t4(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return(0,es.HD)(this.latestValues)&&t4(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==A.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:n}=this.options;if(this.layout&&(r||n)){if(this.resolvedRelativeTargetAt=A.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,N.ge)(),this.relativeTargetOrigin=(0,N.ge)(),W(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),tJ(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=(0,N.ge)(),this.targetWithTransforms=(0,N.ge)()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,z(s.x,o.x,a.x),z(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):tJ(this.target,this.layout.layoutBox),(0,t1.o4)(this.target,this.targetDelta)):tJ(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,N.ge)(),this.relativeTargetOrigin=(0,N.ge)(),W(this.relativeTargetOrigin,this.target,t.target),tJ(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}tV.Q.value&&eo.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||(0,es.vk)(this.parent.latestValues)||(0,es.vF)(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===A.uv.timestamp&&(i=!1),i)return;let{layout:r,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||n))return;tJ(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;(0,t1.OU)(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=(0,N.ge)());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(t0(this.prevProjectionDelta.x,this.projectionDelta.x),t0(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),I(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&er(this.projectionDelta.x,this.prevProjectionDelta.x)&&er(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),tV.Q.value&&eo.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=(0,N.xU)(),this.projectionDelta=(0,N.xU)(),this.projectionDeltaWithTransform=(0,N.xU)()}setAnimationOrigin(t,e=!1){let i,r=this.snapshot,n=r?r.latestValues:{},s={...this.latestValues},o=(0,N.xU)();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=(0,N.ge)(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(eM));this.animationProgress=0,this.mixTargetDelta=e=>{let r=e/1e3;if(eS(o.x,t.x,r),eS(o.y,t.y,r),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,g;W(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=r,eA(p.x,m.x,f.x,g),eA(p.y,m.y,f.y,g),i&&(u=this.relativeTarget,c=i,t9(u.x,c.x)&&t9(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=(0,N.ge)()),tJ(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,r,n,s){n?(t.opacity=(0,M.k)(0,i.opacity??1,tY(r)),t.opacityExit=(0,M.k)(e.opacity??1,0,t_(r))):s&&(t.opacity=(0,M.k)(e.opacity??1,i.opacity??1,r));for(let n=0;n<tq;n++){let s=`border${tG[n]}Radius`,o=tH(e,s),a=tH(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||tK(o)===tK(a)?(t[s]=Math.max((0,M.k)(tX(o),tX(a),r),0),(S.KN.test(a)||S.KN.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=(0,M.k)(e.rotate||0,i.rotate||0,r))}(s,n,this.latestValues,r,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,A.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=A.Gt.update(()=>{tb.hasAnimatedSinceResize=!0,tR.q.layout++,this.motionValue||(this.motionValue=(0,tL.OQ)(0)),this.currentAnimation=(0,tB.z)(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{tR.q.layout--},onComplete:()=>{tR.q.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:r,latestValues:n}=t;if(e&&i&&r){if(this!==t&&this.layout&&r&&ej(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||(0,N.ge)();let e=B(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let r=B(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+r}tJ(e,i),(0,t1.Ww)(e,n),I(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new en),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let r={};i.z&&eh("z",t,r,this.animationValues);for(let e=0;e<ea.length;e++)eh(`rotate${ea[e]}`,t,r,this.animationValues),eh(`skew${ea[e]}`,t,r,this.animationValues);for(let e in t.render(),r)t.setStaticValue(e,r[e]),this.animationValues&&(this.animationValues[e]=r[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return el;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=tN(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=tN(t?.pointerEvents)||""),this.hasProjected&&!(0,es.HD)(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let n=r.animationValues||r.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let r="",n=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((n||s||o)&&(r=`translate3d(${n}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:s,skewX:o,skewY:a}=i;t&&(r=`perspective(${t}px) ${r}`),e&&(r+=`rotate(${e}deg) `),n&&(r+=`rotateX(${n}deg) `),s&&(r+=`rotateY(${s}deg) `),o&&(r+=`skewX(${o}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),i&&(e.transform=i(n,e.transform));let{x:s,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*o.origin}% 0`,r.animationValues?e.opacity=r===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=r===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,tP.H){if(void 0===n[t])continue;let{correct:i,applyTo:s,isCSSVariable:o}=tP.H[t],a="none"===e.transform?n[t]:i(n[t],r);if(s){let t=s.length;for(let i=0;i<t;i++)e[s[i]]=a}else o?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=r===this?tN(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(ev),this.root.sharedNodes.clear()}}}function ec(t){t.updateLayout()}function ep(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:r}=t.layout,{animationType:n}=t.options,s=e.source!==t.layout.source;"size"===n?$(t=>{let r=s?e.measuredBox[t]:e.layoutBox[t],n=B(r);r.min=i[t].min,r.max=r.min+n}):ej(n,e.layoutBox,i)&&$(r=>{let n=s?e.measuredBox[r]:e.layoutBox[r],o=B(i[r]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[r].max=t.relativeTarget[r].min+o)});let o=(0,N.xU)();I(o,i,e.layoutBox);let a=(0,N.xU)();s?I(a,t.applyTransform(r,!0),e.measuredBox):I(a,i,e.layoutBox);let l=!t6(o),u=!1;if(!t.resumeFrom){let r=t.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:s}=r;if(n&&s){let o=(0,N.ge)();W(o,e.layoutBox,n.layoutBox);let a=(0,N.ge)();W(a,i,s.layoutBox),ee(o,a)||(u=!0),r.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=r)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function em(t){tV.Q.value&&eo.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function ef(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function eg(t){t.clearSnapshot()}function ev(t){t.clearMeasurements()}function ey(t){t.isLayoutDirty=!1}function ex(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function eb(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ew(t){t.resolveTargetDelta()}function ek(t){t.calcProjection()}function eT(t){t.resetSkewAndRotation()}function eP(t){t.removeLeadSnapshot()}function eS(t,e,i){t.translate=(0,M.k)(e.translate,0,i),t.scale=(0,M.k)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function eA(t,e,i,r){t.min=(0,M.k)(e.min,i.min,r),t.max=(0,M.k)(e.max,i.max,r)}function eM(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let eV={duration:.45,ease:[.4,0,.1,1]},eE=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),eC=eE("applewebkit/")&&!eE("chrome/")?Math.round:T.l;function eD(t){t.min=eC(t.min),t.max=eC(t.max)}function ej(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(ei(e)-ei(i)))}function eR(t){return t!==t.root&&t.scroll?.wasRoot}let eL=ed({attachResizeListener:(t,e)=>C(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),eF={current:void 0},eB=ed({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!eF.current){let t=new eL({});t.mount(window),t.setOptions({layoutScroll:!0}),eF.current=t}return eF.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});var eO=i(2198);function eI(t,e){let i=(0,eO.K)(t),r=new AbortController;return[i,{passive:!0,...e,signal:r.signal},()=>r.abort()]}function ez(t){return!("touch"===t.pointerType||P.x||P.y)}function eU(t,e,i){let{props:r}=t;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=r["onHover"+i];n&&A.Gt.postRender(()=>n(e,j(e)))}class eW extends x{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=eI(t,i),o=t=>{if(!ez(t))return;let{target:i}=t,r=e(i,t);if("function"!=typeof r||!i)return;let s=t=>{ez(t)&&(r(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,n)};return r.forEach(t=>{t.addEventListener("pointerenter",o,n)}),s}(t,(t,e)=>(eU(this.node,e,"Start"),t=>eU(this.node,t,"End"))))}unmount(){}}class eN extends x{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,H.F)(C(this.node.current,"focus",()=>this.onFocus()),C(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}var e$=i(7351);let eG=(t,e)=>!!e&&(t===e||eG(t,e.parentElement)),eq=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),eX=new WeakSet;function eK(t){return e=>{"Enter"===e.key&&t(e)}}function eH(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let eY=(t,e)=>{let i=t.currentTarget;if(!i)return;let r=eK(()=>{if(eX.has(i))return;eH(i,"down");let t=eK(()=>{eH(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>eH(i,"cancel"),e)});i.addEventListener("keydown",r,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),e)};function e_(t){return D(t)&&!(P.x||P.y)}function eQ(t,e,i){let{props:r}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&r.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=r["onTap"+("End"===i?"":i)];n&&A.Gt.postRender(()=>n(e,j(e)))}class eZ extends x{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=eI(t,i),o=t=>{let r=t.currentTarget;if(!e_(t))return;eX.add(r);let s=e(r,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),eX.has(r)&&eX.delete(r),e_(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,r===window||r===document||i.useGlobalTarget||eG(r,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return r.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,n),(0,e$.s)(t))&&(t.addEventListener("focus",t=>eY(t,n)),eq.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(eQ(this.node,e,"Start"),(t,{success:e})=>eQ(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let eJ=new WeakMap,e0=new WeakMap,e1=t=>{let e=eJ.get(t.target);e&&e(t)},e5=t=>{t.forEach(e1)},e2={some:0,all:1};class e8 extends x{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:r="some",once:n}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:e2[r]};return function(t,e,i){let r=function({root:t,...e}){let i=t||document;e0.has(i)||e0.set(i,{});let r=e0.get(i),n=JSON.stringify(e);return r[n]||(r[n]=new IntersectionObserver(e5,{root:t,...e})),r[n]}(e);return eJ.set(t,i),r.observe(t),()=>{eJ.delete(t),r.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),s=e?i:r;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let e3=(0,tg.createContext)({strict:!1});var e4=i(1508);let e7=(0,tg.createContext)({});var e6=i(9253);function e9(t){return Array.isArray(t)?t.join(" "):t}var it=i(8972),ie=i(6642);let ii=Symbol.for("motionComponentSymbol");var ir=i(1788),is=i(845),io=i(7494),ia=i(3055),il=i(7684);let iu=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ih(t,e,i){for(let r in e)(0,tW.S)(e[r])||(0,ia.z)(r,i)||(t[r]=e[r])}var id=i(2076);let ic=()=>({...iu(),attrs:{}});var ip=i(3095);let im=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ig(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||im.has(t)}let iv=t=>!ig(t);try{!function(t){"function"==typeof t&&(iv=e=>e.startsWith("on")?!ig(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let iy=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ix(t){if("string"!=typeof t||t.includes("-"));else if(iy.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var ib=i(2735),iw=i(2885);let ik=t=>(e,i)=>{let r=(0,tg.useContext)(e7),n=(0,tg.useContext)(is.t),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,r,n){return{latestValues:function(t,e,i,r){let n={},o=r(t,{});for(let t in o)n[t]=tN(o[t]);let{initial:a,animate:l}=t,u=(0,e6.e)(t),h=(0,e6.O)(t);e&&h&&!u&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===l&&(l=e.animate));let d=!!i&&!1===i.initial,c=(d=d||!1===a)?l:a;if(c&&"boolean"!=typeof c&&!(0,s.N)(c)){let e=Array.isArray(c)?c:[c];for(let i=0;i<e.length;i++){let r=(0,ib.a)(t,e[i]);if(r){let{transitionEnd:t,transition:e,...i}=r;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=d?e.length-1:0;e=e[t]}null!==e&&(n[t]=e)}for(let e in t)n[e]=t[e]}}}return n}(i,r,n,t),renderState:e()}})(t,e,r,n);return i?o():(0,iw.M)(o)},iT={useVisualState:ik({scrapeMotionValuesFromProps:i(8609).x,createRenderState:iu})},iP={useVisualState:ik({scrapeMotionValuesFromProps:i(4527).x,createRenderState:ic})};var iS=i(5245),iA=i(728);let iM=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,r)=>"create"===r?t:(e.has(r)||e.set(r,t(r)),e.get(r))})}((r={animation:{Feature:b},exit:{Feature:k},inView:{Feature:e8},tap:{Feature:eZ},focus:{Feature:eN},hover:{Feature:eW},pan:{Feature:tp},drag:{Feature:td,ProjectionNode:eB,MeasureLayout:tA},layout:{ProjectionNode:eB,MeasureLayout:tA}},n=(t,e)=>ix(t)?new iA.l(e):new iS.M(e,{allowProjection:t!==tg.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:r,createVisualElement:n,useRender:s,useVisualState:o,Component:a}=t;function l(t,e){var i,r,l;let u,h={...(0,tg.useContext)(e4.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,tg.useContext)(ty.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:d}=h,p=function(t){let{initial:e,animate:i}=function(t,e){if((0,e6.e)(t)){let{initial:e,animate:i}=t;return{initial:!1===e||(0,c.w)(e)?e:void 0,animate:(0,c.w)(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,tg.useContext)(e7));return(0,tg.useMemo)(()=>({initial:e,animate:i}),[e9(e),e9(i)])}(t),m=o(t,d);if(!d&&it.B){r=0,l=0,(0,tg.useContext)(e3).strict;let t=function(t){let{drag:e,layout:i}=ie.B;if(!e&&!i)return{};let r={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(h);u=t.MeasureLayout,p.visualElement=function(t,e,i,r,n){let{visualElement:s}=(0,tg.useContext)(e7),o=(0,tg.useContext)(e3),a=(0,tg.useContext)(is.t),l=(0,tg.useContext)(e4.Q).reducedMotion,u=(0,tg.useRef)(null);r=r||o.renderer,!u.current&&r&&(u.current=r(t,{visualState:e,parent:s,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let h=u.current,d=(0,tg.useContext)(tx);h&&!h.projection&&n&&("html"===h.type||"svg"===h.type)&&function(t,e,i,r){let{layoutId:n,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!o||a&&X(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:r,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,n,d);let c=(0,tg.useRef)(!1);(0,tg.useInsertionEffect)(()=>{h&&c.current&&h.update(i,a)});let p=i[ir.n],m=(0,tg.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,io.E)(()=>{h&&(c.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),tf.render(h.render),m.current&&h.animationState&&h.animationState.animateChanges())}),(0,tg.useEffect)(()=>{h&&(!m.current&&h.animationState&&h.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),h}(a,m,h,n,t.ProjectionNode)}return(0,tm.jsxs)(e7.Provider,{value:p,children:[u&&p.visualElement?(0,tm.jsx)(u,{visualElement:p.visualElement,...h}):null,s(a,t,(i=p.visualElement,(0,tg.useCallback)(t=>{t&&m.onMount&&m.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):X(e)&&(e.current=t))},[i])),m,d,p.visualElement)]})}r&&function(t){for(let e in t)ie.B[e]={...ie.B[e],...t[e]}}(r),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!=(i=null!=(e=a.displayName)?e:a.name)?i:"",")"));let u=(0,tg.forwardRef)(l);return u[ii]=a,u}({...ix(t)?iP:iT,preloadedFeatures:r,useRender:function(t=!1){return(e,i,r,{latestValues:n},s)=>{let o=(ix(e)?function(t,e,i,r){let n=(0,tg.useMemo)(()=>{let i=ic();return(0,id.B)(i,e,(0,ip.n)(r),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};ih(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},r=function(t,e){let i=t.style||{},r={};return ih(r,i,t),Object.assign(r,function({transformTemplate:t},e){return(0,tg.useMemo)(()=>{let i=iu();return(0,il.O)(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=r,i})(i,n,s,e),a=function(t,e,i){let r={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(iv(n)||!0===i&&ig(n)||!e&&!ig(n)||t.draggable&&n.startsWith("onDrag"))&&(r[n]=t[n]);return r}(i,"string"==typeof e,t),l=e!==tg.Fragment?{...a,...o,ref:r}:{},{children:u}=i,h=(0,tg.useMemo)(()=>(0,tW.S)(u)?u.get():u,[u]);return(0,tg.createElement)(e,{...l,children:h})}}(e),createVisualElement:n,Component:t})}))},255:(t,e,i)=>{function r(t){let{moduleIds:e}=t;return null}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"PreloadChunks",{enumerable:!0,get:function(){return r}}),i(5155),i(7650),i(5744),i(589)},280:(t,e,i)=>{i.d(e,{E4:()=>a,Hr:()=>d,W9:()=>h});var r=i(4160),n=i(18),s=i(7887),o=i(4158);let a=t=>t===s.ai||t===o.px,l=new Set(["x","y","z"]),u=n.U.filter(t=>!l.has(t));function h(t){let e=[];return u.forEach(i=>{let r=t.getValue(i);void 0!==r&&(e.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),e}let d={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>(0,r.ry)(e,"x"),y:(t,{transform:e})=>(0,r.ry)(e,"y")};d.translateX=d.x,d.translateY=d.y},419:(t,e,i)=>{i.d(e,{K:()=>n});var r=i(2735);function n(t,e,i){let n=t.getProps();return(0,r.a)(n,e,void 0!==i?i:n.custom,t)}},600:(t,e,i)=>{i.d(e,{e:()=>r});function r(t,{style:e,vars:i},r,n){for(let s in Object.assign(t.style,e,n&&n.getProjectionStyles(r)),i)t.style.setProperty(s,i[s])}},614:(t,e,i)=>{i.d(e,{S:()=>r});let r=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},637:(t,e,i)=>{i.d(e,{$:()=>s,H:()=>n});var r=i(8606);let n={};function s(t){for(let e in t)n[e]=t[e],(0,r.j)(e)&&(n[e].isCSSVariable=!0)}},728:(t,e,i)=>{i.d(e,{l:()=>p});var r=i(18),n=i(1834),s=i(1786),o=i(5193),a=i(8450),l=i(2076);let u=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);var h=i(3095),d=i(600),c=i(4527);class p extends o.b{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=s.ge}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(r.f.has(e)){let t=(0,n.D)(e);return t&&t.default||0}return e=u.has(e)?e:(0,a.I)(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return(0,c.x)(t,e,i)}build(t,e,i){(0,l.B)(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,r){for(let i in(0,d.e)(t,e,void 0,r),e.attrs)t.setAttribute(u.has(i)?i:(0,a.I)(i),e.attrs[i])}mount(t){this.isSVGTag=(0,h.n)(t.tagName),super.mount(t)}}},760:(t,e,i)=>{i.d(e,{N:()=>y});var r=i(5155),n=i(2115),s=i(869),o=i(2885),a=i(7494),l=i(845),u=i(7351),h=i(1508);class d extends n.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,i=(0,u.s)(t)&&t.offsetWidth||0,r=this.props.sizeRef.current;r.height=e.offsetHeight||0,r.width=e.offsetWidth||0,r.top=e.offsetTop,r.left=e.offsetLeft,r.right=i-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function c(t){let{children:e,isPresent:i,anchorX:s}=t,o=(0,n.useId)(),a=(0,n.useRef)(null),l=(0,n.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,n.useContext)(h.Q);return(0,n.useInsertionEffect)(()=>{let{width:t,height:e,top:r,left:n,right:h}=l.current;if(i||!a.current||!t||!e)return;a.current.dataset.motionPopId=o;let d=document.createElement("style");return u&&(d.nonce=u),document.head.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(o,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            ").concat("left"===s?"left: ".concat(n):"right: ".concat(h),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{document.head.contains(d)&&document.head.removeChild(d)}},[i]),(0,r.jsx)(d,{isPresent:i,childRef:a,sizeRef:l,children:n.cloneElement(e,{ref:a})})}let p=t=>{let{children:e,initial:i,isPresent:s,onExitComplete:a,custom:u,presenceAffectsLayout:h,mode:d,anchorX:p}=t,f=(0,o.M)(m),g=(0,n.useId)(),v=!0,y=(0,n.useMemo)(()=>(v=!1,{id:g,initial:i,isPresent:s,custom:u,onExitComplete:t=>{for(let e of(f.set(t,!0),f.values()))if(!e)return;a&&a()},register:t=>(f.set(t,!1),()=>f.delete(t))}),[s,f,a]);return h&&v&&(y={...y}),(0,n.useMemo)(()=>{f.forEach((t,e)=>f.set(e,!1))},[s]),n.useEffect(()=>{s||f.size||!a||a()},[s]),"popLayout"===d&&(e=(0,r.jsx)(c,{isPresent:s,anchorX:p,children:e})),(0,r.jsx)(l.t.Provider,{value:y,children:e})};function m(){return new Map}var f=i(2082);let g=t=>t.key||"";function v(t){let e=[];return n.Children.forEach(t,t=>{(0,n.isValidElement)(t)&&e.push(t)}),e}let y=t=>{let{children:e,custom:i,initial:l=!0,onExitComplete:u,presenceAffectsLayout:h=!0,mode:d="sync",propagate:c=!1,anchorX:m="left"}=t,[y,x]=(0,f.xQ)(c),b=(0,n.useMemo)(()=>v(e),[e]),w=c&&!y?[]:b.map(g),k=(0,n.useRef)(!0),T=(0,n.useRef)(b),P=(0,o.M)(()=>new Map),[S,A]=(0,n.useState)(b),[M,V]=(0,n.useState)(b);(0,a.E)(()=>{k.current=!1,T.current=b;for(let t=0;t<M.length;t++){let e=g(M[t]);w.includes(e)?P.delete(e):!0!==P.get(e)&&P.set(e,!1)}},[M,w.length,w.join("-")]);let E=[];if(b!==S){let t=[...b];for(let e=0;e<M.length;e++){let i=M[e],r=g(i);w.includes(r)||(t.splice(e,0,i),E.push(i))}return"wait"===d&&E.length&&(t=E),V(v(t)),A(b),null}let{forceRender:C}=(0,n.useContext)(s.L);return(0,r.jsx)(r.Fragment,{children:M.map(t=>{let e=g(t),n=(!c||!!y)&&(b===M||w.includes(e));return(0,r.jsx)(p,{isPresent:n,initial:(!k.current||!!l)&&void 0,custom:i,presenceAffectsLayout:h,mode:d,onExitComplete:n?void 0:()=>{if(!P.has(e))return;P.set(e,!0);let t=!0;P.forEach(e=>{e||(t=!1)}),t&&(null==C||C(),V(T.current),c&&(null==x||x()),u&&u())},anchorX:m,children:t},e)})})}},845:(t,e,i)=>{i.d(e,{t:()=>r});let r=(0,i(2115).createContext)(null)},869:(t,e,i)=>{i.d(e,{L:()=>r});let r=(0,i(2115).createContext)({})},1081:(t,e,i)=>{i.d(e,{h:()=>r});let r=t=>Array.isArray(t)&&"number"!=typeof t[0]},1297:(t,e,i)=>{i.d(e,{q:()=>r});let r=(t,e,i)=>i>e?e:i<t?t:i},1335:(t,e,i)=>{i.d(e,{u:()=>n});var r=i(9064);let n={test:(0,i(5920).$)("#"),parse:function(t){let e="",i="",r="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),r=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),r=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,r+=r,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:r.B.transform}},1508:(t,e,i)=>{i.d(e,{Q:()=>r});let r=(0,i(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},1557:(t,e,i)=>{i.d(e,{a:()=>r});let r=t=>Math.round(1e5*t)/1e5},1765:(t,e,i)=>{i.d(e,{V:()=>r});let r=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2},1786:(t,e,i)=>{i.d(e,{ge:()=>o,xU:()=>n});let r=()=>({translate:0,scale:1,origin:0,originPoint:0}),n=()=>({x:r(),y:r()}),s=()=>({min:0,max:0}),o=()=>({x:s(),y:s()})},1788:(t,e,i)=>{i.d(e,{n:()=>r});let r="data-"+(0,i(8450).I)("framerAppearId")},1834:(t,e,i)=>{i.d(e,{D:()=>o});var r=i(4272),n=i(2171);let s={...i(2403).W,color:r.y,backgroundColor:r.y,outlineColor:r.y,fill:r.y,stroke:r.y,borderColor:r.y,borderTopColor:r.y,borderRightColor:r.y,borderBottomColor:r.y,borderLeftColor:r.y,filter:n.p,WebkitFilter:n.p},o=t=>s[t]},2017:(t,e,i)=>{i.d(e,{f:()=>s});var r=i(5818),n=i(3210);function s(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let o=(0,r.q)(0,e,s);t.push((0,n.k)(i,1,o))}}},2076:(t,e,i)=>{i.d(e,{B:()=>a});var r=i(7684),n=i(4158);let s={offset:"stroke-dashoffset",array:"stroke-dasharray"},o={offset:"strokeDashoffset",array:"strokeDasharray"};function a(t,{attrX:e,attrY:i,attrScale:a,pathLength:l,pathSpacing:u=1,pathOffset:h=0,...d},c,p,m){if((0,r.O)(t,d,p),c){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:f,style:g}=t;f.transform&&(g.transform=f.transform,delete f.transform),(g.transform||f.transformOrigin)&&(g.transformOrigin=f.transformOrigin??"50% 50%",delete f.transformOrigin),g.transform&&(g.transformBox=m?.transformBox??"fill-box",delete f.transformBox),void 0!==e&&(f.x=e),void 0!==i&&(f.y=i),void 0!==a&&(f.scale=a),void 0!==l&&function(t,e,i=1,r=0,a=!0){t.pathLength=1;let l=a?s:o;t[l.offset]=n.px.transform(-r);let u=n.px.transform(e),h=n.px.transform(i);t[l.array]=`${u} ${h}`}(f,l,u,h,!1)}},2082:(t,e,i)=>{i.d(e,{xQ:()=>s});var r=i(2115),n=i(845);function s(t=!0){let e=(0,r.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,r.useId)();(0,r.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,r.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},2146:(t,e,i)=>{function r(t){let{reason:e,children:i}=t;return i}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"BailoutToCSR",{enumerable:!0,get:function(){return r}}),i(5262)},2171:(t,e,i)=>{i.d(e,{p:()=>l});var r=i(10),n=i(614);let s=new Set(["brightness","contrast","saturate","opacity"]);function o(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[r]=i.match(n.S)||[];if(!r)return t;let o=i.replace(r,""),a=+!!s.has(e);return r!==i&&(a*=100),e+"("+a+o+")"}let a=/\b([a-z-]*)\(.*?\)/gu,l={...r.f,getAnimatableNone:t=>{let e=t.match(a);return e?e.map(o).join(" "):t}}},2198:(t,e,i)=>{i.d(e,{K:()=>r});function r(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let r=document;e&&(r=e.current);let n=i?.[t]??r.querySelectorAll(t);return n?Array.from(n):[]}return Array.from(t)}},2403:(t,e,i)=>{i.d(e,{W:()=>a});var r=i(7887);let n={...r.ai,transform:Math.round};var s=i(4158);let o={rotate:s.uj,rotateX:s.uj,rotateY:s.uj,rotateZ:s.uj,scale:r.hs,scaleX:r.hs,scaleY:r.hs,scaleZ:r.hs,skew:s.uj,skewX:s.uj,skewY:s.uj,distance:s.px,translateX:s.px,translateY:s.px,translateZ:s.px,x:s.px,y:s.px,z:s.px,perspective:s.px,transformPerspective:s.px,opacity:r.X4,originX:s.gQ,originY:s.gQ,originZ:s.px},a={borderWidth:s.px,borderTopWidth:s.px,borderRightWidth:s.px,borderBottomWidth:s.px,borderLeftWidth:s.px,borderRadius:s.px,radius:s.px,borderTopLeftRadius:s.px,borderTopRightRadius:s.px,borderBottomRightRadius:s.px,borderBottomLeftRadius:s.px,width:s.px,maxWidth:s.px,height:s.px,maxHeight:s.px,top:s.px,right:s.px,bottom:s.px,left:s.px,padding:s.px,paddingTop:s.px,paddingRight:s.px,paddingBottom:s.px,paddingLeft:s.px,margin:s.px,marginTop:s.px,marginRight:s.px,marginBottom:s.px,marginLeft:s.px,backgroundPositionX:s.px,backgroundPositionY:s.px,...o,zIndex:n,fillOpacity:r.X4,strokeOpacity:r.X4,numOctaves:n}},2458:(t,e,i)=>{i.d(e,{Y:()=>r,t:()=>n});let r=2e4;function n(t){let e=0,i=t.next(e);for(;!i.done&&e<r;)e+=50,i=t.next(e);return e>=r?1/0:e}},2596:(t,e,i)=>{i.d(e,{$:()=>r});function r(){for(var t,e,i=0,r="",n=arguments.length;i<n;i++)(t=arguments[i])&&(e=function t(e){var i,r,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(i=0;i<s;i++)e[i]&&(r=t(e[i]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}(t))&&(r&&(r+=" "),r+=e);return r}},2662:(t,e,i)=>{function r(t){return void 0===t||1===t}function n({scale:t,scaleX:e,scaleY:i}){return!r(t)||!r(e)||!r(i)}function s(t){return n(t)||o(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function o(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}i.d(e,{HD:()=>s,vF:()=>o,vk:()=>n})},2735:(t,e,i)=>{function r(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function n(t,e,i,n){if("function"==typeof e){let[s,o]=r(n);e=e(void 0!==i?i:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[s,o]=r(n);e=e(void 0!==i?i:t.custom,s,o)}return e}i.d(e,{a:()=>n})},2885:(t,e,i)=>{i.d(e,{M:()=>n});var r=i(2115);function n(t){let e=(0,r.useRef)(null);return null===e.current&&(e.current=t()),e.current}},2923:(t,e,i)=>{i.d(e,{f:()=>r});function r(t,e){return e?1e3/e*t:0}},3014:(t,e,i)=>{i.d(e,{i:()=>r});let r=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t)},3055:(t,e,i)=>{i.d(e,{z:()=>s});var r=i(18),n=i(637);function s(t,{layout:e,layoutId:i}){return r.f.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!n.H[t]||"opacity"===t)}},3095:(t,e,i)=>{i.d(e,{n:()=>r});let r=t=>"string"==typeof t&&"svg"===t.toLowerCase()},3128:(t,e,i)=>{i.d(e,{W:()=>r});function r(t){return"function"==typeof t&&"applyToOptions"in t}},3191:(t,e,i)=>{i.d(e,{F:()=>n});let r=(t,e)=>i=>e(t(i)),n=(...t)=>t.reduce(r)},3210:(t,e,i)=>{i.d(e,{k:()=>r});let r=(t,e,i)=>t+(e-t)*i},3387:(t,e,i)=>{i.d(e,{W:()=>r});let r={}},3522:(t,e,i)=>{i.d(e,{w:()=>r});let r=t=>e=>e.test(t)},3562:(t,e,i)=>{i.d(e,{B:()=>V});var r=i(7322),n=i(4261),s=i(9515),o=i(4803),a=i(18),l=i(98),u=i(4272),h=i(10),d=i(4050),c=i(3522);let p=[...d.T,u.y,h.f],m=t=>p.find((0,c.w)(t));var f=i(7277),g=i(3014),v=i(7312),y=i(5626),x=i(6642),b=i(1786),w=i(8972);let k={current:null},T={current:!1};var P=i(5511),S=i(9253),A=i(2735);let M=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class V{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:a,blockInitialAnimation:l,visualState:u},h={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=r.h,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=n.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,s.Gt.render(this.render,!1,!0))};let{latestValues:d,renderState:c}=u;this.latestValues=d,this.baseTarget={...d},this.initialValues=e.initial?{...d}:{},this.renderState=c,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=a,this.options=h,this.blockInitialAnimation=!!l,this.isControllingVariants=(0,S.e)(e),this.isVariantNode=(0,S.O)(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:p,...m}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in m){let e=m[t];void 0!==d[t]&&(0,o.S)(e)&&e.set(d[t],!1)}}mount(t){this.current=t,P.C.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),T.current||function(){if(T.current=!0,w.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>k.current=t.matches;t.addListener(e),e()}else k.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||k.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,s.WG)(this.notifyUpdate),(0,s.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let r=a.f.has(t);r&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&s.Gt.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),o(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in x.B){let e=x.B[t];if(!e)continue;let{isEnabled:i,Feature:r}=e;if(!this.features[t]&&r&&i(this.props)&&(this.features[t]=new r(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):(0,b.ge)()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<M.length;e++){let i=M[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=t["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(t,e,i){for(let r in e){let n=e[r],s=i[r];if((0,o.S)(n))t.addValue(r,n);else if((0,o.S)(s))t.addValue(r,(0,l.OQ)(n,{owner:t}));else if(s!==n)if(t.hasValue(r)){let e=t.getValue(r);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(r);t.addValue(r,(0,l.OQ)(void 0!==e?e:n,{owner:t}))}}for(let r in i)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,l.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&((0,g.i)(i)||(0,v.$)(i))?i=parseFloat(i):!m(i)&&h.f.test(e)&&(i=(0,f.J)(t,e)),this.setBaseTarget(t,(0,o.S)(i)?i.get():i)),(0,o.S)(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let r=(0,A.a)(this.props,i,this.presenceContext?.custom);r&&(e=r[t])}if(i&&void 0!==e)return e;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||(0,o.S)(r)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new y.v),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}},3704:(t,e,i)=>{i.d(e,{q:()=>r});let r={layout:0,mainThread:0,waapi:0}},3757:(t,e,i)=>{i.d(e,{L:()=>o,m:()=>s});var r=i(8588),n=i(6147);function s(t,e){return(0,r.FY)((0,r.bS)(t.getBoundingClientRect(),e))}function o(t,e,i){let r=s(t,i),{scroll:o}=e;return o&&((0,n.Ql)(r.x,o.offset.x),(0,n.Ql)(r.y,o.offset.y)),r}},3945:(t,e,i)=>{i.d(e,{Y:()=>n});var r=i(2923);function n(t,e,i){let n=Math.max(e-5,0);return(0,r.f)(i-t(n),e-n)}},4050:(t,e,i)=>{i.d(e,{T:()=>o,n:()=>a});var r=i(7887),n=i(4158),s=i(3522);let o=[r.ai,n.px,n.KN,n.uj,n.vw,n.vh,{test:t=>"auto"===t,parse:t=>t}],a=t=>o.find((0,s.w)(t))},4054:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{bindSnapshot:function(){return o},createAsyncLocalStorage:function(){return s},createSnapshot:function(){return a}});let i=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class r{disable(){throw i}getStore(){}run(){throw i}exit(){throw i}enterWith(){throw i}static bind(t){return t}}let n="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function s(){return n?new n:new r}function o(t){return n?n.bind(t):r.bind(t)}function a(){return n?n.snapshot():function(t,...e){return t(...e)}}},4158:(t,e,i)=>{i.d(e,{KN:()=>s,gQ:()=>u,px:()=>o,uj:()=>n,vh:()=>a,vw:()=>l});let r=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),n=r("deg"),s=r("%"),o=r("px"),a=r("vh"),l=r("vw"),u={...s,parse:t=>s.parse(t)/100,transform:t=>s.transform(100*t)}},4160:(t,e,i)=>{i.d(e,{Ib:()=>c,ry:()=>d,zs:()=>h});let r=t=>180*t/Math.PI,n=t=>o(r(Math.atan2(t[1],t[0]))),s={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:n,rotateZ:n,skewX:t=>r(Math.atan(t[1])),skewY:t=>r(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},o=t=>((t%=360)<0&&(t+=360),t),a=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),l=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),u={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:a,scaleY:l,scale:t=>(a(t)+l(t))/2,rotateX:t=>o(r(Math.atan2(t[6],t[5]))),rotateY:t=>o(r(Math.atan2(-t[2],t[0]))),rotateZ:n,rotate:n,skewX:t=>r(Math.atan(t[4])),skewY:t=>r(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function h(t){return+!!t.includes("scale")}function d(t,e){let i,r;if(!t||"none"===t)return h(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=u,r=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=s,r=e}if(!r)return h(e);let o=i[e],a=r[1].split(",").map(p);return"function"==typeof o?o(a):a[o]}let c=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return d(i,e)};function p(t){return parseFloat(t.trim())}},4180:(t,e,i)=>{i.d(e,{G:()=>r});let r=t=>e=>1-t(1-e)},4261:(t,e,i)=>{let r;i.d(e,{k:()=>a});var n=i(3387),s=i(9515);function o(){r=void 0}let a={now:()=>(void 0===r&&a.set(s.uv.isProcessing||n.W.useManualTiming?s.uv.timestamp:performance.now()),r),set:t=>{r=t,queueMicrotask(o)}}},4272:(t,e,i)=>{i.d(e,{y:()=>o});var r=i(1335),n=i(8476),s=i(9064);let o={test:t=>s.B.test(t)||r.u.test(t)||n.V.test(t),parse:t=>s.B.test(t)?s.B.parse(t):n.V.test(t)?n.V.parse(t):r.u.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?s.B.transform(t):n.V.transform(t),getAnimatableNone:t=>{let e=o.parse(t);return e.alpha=0,o.transform(e)}}},4527:(t,e,i)=>{i.d(e,{x:()=>o});var r=i(4803),n=i(18),s=i(8609);function o(t,e,i){let o=(0,s.x)(t,e,i);for(let i in t)((0,r.S)(t[i])||(0,r.S)(e[i]))&&(o[-1!==n.U.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return o}},4542:(t,e,i)=>{i.d(e,{$:()=>r,V:()=>n});let r=()=>{},n=()=>{}},4608:(t,e,i)=>{i.d(e,{X:()=>s});var r=i(7215),n=i(2458);function s(t,e=100,i){let o=i({...t,keyframes:[0,e]}),a=Math.min((0,n.t)(o),n.Y);return{type:"keyframes",ease:t=>o.next(a*t).value/e,duration:(0,r.X)(a)}}},4687:(t,e,i)=>{i.d(e,{o:()=>f});var r=i(1297),n=i(7215),s=i(7705),o=i(2458),a=i(4608),l=i(3945);let u={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};var h=i(4542);function d(t,e){return t*Math.sqrt(1-e*e)}let c=["duration","bounce"],p=["stiffness","damping","mass"];function m(t,e){return e.some(e=>void 0!==t[e])}function f(t=u.visualDuration,e=u.bounce){let i,a="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:g,restDelta:v}=a,y=a.keyframes[0],x=a.keyframes[a.keyframes.length-1],b={done:!1,value:y},{stiffness:w,damping:k,mass:T,duration:P,velocity:S,isResolvedFromDuration:A}=function(t){let e={velocity:u.velocity,stiffness:u.stiffness,damping:u.damping,mass:u.mass,isResolvedFromDuration:!1,...t};if(!m(t,p)&&m(t,c))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,s=2*(0,r.q)(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:u.mass,stiffness:n,damping:s}}else{let i=function({duration:t=u.duration,bounce:e=u.bounce,velocity:i=u.velocity,mass:s=u.mass}){let o,a;(0,h.$)(t<=(0,n.f)(u.maxDuration),"Spring duration must be 10 seconds or less");let l=1-e;l=(0,r.q)(u.minDamping,u.maxDamping,l),t=(0,r.q)(u.minDuration,u.maxDuration,(0,n.X)(t)),l<1?(o=e=>{let r=e*l,n=r*t;return .001-(r-i)/d(e,l)*Math.exp(-n)},a=e=>{let r=e*l*t,n=Math.pow(l,2)*Math.pow(e,2)*t,s=Math.exp(-r),a=d(Math.pow(e,2),l);return(r*i+i-n)*s*(-o(e)+.001>0?-1:1)/a}):(o=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),a=e=>t*t*(i-e)*Math.exp(-e*t));let c=function(t,e,i){let r=i;for(let i=1;i<12;i++)r-=t(r)/e(r);return r}(o,a,5/t);if(t=(0,n.f)(t),isNaN(c))return{stiffness:u.stiffness,damping:u.damping,duration:t};{let e=Math.pow(c,2)*s;return{stiffness:e,damping:2*l*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:u.mass}).isResolvedFromDuration=!0}return e}({...a,velocity:-(0,n.X)(a.velocity||0)}),M=S||0,V=k/(2*Math.sqrt(w*T)),E=x-y,C=(0,n.X)(Math.sqrt(w/T)),D=5>Math.abs(E);if(g||(g=D?u.restSpeed.granular:u.restSpeed.default),v||(v=D?u.restDelta.granular:u.restDelta.default),V<1){let t=d(C,V);i=e=>x-Math.exp(-V*C*e)*((M+V*C*E)/t*Math.sin(t*e)+E*Math.cos(t*e))}else if(1===V)i=t=>x-Math.exp(-C*t)*(E+(M+C*E)*t);else{let t=C*Math.sqrt(V*V-1);i=e=>{let i=Math.exp(-V*C*e),r=Math.min(t*e,300);return x-i*((M+V*C*E)*Math.sinh(r)+t*E*Math.cosh(r))/t}}let j={calculatedDuration:A&&P||null,next:t=>{let e=i(t);if(A)b.done=t>=P;else{let r=0===t?M:0;V<1&&(r=0===t?(0,n.f)(M):(0,l.Y)(i,t,e));let s=Math.abs(x-e)<=v;b.done=Math.abs(r)<=g&&s}return b.value=b.done?x:e,b},toString:()=>{let t=Math.min((0,o.t)(j),o.Y),e=(0,s.K)(e=>j.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return j}f.applyToOptions=t=>{let e=(0,a.X)(t,100,f);return t.ease=e.ease,t.duration=(0,n.f)(e.duration),t.type="keyframes",t}},4744:(t,e,i)=>{i.d(e,{Q:()=>r});let r={value:null,addProjectionMetrics:null}},4803:(t,e,i)=>{i.d(e,{S:()=>r});let r=t=>!!(t&&t.getVelocity)},5028:(t,e,i)=>{i.d(e,{default:()=>n.a});var r=i(6645),n=i.n(r)},5193:(t,e,i)=>{i.d(e,{b:()=>y});var r=i(8109),n=i(4050),s=i(4542),o=i(3014),a=i(8606);let l=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;var u=i(7322),h=i(7312),d=i(10),c=i(7277);let p=new Set(["auto","none","0"]);var m=i(280);class f extends u.h{constructor(t,e,i,r,n){super(t,e,i,r,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let r=t[i];if("string"==typeof r&&(r=r.trim(),(0,a.p)(r))){let n=function t(e,i,r=1){(0,s.V)(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,u]=function(t){let e=l.exec(t);if(!e)return[,];let[,i,r,n]=e;return[`--${i??r}`,n]}(e);if(!n)return;let h=window.getComputedStyle(i).getPropertyValue(n);if(h){let t=h.trim();return(0,o.i)(t)?parseFloat(t):t}return(0,a.p)(u)?t(u,i,r+1):u}(r,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!r.$.has(i)||2!==t.length)return;let[u,h]=t,d=(0,n.n)(u),c=(0,n.n)(h);if(d!==c)if((0,m.E4)(d)&&(0,m.E4)(c))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else m.Hr[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var r;(null===t[e]||("number"==typeof(r=t[e])?0===r:null===r||"none"===r||"0"===r||(0,h.$)(r)))&&i.push(e)}i.length&&function(t,e,i){let r,n=0;for(;n<t.length&&!r;){let e=t[n];"string"==typeof e&&!p.has(e)&&(0,d.V)(e).values.length&&(r=t[n]),n++}if(r&&i)for(let n of e)t[n]=(0,c.J)(i,r)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=m.Hr[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let r=e[e.length-1];void 0!==r&&t.getValue(i,r).jump(r,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let r=t.getValue(e);r&&r.jump(this.measuredOrigin,!1);let n=i.length-1,s=i[n];i[n]=m.Hr[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}var g=i(4803),v=i(3562);class y extends v.B{constructor(){super(...arguments),this.KeyframeResolver=f}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;(0,g.S)(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}},5245:(t,e,i)=>{i.d(e,{M:()=>d});var r=i(18),n=i(4160),s=i(8606),o=i(3757),a=i(5193),l=i(7684),u=i(600),h=i(8609);class d extends a.b{constructor(){super(...arguments),this.type="html",this.renderInstance=u.e}readValueFromInstance(t,e){if(r.f.has(e))return this.projection?.isProjecting?(0,n.zs)(e):(0,n.Ib)(t,e);{let i=window.getComputedStyle(t),r=((0,s.j)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return(0,o.m)(t,e)}build(t,e,i){(0,l.O)(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return(0,h.x)(t,e,i)}}},5305:(t,e,i)=>{i.d(e,{w:()=>r});function r(t){return"string"==typeof t||Array.isArray(t)}},5511:(t,e,i)=>{i.d(e,{C:()=>r});let r=new WeakMap},5580:(t,e,i)=>{i.d(e,{z:()=>o});var r=i(4803),n=i(98),s=i(6051);function o(t,e,i){let o=(0,r.S)(t)?t:(0,n.OQ)(t);return o.start((0,s.f)("",o,e,i)),o.animation}},5626:(t,e,i)=>{i.d(e,{v:()=>n});var r=i(6668);class n{constructor(){this.subscriptions=[]}add(t){return(0,r.Kq)(this.subscriptions,t),()=>(0,r.Ai)(this.subscriptions,t)}notify(t,e,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](t,e,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},5653:(t,e,i)=>{i.d(e,{l:()=>G});var r=i(2885),n=i(2115);class s{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>t.finished))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t){let e=this.animations.map(e=>e.attachTimeline(t));return()=>{e.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class o extends s{then(t,e){return this.finished.finally(t).then(()=>{})}}var a=i(4687),l=i(9115),u=i(3128),h=i(4608),d=i(2017),c=i(4803),p=i(7215),m=i(4542);let f=(t,e,i)=>{let r=e-t;return((i-t)%r+r)%r+t};var g=i(1081);function v(t,e){return(0,g.h)(t)?t[f(0,t.length,e)]:t}var y=i(5818),x=i(2198);function b(t){return"object"==typeof t&&!Array.isArray(t)}function w(t,e,i,r){return"string"==typeof t&&b(e)?(0,x.K)(t,i,r):t instanceof NodeList?Array.from(t):Array.isArray(t)?t:[t]}function k(t,e,i,r){return"number"==typeof e?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?i:e.startsWith("<")?Math.max(0,i+parseFloat(e.slice(1))):r.get(e)??t}var T=i(3210),P=i(6668);function S(t,e){return t.at!==e.at?t.at-e.at:null===t.value?1:null===e.value?-1:0}function A(t,e){return e.has(t)||e.set(t,{}),e.get(t)}function M(t,e){return e[t]||(e[t]=[]),e[t]}let V=t=>"number"==typeof t,E=t=>t.every(V);var C=i(5511),D=i(7934),j=i(9782),R=i(5943),L=i(5245),F=i(1786),B=i(3562);class O extends B.B{constructor(){super(...arguments),this.type="object"}readValueFromInstance(t,e){if(e in t){let i=t[e];if("string"==typeof i||"number"==typeof i)return i}}getBaseTargetFromProps(){}removeValueFromRenderState(t,e){delete e.output[t]}measureInstanceViewportBox(){return(0,F.ge)()}build(t,e){Object.assign(t.output,e)}renderInstance(t,{output:e}){Object.assign(t,e)}sortInstanceNodePosition(){return 0}}var I=i(728);function z(t){let e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},i=(0,j.x)(t)&&!(0,R.h)(t)?new I.l(e):new L.M(e);i.mount(t),C.C.set(t,i)}function U(t){let e=new O({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});e.mount(t),C.C.set(t,e)}var W=i(5580);function N(t,e,i,r){let n=[];if((0,c.S)(t)||"number"==typeof t||"string"==typeof t&&!b(e))n.push((0,W.z)(t,b(e)&&e.default||e,i&&i.default||i));else{let s=w(t,e,r),o=s.length;(0,m.V)(!!o,"No valid elements provided.");for(let t=0;t<o;t++){let r=s[t],a=r instanceof Element?z:U;C.C.has(r)||a(r);let l=C.C.get(r),u={...i};"delay"in u&&"function"==typeof u.delay&&(u.delay=u.delay(t,o)),n.push(...(0,D.$)(l,{...e,transition:u},{}))}}return n}function $(t){return function(e,i,r){let n=[],s=new o(n=Array.isArray(e)&&e.some(Array.isArray)?function(t,e,i){let r=[];return(function(t,{defaultTransition:e={},...i}={},r,n){let s=e.duration||.3,o=new Map,a=new Map,f={},g=new Map,x=0,b=0,V=0;for(let i=0;i<t.length;i++){let o=t[i];if("string"==typeof o){g.set(o,b);continue}if(!Array.isArray(o)){g.set(o.name,k(b,o.at,x,g));continue}let[y,S,j={}]=o;void 0!==j.at&&(b=k(b,j.at,x,g));let R=0,L=(t,i,r,o=0,a=0)=>{var c;let f=Array.isArray(c=t)?c:[c],{delay:g=0,times:y=(0,l.Z)(f),type:x="keyframes",repeat:w,repeatType:k,repeatDelay:S=0,...A}=i,{ease:M=e.ease||"easeOut",duration:C}=i,D="function"==typeof g?g(o,a):g,j=f.length,L=(0,u.W)(x)?x:n?.[x||"keyframes"];if(j<=2&&L){let t=100;2===j&&E(f)&&(t=Math.abs(f[1]-f[0]));let e={...A};void 0!==C&&(e.duration=(0,p.f)(C));let i=(0,h.X)(e,t,L);M=i.ease,C=i.duration}C??(C=s);let F=b+D;1===y.length&&0===y[0]&&(y[1]=1);let B=y.length-f.length;if(B>0&&(0,d.f)(y,B),1===f.length&&f.unshift(null),w){(0,m.V)(w<20,"Repeat count too high, must be less than 20");C*=w+1;let t=[...f],e=[...y],i=[...M=Array.isArray(M)?[...M]:[M]];for(let r=0;r<w;r++){f.push(...t);for(let n=0;n<t.length;n++)y.push(e[n]+(r+1)),M.push(0===n?"linear":v(i,n-1))}for(let t=0;t<y.length;t++)y[t]=y[t]/(w+1)}let O=F+C;!function(t,e,i,r,n,s){for(let e=0;e<t.length;e++){let i=t[e];i.at>n&&i.at<s&&((0,P.Ai)(t,i),e--)}for(let o=0;o<e.length;o++)t.push({value:e[o],at:(0,T.k)(n,s,r[o]),easing:v(i,o)})}(r,f,M,y,F,O),R=Math.max(D+C,R),V=Math.max(O,V)};if((0,c.S)(y))L(S,j,M("default",A(y,a)));else{let t=w(y,S,r,f),e=t.length;for(let i=0;i<e;i++){let r=A(t[i],a);for(let t in S){var C,D;L(S[t],(C=j,D=t,C&&C[D]?{...C,...C[D]}:{...C}),M(t,r),i,e)}}}x=b,b+=R}return a.forEach((t,r)=>{for(let n in t){let s=t[n];s.sort(S);let a=[],l=[],u=[];for(let t=0;t<s.length;t++){let{at:e,value:i,easing:r}=s[t];a.push(i),l.push((0,y.q)(0,V,e)),u.push(r||"easeOut")}0!==l[0]&&(l.unshift(0),a.unshift(a[0]),u.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),a.push(null)),o.has(r)||o.set(r,{keyframes:{},transition:{}});let h=o.get(r);h.keyframes[n]=a,h.transition[n]={...e,duration:V,ease:u,times:l,...i}}}),o})(t,e,i,{spring:a.o}).forEach(({keyframes:t,transition:e},i)=>{r.push(...N(i,t,e))}),r}(e,i,t):N(e,i,r,t));return t&&t.animations.push(s),s}}function G(){var t;let e=(0,r.M)(()=>({current:null,animations:[]})),i=(0,r.M)(()=>$(e));return t=()=>{e.animations.forEach(t=>t.stop())},(0,n.useEffect)(()=>()=>t(),[]),[e,i]}$()},5744:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"workAsyncStorage",{enumerable:!0,get:function(){return r.workAsyncStorageInstance}});let r=i(7828)},5818:(t,e,i)=>{i.d(e,{q:()=>r});let r=(t,e,i)=>{let r=e-t;return 0===r?1:(i-t)/r}},5910:(t,e,i)=>{i.d(e,{p:()=>r});let r=t=>Array.isArray(t)},5920:(t,e,i)=>{i.d(e,{$:()=>s,q:()=>o});var r=i(614);let n=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,s=(t,e)=>i=>!!("string"==typeof i&&n.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),o=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[s,o,a,l]=n.match(r.S);return{[t]:parseFloat(s),[e]:parseFloat(o),[i]:parseFloat(a),alpha:void 0!==l?parseFloat(l):1}}},5943:(t,e,i)=>{i.d(e,{h:()=>n});var r=i(9782);function n(t){return(0,r.x)(t)&&"svg"===t.tagName}},6051:(t,e,i)=>{i.d(e,{f:()=>tI});var r=i(8777),n=i(9515),s=i(3191),o=i(1297),a=i(7215),l=i(4261),u=i(3704),h=i(4542),d=i(8606),c=i(4272),p=i(10),m=i(1335),f=i(8476);function g(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}var v=i(9064);function y(t,e){return i=>i>0?e:t}var x=i(3210);let b=(t,e,i)=>{let r=t*t,n=i*(e*e-r)+r;return n<0?0:Math.sqrt(n)},w=[m.u,v.B,f.V],k=t=>w.find(e=>e.test(t));function T(t){let e=k(t);if((0,h.$)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===f.V&&(i=function({hue:t,saturation:e,lightness:i,alpha:r}){t/=360,i/=100;let n=0,s=0,o=0;if(e/=100){let r=i<.5?i*(1+e):i+e-i*e,a=2*i-r;n=g(a,r,t+1/3),s=g(a,r,t),o=g(a,r,t-1/3)}else n=s=o=i;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*o),alpha:r}}(i)),i}let P=(t,e)=>{let i=T(t),r=T(e);if(!i||!r)return y(t,e);let n={...i};return t=>(n.red=b(i.red,r.red,t),n.green=b(i.green,r.green,t),n.blue=b(i.blue,r.blue,t),n.alpha=(0,x.k)(i.alpha,r.alpha,t),v.B.transform(n))},S=new Set(["none","hidden"]);function A(t,e){return i=>(0,x.k)(t,e,i)}function M(t){return"number"==typeof t?A:"string"==typeof t?(0,d.p)(t)?y:c.y.test(t)?P:C:Array.isArray(t)?V:"object"==typeof t?c.y.test(t)?P:E:y}function V(t,e){let i=[...t],r=i.length,n=t.map((t,i)=>M(t)(t,e[i]));return t=>{for(let e=0;e<r;e++)i[e]=n[e](t);return i}}function E(t,e){let i={...t,...e},r={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(r[n]=M(t[n])(t[n],e[n]));return t=>{for(let e in r)i[e]=r[e](t);return i}}let C=(t,e)=>{let i=p.f.createTransformer(e),r=(0,p.V)(t),n=(0,p.V)(e);return r.indexes.var.length===n.indexes.var.length&&r.indexes.color.length===n.indexes.color.length&&r.indexes.number.length>=n.indexes.number.length?S.has(t)&&!n.values.length||S.has(e)&&!r.values.length?function(t,e){return S.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):(0,s.F)(V(function(t,e){let i=[],r={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let s=e.types[n],o=t.indexes[s][r[s]],a=t.values[o]??0;i[n]=a,r[s]++}return i}(r,n),n.values),i):((0,h.$)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),y(t,e))};function D(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?(0,x.k)(t,e,i):M(t)(t,e)}let j=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>n.Gt.update(e,t),stop:()=>(0,n.WG)(e),now:()=>n.uv.isProcessing?n.uv.timestamp:l.k.now()}};var R=i(4687),L=i(3945);function F({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c,p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,v=i*e,y=p+v,x=void 0===o?y:o(y);x!==y&&(v=x-p);let b=t=>-v*Math.exp(-t/r),w=t=>x+b(t),k=t=>{let e=b(t),i=w(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},T=t=>{f(m.value)&&(d=t,c=(0,R.o)({keyframes:[m.value,g(m.value)],velocity:(0,L.Y)(w,t,m.value),damping:n,stiffness:s,restDelta:u,restSpeed:h}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,k(t),T(t)),void 0!==d&&t>=d)?c.next(t-d):(e||k(t),m)}}}var B=i(9827);let O=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function I(t,e,i,r){if(t===e&&i===r)return B.l;let n=e=>(function(t,e,i,r,n){let s,o,a=0;do(s=O(o=e+(i-e)/2,r,n)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:O(n(t),e,r)}let z=I(.42,0,1,1),U=I(0,0,.58,1),W=I(.42,0,.58,1);var N=i(1081),$=i(1765),G=i(4180);let q=I(.33,1.53,.69,.99),X=(0,G.G)(q),K=(0,$.V)(X),H=t=>(t*=2)<1?.5*X(t):.5*(2-Math.pow(2,-10*(t-1)));var Y=i(7712);let _=t=>Array.isArray(t)&&"number"==typeof t[0],Q={linear:B.l,easeIn:z,easeInOut:W,easeOut:U,circIn:Y.po,circInOut:Y.tn,circOut:Y.yT,backIn:X,backInOut:K,backOut:q,anticipate:H},Z=t=>"string"==typeof t,J=t=>{if(_(t)){(0,h.V)(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,r,n]=t;return I(e,i,r,n)}return Z(t)?((0,h.V)(void 0!==Q[t],`Invalid easing type '${t}'`),Q[t]):t};var tt=i(3387),te=i(5818),ti=i(9115);function tr({duration:t=300,keyframes:e,times:i,ease:r="easeInOut"}){var n;let a=(0,N.h)(r)?r.map(J):J(r),l={done:!1,value:e[0]},u=function(t,e,{clamp:i=!0,ease:r,mixer:n}={}){let a=t.length;if((0,h.V)(a===e.length,"Both input and output ranges must be the same length"),1===a)return()=>e[0];if(2===a&&e[0]===e[1])return()=>e[1];let l=t[0]===t[1];t[0]>t[a-1]&&(t=[...t].reverse(),e=[...e].reverse());let u=function(t,e,i){let r=[],n=i||tt.W.mix||D,o=t.length-1;for(let i=0;i<o;i++){let o=n(t[i],t[i+1]);if(e){let t=Array.isArray(e)?e[i]||B.l:e;o=(0,s.F)(t,o)}r.push(o)}return r}(e,r,n),d=u.length,c=i=>{if(l&&i<t[0])return e[0];let r=0;if(d>1)for(;r<t.length-2&&!(i<t[r+1]);r++);let n=(0,te.q)(t[r],t[r+1],i);return u[r](n)};return i?e=>c((0,o.q)(t[0],t[a-1],e)):c}((n=i&&i.length===e.length?i:(0,ti.Z)(e),n.map(e=>e*t)),e,{ease:Array.isArray(a)?a:e.map(()=>a||W).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(l.value=u(e),l.done=e>=t,l)}}var tn=i(2458);let ts=t=>null!==t;function to(t,{repeat:e,repeatType:i="loop"},r,n=1){let s=t.filter(ts),o=n<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return o&&void 0!==r?r:s[o]}let ta={decay:F,inertia:F,tween:tr,keyframes:tr,spring:R.o};function tl(t){"string"==typeof t.type&&(t.type=ta[t.type])}class tu{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let th=t=>t/100;class td extends tu{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==l.k.now()&&this.tick(l.k.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},u.q.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;tl(t);let{type:e=tr,repeat:i=0,repeatDelay:r=0,repeatType:n,velocity:o=0}=t,{keyframes:a}=t,l=e||tr;l!==tr&&"number"!=typeof a[0]&&(this.mixKeyframes=(0,s.F)(th,D(a[0],a[1])),a=[0,100]);let u=l({...t,keyframes:a});"mirror"===n&&(this.mirroredGenerator=l({...t,keyframes:[...a].reverse(),velocity:-o})),null===u.calculatedDuration&&(u.calculatedDuration=(0,tn.t)(u));let{calculatedDuration:h}=u;this.calculatedDuration=h,this.resolvedDuration=h+r,this.totalDuration=this.resolvedDuration*(i+1)-r,this.generator=u}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:r,mixKeyframes:n,mirroredGenerator:s,resolvedDuration:a,calculatedDuration:l}=this;if(null===this.startTime)return i.next(0);let{delay:u=0,keyframes:h,repeat:d,repeatType:c,repeatDelay:p,type:m,onUpdate:f,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-r/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let v=this.currentTime-u*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?v<0:v>r;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let x=this.currentTime,b=i;if(d){let t=Math.min(this.currentTime,r)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,d+1))%2&&("reverse"===c?(i=1-i,p&&(i-=p/a)):"mirror"===c&&(b=s)),x=(0,o.q)(0,1,i)*a}let w=y?{done:!1,value:h[0]}:b.next(x);n&&(w.value=n(w.value));let{done:k}=w;y||null===l||(k=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&k);return T&&m!==F&&(w.value=to(h,this.options,g,this.speed)),f&&f(w.value),T&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return(0,a.X)(this.calculatedDuration)}get time(){return(0,a.X)(this.currentTime)}set time(t){t=(0,a.f)(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(l.k.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=(0,a.X)(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=j,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(l.k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,u.q.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}var tc=i(7322);let tp=t=>t.startsWith("--");function tm(t){let e;return()=>(void 0===e&&(e=t()),e)}let tf=tm(()=>void 0!==window.ScrollTimeline);var tg=i(4744);let tv={},ty=function(t,e){let i=tm(t);return()=>tv[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing");var tx=i(7705);let tb=([t,e,i,r])=>`cubic-bezier(${t}, ${e}, ${i}, ${r})`,tw={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tb([0,.65,.55,1]),circOut:tb([.55,0,1,.45]),backIn:tb([.31,.01,.66,-.59]),backOut:tb([.33,1.53,.69,.99])};var tk=i(3128);class tT extends tu{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:r,pseudoElement:n,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!n,this.allowFlatten=s,this.options=t,(0,h.V)("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return(0,tk.W)(t)&&ty()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:r=0,duration:n=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},h){let d={[e]:i};l&&(d.offset=l);let c=function t(e,i){if(e)return"function"==typeof e?ty()?(0,tx.K)(e,i):"ease-out":_(e)?tb(e):Array.isArray(e)?e.map(e=>t(e,i)||tw.easeOut):tw[e]}(a,n);Array.isArray(c)&&(d.easing=c),tg.Q.value&&u.q.waapi++;let p={delay:r,duration:n,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};h&&(p.pseudoElement=h);let m=t.animate(d,p);return tg.Q.value&&m.finished.finally(()=>{u.q.waapi--}),m}(e,i,r,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=to(r,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){tp(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){let t=this.animation.effect?.getComputedTiming?.().duration||0;return(0,a.X)(Number(t))}get time(){return(0,a.X)(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=(0,a.f)(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&tf())?(this.animation.timeline=t,B.l):e(this)}}let tP={anticipate:H,backInOut:K,circInOut:Y.tn};class tS extends tT{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in tP&&(t.ease=tP[t.ease])}(t),tl(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:r,element:n,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new td({...s,autoplay:!1}),l=(0,a.f)(this.finishedTime??this.time);e.setWithVelocity(o.sample(l-10).value,o.sample(l).value,10),o.stop()}}let tA=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(p.f.test(t)||"0"===t)&&!t.startsWith("url("));var tM=i(7351);let tV=new Set(["opacity","clipPath","filter","transform"]),tE=tm(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tC extends tu{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:s="loop",keyframes:o,name:a,motionValue:u,element:h,...d}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=l.k.now();let c={autoplay:t,delay:e,type:i,repeat:r,repeatDelay:n,repeatType:s,name:a,motionValue:u,element:h,...d},p=h?.KeyframeResolver||tc.h;this.keyframeResolver=new p(o,(t,e,i)=>this.onKeyframesResolved(t,e,c,!i),a,u,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,r){this.keyframeResolver=void 0;let{name:n,type:s,velocity:o,delay:a,isHandoff:u,onUpdate:d}=i;this.resolvedAt=l.k.now(),!function(t,e,i,r){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=tA(n,e),a=tA(s,e);return(0,h.$)(o===a,`You are trying to animate ${e} from "${n}" to "${s}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||(0,tk.W)(i))&&r)}(t,n,s,o)&&((tt.W.instantAnimations||!a)&&d?.(to(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let c={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},p=!u&&function(t){let{motionValue:e,name:i,repeatDelay:r,repeatType:n,damping:s,type:o}=t;if(!(0,tM.s)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return tE()&&i&&tV.has(i)&&("transform"!==i||!l)&&!a&&!r&&"mirror"!==n&&0!==s&&"inertia"!==o}(c)?new tS({...c,element:c.motionValue.owner.current}):new td(c);p.finished.then(()=>this.notifyFinished()).catch(B.l),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),(0,tc.q)()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let tD=t=>null!==t;var tj=i(18);let tR={type:"spring",stiffness:500,damping:25,restSpeed:10},tL=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),tF={type:"keyframes",duration:.8},tB={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},tO=(t,{keyframes:e})=>e.length>2?tF:tj.f.has(t)?t.startsWith("scale")?tL(e[1]):tR:tB,tI=(t,e,i,s={},o,l)=>u=>{let h=(0,r.r)(s,t)||{},d=h.delay||s.delay||0,{elapsed:c=0}=s;c-=(0,a.f)(d);let p={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...h,delay:-c,onUpdate:t=>{e.set(t),h.onUpdate&&h.onUpdate(t)},onComplete:()=>{u(),h.onComplete&&h.onComplete()},name:t,motionValue:e,element:l?void 0:o};!function({when:t,delay:e,delayChildren:i,staggerChildren:r,staggerDirection:n,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(h)&&Object.assign(p,tO(t,p)),p.duration&&(p.duration=(0,a.f)(p.duration)),p.repeatDelay&&(p.repeatDelay=(0,a.f)(p.repeatDelay)),void 0!==p.from&&(p.keyframes[0]=p.from);let m=!1;if(!1!==p.type&&(0!==p.duration||p.repeatDelay)||(p.duration=0,0===p.delay&&(m=!0)),(tt.W.instantAnimations||tt.W.skipAnimations)&&(m=!0,p.duration=0,p.delay=0),p.allowFlatten=!h.type&&!h.ease,m&&!l&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},r){let n=t.filter(tD),s=e&&"loop"!==i&&e%2==1?0:n.length-1;return n[s]}(p.keyframes,h);if(void 0!==t)return void n.Gt.update(()=>{p.onUpdate(t),p.onComplete()})}return h.isSync?new td(p):new tC(p)}},6147:(t,e,i)=>{i.d(e,{OU:()=>u,Ql:()=>h,Ww:()=>c,hq:()=>s,o4:()=>l});var r=i(3210),n=i(2662);function s(t,e,i){return i+e*(t-i)}function o(t,e,i,r,n){return void 0!==n&&(t=r+n*(t-r)),r+i*(t-r)+e}function a(t,e=0,i=1,r,n){t.min=o(t.min,e,i,r,n),t.max=o(t.max,e,i,r,n)}function l(t,{x:e,y:i}){a(t.x,e.translate,e.scale,e.originPoint),a(t.y,i.translate,i.scale,i.originPoint)}function u(t,e,i,r=!1){let s,o,a=i.length;if(a){e.x=e.y=1;for(let u=0;u<a;u++){o=(s=i[u]).projectionDelta;let{visualElement:a}=s.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(r&&s.options.layoutScroll&&s.scroll&&s!==s.root&&c(t,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),o&&(e.x*=o.x.scale,e.y*=o.y.scale,l(t,o)),r&&(0,n.HD)(s.latestValues)&&c(t,s.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}function h(t,e){t.min=t.min+e,t.max=t.max+e}function d(t,e,i,n,s=.5){let o=(0,r.k)(t.min,t.max,s);a(t,e,i,o,n)}function c(t,e){d(t.x,e.x,e.scaleX,e.scale,e.originX),d(t.y,e.y,e.scaleY,e.scale,e.originY)}},6333:(t,e,i)=>{i.d(e,{g:()=>s});var r=i(3387),n=i(4803);function s(t,e){let i=t.getValue("willChange");if((0,n.S)(i)&&i.add)return i.add(e);if(!i&&r.W.WillChange){let i=new r.W.WillChange("auto");t.addValue("willChange",i),i.add(e)}}},6340:(t,e,i)=>{i.d(e,{N:()=>r});function r(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}},6642:(t,e,i)=>{i.d(e,{B:()=>n});let r={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},n={};for(let t in r)n[t]={isEnabled:e=>r[t].some(t=>!!e[t])}},6645:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return n}});let r=i(8229)._(i(7357));function n(t,e){var i;let n={};"function"==typeof t&&(n.loader=t);let s={...n,...e};return(0,r.default)({...s,modules:null==(i=s.loadableGenerated)?void 0:i.modules})}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6668:(t,e,i)=>{function r(t,e){-1===t.indexOf(e)&&t.push(e)}function n(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}i.d(e,{Ai:()=>n,Kq:()=>r})},6926:(t,e,i)=>{i.d(e,{P:()=>n});var r=i(1788);function n(t){return t.props[r.n]}},6983:(t,e,i)=>{i.d(e,{G:()=>r});function r(t){return"object"==typeof t&&null!==t}},7215:(t,e,i)=>{i.d(e,{X:()=>n,f:()=>r});let r=t=>1e3*t,n=t=>t/1e3},7277:(t,e,i)=>{i.d(e,{J:()=>o});var r=i(10),n=i(2171),s=i(1834);function o(t,e){let i=(0,s.D)(t);return i!==n.p&&(i=r.f),i.getAnimatableNone?i.getAnimatableNone(e):void 0}},7312:(t,e,i)=>{i.d(e,{$:()=>r});let r=t=>/^0[^.\s]+$/u.test(t)},7322:(t,e,i)=>{i.d(e,{h:()=>c,q:()=>d});var r=i(280),n=i(9515);let s=new Set,o=!1,a=!1,l=!1;function u(){if(a){let t=Array.from(s).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=(0,r.W9)(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}a=!1,o=!1,s.forEach(t=>t.complete(l)),s.clear()}function h(){s.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(a=!0)})}function d(){l=!0,h(),u(),l=!1}class c{constructor(t,e,i,r,n,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=r,this.element=n,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(s.add(this),o||(o=!0,n.Gt.read(h),n.Gt.resolveKeyframes(u))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:r}=this;if(null===t[0]){let n=r?.get(),s=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let r=i.readValue(e,s);null!=r&&(t[0]=r)}void 0===t[0]&&(t[0]=s),r&&void 0===n&&r.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),s.delete(this)}cancel(){"scheduled"===this.state&&(s.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}},7351:(t,e,i)=>{i.d(e,{s:()=>n});var r=i(6983);function n(t){return(0,r.G)(t)&&"offsetHeight"in t}},7357:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:function(){return l}});let r=i(5155),n=i(2115),s=i(2146);function o(t){return{default:t&&"default"in t?t.default:t}}i(255);let a={loader:()=>Promise.resolve(o(()=>null)),loading:null,ssr:!0},l=function(t){let e={...a,...t},i=(0,n.lazy)(()=>e.loader().then(o)),l=e.loading;function u(t){let o=l?(0,r.jsx)(l,{isLoading:!0,pastDelay:!0,error:null}):null,a=!e.ssr||!!e.loading,u=a?n.Suspense:n.Fragment,h=e.ssr?(0,r.jsxs)(r.Fragment,{children:[null,(0,r.jsx)(i,{...t})]}):(0,r.jsx)(s.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(i,{...t})});return(0,r.jsx)(u,{...a?{fallback:o}:{},children:h})}return u.displayName="LoadableComponent",u}},7494:(t,e,i)=>{i.d(e,{E:()=>n});var r=i(2115);let n=i(8972).B?r.useLayoutEffect:r.useEffect},7684:(t,e,i)=>{i.d(e,{O:()=>u});var r=i(18),n=i(8606);let s=(t,e)=>e&&"number"==typeof t?e.transform(t):t;var o=i(2403);let a={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},l=r.U.length;function u(t,e,i){let{style:u,vars:h,transformOrigin:d}=t,c=!1,p=!1;for(let t in e){let i=e[t];if(r.f.has(t)){c=!0;continue}if((0,n.j)(t)){h[t]=i;continue}{let e=s(i,o.W[t]);t.startsWith("origin")?(p=!0,d[t]=e):u[t]=e}}if(!e.transform&&(c||i?u.transform=function(t,e,i){let n="",u=!0;for(let h=0;h<l;h++){let l=r.U[h],d=t[l];if(void 0===d)continue;let c=!0;if(!(c="number"==typeof d?d===+!!l.startsWith("scale"):0===parseFloat(d))||i){let t=s(d,o.W[l]);if(!c){u=!1;let e=a[l]||l;n+=`${e}(${t}) `}i&&(e[l]=t)}}return n=n.trim(),i?n=i(e,u?"":n):u&&(n="none"),n}(e,t.transform,i):u.transform&&(u.transform="none")),p){let{originX:t="50%",originY:e="50%",originZ:i=0}=d;u.transformOrigin=`${t} ${e} ${i}`}}},7705:(t,e,i)=>{i.d(e,{K:()=>r});let r=(t,e,i=10)=>{let r="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)r+=Math.round(1e4*t(e/(n-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`}},7712:(t,e,i)=>{i.d(e,{po:()=>s,tn:()=>a,yT:()=>o});var r=i(1765),n=i(4180);let s=t=>1-Math.sin(Math.acos(t)),o=(0,n.G)(s),a=(0,r.V)(s)},7828:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"workAsyncStorageInstance",{enumerable:!0,get:function(){return r}});let r=(0,i(4054).createAsyncLocalStorage)()},7887:(t,e,i)=>{i.d(e,{X4:()=>s,ai:()=>n,hs:()=>o});var r=i(1297);let n={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},s={...n,transform:t=>(0,r.q)(0,1,t)},o={...n,default:1}},7934:(t,e,i)=>{i.d(e,{$:()=>c});var r=i(8777),n=i(9515),s=i(8109),o=i(98),a=i(5910),l=i(419),u=i(6333),h=i(6926),d=i(6051);function c(t,e,{delay:i=0,transitionOverride:p,type:m}={}){let{transition:f=t.getDefaultTransition(),transitionEnd:g,...v}=e;p&&(f=p);let y=[],x=m&&t.animationState&&t.animationState.getState()[m];for(let e in v){let o=t.getValue(e,t.latestValues[e]??null),a=v[e];if(void 0===a||x&&function({protectedKeys:t,needsAnimating:e},i){let r=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,r}(x,e))continue;let l={delay:i,...(0,r.r)(f||{},e)},c=o.get();if(void 0!==c&&!o.isAnimating&&!Array.isArray(a)&&a===c&&!l.velocity)continue;let p=!1;if(window.MotionHandoffAnimation){let i=(0,h.P)(t);if(i){let t=window.MotionHandoffAnimation(i,e,n.Gt);null!==t&&(l.startTime=t,p=!0)}}(0,u.g)(t,e),o.start((0,d.f)(e,o,a,t.shouldReduceMotion&&s.$.has(e)?{type:!1}:l,t,p));let m=o.animation;m&&y.push(m)}return g&&Promise.all(y).then(()=>{n.Gt.update(()=>{g&&function(t,e){let{transitionEnd:i={},transition:r={},...n}=(0,l.K)(t,e)||{};for(let e in n={...n,...i}){var s;let i=(s=n[e],(0,a.p)(s)?s[s.length-1]||0:s);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,o.OQ)(i))}}(t,g)})}),y}},8109:(t,e,i)=>{i.d(e,{$:()=>r});let r=new Set(["width","height","top","left","right","bottom",...i(18).U])},8312:(t,e,i)=>{i.d(e,{U:()=>r,_:()=>n});let r=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],n=["initial",...r]},8437:(t,e,i)=>{i.d(e,{I:()=>o});var r=i(3387);let n=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];var s=i(4744);function o(t,e){let i=!1,o=!0,a={delta:0,timestamp:0,isProcessing:!1},l=()=>i=!0,u=n.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,r=new Set,n=!1,o=!1,a=new WeakSet,l={delta:0,timestamp:0,isProcessing:!1},u=0;function h(e){a.has(e)&&(d.schedule(e),t()),u++,e(l)}let d={schedule:(t,e=!1,s=!1)=>{let o=s&&n?i:r;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{r.delete(t),a.delete(t)},process:t=>{if(l=t,n){o=!0;return}n=!0,[i,r]=[r,i],i.forEach(h),e&&s.Q.value&&s.Q.value.frameloop[e].push(u),u=0,i.clear(),n=!1,o&&(o=!1,d.process(t))}};return d}(l,e?i:void 0),t),{}),{setup:h,read:d,resolveKeyframes:c,preUpdate:p,update:m,preRender:f,render:g,postRender:v}=u,y=()=>{let n=r.W.useManualTiming?a.timestamp:performance.now();i=!1,r.W.useManualTiming||(a.delta=o?1e3/60:Math.max(Math.min(n-a.timestamp,40),1)),a.timestamp=n,a.isProcessing=!0,h.process(a),d.process(a),c.process(a),p.process(a),m.process(a),f.process(a),g.process(a),v.process(a),a.isProcessing=!1,i&&e&&(o=!1,t(y))},x=()=>{i=!0,o=!0,a.isProcessing||t(y)};return{schedule:n.reduce((t,e)=>{let r=u[e];return t[e]=(t,e=!1,n=!1)=>(i||x(),r.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<n.length;e++)u[n[e]].cancel(t)},state:a,steps:u}}},8450:(t,e,i)=>{i.d(e,{I:()=>r});let r=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase()},8476:(t,e,i)=>{i.d(e,{V:()=>a});var r=i(7887),n=i(4158),s=i(1557),o=i(5920);let a={test:(0,o.$)("hsl","hue"),parse:(0,o.q)("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:o=1})=>"hsla("+Math.round(t)+", "+n.KN.transform((0,s.a)(e))+", "+n.KN.transform((0,s.a)(i))+", "+(0,s.a)(r.X4.transform(o))+")"}},8588:(t,e,i)=>{function r({top:t,left:e,right:i,bottom:r}){return{x:{min:e,max:i},y:{min:t,max:r}}}function n({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function s(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}i.d(e,{FY:()=>r,bS:()=>s,pA:()=>n})},8606:(t,e,i)=>{i.d(e,{j:()=>n,p:()=>o});let r=t=>e=>"string"==typeof e&&e.startsWith(t),n=r("--"),s=r("var(--"),o=t=>!!s(t)&&a.test(t.split("/*")[0].trim()),a=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},8609:(t,e,i)=>{i.d(e,{x:()=>s});var r=i(4803),n=i(3055);function s(t,e,i){let{style:s}=t,o={};for(let a in s)((0,r.S)(s[a])||e.style&&(0,r.S)(e.style[a])||(0,n.z)(a,t)||i?.getValue(a)?.liveStyle!==void 0)&&(o[a]=s[a]);return o}},8777:(t,e,i)=>{i.d(e,{r:()=>r});function r(t,e){return t?.[e]??t?.default??t}},8972:(t,e,i)=>{i.d(e,{B:()=>r});let r="undefined"!=typeof window},9064:(t,e,i)=>{i.d(e,{B:()=>u});var r=i(1297),n=i(7887),s=i(1557),o=i(5920);let a=t=>(0,r.q)(0,255,t),l={...n.ai,transform:t=>Math.round(a(t))},u={test:(0,o.$)("rgb","red"),parse:(0,o.q)("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:r=1})=>"rgba("+l.transform(t)+", "+l.transform(e)+", "+l.transform(i)+", "+(0,s.a)(n.X4.transform(r))+")"}},9115:(t,e,i)=>{i.d(e,{Z:()=>n});var r=i(2017);function n(t){let e=[0];return(0,r.f)(e,t.length-1),e}},9253:(t,e,i)=>{i.d(e,{O:()=>a,e:()=>o});var r=i(6340),n=i(5305),s=i(8312);function o(t){return(0,r.N)(t.animate)||s._.some(e=>(0,n.w)(t[e]))}function a(t){return!!(o(t)||t.variants)}},9515:(t,e,i)=>{i.d(e,{Gt:()=>n,PP:()=>a,WG:()=>s,uv:()=>o});var r=i(9827);let{schedule:n,cancel:s,state:o,steps:a}=(0,i(8437).I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:r.l,!0)},9688:(t,e,i)=>{i.d(e,{QP:()=>tu});let r=t=>{let e=a(t),{conflictingClassGroups:i,conflictingClassGroupModifiers:r}=t;return{getClassGroupId:t=>{let i=t.split("-");return""===i[0]&&1!==i.length&&i.shift(),n(i,e)||o(t)},getConflictingClassGroupIds:(t,e)=>{let n=i[t]||[];return e&&r[t]?[...n,...r[t]]:n}}},n=(t,e)=>{if(0===t.length)return e.classGroupId;let i=t[0],r=e.nextPart.get(i),s=r?n(t.slice(1),r):void 0;if(s)return s;if(0===e.validators.length)return;let o=t.join("-");return e.validators.find(({validator:t})=>t(o))?.classGroupId},s=/^\[(.+)\]$/,o=t=>{if(s.test(t)){let e=s.exec(t)[1],i=e?.substring(0,e.indexOf(":"));if(i)return"arbitrary.."+i}},a=t=>{let{theme:e,classGroups:i}=t,r={nextPart:new Map,validators:[]};for(let t in i)l(i[t],r,t,e);return r},l=(t,e,i,r)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:u(e,t)).classGroupId=i;return}if("function"==typeof t)return h(t)?void l(t(r),e,i,r):void e.validators.push({validator:t,classGroupId:i});Object.entries(t).forEach(([t,n])=>{l(n,u(e,t),i,r)})})},u=(t,e)=>{let i=t;return e.split("-").forEach(t=>{i.nextPart.has(t)||i.nextPart.set(t,{nextPart:new Map,validators:[]}),i=i.nextPart.get(t)}),i},h=t=>t.isThemeGetter,d=t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,i=new Map,r=new Map,n=(n,s)=>{i.set(n,s),++e>t&&(e=0,r=i,i=new Map)};return{get(t){let e=i.get(t);return void 0!==e?e:void 0!==(e=r.get(t))?(n(t,e),e):void 0},set(t,e){i.has(t)?i.set(t,e):n(t,e)}}},c=t=>{let{prefix:e,experimentalParseClassName:i}=t,r=t=>{let e,i=[],r=0,n=0,s=0;for(let o=0;o<t.length;o++){let a=t[o];if(0===r&&0===n){if(":"===a){i.push(t.slice(s,o)),s=o+1;continue}if("/"===a){e=o;continue}}"["===a?r++:"]"===a?r--:"("===a?n++:")"===a&&n--}let o=0===i.length?t:t.substring(s),a=p(o);return{modifiers:i,hasImportantModifier:a!==o,baseClassName:a,maybePostfixModifierPosition:e&&e>s?e-s:void 0}};if(e){let t=e+":",i=r;r=e=>e.startsWith(t)?i(e.substring(t.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:e,maybePostfixModifierPosition:void 0}}if(i){let t=r;r=e=>i({className:e,parseClassName:t})}return r},p=t=>t.endsWith("!")?t.substring(0,t.length-1):t.startsWith("!")?t.substring(1):t,m=t=>{let e=Object.fromEntries(t.orderSensitiveModifiers.map(t=>[t,!0]));return t=>{if(t.length<=1)return t;let i=[],r=[];return t.forEach(t=>{"["===t[0]||e[t]?(i.push(...r.sort(),t),r=[]):r.push(t)}),i.push(...r.sort()),i}},f=t=>({cache:d(t.cacheSize),parseClassName:c(t),sortModifiers:m(t),...r(t)}),g=/\s+/,v=(t,e)=>{let{parseClassName:i,getClassGroupId:r,getConflictingClassGroupIds:n,sortModifiers:s}=e,o=[],a=t.trim().split(g),l="";for(let t=a.length-1;t>=0;t-=1){let e=a[t],{isExternal:u,modifiers:h,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:p}=i(e);if(u){l=e+(l.length>0?" "+l:l);continue}let m=!!p,f=r(m?c.substring(0,p):c);if(!f){if(!m||!(f=r(c))){l=e+(l.length>0?" "+l:l);continue}m=!1}let g=s(h).join(":"),v=d?g+"!":g,y=v+f;if(o.includes(y))continue;o.push(y);let x=n(f,m);for(let t=0;t<x.length;++t){let e=x[t];o.push(v+e)}l=e+(l.length>0?" "+l:l)}return l};function y(){let t,e,i=0,r="";for(;i<arguments.length;)(t=arguments[i++])&&(e=x(t))&&(r&&(r+=" "),r+=e);return r}let x=t=>{let e;if("string"==typeof t)return t;let i="";for(let r=0;r<t.length;r++)t[r]&&(e=x(t[r]))&&(i&&(i+=" "),i+=e);return i},b=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},w=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,T=/^\d+\/\d+$/,P=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,S=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,A=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,M=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,V=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,E=t=>T.test(t),C=t=>!!t&&!Number.isNaN(Number(t)),D=t=>!!t&&Number.isInteger(Number(t)),j=t=>t.endsWith("%")&&C(t.slice(0,-1)),R=t=>P.test(t),L=()=>!0,F=t=>S.test(t)&&!A.test(t),B=()=>!1,O=t=>M.test(t),I=t=>V.test(t),z=t=>!W(t)&&!K(t),U=t=>tt(t,tn,B),W=t=>w.test(t),N=t=>tt(t,ts,F),$=t=>tt(t,to,C),G=t=>tt(t,ti,B),q=t=>tt(t,tr,I),X=t=>tt(t,tl,O),K=t=>k.test(t),H=t=>te(t,ts),Y=t=>te(t,ta),_=t=>te(t,ti),Q=t=>te(t,tn),Z=t=>te(t,tr),J=t=>te(t,tl,!0),tt=(t,e,i)=>{let r=w.exec(t);return!!r&&(r[1]?e(r[1]):i(r[2]))},te=(t,e,i=!1)=>{let r=k.exec(t);return!!r&&(r[1]?e(r[1]):i)},ti=t=>"position"===t||"percentage"===t,tr=t=>"image"===t||"url"===t,tn=t=>"length"===t||"size"===t||"bg-size"===t,ts=t=>"length"===t,to=t=>"number"===t,ta=t=>"family-name"===t,tl=t=>"shadow"===t;Symbol.toStringTag;let tu=function(t,...e){let i,r,n,s=function(a){return r=(i=f(e.reduce((t,e)=>e(t),t()))).cache.get,n=i.cache.set,s=o,o(a)};function o(t){let e=r(t);if(e)return e;let s=v(t,i);return n(t,s),s}return function(){return s(y.apply(null,arguments))}}(()=>{let t=b("color"),e=b("font"),i=b("text"),r=b("font-weight"),n=b("tracking"),s=b("leading"),o=b("breakpoint"),a=b("container"),l=b("spacing"),u=b("radius"),h=b("shadow"),d=b("inset-shadow"),c=b("text-shadow"),p=b("drop-shadow"),m=b("blur"),f=b("perspective"),g=b("aspect"),v=b("ease"),y=b("animate"),x=()=>["auto","avoid","all","avoid-page","page","left","right","column"],w=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...w(),K,W],T=()=>["auto","hidden","clip","visible","scroll"],P=()=>["auto","contain","none"],S=()=>[K,W,l],A=()=>[E,"full","auto",...S()],M=()=>[D,"none","subgrid",K,W],V=()=>["auto",{span:["full",D,K,W]},D,K,W],F=()=>[D,"auto",K,W],B=()=>["auto","min","max","fr",K,W],O=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],I=()=>["start","end","center","stretch","center-safe","end-safe"],tt=()=>["auto",...S()],te=()=>[E,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...S()],ti=()=>[t,K,W],tr=()=>[...w(),_,G,{position:[K,W]}],tn=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ts=()=>["auto","cover","contain",Q,U,{size:[K,W]}],to=()=>[j,H,N],ta=()=>["","none","full",u,K,W],tl=()=>["",C,H,N],tu=()=>["solid","dashed","dotted","double"],th=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],td=()=>[C,j,_,G],tc=()=>["","none",m,K,W],tp=()=>["none",C,K,W],tm=()=>["none",C,K,W],tf=()=>[C,K,W],tg=()=>[E,"full",...S()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[R],breakpoint:[R],color:[L],container:[R],"drop-shadow":[R],ease:["in","out","in-out"],font:[z],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[R],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[R],shadow:[R],spacing:["px",C],text:[R],"text-shadow":[R],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",E,W,K,g]}],container:["container"],columns:[{columns:[C,W,K,a]}],"break-after":[{"break-after":x()}],"break-before":[{"break-before":x()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:T()}],"overflow-x":[{"overflow-x":T()}],"overflow-y":[{"overflow-y":T()}],overscroll:[{overscroll:P()}],"overscroll-x":[{"overscroll-x":P()}],"overscroll-y":[{"overscroll-y":P()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:A()}],"inset-x":[{"inset-x":A()}],"inset-y":[{"inset-y":A()}],start:[{start:A()}],end:[{end:A()}],top:[{top:A()}],right:[{right:A()}],bottom:[{bottom:A()}],left:[{left:A()}],visibility:["visible","invisible","collapse"],z:[{z:[D,"auto",K,W]}],basis:[{basis:[E,"full","auto",a,...S()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[C,E,"auto","initial","none",W]}],grow:[{grow:["",C,K,W]}],shrink:[{shrink:["",C,K,W]}],order:[{order:[D,"first","last","none",K,W]}],"grid-cols":[{"grid-cols":M()}],"col-start-end":[{col:V()}],"col-start":[{"col-start":F()}],"col-end":[{"col-end":F()}],"grid-rows":[{"grid-rows":M()}],"row-start-end":[{row:V()}],"row-start":[{"row-start":F()}],"row-end":[{"row-end":F()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":B()}],"auto-rows":[{"auto-rows":B()}],gap:[{gap:S()}],"gap-x":[{"gap-x":S()}],"gap-y":[{"gap-y":S()}],"justify-content":[{justify:[...O(),"normal"]}],"justify-items":[{"justify-items":[...I(),"normal"]}],"justify-self":[{"justify-self":["auto",...I()]}],"align-content":[{content:["normal",...O()]}],"align-items":[{items:[...I(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...I(),{baseline:["","last"]}]}],"place-content":[{"place-content":O()}],"place-items":[{"place-items":[...I(),"baseline"]}],"place-self":[{"place-self":["auto",...I()]}],p:[{p:S()}],px:[{px:S()}],py:[{py:S()}],ps:[{ps:S()}],pe:[{pe:S()}],pt:[{pt:S()}],pr:[{pr:S()}],pb:[{pb:S()}],pl:[{pl:S()}],m:[{m:tt()}],mx:[{mx:tt()}],my:[{my:tt()}],ms:[{ms:tt()}],me:[{me:tt()}],mt:[{mt:tt()}],mr:[{mr:tt()}],mb:[{mb:tt()}],ml:[{ml:tt()}],"space-x":[{"space-x":S()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":S()}],"space-y-reverse":["space-y-reverse"],size:[{size:te()}],w:[{w:[a,"screen",...te()]}],"min-w":[{"min-w":[a,"screen","none",...te()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[o]},...te()]}],h:[{h:["screen","lh",...te()]}],"min-h":[{"min-h":["screen","lh","none",...te()]}],"max-h":[{"max-h":["screen","lh",...te()]}],"font-size":[{text:["base",i,H,N]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,K,$]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",j,W]}],"font-family":[{font:[Y,W,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,K,W]}],"line-clamp":[{"line-clamp":[C,"none",K,$]}],leading:[{leading:[s,...S()]}],"list-image":[{"list-image":["none",K,W]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",K,W]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:ti()}],"text-color":[{text:ti()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...tu(),"wavy"]}],"text-decoration-thickness":[{decoration:[C,"from-font","auto",K,N]}],"text-decoration-color":[{decoration:ti()}],"underline-offset":[{"underline-offset":[C,"auto",K,W]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:S()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",K,W]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",K,W]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:tr()}],"bg-repeat":[{bg:tn()}],"bg-size":[{bg:ts()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},D,K,W],radial:["",K,W],conic:[D,K,W]},Z,q]}],"bg-color":[{bg:ti()}],"gradient-from-pos":[{from:to()}],"gradient-via-pos":[{via:to()}],"gradient-to-pos":[{to:to()}],"gradient-from":[{from:ti()}],"gradient-via":[{via:ti()}],"gradient-to":[{to:ti()}],rounded:[{rounded:ta()}],"rounded-s":[{"rounded-s":ta()}],"rounded-e":[{"rounded-e":ta()}],"rounded-t":[{"rounded-t":ta()}],"rounded-r":[{"rounded-r":ta()}],"rounded-b":[{"rounded-b":ta()}],"rounded-l":[{"rounded-l":ta()}],"rounded-ss":[{"rounded-ss":ta()}],"rounded-se":[{"rounded-se":ta()}],"rounded-ee":[{"rounded-ee":ta()}],"rounded-es":[{"rounded-es":ta()}],"rounded-tl":[{"rounded-tl":ta()}],"rounded-tr":[{"rounded-tr":ta()}],"rounded-br":[{"rounded-br":ta()}],"rounded-bl":[{"rounded-bl":ta()}],"border-w":[{border:tl()}],"border-w-x":[{"border-x":tl()}],"border-w-y":[{"border-y":tl()}],"border-w-s":[{"border-s":tl()}],"border-w-e":[{"border-e":tl()}],"border-w-t":[{"border-t":tl()}],"border-w-r":[{"border-r":tl()}],"border-w-b":[{"border-b":tl()}],"border-w-l":[{"border-l":tl()}],"divide-x":[{"divide-x":tl()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":tl()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...tu(),"hidden","none"]}],"divide-style":[{divide:[...tu(),"hidden","none"]}],"border-color":[{border:ti()}],"border-color-x":[{"border-x":ti()}],"border-color-y":[{"border-y":ti()}],"border-color-s":[{"border-s":ti()}],"border-color-e":[{"border-e":ti()}],"border-color-t":[{"border-t":ti()}],"border-color-r":[{"border-r":ti()}],"border-color-b":[{"border-b":ti()}],"border-color-l":[{"border-l":ti()}],"divide-color":[{divide:ti()}],"outline-style":[{outline:[...tu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[C,K,W]}],"outline-w":[{outline:["",C,H,N]}],"outline-color":[{outline:ti()}],shadow:[{shadow:["","none",h,J,X]}],"shadow-color":[{shadow:ti()}],"inset-shadow":[{"inset-shadow":["none",d,J,X]}],"inset-shadow-color":[{"inset-shadow":ti()}],"ring-w":[{ring:tl()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:ti()}],"ring-offset-w":[{"ring-offset":[C,N]}],"ring-offset-color":[{"ring-offset":ti()}],"inset-ring-w":[{"inset-ring":tl()}],"inset-ring-color":[{"inset-ring":ti()}],"text-shadow":[{"text-shadow":["none",c,J,X]}],"text-shadow-color":[{"text-shadow":ti()}],opacity:[{opacity:[C,K,W]}],"mix-blend":[{"mix-blend":[...th(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":th()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[C]}],"mask-image-linear-from-pos":[{"mask-linear-from":td()}],"mask-image-linear-to-pos":[{"mask-linear-to":td()}],"mask-image-linear-from-color":[{"mask-linear-from":ti()}],"mask-image-linear-to-color":[{"mask-linear-to":ti()}],"mask-image-t-from-pos":[{"mask-t-from":td()}],"mask-image-t-to-pos":[{"mask-t-to":td()}],"mask-image-t-from-color":[{"mask-t-from":ti()}],"mask-image-t-to-color":[{"mask-t-to":ti()}],"mask-image-r-from-pos":[{"mask-r-from":td()}],"mask-image-r-to-pos":[{"mask-r-to":td()}],"mask-image-r-from-color":[{"mask-r-from":ti()}],"mask-image-r-to-color":[{"mask-r-to":ti()}],"mask-image-b-from-pos":[{"mask-b-from":td()}],"mask-image-b-to-pos":[{"mask-b-to":td()}],"mask-image-b-from-color":[{"mask-b-from":ti()}],"mask-image-b-to-color":[{"mask-b-to":ti()}],"mask-image-l-from-pos":[{"mask-l-from":td()}],"mask-image-l-to-pos":[{"mask-l-to":td()}],"mask-image-l-from-color":[{"mask-l-from":ti()}],"mask-image-l-to-color":[{"mask-l-to":ti()}],"mask-image-x-from-pos":[{"mask-x-from":td()}],"mask-image-x-to-pos":[{"mask-x-to":td()}],"mask-image-x-from-color":[{"mask-x-from":ti()}],"mask-image-x-to-color":[{"mask-x-to":ti()}],"mask-image-y-from-pos":[{"mask-y-from":td()}],"mask-image-y-to-pos":[{"mask-y-to":td()}],"mask-image-y-from-color":[{"mask-y-from":ti()}],"mask-image-y-to-color":[{"mask-y-to":ti()}],"mask-image-radial":[{"mask-radial":[K,W]}],"mask-image-radial-from-pos":[{"mask-radial-from":td()}],"mask-image-radial-to-pos":[{"mask-radial-to":td()}],"mask-image-radial-from-color":[{"mask-radial-from":ti()}],"mask-image-radial-to-color":[{"mask-radial-to":ti()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":w()}],"mask-image-conic-pos":[{"mask-conic":[C]}],"mask-image-conic-from-pos":[{"mask-conic-from":td()}],"mask-image-conic-to-pos":[{"mask-conic-to":td()}],"mask-image-conic-from-color":[{"mask-conic-from":ti()}],"mask-image-conic-to-color":[{"mask-conic-to":ti()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:tr()}],"mask-repeat":[{mask:tn()}],"mask-size":[{mask:ts()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",K,W]}],filter:[{filter:["","none",K,W]}],blur:[{blur:tc()}],brightness:[{brightness:[C,K,W]}],contrast:[{contrast:[C,K,W]}],"drop-shadow":[{"drop-shadow":["","none",p,J,X]}],"drop-shadow-color":[{"drop-shadow":ti()}],grayscale:[{grayscale:["",C,K,W]}],"hue-rotate":[{"hue-rotate":[C,K,W]}],invert:[{invert:["",C,K,W]}],saturate:[{saturate:[C,K,W]}],sepia:[{sepia:["",C,K,W]}],"backdrop-filter":[{"backdrop-filter":["","none",K,W]}],"backdrop-blur":[{"backdrop-blur":tc()}],"backdrop-brightness":[{"backdrop-brightness":[C,K,W]}],"backdrop-contrast":[{"backdrop-contrast":[C,K,W]}],"backdrop-grayscale":[{"backdrop-grayscale":["",C,K,W]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[C,K,W]}],"backdrop-invert":[{"backdrop-invert":["",C,K,W]}],"backdrop-opacity":[{"backdrop-opacity":[C,K,W]}],"backdrop-saturate":[{"backdrop-saturate":[C,K,W]}],"backdrop-sepia":[{"backdrop-sepia":["",C,K,W]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":S()}],"border-spacing-x":[{"border-spacing-x":S()}],"border-spacing-y":[{"border-spacing-y":S()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",K,W]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[C,"initial",K,W]}],ease:[{ease:["linear","initial",v,K,W]}],delay:[{delay:[C,K,W]}],animate:[{animate:["none",y,K,W]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,K,W]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:tp()}],"rotate-x":[{"rotate-x":tp()}],"rotate-y":[{"rotate-y":tp()}],"rotate-z":[{"rotate-z":tp()}],scale:[{scale:tm()}],"scale-x":[{"scale-x":tm()}],"scale-y":[{"scale-y":tm()}],"scale-z":[{"scale-z":tm()}],"scale-3d":["scale-3d"],skew:[{skew:tf()}],"skew-x":[{"skew-x":tf()}],"skew-y":[{"skew-y":tf()}],transform:[{transform:[K,W,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:tg()}],"translate-x":[{"translate-x":tg()}],"translate-y":[{"translate-y":tg()}],"translate-z":[{"translate-z":tg()}],"translate-none":["translate-none"],accent:[{accent:ti()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:ti()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",K,W]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":S()}],"scroll-mx":[{"scroll-mx":S()}],"scroll-my":[{"scroll-my":S()}],"scroll-ms":[{"scroll-ms":S()}],"scroll-me":[{"scroll-me":S()}],"scroll-mt":[{"scroll-mt":S()}],"scroll-mr":[{"scroll-mr":S()}],"scroll-mb":[{"scroll-mb":S()}],"scroll-ml":[{"scroll-ml":S()}],"scroll-p":[{"scroll-p":S()}],"scroll-px":[{"scroll-px":S()}],"scroll-py":[{"scroll-py":S()}],"scroll-ps":[{"scroll-ps":S()}],"scroll-pe":[{"scroll-pe":S()}],"scroll-pt":[{"scroll-pt":S()}],"scroll-pr":[{"scroll-pr":S()}],"scroll-pb":[{"scroll-pb":S()}],"scroll-pl":[{"scroll-pl":S()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",K,W]}],fill:[{fill:["none",...ti()]}],"stroke-w":[{stroke:[C,H,N,$]}],stroke:[{stroke:["none",...ti()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9782:(t,e,i)=>{i.d(e,{x:()=>n});var r=i(6983);function n(t){return(0,r.G)(t)&&"ownerSVGElement"in t}},9827:(t,e,i)=>{i.d(e,{l:()=>r});let r=t=>t}}]);