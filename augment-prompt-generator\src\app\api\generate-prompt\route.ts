import { NextRequest, NextResponse } from 'next/server';
import { ProjectInput } from '@/lib/types';

const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

export async function POST(request: NextRequest) {
  try {
    if (!OPENROUTER_API_KEY) {
      return NextResponse.json(
        { error: 'OpenRouter API key not configured' },
        { status: 500 }
      );
    }

    const projectInput: ProjectInput = await request.json();

    // Create the system prompt for DeepSeek V3
    const systemPrompt = `You are an expert prompt engineer specializing in creating optimized prompts for Augment Agent - the world's most advanced coding AI assistant with codebase context engine and task management capabilities.

Your task is to generate a comprehensive, professional prompt that:
1. Leverages Augment Agent's unique capabilities (codebase context, task management, code analysis)
2. Follows software development best practices
3. Includes step-by-step implementation approach
4. Covers the entire development lifecycle
5. Is specific to the project type and technology stack
6. Includes testing and deployment considerations

Generate a detailed, actionable prompt that will help developers build high-quality software with Augment Agent's assistance.`;

    // Create the user prompt with project details
    const userPrompt = `Generate an optimized prompt for Augment Agent to build the following project:

**Project Details:**
- Name: ${projectInput.projectName}
- Type: ${projectInput.projectType}
- Platform: ${projectInput.platform}
- Complexity: ${projectInput.complexity}
- Concept: ${projectInput.projectIdea}
- Technologies: ${projectInput.technologies.map(t => t.name).join(', ') || 'Not specified - suggest appropriate stack'}
- Features: ${projectInput.features?.join(', ') || 'Core functionality as described'}
- Additional Requirements: ${projectInput.additionalRequirements || 'None specified'}

**Requirements for the generated prompt:**
1. Start with a clear project description and goals
2. Include specific technical requirements and architecture considerations
3. Provide a detailed step-by-step implementation plan
4. Include Augment Agent specific instructions:
   - Use codebase-retrieval tool before making changes
   - Break down work into manageable tasks
   - Request code reviews and optimizations
   - Follow best practices for the chosen technology stack
5. Include comprehensive testing strategy
6. Cover deployment and DevOps considerations
7. Be professional, detailed, and actionable

Generate a prompt that will result in a high-quality, production-ready application when used with Augment Agent.`;

    // Call OpenRouter API with DeepSeek V3
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://augment-prompt-generator.vercel.app',
        'X-Title': 'Augment Prompt Generator'
      },
      body: JSON.stringify({
        model: 'deepseek/deepseek-chat-v3-0324:free',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: userPrompt
          }
        ],
        temperature: 0.7,
        max_tokens: 4000,
        top_p: 0.9,
        frequency_penalty: 0,
        presence_penalty: 0
      })
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('OpenRouter API error:', errorData);
      return NextResponse.json(
        { error: 'Failed to generate prompt with AI' },
        { status: response.status }
      );
    }

    const data = await response.json();
    const generatedPrompt = data.choices[0]?.message?.content;

    if (!generatedPrompt) {
      return NextResponse.json(
        { error: 'No prompt generated' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      prompt: generatedPrompt,
      model: 'deepseek-chat-v3-0324',
      usage: data.usage
    });

  } catch (error) {
    console.error('Error generating prompt:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
