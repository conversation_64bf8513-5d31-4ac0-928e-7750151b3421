@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

:root {
  --background: #000000;
  --foreground: #ffffff;

  /* Enhanced Typography Scale */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;

  /* Spacing Scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;

  /* Enhanced Glass Effects */
  --glass-bg: rgba(0, 0, 0, 0.05);
  --glass-bg-hover: rgba(0, 0, 0, 0.08);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-border-hover: rgba(255, 255, 255, 0.15);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  --glass-shadow-hover: 0 12px 48px rgba(0, 0, 0, 0.18);
  --glass-backdrop: blur(16px);

  /* Animation Curves */
  --ease-out-cubic: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-in-out-cubic: cubic-bezier(0.4, 0, 0.6, 1);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-family-primary);
  --font-mono: var(--font-family-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-family-primary);
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Glass Components */
.glass-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  backdrop-filter: var(--glass-backdrop);
  transition: all 0.3s var(--ease-out-cubic);
}

.glass-card:hover {
  background: var(--glass-bg-hover);
  border-color: var(--glass-border-hover);
  box-shadow: var(--glass-shadow-hover);
}

.glass-input {
  background: rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(12px);
  transition: all 0.2s var(--ease-out-cubic);
}

.glass-input:focus {
  background: rgba(0, 0, 0, 0.05);
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.glass-button {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  transition: all 0.2s var(--ease-out-cubic);
}

.glass-button:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.glass-button:active {
  transform: translateY(0);
}

/* Smooth Animations */
.animate-fade-in {
  animation: fadeIn 0.3s var(--ease-out-cubic);
}

.animate-slide-up {
  animation: slideUp 0.3s var(--ease-out-cubic);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Text Truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Focus Styles for Accessibility */
.focus-visible:focus-visible {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Enhanced Select Dropdown Styling */
select {
  color-scheme: dark;
}

select option {
  background-color: #1a1a1a;
  color: #ffffff;
  padding: 8px 12px;
  border: none;
}

select option:hover,
select option:focus {
  background-color: #2a2a2a;
  color: #ffffff;
}

select option:checked {
  background-color: #3a3a3a;
  color: #ffffff;
  font-weight: 600;
}

/* Loading Animation */
.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
  0%, 20% {
    color: rgba(0,0,0,0);
    text-shadow:
      .25em 0 0 rgba(0,0,0,0),
      .5em 0 0 rgba(0,0,0,0);
  }
  40% {
    color: white;
    text-shadow:
      .25em 0 0 rgba(0,0,0,0),
      .5em 0 0 rgba(0,0,0,0);
  }
  60% {
    text-shadow:
      .25em 0 0 white,
      .5em 0 0 rgba(0,0,0,0);
  }
  80%, 100% {
    text-shadow:
      .25em 0 0 white,
      .5em 0 0 white;
  }
}

/* Typewriter Cursor Animation */
.animate-cursor {
  animation: cursor 1s infinite;
}

@keyframes cursor {
  0%, 50% {
    border-color: white;
  }
  51%, 100% {
    border-color: transparent;
  }
}
