(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{4843:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});var a=n(5155),i=n(5028);let s=e=>{let{children:t}=e;return(0,a.jsx)(a.<PERSON>ag<PERSON>,{children:t})},r=(0,i.default)(()=>Promise.resolve(s),{ssr:!1,loading:()=>(0,a.jsx)("div",{className:"min-h-screen bg-black py-8 px-4",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("header",{className:"text-center mb-8",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-white mb-2",children:"\uD83E\uDD16 AI Prompt Generator for Augment Agent"}),(0,a.jsx)("p",{className:"text-lg text-gray-300",children:"Loading AI-powered prompt generation..."})]}),(0,a.jsx)("div",{className:"flex justify-center items-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white"})})]})})})},5782:(e,t,n)=>{"use strict";n.d(t,{default:()=>P});var a=n(5155),i=n(2115);let s={frontend:[{category:"frontend",name:"React",description:"Popular JavaScript library for building user interfaces"},{category:"frontend",name:"Next.js",description:"React framework with SSR and static generation"},{category:"frontend",name:"Vue.js",description:"Progressive JavaScript framework"},{category:"frontend",name:"Angular",description:"TypeScript-based web application framework"},{category:"frontend",name:"Svelte",description:"Compile-time optimized framework"},{category:"frontend",name:"Vanilla JavaScript",description:"Pure JavaScript without frameworks"},{category:"frontend",name:"TypeScript",description:"Typed superset of JavaScript"}],backend:[{category:"backend",name:"Node.js",description:"JavaScript runtime for server-side development"},{category:"backend",name:"Express.js",description:"Fast, unopinionated web framework for Node.js"},{category:"backend",name:"Python/Django",description:"High-level Python web framework"},{category:"backend",name:"Python/Flask",description:"Lightweight Python web framework"},{category:"backend",name:"Python/FastAPI",description:"Modern, fast Python API framework"},{category:"backend",name:"Go",description:"Efficient, compiled programming language"},{category:"backend",name:"Rust",description:"Systems programming language focused on safety"},{category:"backend",name:"Java/Spring Boot",description:"Enterprise Java framework"},{category:"backend",name:"C#/.NET",description:"Microsoft's cross-platform framework"}],database:[{category:"database",name:"PostgreSQL",description:"Advanced open-source relational database"},{category:"database",name:"MongoDB",description:"NoSQL document database"},{category:"database",name:"SQLite",description:"Lightweight embedded database"},{category:"database",name:"MySQL",description:"Popular open-source relational database"},{category:"database",name:"Redis",description:"In-memory data structure store"},{category:"database",name:"Supabase",description:"Open-source Firebase alternative"},{category:"database",name:"Firebase",description:"Google's mobile and web development platform"}],styling:[{category:"styling",name:"Tailwind CSS",description:"Utility-first CSS framework"},{category:"styling",name:"CSS Modules",description:"Localized CSS"},{category:"styling",name:"Styled Components",description:"CSS-in-JS library"},{category:"styling",name:"SCSS/Sass",description:"CSS preprocessor"},{category:"styling",name:"Material-UI",description:"React components implementing Material Design"},{category:"styling",name:"Chakra UI",description:"Simple, modular and accessible component library"}],"mobile-framework":[{category:"mobile-framework",name:"React Native",description:"Cross-platform mobile development with React"},{category:"mobile-framework",name:"Flutter",description:"Google's UI toolkit for mobile, web, and desktop"},{category:"mobile-framework",name:"Swift",description:"Native iOS development language"},{category:"mobile-framework",name:"Kotlin",description:"Modern programming language for Android"},{category:"mobile-framework",name:"Expo",description:"Platform for universal React applications"}],"desktop-framework":[{category:"desktop-framework",name:"Electron",description:"Build desktop apps with web technologies"},{category:"desktop-framework",name:"Tauri",description:"Rust-based desktop app framework"},{category:"desktop-framework",name:"Qt",description:"Cross-platform application framework"},{category:"desktop-framework",name:"WPF",description:"Windows Presentation Foundation"},{category:"desktop-framework",name:"JavaFX",description:"Java platform for desktop applications"}],deployment:[{category:"deployment",name:"Vercel",description:"Platform for frontend frameworks and static sites"},{category:"deployment",name:"Netlify",description:"Platform for modern web projects"},{category:"deployment",name:"AWS",description:"Amazon Web Services cloud platform"},{category:"deployment",name:"Google Cloud",description:"Google's cloud computing services"},{category:"deployment",name:"Docker",description:"Containerization platform"},{category:"deployment",name:"Kubernetes",description:"Container orchestration platform"}],testing:[{category:"testing",name:"Jest",description:"JavaScript testing framework"},{category:"testing",name:"Vitest",description:"Fast unit test framework"},{category:"testing",name:"Cypress",description:"End-to-end testing framework"},{category:"testing",name:"Playwright",description:"Cross-browser automation library"},{category:"testing",name:"React Testing Library",description:"Testing utilities for React components"}],authentication:[{category:"authentication",name:"Auth0",description:"Identity platform for developers"},{category:"authentication",name:"Firebase Auth",description:"Google's authentication service"},{category:"authentication",name:"NextAuth.js",description:"Authentication for Next.js"},{category:"authentication",name:"Supabase Auth",description:"Open-source authentication"},{category:"authentication",name:"JWT",description:"JSON Web Tokens for stateless authentication"}],"state-management":[{category:"state-management",name:"Redux Toolkit",description:"Modern Redux with simplified API"},{category:"state-management",name:"Zustand",description:"Lightweight state management solution"},{category:"state-management",name:"Recoil",description:"Experimental state management for React"},{category:"state-management",name:"MobX",description:"Reactive state management through observables"},{category:"state-management",name:"Context API",description:"React's built-in state management"},{category:"state-management",name:"Valtio",description:"Proxy-based state management"}],"api-tools":[{category:"api-tools",name:"GraphQL",description:"Query language for APIs"},{category:"api-tools",name:"Apollo Client",description:"Comprehensive GraphQL client"},{category:"api-tools",name:"React Query",description:"Data fetching and caching library"},{category:"api-tools",name:"SWR",description:"Data fetching with caching and revalidation"},{category:"api-tools",name:"Axios",description:"Promise-based HTTP client"},{category:"api-tools",name:"Fetch API",description:"Native browser API for HTTP requests"},{category:"api-tools",name:"tRPC",description:"End-to-end typesafe APIs"}],monitoring:[{category:"monitoring",name:"Sentry",description:"Error tracking and performance monitoring"},{category:"monitoring",name:"LogRocket",description:"Session replay and logging"},{category:"monitoring",name:"New Relic",description:"Application performance monitoring"},{category:"monitoring",name:"Datadog",description:"Infrastructure and application monitoring"},{category:"monitoring",name:"Prometheus",description:"Open-source monitoring and alerting"},{category:"monitoring",name:"Grafana",description:"Analytics and monitoring dashboards"}],"ci-cd":[{category:"ci-cd",name:"GitHub Actions",description:"CI/CD platform integrated with GitHub"},{category:"ci-cd",name:"GitLab CI",description:"Built-in CI/CD for GitLab"},{category:"ci-cd",name:"Jenkins",description:"Open-source automation server"},{category:"ci-cd",name:"CircleCI",description:"Cloud-based CI/CD platform"},{category:"ci-cd",name:"Azure DevOps",description:"Microsoft's DevOps platform"},{category:"ci-cd",name:"Travis CI",description:"Hosted CI service for GitHub projects"}],"version-control":[{category:"version-control",name:"Git",description:"Distributed version control system"},{category:"version-control",name:"GitHub",description:"Git hosting with collaboration features"},{category:"version-control",name:"GitLab",description:"DevOps platform with Git repository"},{category:"version-control",name:"Bitbucket",description:"Git solution for teams"},{category:"version-control",name:"Azure Repos",description:"Git repositories in Azure DevOps"}],"package-managers":[{category:"package-managers",name:"npm",description:"Node.js package manager"},{category:"package-managers",name:"Yarn",description:"Fast, reliable package manager"},{category:"package-managers",name:"pnpm",description:"Efficient package manager with shared dependencies"},{category:"package-managers",name:"Bun",description:"Fast all-in-one JavaScript runtime and package manager"}],"build-tools":[{category:"build-tools",name:"Webpack",description:"Module bundler for JavaScript applications"},{category:"build-tools",name:"Vite",description:"Fast build tool for modern web projects"},{category:"build-tools",name:"Rollup",description:"Module bundler for JavaScript libraries"},{category:"build-tools",name:"Parcel",description:"Zero-configuration build tool"},{category:"build-tools",name:"esbuild",description:"Extremely fast JavaScript bundler"},{category:"build-tools",name:"Turbopack",description:"Incremental bundler optimized for JavaScript and TypeScript"}],"code-quality":[{category:"code-quality",name:"ESLint",description:"JavaScript linting utility"},{category:"code-quality",name:"Prettier",description:"Code formatter"},{category:"code-quality",name:"Husky",description:"Git hooks for code quality"},{category:"code-quality",name:"lint-staged",description:"Run linters on staged files"},{category:"code-quality",name:"SonarQube",description:"Code quality and security analysis"},{category:"code-quality",name:"CodeClimate",description:"Automated code review and quality analytics"}],documentation:[{category:"documentation",name:"Storybook",description:"Tool for building UI components in isolation"},{category:"documentation",name:"Docusaurus",description:"Documentation website generator"},{category:"documentation",name:"GitBook",description:"Documentation platform"},{category:"documentation",name:"Notion",description:"All-in-one workspace for documentation"},{category:"documentation",name:"Confluence",description:"Team collaboration and documentation"},{category:"documentation",name:"JSDoc",description:"API documentation generator for JavaScript"}]},r={"deepseek-chat":{id:"deepseek/deepseek-chat-v3-0324:free",name:"DeepSeek V3",provider:"DeepSeek",description:"Advanced reasoning and code generation with free access",maxTokens:8192,costPer1kTokens:0,strengths:["Code generation","Technical documentation","Complex reasoning","Mathematical thinking"],bestFor:["Development projects","Technical specifications","API documentation","Algorithm design"]},"claude-sonnet":{id:"anthropic/claude-3-5-sonnet",name:"Claude Sonnet 4",provider:"Anthropic",description:"Excellent for creative and analytical tasks with structured thinking",maxTokens:8192,costPer1kTokens:3,strengths:["Creative writing","Analysis","Structured thinking","User experience design"],bestFor:["Content creation","Business planning","User experience","Complex project planning"]},"gpt-4o":{id:"openai/gpt-4o",name:"ChatGPT (GPT-4o)",provider:"OpenAI",description:"Versatile multimodal AI with strong general capabilities",maxTokens:8192,costPer1kTokens:5,strengths:["General intelligence","Multimodal","Versatile","Creative solutions"],bestFor:["General projects","Multimodal apps","Versatile solutions","Creative development"]},"gemini-pro":{id:"google/gemini-pro",name:"Google Gemini Pro",provider:"Google",description:"Large context window and multimodal capabilities",maxTokens:32768,costPer1kTokens:2.5,strengths:["Large context","Multimodal","Data analysis","Research synthesis"],bestFor:["Large documents","Data projects","Research tools","Complex analysis"]},"cursor-small":{id:"cursor/cursor-small",name:"Cursor AI",provider:"Cursor",description:"IDE-integrated AI assistant for development workflows",maxTokens:4096,costPer1kTokens:1,strengths:["IDE integration","Code completion","Workflow optimization","Real-time assistance"],bestFor:["IDE workflows","Code completion","Development assistance","Project setup"]},"perplexity-small":{id:"perplexity/llama-3.1-sonar-small-128k-online",name:"Perplexity AI",provider:"Perplexity",description:"Research-focused AI with real-time web access",maxTokens:8192,costPer1kTokens:1.5,strengths:["Research","Real-time data","Information synthesis","Fact-checking"],bestFor:["Research projects","Data gathering","Market analysis","Technical research"]},"cohere-command":{id:"cohere/command-r-plus",name:"Cohere Command R+",provider:"Cohere",description:"Enterprise-focused AI with strong reasoning capabilities",maxTokens:8192,costPer1kTokens:2,strengths:["Enterprise solutions","Business logic","Structured output","Professional writing"],bestFor:["Enterprise projects","Business applications","Professional documentation","Workflow automation"]},"huggingface-mixtral":{id:"mistralai/mixtral-8x7b-instruct",name:"Mixtral 8x7B (HuggingFace)",provider:"Hugging Face",description:"Open-source mixture of experts model",maxTokens:8192,costPer1kTokens:.5,strengths:["Open source","Cost effective","Multilingual","Code generation"],bestFor:["Open source projects","Cost-sensitive applications","Multilingual support","Experimentation"]}};class o{async generatePrompt(e,t){if(!this.apiKey)return this.generatePromptFallback(e,t);let n=this.buildSystemPrompt(t),a=this.buildUserPrompt(e,t);try{var i,s,o;let e=await fetch("".concat(this.baseUrl,"/chat/completions"),{method:"POST",headers:{Authorization:"Bearer ".concat(this.apiKey),"Content-Type":"application/json","HTTP-Referer":window.location.origin,"X-Title":"AI Prompt Generator"},body:JSON.stringify({model:t.model,messages:[{role:"system",content:n},{role:"user",content:a}],temperature:.7,max_tokens:2048,top_p:.9,frequency_penalty:.1,presence_penalty:.1})});if(!e.ok)throw Error("AI API Error: ".concat(e.status," ").concat(e.statusText));let c=await e.json(),l=(null==(s=c.choices[0])||null==(i=s.message)?void 0:i.content)||"",d=(null==(o=c.usage)?void 0:o.total_tokens)||0,m=r[t.model]||r["deepseek-chat"],p=d/1e3*m.costPer1kTokens;return{prompt:l,metadata:{model:t.model,tokensUsed:d,cost:p,optimizationLevel:t.optimizationLevel,timestamp:new Date().toISOString()}}}catch(n){return console.error("AI Service Error:",n),this.generatePromptFallback(e,t)}}async generatePromptFallback(e,t){try{var n;let a=await fetch("/api/generate-prompt",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok)throw Error("Failed to generate AI prompt");let i=await a.json(),s=(null==(n=i.metadata)?void 0:n.tokensUsed)||0,o=r[t.model]||r["deepseek-chat"],c=s/1e3*o.costPer1kTokens;return{prompt:i.prompt,metadata:{model:t.model,tokensUsed:s,cost:c,optimizationLevel:t.optimizationLevel,timestamp:new Date().toISOString()}}}catch(t){console.error("Fallback API Error:",t);let e=t instanceof Error?t.message:"Unknown error occurred";throw Error("Failed to generate prompt: ".concat(e))}}buildSystemPrompt(e){let t=this.getAIToolName(e.model),n=this.getAICapabilities(e.model),a="You are an expert AI prompt engineer specializing in creating optimized prompts for ".concat(t,", a powerful AI coding assistant.\n\nYour task is to transform user project descriptions into highly effective, detailed prompts that leverage ").concat(t,"'s capabilities:\n").concat(n.map(e=>"- ".concat(e)).join("\n"));return"".concat(a,"\n\nOPTIMIZATION LEVEL: ").concat(e.optimizationLevel.toUpperCase(),"\n").concat({basic:"Create a clear, structured prompt with essential project details.",enhanced:"Create a comprehensive prompt with detailed specifications, best practices, and implementation guidance.",expert:"Create an expert-level prompt with advanced architectural considerations, performance optimization, security requirements, and scalability planning."}[e.optimizationLevel],"\n\nTARGET AUDIENCE: ").concat(e.targetAudience.toUpperCase(),"\n").concat({developer:"Focus on technical implementation details, code architecture, and development best practices.",business:"Emphasize business value, user experience, and project outcomes.",technical:"Include technical specifications, system requirements, and integration details.",general:"Balance technical and business considerations for a general audience."}[e.targetAudience],"\n\n").concat(e.includeExamples?"Include relevant examples and code snippets where appropriate.":"","\n").concat(e.includeConstraints?"Specify technical constraints, limitations, and requirements.":"","\n").concat(e.includeMetrics?"Include success metrics, KPIs, and measurable outcomes.":"","\n\nGenerate a prompt that will help ").concat(this.getAIToolName(e.model)," create exceptional results for this project.")}getAIToolName(e){return e.includes("deepseek")?"DeepSeek AI":e.includes("claude")?"Claude Sonnet 4":e.includes("gpt")||e.includes("openai")?"ChatGPT":e.includes("gemini")?"Google Gemini":e.includes("cursor")?"Cursor AI":e.includes("perplexity")?"Perplexity AI":e.includes("cohere")?"Cohere Command":"AI Assistant"}getAICapabilities(e){let t=["Advanced code generation and analysis","Multi-language programming support","Best practices and design patterns","Code optimization and refactoring"];return e.includes("deepseek")?[...t,"Deep reasoning and problem-solving","Mathematical and algorithmic thinking","Technical documentation generation"]:e.includes("claude")?[...t,"Structured thinking and analysis","Creative problem-solving approaches","Comprehensive project planning","User experience considerations"]:e.includes("gpt")||e.includes("openai")?[...t,"Versatile general intelligence","Creative and innovative solutions","Integration with development tools","Comprehensive documentation"]:e.includes("gemini")?[...t,"Large context understanding","Multimodal capabilities","Data analysis and insights","Research and information synthesis"]:e.includes("cursor")?[...t,"IDE integration and workflow optimization","Real-time code suggestions","Project-aware assistance","Development environment setup"]:t}buildUserPrompt(e,t){let n=this.getAIToolName(t.model);return"Please create an optimized prompt for ".concat(n," based on this project:\n\nPROJECT NAME: ").concat(e.projectName,"\nPROJECT TYPE: ").concat(e.projectType,"\nTARGET PLATFORM: ").concat(e.platform,"\nCOMPLEXITY: ").concat(e.complexity,"\n\nPROJECT DESCRIPTION:\n").concat(e.projectIdea,"\n\nSELECTED TECHNOLOGIES:\n").concat(e.technologies.map(e=>"- ".concat(e.name,": ").concat(e.description)).join("\n")||"No specific technologies selected","\n\nADDITIONAL REQUIREMENTS:\n").concat(e.additionalRequirements||"None specified","\n\nPlease generate a comprehensive, actionable prompt that will help ").concat(n," deliver exceptional results for this ").concat(e.projectType," project.")}getModelRecommendation(e){let{projectType:t,complexity:n,technologies:a}=e;return t.includes("machine-learning")||t.includes("data-analysis")?"deepseek-chat":"advanced"===n||a.length>5?"gemini-pro":t.includes("web-application")||t.includes("mobile")?"claude-sonnet":"desktop-application"===t?"cursor-small":"api-backend"===t?"gpt-4o":t.includes("research")||e.projectIdea.toLowerCase().includes("research")?"perplexity-small":e.projectIdea.toLowerCase().includes("enterprise")?"cohere-command":"deepseek-chat"}estimateCost(e,t){let n=Object.values(r).find(e=>e.id===t);return n?e/1e3*n.costPer1kTokens:0}constructor(e="",t="https://openrouter.ai/api/v1"){this.apiKey=e,this.baseUrl=t}}let c=new o,l=()=>{let e=(0,i.useRef)(null),t=(0,i.useRef)(null),n=(0,i.useRef)([]),s=(0,i.useRef)({x:0,y:0}),[r,o]=(0,i.useState)({width:0,height:0}),c=(e,t)=>{let a=[],i=Math.floor(e*t/6e3);for(let n=0;n<i;n++)a.push({x:Math.random()*e,y:Math.random()*t,vx:(Math.random()-.5)*.8,vy:(Math.random()-.5)*.8,radius:3*Math.random()+.3,opacity:.9*Math.random()+.1,twinkleSpeed:.03*Math.random()+.005,twinklePhase:Math.random()*Math.PI*2});n.current=a},l=(0,i.useCallback)(()=>{if(e.current){let{innerWidth:t,innerHeight:n}=window;o({width:t,height:n}),e.current.width=t,e.current.height=n,c(t,n)}},[]),d=e=>{s.current={x:e.clientX,y:e.clientY}},m=(0,i.useCallback)(()=>{let a=e.current;if(!a)return;let i=a.getContext("2d");if(!i)return;let{width:o,height:c}=r,l=s.current;if(i.clearRect(0,0,o,c),n.current.forEach(e=>{let t=l.x-e.x,n=l.y-e.y,a=Math.sqrt(t*t+n*n);if(a<200&&a>0){let i=(200-a)/200;a<80?(e.vx-=t/a*i*.003,e.vy-=n/a*i*.003):(e.vx+=t/a*i*.0015,e.vy+=n/a*i*.0015)}e.x+=e.vx,e.y+=e.vy,e.x<0&&(e.x=o),e.x>o&&(e.x=0),e.y<0&&(e.y=c),e.y>c&&(e.y=0),e.vx*=.99,e.vy*=.99,e.twinklePhase+=e.twinkleSpeed;let s=.3*Math.sin(e.twinklePhase)+.7;if(i.beginPath(),i.arc(e.x,e.y,e.radius,0,2*Math.PI),i.fillStyle="rgba(255, 255, 255, ".concat(e.opacity*s,")"),i.fill(),e.radius>1.5&&(i.beginPath(),i.arc(e.x,e.y,3*e.radius,0,2*Math.PI),i.fillStyle="rgba(255, 255, 255, ".concat(e.opacity*s*.05,")"),i.fill(),i.beginPath(),i.arc(e.x,e.y,2*e.radius,0,2*Math.PI),i.fillStyle="rgba(255, 255, 255, ".concat(e.opacity*s*.1,")"),i.fill(),i.beginPath(),i.arc(e.x,e.y,.5*e.radius,0,2*Math.PI),i.fillStyle="rgba(255, 255, 255, ".concat(Math.min(1,e.opacity*s*1.5),")"),i.fill()),e.radius>2.5){let t=4*e.radius,n=e.opacity*s*.3;i.beginPath(),i.moveTo(e.x-t,e.y),i.lineTo(e.x+t,e.y),i.moveTo(e.x,e.y-t),i.lineTo(e.x,e.y+t),i.strokeStyle="rgba(255, 255, 255, ".concat(n,")"),i.lineWidth=.5,i.stroke()}}),n.current.forEach((e,t)=>{n.current.slice(t+1).forEach(t=>{let n=e.x-t.x,a=e.y-t.y,s=Math.sqrt(n*n+a*a);s<120&&(i.beginPath(),i.moveTo(e.x,e.y),i.lineTo(t.x,t.y),i.strokeStyle="rgba(255, 255, 255, ".concat((120-s)/120*.15,")"),i.lineWidth=.8,i.stroke())})}),l.x>0&&l.y>0&&n.current.forEach(e=>{let t=l.x-e.x,n=l.y-e.y,a=Math.sqrt(t*t+n*n);a<150&&(i.beginPath(),i.moveTo(l.x,l.y),i.lineTo(e.x,e.y),i.strokeStyle="rgba(255, 255, 255, ".concat((150-a)/150*.3,")"),i.lineWidth=1,i.stroke())}),l.x>0&&l.y>0){let e=.3*Math.sin(.005*Date.now())+.7;i.beginPath(),i.arc(l.x,l.y,60*e,0,2*Math.PI),i.strokeStyle="rgba(255, 255, 255, ".concat(.1*e,")"),i.lineWidth=2,i.stroke(),i.beginPath(),i.arc(l.x,l.y,35*e,0,2*Math.PI),i.strokeStyle="rgba(255, 255, 255, ".concat(.2*e,")"),i.lineWidth=1.5,i.stroke(),i.beginPath(),i.arc(l.x,l.y,15*e,0,2*Math.PI),i.strokeStyle="rgba(255, 255, 255, ".concat(.3*e,")"),i.lineWidth=1,i.stroke(),i.beginPath(),i.arc(l.x,l.y,3,0,2*Math.PI),i.fillStyle="rgba(255, 255, 255, ".concat(.8*e,")"),i.fill()}t.current=requestAnimationFrame(m)},[r]);return(0,i.useEffect)(()=>(l(),window.addEventListener("resize",l),window.addEventListener("mousemove",d),m(),()=>{window.removeEventListener("resize",l),window.removeEventListener("mousemove",d),t.current&&cancelAnimationFrame(t.current)}),[r.width,r.height,m,l]),(0,a.jsx)("canvas",{ref:e,className:"fixed inset-0 pointer-events-none z-0",style:{background:"\n          radial-gradient(circle at 20% 80%, rgba(30, 30, 30, 0.3) 0%, transparent 50%),\n          radial-gradient(circle at 80% 20%, rgba(40, 40, 40, 0.2) 0%, transparent 50%),\n          radial-gradient(circle at 40% 40%, rgba(20, 20, 20, 0.4) 0%, transparent 50%),\n          linear-gradient(135deg, #000000 0%, #0a0a0a 25%, #111111 50%, #0a0a0a 75%, #000000 100%)\n        "}})};class d{static getInstance(){return d.instance||(d.instance=new d),d.instance}register(e,t){this.callbacks.set(e,t)}unregister(e){this.callbacks.delete(e)}openDropdown(e){if(this.openDropdownId&&this.openDropdownId!==e){let e=this.callbacks.get(this.openDropdownId);e&&e()}this.openDropdownId=e}closeDropdown(e){this.openDropdownId===e&&(this.openDropdownId=null)}constructor(){this.openDropdownId=null,this.callbacks=new Map}}function m(e){let{value:t,onChange:n,options:s,placeholder:r="Select an option",className:o="",disabled:c=!1}=e,[l,m]=(0,i.useState)(!1),[p,g]=(0,i.useState)(-1),u=(0,i.useRef)(null),h=(0,i.useId)(),y=d.getInstance(),x=s.find(e=>e.value===t),f=(0,i.useCallback)(()=>{m(!1),y.closeDropdown(h)},[y,h]),b=()=>{y.openDropdown(h),m(!0)};return(0,i.useEffect)(()=>{y.register(h,f);let e=e=>{u.current&&!u.current.contains(e.target)&&f()};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e),y.unregister(h)}},[h,f,y]),(0,i.useEffect)(()=>{let e=e=>{if(l)switch(e.key){case"Escape":f();break;case"ArrowDown":e.preventDefault(),g(e=>e<s.length-1?e+1:0);break;case"ArrowUp":e.preventDefault(),g(e=>e>0?e-1:s.length-1);break;case"Enter":e.preventDefault(),p>=0&&(n(s[p].value),f())}};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[l,p,s,n,f]),(0,a.jsxs)("div",{ref:u,className:"relative ".concat(o),children:[(0,a.jsxs)("button",{type:"button",onClick:()=>!c&&(l?f():b()),disabled:c,className:"group w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-black text-sm text-white bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl border border-white/10 transition-all duration-300 hover:border-white/20 hover:shadow-lg text-left flex items-center justify-between ".concat(c?"opacity-50 cursor-not-allowed":"cursor-pointer hover:from-gray-700/90 hover:to-gray-800/90"," ").concat(l?"ring-2 ring-white/20 border-white/25 shadow-xl":""),style:{boxShadow:l?"0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1)":"0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:[(0,a.jsx)("span",{className:"font-medium ".concat(x?"text-white":"text-gray-400"," transition-colors duration-200"),children:x?x.label:r}),(0,a.jsx)("svg",{className:"w-5 h-5 transition-all duration-300 text-gray-300 group-hover:text-white ".concat(l?"rotate-180 text-white":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",strokeWidth:2.5,children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19 9l-7 7-7-7"})})]}),l&&(0,a.jsx)("div",{className:"absolute z-[9999] w-full mt-2 bg-gradient-to-b from-gray-900/95 to-black/95 backdrop-blur-2xl rounded-xl shadow-2xl border border-white/15 max-h-64 overflow-y-auto animate-fade-in",style:{boxShadow:"0 32px 64px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1)",backgroundColor:"rgba(0, 0, 0, 0.92)"},children:(0,a.jsx)("div",{className:"p-1",children:s.map((e,i)=>(0,a.jsxs)("button",{type:"button",onClick:()=>{n(e.value),f()},onMouseEnter:()=>g(i),className:"group w-full text-left px-4 py-3 text-sm transition-all duration-200 rounded-lg mb-1 last:mb-0 ".concat(i===p||e.value===t?"bg-gradient-to-r from-white/20 to-white/15 text-white shadow-lg border border-white/20":"text-gray-200 hover:bg-gradient-to-r hover:from-white/10 hover:to-white/5 hover:text-white border border-transparent hover:border-white/10"),style:{boxShadow:i===p||e.value===t?"0 4px 12px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)":"none"},children:[(0,a.jsx)("div",{className:"font-semibold text-sm leading-tight",children:e.label}),e.description&&(0,a.jsx)("div",{className:"text-xs text-gray-300 mt-1.5 leading-relaxed opacity-90 group-hover:opacity-100 transition-opacity duration-200",children:e.description})]},e.value))})})]})}var p=n(7650);let g=[{id:"github",name:"GitHub",description:"Create repositories and issues from prompts",category:"development",icon:"\uD83D\uDC19",baseUrl:"https://api.github.com",authType:"api_key",isEnabled:!1,config:{}},{id:"gitlab",name:"GitLab",description:"Create projects and merge requests",category:"development",icon:"\uD83E\uDD8A",baseUrl:"https://gitlab.com/api/v4",authType:"api_key",isEnabled:!1,config:{}},{id:"linear",name:"Linear",description:"Create issues and projects",category:"development",icon:"\uD83D\uDCCB",baseUrl:"https://api.linear.app/graphql",authType:"api_key",isEnabled:!1,config:{}},{id:"github-actions",name:"GitHub Actions",description:"Trigger CI/CD workflows",category:"development",icon:"⚡",baseUrl:"https://api.github.com",authType:"api_key",isEnabled:!1,config:{}},{id:"gitlab-ci",name:"GitLab CI/CD",description:"Manage CI/CD pipelines",category:"development",icon:"\uD83D\uDD04",baseUrl:"https://gitlab.com/api/v4",authType:"api_key",isEnabled:!1,config:{}},{id:"jira",name:"Jira",description:"Create tickets and manage projects",category:"productivity",icon:"\uD83C\uDFAF",baseUrl:"https://api.atlassian.com",authType:"bearer",isEnabled:!1,config:{}},{id:"asana",name:"Asana",description:"Create tasks and manage workflows",category:"productivity",icon:"\uD83D\uDCCA",baseUrl:"https://app.asana.com/api/1.0",authType:"bearer",isEnabled:!1,config:{}},{id:"monday",name:"Monday.com",description:"Create boards and manage projects",category:"productivity",icon:"\uD83D\uDCC5",baseUrl:"https://api.monday.com/v2",authType:"api_key",isEnabled:!1,config:{}},{id:"trello",name:"Trello",description:"Create cards and manage boards",category:"productivity",icon:"\uD83D\uDCCB",baseUrl:"https://api.trello.com/1",authType:"api_key",isEnabled:!1,config:{}},{id:"notion",name:"Notion",description:"Save prompts as Notion pages",category:"productivity",icon:"\uD83D\uDCDD",baseUrl:"https://api.notion.com/v1",authType:"bearer",isEnabled:!1,config:{}},{id:"slack",name:"Slack",description:"Share prompts in Slack channels",category:"communication",icon:"\uD83D\uDCAC",baseUrl:"https://slack.com/api",authType:"bearer",isEnabled:!1,config:{}},{id:"discord",name:"Discord",description:"Share prompts in Discord servers",category:"communication",icon:"\uD83C\uDFAE",baseUrl:"https://discord.com/api/v10",authType:"bearer",isEnabled:!1,config:{}},{id:"microsoft-teams",name:"Microsoft Teams",description:"Share prompts in Teams channels",category:"communication",icon:"\uD83D\uDC65",baseUrl:"https://graph.microsoft.com/v1.0",authType:"bearer",isEnabled:!1,config:{}},{id:"telegram",name:"Telegram",description:"Send prompts via Telegram bot",category:"communication",icon:"✈️",baseUrl:"https://api.telegram.org",authType:"api_key",isEnabled:!1,config:{}},{id:"aws",name:"Amazon Web Services",description:"Deploy to AWS services",category:"cloud",icon:"☁️",baseUrl:"https://aws.amazon.com",authType:"api_key",isEnabled:!1,config:{}},{id:"google-cloud",name:"Google Cloud Platform",description:"Deploy to GCP services",category:"cloud",icon:"\uD83C\uDF29️",baseUrl:"https://cloud.google.com",authType:"oauth",isEnabled:!1,config:{}},{id:"azure",name:"Microsoft Azure",description:"Deploy to Azure services",category:"cloud",icon:"\uD83D\uDD37",baseUrl:"https://management.azure.com",authType:"bearer",isEnabled:!1,config:{}},{id:"vercel",name:"Vercel",description:"Deploy web applications",category:"cloud",icon:"▲",baseUrl:"https://api.vercel.com",authType:"bearer",isEnabled:!1,config:{}},{id:"netlify",name:"Netlify",description:"Deploy static sites",category:"cloud",icon:"\uD83C\uDF10",baseUrl:"https://api.netlify.com/api/v1",authType:"bearer",isEnabled:!1,config:{}},{id:"figma",name:"Figma",description:"Create design briefs from prompts",category:"design",icon:"\uD83C\uDFA8",baseUrl:"https://api.figma.com/v1",authType:"api_key",isEnabled:!1,config:{}},{id:"sketch",name:"Sketch",description:"Create design documents",category:"design",icon:"\uD83D\uDC8E",baseUrl:"https://api.sketch.com/v1",authType:"bearer",isEnabled:!1,config:{}},{id:"datadog",name:"Datadog",description:"Set up monitoring and alerts",category:"analytics",icon:"\uD83D\uDCCA",baseUrl:"https://api.datadoghq.com/api/v1",authType:"api_key",isEnabled:!1,config:{}},{id:"new-relic",name:"New Relic",description:"Application performance monitoring",category:"analytics",icon:"\uD83D\uDCC8",baseUrl:"https://api.newrelic.com/v2",authType:"api_key",isEnabled:!1,config:{}},{id:"sentry",name:"Sentry",description:"Error tracking and monitoring",category:"analytics",icon:"\uD83D\uDEA8",baseUrl:"https://sentry.io/api/0",authType:"bearer",isEnabled:!1,config:{}},{id:"google-analytics",name:"Google Analytics",description:"Web analytics and reporting",category:"analytics",icon:"\uD83D\uDCCA",baseUrl:"https://analyticsreporting.googleapis.com/v4",authType:"oauth",isEnabled:!1,config:{}},{id:"openai",name:"OpenAI",description:"Send prompts to ChatGPT and GPT models",category:"ai",icon:"\uD83E\uDD16",baseUrl:"https://api.openai.com/v1",authType:"bearer",isEnabled:!1,config:{}},{id:"anthropic",name:"Anthropic Claude",description:"Send prompts to Claude Sonnet 4",category:"ai",icon:"\uD83E\uDDE0",baseUrl:"https://api.anthropic.com/v1",authType:"api_key",isEnabled:!1,config:{}},{id:"google-gemini",name:"Google Gemini",description:"Send prompts to Gemini Pro models",category:"ai",icon:"\uD83D\uDC8E",baseUrl:"https://generativelanguage.googleapis.com/v1",authType:"api_key",isEnabled:!1,config:{}},{id:"cursor",name:"Cursor IDE",description:"Send prompts to Cursor AI assistant",category:"ai",icon:"⚡",baseUrl:"https://api.cursor.sh/v1",authType:"bearer",isEnabled:!1,config:{}},{id:"deepseek",name:"DeepSeek",description:"Send prompts to DeepSeek V3 models",category:"ai",icon:"\uD83D\uDD0D",baseUrl:"https://api.deepseek.com/v1",authType:"bearer",isEnabled:!1,config:{}},{id:"perplexity",name:"Perplexity AI",description:"Send prompts to Perplexity models",category:"ai",icon:"\uD83D\uDD2E",baseUrl:"https://api.perplexity.ai",authType:"bearer",isEnabled:!1,config:{}},{id:"cohere",name:"Cohere",description:"Send prompts to Cohere Command models",category:"ai",icon:"\uD83C\uDF1F",baseUrl:"https://api.cohere.ai/v1",authType:"bearer",isEnabled:!1,config:{}},{id:"huggingface",name:"Hugging Face",description:"Access open-source AI models",category:"ai",icon:"\uD83E\uDD17",baseUrl:"https://api-inference.huggingface.co",authType:"bearer",isEnabled:!1,config:{}}];class u{loadIntegrations(){try{let e=localStorage.getItem(this.storageKey);e?this.integrations=JSON.parse(e):this.integrations=[...g]}catch(e){console.error("Failed to load integrations:",e),this.integrations=[...g]}}loadWebhooks(){try{let e=localStorage.getItem(this.webhooksKey);e&&(this.webhooks=JSON.parse(e))}catch(e){console.error("Failed to load webhooks:",e)}}saveIntegrations(){try{localStorage.setItem(this.storageKey,JSON.stringify(this.integrations))}catch(e){console.error("Failed to save integrations:",e)}}saveWebhooks(){try{localStorage.setItem(this.webhooksKey,JSON.stringify(this.webhooks))}catch(e){console.error("Failed to save webhooks:",e)}}getIntegrations(){return this.integrations}getEnabledIntegrations(){return this.integrations.filter(e=>e.isEnabled)}enableIntegration(e,t){let n=this.integrations.find(t=>t.id===e);return!!n&&(n.isEnabled=!0,n.config={...n.config,...t},this.saveIntegrations(),!0)}disableIntegration(e){let t=this.integrations.find(t=>t.id===e);return!!t&&(t.isEnabled=!1,t.config={},this.saveIntegrations(),!0)}async testIntegration(e){let t=this.integrations.find(t=>t.id===e);if(!t||!t.isEnabled)return{success:!1,error:"Integration not found or not enabled"};try{return await new Promise(e=>setTimeout(e,1e3)),({github:{success:!0,data:{user:"demo-user",repos:42}},gitlab:{success:!0,data:{user:"demo-user",projects:28}},notion:{success:!0,data:{workspace:"Demo Workspace"}},slack:{success:!0,data:{team:"Demo Team",channels:15}},figma:{success:!0,data:{teams:3,projects:12}},linear:{success:!0,data:{workspace:"Demo Workspace",issues:28}},discord:{success:!0,data:{guild:"Demo Server",channels:8}},jira:{success:!0,data:{projects:12,issues:156}},asana:{success:!0,data:{workspaces:3,tasks:89}},monday:{success:!0,data:{boards:8,items:234}},trello:{success:!0,data:{boards:15,cards:67}},"microsoft-teams":{success:!0,data:{teams:5,channels:23}},telegram:{success:!0,data:{bot:"active",chats:12}},aws:{success:!0,data:{regions:3,services:45}},"google-cloud":{success:!0,data:{projects:2,services:32}},azure:{success:!0,data:{subscriptions:1,resources:28}},vercel:{success:!0,data:{projects:8,deployments:156}},netlify:{success:!0,data:{sites:12,builds:89}},sketch:{success:!0,data:{documents:15,symbols:234}},datadog:{success:!0,data:{dashboards:8,monitors:45}},"new-relic":{success:!0,data:{applications:6,alerts:23}},sentry:{success:!0,data:{projects:4,errors:12}},"google-analytics":{success:!0,data:{properties:3,reports:67}},openai:{success:!0,data:{models:8,usage:"active"}},anthropic:{success:!0,data:{models:3,status:"connected"}},"google-gemini":{success:!0,data:{models:5,quota:"available"}},cursor:{success:!0,data:{workspace:"connected",features:12}},deepseek:{success:!0,data:{models:4,status:"active"}},perplexity:{success:!0,data:{models:3,credits:1e3}},cohere:{success:!0,data:{models:6,usage:"normal"}},huggingface:{success:!0,data:{models:50,status:"connected"}}})[e]||{success:!0,data:{status:"connected"}}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error occurred"}}}async sendToIntegration(e,t,n){arguments.length>3&&void 0!==arguments[3]&&arguments[3];let a=this.integrations.find(t=>t.id===e);if(!a||!a.isEnabled)return{success:!1,error:"Integration not found or not enabled"};try{return await new Promise(e=>setTimeout(e,1500)),({github:{success:!0,data:{repository:"".concat(t.projectName.toLowerCase().replace(/\s+/g,"-")),url:"https://github.com/demo-user/".concat(t.projectName.toLowerCase().replace(/\s+/g,"-")),action:"Repository created"}},notion:{success:!0,data:{page:t.projectName,url:"https://notion.so/demo-page",action:"Page created"}},slack:{success:!0,data:{channel:"#general",message:"Prompt shared successfully",action:"Message sent"}},figma:{success:!0,data:{file:"".concat(t.projectName," Design Brief"),url:"https://figma.com/file/demo",action:"Design file created"}},linear:{success:!0,data:{issue:"".concat(t.projectName," - Implementation"),url:"https://linear.app/demo/issue/DEMO-123",action:"Issue created"}},discord:{success:!0,data:{channel:"#development",message:"Prompt shared in Discord",action:"Message sent"}},gitlab:{success:!0,data:{project:"".concat(t.projectName.toLowerCase().replace(/\s+/g,"-")),url:"https://gitlab.com/demo-user/".concat(t.projectName.toLowerCase().replace(/\s+/g,"-")),action:"Project created"}},jira:{success:!0,data:{ticket:"".concat(t.projectName," - Epic"),url:"https://demo.atlassian.net/browse/PROJ-123",action:"Epic created"}},asana:{success:!0,data:{task:"".concat(t.projectName," - Project"),url:"https://app.asana.com/0/123456/789012",action:"Task created"}},monday:{success:!0,data:{item:"".concat(t.projectName," - Board Item"),url:"https://demo.monday.com/boards/123456",action:"Item created"}},openai:{success:!0,data:{response:"Prompt sent to ChatGPT",model:"gpt-4",action:"Prompt processed"}},anthropic:{success:!0,data:{response:"Prompt sent to Claude Sonnet 4",model:"claude-3-5-sonnet",action:"Prompt processed"}},"google-gemini":{success:!0,data:{response:"Prompt sent to Gemini Pro",model:"gemini-pro",action:"Prompt processed"}},cursor:{success:!0,data:{response:"Prompt sent to Cursor AI",workspace:"active",action:"Prompt processed"}},vercel:{success:!0,data:{deployment:"".concat(t.projectName.toLowerCase().replace(/\s+/g,"-")),url:"https://".concat(t.projectName.toLowerCase().replace(/\s+/g,"-"),".vercel.app"),action:"Deployment created"}},aws:{success:!0,data:{stack:"".concat(t.projectName,"-stack"),region:"us-east-1",action:"CloudFormation stack created"}}})[e]||{success:!0,data:{action:"Completed"}}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error occurred"}}}addWebhook(e){let t={...e,id:"webhook_"+Math.random().toString(36).substring(2,11)};return this.webhooks.push(t),this.saveWebhooks(),t}updateWebhook(e,t){let n=this.webhooks.findIndex(t=>t.id===e);return -1!==n&&(this.webhooks[n]={...this.webhooks[n],...t},this.saveWebhooks(),!0)}deleteWebhook(e){let t=this.webhooks.findIndex(t=>t.id===e);return -1!==t&&(this.webhooks.splice(t,1),this.saveWebhooks(),!0)}getWebhooks(){return this.webhooks}async triggerWebhook(e,t,n){let a=this.webhooks.find(t=>t.id===e);if(!a||!a.isActive||!a.events.includes(t))return{success:!1,error:"Webhook not found, inactive, or event not supported"};try{return await new Promise(e=>setTimeout(e,800)),{success:!0,data:{webhook:a.name,event:t,delivered:!0}}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error occurred"}}}exportIntegrationData(){return JSON.stringify({integrations:this.integrations,webhooks:this.webhooks,exportedAt:new Date},null,2)}clearData(){this.integrations=[...g],this.webhooks=[],localStorage.removeItem(this.storageKey),localStorage.removeItem(this.webhooksKey)}constructor(){this.integrations=[],this.webhooks=[],this.storageKey="promptGenerator_integrations",this.webhooksKey="promptGenerator_webhooks",this.loadIntegrations(),this.loadWebhooks()}}let h=new u;function y(e){let{isOpen:t,onClose:n,settings:s,onSettingsChange:o,recommendedModel:c,projectInput:l,generatedPrompt:d}=e,[g,u]=(0,i.useState)(s),[y,x]=(0,i.useState)("ai"),[f,b]=(0,i.useState)([]),[v,w]=(0,i.useState)(null),[j,k]=(0,i.useState)(!1),[S,A]=(0,i.useState)(!1),[N,C]=(0,i.useState)(!1),[I,P]=(0,i.useState)({}),[M,T]=(0,i.useState)({});(0,i.useEffect)(()=>{t&&"integrations"===y&&R()},[t,y]);let R=()=>{b(h.getIntegrations())},D=async e=>{w(e),k(!0),T({})},E=async()=>{v&&h.enableIntegration(v.id,M)&&(R(),k(!1),w(null),T({}))},z=e=>{h.disableIntegration(e),R()},L=async e=>{A(!0);let t=await h.testIntegration(e.id);P({...I,[e.id]:t}),A(!1)},F=async e=>{if(!l||!d)return;C(!0);let t=await h.sendToIntegration(e.id,l,d);P({...I,["".concat(e.id,"_send")]:t}),C(!1)},G=Object.entries(r).map(e=>{let[,t]=e;return{value:t.id,label:t.name,description:"".concat(t.provider," • ").concat(t.description)}});if(!t)return null;let K=Object.values(r).find(e=>e.id===g.model);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-2 sm:p-4",children:(0,a.jsxs)("div",{className:"bg-black border border-white/20 rounded-xl shadow-2xl p-4 sm:p-6 w-full max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-y-auto",style:{boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.8)"},children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4 sm:mb-6",children:[(0,a.jsx)("h3",{className:"text-lg sm:text-xl font-semibold text-white",children:"AI Settings & Integrations"}),(0,a.jsx)("button",{onClick:n,className:"bg-gray-800 text-white px-3 py-1 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm flex-shrink-0",children:"Close"})]}),(0,a.jsxs)("div",{className:"flex gap-2 sm:gap-3 mb-4 sm:mb-6 overflow-x-auto",children:[(0,a.jsx)("button",{onClick:()=>x("ai"),className:"px-3 sm:px-4 py-2 rounded-md transition-all duration-300 whitespace-nowrap text-sm sm:text-base flex-shrink-0 ".concat("ai"===y?"bg-white text-black":"bg-gray-800 text-gray-400 hover:bg-gray-700"),children:"\uD83E\uDD16 AI Settings"}),(0,a.jsx)("button",{onClick:()=>x("integrations"),className:"px-3 sm:px-4 py-2 rounded-md transition-all duration-300 whitespace-nowrap text-sm sm:text-base flex-shrink-0 ".concat("integrations"===y?"bg-white text-black":"bg-gray-800 text-gray-400 hover:bg-gray-700"),children:"\uD83D\uDD17 Integrations"})]}),"ai"===y&&(0,a.jsxs)("div",{className:"space-y-4 sm:space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-white mb-2",children:["AI Model",c&&g.model===c&&(0,a.jsx)("span",{className:"ml-2 text-xs bg-white text-black px-2 py-1 rounded",children:"Recommended"})]}),(0,a.jsx)(m,{value:g.model,onChange:e=>u(t=>({...t,model:e})),options:G,placeholder:"Select AI model"}),K&&(0,a.jsxs)("div",{className:"mt-2 p-3 bg-gray-900 border border-white/10 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm text-white font-medium mb-1",children:K.name}),(0,a.jsx)("div",{className:"text-xs text-gray-400 mb-2",children:K.description}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1 mb-2",children:K.strengths.map((e,t)=>(0,a.jsx)("span",{className:"bg-gray-700 text-gray-200 text-xs px-2 py-1 rounded",children:e},t))}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["Max tokens: ",K.maxTokens.toLocaleString()," • Cost: $",K.costPer1kTokens,"/1k tokens"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"Optimization Level"}),(0,a.jsx)(m,{value:g.optimizationLevel,onChange:e=>u(t=>({...t,optimizationLevel:e})),options:[{value:"basic",label:"Basic",description:"Clear, structured prompt with essentials"},{value:"enhanced",label:"Enhanced",description:"Comprehensive with detailed specifications"},{value:"expert",label:"Expert",description:"Advanced architectural considerations"}],placeholder:"Select optimization level"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"Target Audience"}),(0,a.jsx)(m,{value:g.targetAudience,onChange:e=>u(t=>({...t,targetAudience:e})),options:[{value:"developer",label:"Developer",description:"Technical implementation focus"},{value:"business",label:"Business",description:"Business value and outcomes"},{value:"technical",label:"Technical",description:"System requirements and integration"},{value:"general",label:"General",description:"Balanced technical and business"}],placeholder:"Select target audience"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-white mb-3",children:"Additional Options"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:g.includeExamples,onChange:e=>u(t=>({...t,includeExamples:e.target.checked})),className:"mr-3 accent-white"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-white text-sm font-medium",children:"Include Examples"}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:"Add relevant code examples and snippets"})]})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:g.includeConstraints,onChange:e=>u(t=>({...t,includeConstraints:e.target.checked})),className:"mr-3 accent-white"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-white text-sm font-medium",children:"Include Constraints"}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:"Specify technical limitations and requirements"})]})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:g.includeMetrics,onChange:e=>u(t=>({...t,includeMetrics:e.target.checked})),className:"mr-3 accent-white"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-white text-sm font-medium",children:"Include Success Metrics"}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:"Add KPIs and measurable outcomes"})]})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between pt-4 sm:pt-6 border-t border-white/10 space-y-3 sm:space-y-0",children:[(0,a.jsx)("button",{onClick:()=>{u({model:c||"deepseek/deepseek-chat-v3-0324:free",optimizationLevel:"enhanced",includeExamples:!0,includeConstraints:!0,includeMetrics:!1,targetAudience:"developer"})},className:"group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-medium text-white bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl border border-white/10 transition-all duration-300 hover:border-white/20 hover:from-gray-700/90 hover:to-gray-800/90 focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-black",style:{boxShadow:"0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:(0,a.jsxs)("span",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4 transition-transform duration-300 group-hover:rotate-180",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),(0,a.jsx)("span",{children:"Reset to Defaults"})]})}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3",children:[(0,a.jsx)("button",{onClick:n,className:"group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-medium text-gray-300 bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl border border-white/10 transition-all duration-300 hover:border-white/20 hover:from-gray-700/90 hover:to-gray-800/90 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-black",style:{boxShadow:"0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:(0,a.jsxs)("span",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),(0,a.jsx)("span",{children:"Cancel"})]})}),(0,a.jsx)("button",{onClick:()=>{o(g),n()},className:"group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-semibold text-black bg-gradient-to-br from-white to-gray-100 backdrop-blur-xl border border-white/20 transition-all duration-300 hover:from-gray-100 hover:to-gray-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-black transform hover:scale-[1.02]",style:{boxShadow:"0 6px 20px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)"},children:(0,a.jsxs)("span",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4 transition-transform duration-300 group-hover:scale-110",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,a.jsx)("span",{children:"Save Settings"})]})})]})]})]}),"integrations"===y&&(0,a.jsx)("div",{className:"space-y-4 sm:space-y-6",children:(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4",children:f.map(e=>(0,a.jsxs)("div",{className:"p-3 sm:p-4 rounded-lg border transition-all duration-300 ".concat(e.isEnabled?"bg-gray-800 border-white":"bg-gray-900 border-white/10 hover:border-white/20"),children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-2 sm:space-x-3 flex-1 min-w-0",children:[(0,a.jsx)("div",{className:"text-xl sm:text-2xl flex-shrink-0",children:e.icon}),(0,a.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,a.jsx)("h4",{className:"text-white font-medium text-sm sm:text-base truncate",children:e.name}),(0,a.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm line-clamp-2",children:e.description})]})]}),(0,a.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e.isEnabled?"bg-white":"bg-gray-500")})]}),(0,a.jsx)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2",children:e.isEnabled?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>L(e),disabled:S,className:"flex-1 bg-gray-700 text-white py-2 px-3 rounded-md hover:bg-gray-600 transition-all duration-300 text-xs sm:text-sm disabled:opacity-50",children:S?"Testing...":"Test"}),l&&d&&(0,a.jsx)("button",{onClick:()=>F(e),disabled:N,className:"flex-1 bg-gray-800 text-white py-2 px-3 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm disabled:opacity-50",children:N?"Sending...":"Send"}),(0,a.jsx)("button",{onClick:()=>z(e.id),className:"bg-gray-800 text-white py-2 px-3 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm",children:"Disable"})]}):(0,a.jsx)("button",{onClick:()=>D(e),className:"flex-1 bg-gray-800 text-white py-2 px-3 rounded-md hover:bg-gray-700 transition-all duration-300 text-xs sm:text-sm",children:"Enable"})}),I[e.id]&&(0,a.jsxs)("div",{className:"mt-3 p-2 bg-gray-800 border border-white/10 rounded text-xs",children:[(0,a.jsx)("div",{className:"font-medium ".concat(I[e.id].success?"text-white":"text-gray-300"),children:I[e.id].success?"Success":"Failed"}),(0,a.jsx)("div",{className:"text-gray-400 mt-1",children:I[e.id].error||"Connection successful"})]})]},e.id))})})]})}),j&&v&&(0,p.createPortal)((0,a.jsx)("div",{className:"fixed inset-0 bg-black/90 flex items-center justify-center p-4",style:{position:"fixed",top:0,left:0,right:0,bottom:0,zIndex:1e4,margin:0,padding:"1rem"},onClick:e=>{e.target===e.currentTarget&&k(!1)},children:(0,a.jsxs)("div",{className:"bg-black border border-white/20 rounded-xl shadow-2xl p-6 w-full max-w-md mx-auto my-auto",style:{maxHeight:"90vh",overflow:"auto",position:"relative",transform:"none"},onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4",children:["Configure ",v.name]}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"API Token"}),(0,a.jsx)("input",{type:"password",value:M.token||"",onChange:e=>T({...M,token:e.target.value}),className:"w-full px-3 py-2 bg-gray-900 border border-white/10 rounded-md text-white placeholder-gray-400 focus:border-white/30 focus:outline-none",placeholder:"Enter your API token"})]})}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 mt-4 sm:mt-6",children:[(0,a.jsx)("button",{onClick:()=>k(!1),className:"group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-medium text-gray-300 bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl border border-white/10 transition-all duration-300 hover:border-white/20 hover:from-gray-700/90 hover:to-gray-800/90 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-black",style:{boxShadow:"0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:(0,a.jsxs)("span",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),(0,a.jsx)("span",{children:"Cancel"})]})}),(0,a.jsx)("button",{onClick:E,className:"group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-semibold text-black bg-gradient-to-br from-white to-gray-100 backdrop-blur-xl border border-white/20 transition-all duration-300 hover:from-gray-100 hover:to-gray-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-black transform hover:scale-[1.02]",style:{boxShadow:"0 6px 20px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)"},children:(0,a.jsxs)("span",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4 transition-transform duration-300 group-hover:scale-110",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,a.jsx)("span",{children:"Save"})]})})]})]})}),document.body)]})}var x=n(5653),f=n(182);function b(e){let{sequences:t=[{text:"AI Prompt Generator",deleteAfter:!0},{text:"Professional Prompts",deleteAfter:!0},{text:"Augment Agent Ready",deleteAfter:!1}],typingSpeed:n=75,startDelay:s=500,autoLoop:r=!0,loopDelay:o=3e3,className:c=""}=e,[l,d]=(0,x.l)(),[m,p]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{if(m)return;let e=!0;return(async()=>{var a;let i=null==(a=l.current)?void 0:a.querySelector("[data-typewriter]");if(i)for(p(!0),l.current&&await d(l.current,{opacity:1}),i.textContent="",await new Promise(e=>setTimeout(e,s));e;){for(let a of t){if(!e)break;for(let t=0;t<a.text.length&&e;t++)i.textContent=a.text.slice(0,t+1),await new Promise(e=>setTimeout(e,n));if(a.pauseAfter&&await new Promise(e=>setTimeout(e,a.pauseAfter)),a.deleteAfter){await new Promise(e=>setTimeout(e,500));for(let t=a.text.length;t>0&&e;t--)i.textContent=a.text.slice(0,t),await new Promise(e=>setTimeout(e,n/2))}}if(!r||!e)break;await new Promise(e=>setTimeout(e,o)),e&&(i.textContent="")}})(),()=>{e=!1}},[]),(0,a.jsx)("div",{className:"relative w-full max-w-4xl mx-auto py-8 ".concat(c),children:(0,a.jsx)("div",{className:"relative text-center z-10 flex flex-col items-center justify-center",ref:l,children:(0,a.jsx)(f.P.div,{className:"text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-mono text-white tracking-tight flex items-center gap-2",initial:{opacity:0},animate:{opacity:1},children:(0,a.jsx)("span",{"data-typewriter":!0,className:"inline-block border-r-2 border-white animate-cursor pr-1",children:t[0].text})})})})}var v=n(2596),w=n(9688);function j(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,w.QP)((0,v.$)(t))}function k(e){let{text:t="Text Shimmer",className:n}=e,[s,r]=(0,i.useState)(!1);return((0,i.useEffect)(()=>{let e=navigator.userAgent.toLowerCase();r(e.includes("safari")&&!e.includes("chrome"))},[]),s)?(0,a.jsx)(f.P.span,{className:j("text-white font-medium",n),initial:{opacity:.7},animate:{opacity:[.7,1,.7]},transition:{duration:2.5,ease:"easeInOut",repeat:Number.POSITIVE_INFINITY},children:t}):(0,a.jsxs)(f.P.span,{className:j("relative font-medium text-white",n),initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},style:{background:"linear-gradient(90deg, #ffffff 0%, #d1d5db 50%, #ffffff 100%)",backgroundSize:"200% 100%",WebkitBackgroundClip:"text",backgroundClip:"text",WebkitTextFillColor:"transparent",color:"transparent"},children:[(0,a.jsx)(f.P.span,{animate:{backgroundPosition:["200% center","-200% center"]},transition:{duration:2.5,ease:"linear",repeat:Number.POSITIVE_INFINITY},style:{background:"inherit",backgroundSize:"inherit",WebkitBackgroundClip:"inherit",backgroundClip:"inherit",WebkitTextFillColor:"inherit",color:"inherit"},children:t}),(0,a.jsx)("span",{className:"absolute inset-0 text-white opacity-0",style:{opacity:+!!s,WebkitTextFillColor:"white",color:"white"},children:t})]})}var S=n(760);function A(e){let{texts:t=["Thinking...","Processing...","Analyzing...","Computing...","Almost..."],className:n,interval:s=1500}=e,[r,o]=(0,i.useState)(0);return(0,i.useEffect)(()=>{let e=setInterval(()=>{o(e=>(e+1)%t.length)},s);return()=>clearInterval(e)},[s,t.length]),(0,a.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,a.jsx)(f.P.div,{className:"relative px-4 py-2 w-full",initial:{opacity:0},animate:{opacity:1},transition:{duration:.4},children:(0,a.jsx)(S.N,{mode:"wait",children:(0,a.jsx)(f.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0,backgroundPosition:["200% center","-200% center"]},exit:{opacity:0,y:-20},transition:{opacity:{duration:.3},y:{duration:.3},backgroundPosition:{duration:2.5,ease:"linear",repeat:1/0}},className:j("flex justify-center text-3xl font-bold bg-gradient-to-r from-neutral-950 via-neutral-400 to-neutral-950 dark:from-white dark:via-neutral-600 dark:to-white bg-[length:200%_100%] bg-clip-text text-transparent whitespace-nowrap min-w-max",n),children:t[r]},r)})})})}let N=[{id:"fintech-trading-platform",name:"FinTech Trading Platform",industry:"Financial Technology",description:"Real-time trading platform with advanced analytics and risk management",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","WebSocket","PostgreSQL","Redis","Docker"],features:["Real-time trading","Portfolio management","Risk analytics","Compliance reporting"],businessGoals:["Increase trading volume","Reduce latency","Ensure regulatory compliance"],targetAudience:"Professional traders and financial institutions",timeline:"12-18 months",budget:"$500K - $2M",successMetrics:["Sub-100ms latency","99.9% uptime","Full regulatory compliance"],risks:["Regulatory changes","Market volatility","Security breaches"],template:"Build a comprehensive FinTech trading platform with real-time capabilities and regulatory compliance.\n\nKey Requirements:\n- Real-time market data processing and trading execution\n- Advanced risk management and portfolio analytics\n- Regulatory compliance and audit trails\n- High-performance architecture with low latency\n- Secure user authentication and authorization\n- Integration with market data providers and clearing houses\n\nTechnical Specifications:\n- Microservices architecture for scalability\n- WebSocket connections for real-time updates\n- Redis for caching and session management\n- PostgreSQL for transactional data\n- Docker containerization for deployment\n- Comprehensive API documentation\n\nSuccess Criteria:\n- Sub-100ms trade execution latency\n- 99.9% uptime during market hours\n- Full regulatory compliance (MiFID II, GDPR)\n- Support for 10,000+ concurrent users"},{id:"digital-banking-app",name:"Digital Banking Application",industry:"Financial Technology",description:"Mobile-first digital banking platform with AI-powered financial insights",projectType:"mobile-application",platform:"mobile",complexity:"advanced",technologies:["React Native","Node.js","PostgreSQL","AWS","TensorFlow","Plaid"],features:["Account management","Mobile payments","AI budgeting","Investment tracking"],businessGoals:["Increase customer engagement","Reduce operational costs","Expand digital services"],targetAudience:"Banking customers and financial service users",timeline:"10-15 months",budget:"$400K - $1.5M",successMetrics:["90% customer adoption","50% cost reduction","4.8+ app store rating"],risks:["Regulatory compliance","Security vulnerabilities","Competition from neobanks"],template:"Create a next-generation digital banking application with AI-powered financial management.\n\nKey Requirements:\n- Secure account management and transaction history\n- Real-time mobile payments and transfers\n- AI-powered budgeting and spending insights\n- Investment portfolio tracking and recommendations\n- Biometric authentication and fraud detection\n- Integration with third-party financial services\n\nTechnical Specifications:\n- Cross-platform mobile development\n- End-to-end encryption for all transactions\n- Real-time fraud detection algorithms\n- Open banking API integrations\n- Cloud-native architecture with auto-scaling\n- Comprehensive security audit and penetration testing\n\nSuccess Criteria:\n- PCI DSS compliance certification\n- 99.99% transaction success rate\n- <3 second app load times\n- Zero critical security vulnerabilities"},{id:"cryptocurrency-exchange",name:"Cryptocurrency Exchange Platform",industry:"Financial Technology",description:"Secure cryptocurrency trading platform with advanced order matching",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Go","PostgreSQL","Redis","Kubernetes","WebSocket"],features:["Crypto trading","Order matching","Wallet management","KYC/AML compliance"],businessGoals:["Capture crypto market share","Ensure regulatory compliance","Maximize trading volume"],targetAudience:"Cryptocurrency traders and investors",timeline:"12-18 months",budget:"$600K - $2.5M",successMetrics:["$100M+ daily volume","99.99% uptime","Full regulatory compliance"],risks:["Regulatory uncertainty","Security attacks","Market manipulation"],template:"Develop a secure and scalable cryptocurrency exchange with institutional-grade features.\n\nKey Requirements:\n- High-performance order matching engine\n- Multi-cryptocurrency wallet management\n- Advanced trading features (limit, market, stop orders)\n- KYC/AML compliance and reporting\n- Cold storage security for digital assets\n- Real-time market data and charting tools\n\nTechnical Specifications:\n- Microservices architecture with Go backend\n- High-frequency trading support (>100k TPS)\n- Multi-signature wallet security\n- Real-time WebSocket market data feeds\n- Kubernetes orchestration for scalability\n- Comprehensive audit logging and monitoring\n\nSuccess Criteria:\n- Handle 1M+ transactions per day\n- 99.99% platform availability\n- SOC 2 Type II compliance\n- Zero successful security breaches"},{id:"telemedicine-platform",name:"Telemedicine Platform",industry:"Healthcare & Medical",description:"HIPAA-compliant telemedicine platform for remote patient consultations",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","WebRTC","PostgreSQL","AWS","Socket.io"],features:["Video consultations","Patient records","Prescription management","Appointment scheduling"],businessGoals:["Improve patient access","Reduce healthcare costs","Ensure HIPAA compliance"],targetAudience:"Healthcare providers and patients",timeline:"8-12 months",budget:"$200K - $800K",successMetrics:["Patient satisfaction >4.5","Consultation completion >95%","Zero HIPAA violations"],risks:["Regulatory compliance","Data security breaches","Video quality issues"],template:"Create a HIPAA-compliant telemedicine platform enabling secure remote healthcare delivery.\n\nKey Requirements:\n- End-to-end encrypted video consultations\n- Electronic health records (EHR) integration\n- Prescription management and e-prescribing\n- Secure patient-provider messaging\n- Appointment scheduling and calendar integration\n- Payment processing and insurance verification\n\nTechnical Specifications:\n- WebRTC for real-time video communication\n- HIPAA-compliant cloud infrastructure\n- Role-based access control (RBAC)\n- Audit logging for all patient interactions\n- Integration with existing EHR systems\n- Mobile-responsive design for accessibility\n\nSuccess Criteria:\n- HIPAA compliance certification\n- 99.9% uptime for critical services\n- <2 second video connection establishment\n- Support for 1,000+ concurrent consultations"},{id:"patient-portal",name:"Patient Portal System",industry:"Healthcare & Medical",description:"Comprehensive patient portal for accessing medical records and managing healthcare",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["Vue.js","Laravel","MySQL","Redis","Docker"],features:["Medical records access","Appointment booking","Lab results","Billing management"],businessGoals:["Improve patient engagement","Reduce administrative burden","Enhance care coordination"],targetAudience:"Patients and healthcare administrators",timeline:"6-9 months",budget:"$150K - $400K",successMetrics:["Patient adoption >70%","Phone calls reduced 40%","Patient satisfaction >4.0"],risks:["Low user adoption","Integration complexity","Data privacy concerns"],template:"Develop a user-friendly patient portal that empowers patients to manage their healthcare digitally.\n\nKey Requirements:\n- Secure access to medical records and test results\n- Online appointment scheduling and management\n- Prescription refill requests and medication tracking\n- Secure messaging with healthcare providers\n- Billing and insurance information management\n- Health education resources and reminders\n\nTechnical Specifications:\n- Multi-factor authentication for security\n- Integration with EHR and practice management systems\n- Mobile-first responsive design\n- Automated appointment reminders via SMS/email\n- Document upload and sharing capabilities\n- Accessibility compliance (WCAG 2.1)\n\nSuccess Criteria:\n- 70% patient adoption within 6 months\n- 40% reduction in administrative phone calls\n- 99.5% uptime for patient-facing services\n- Full HIPAA compliance and security audit passed"},{id:"medical-inventory-system",name:"Medical Inventory Management",industry:"Healthcare & Medical",description:"Smart inventory management system for medical supplies and pharmaceuticals",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["Angular","Spring Boot","PostgreSQL","Redis","Kubernetes"],features:["Real-time inventory tracking","Automated reordering","Expiration monitoring","Compliance reporting"],businessGoals:["Reduce waste","Ensure supply availability","Maintain regulatory compliance"],targetAudience:"Hospital administrators and pharmacy staff",timeline:"5-8 months",budget:"$100K - $300K",successMetrics:["Inventory waste reduction 25%","Stockout incidents <2%","Compliance score >98%"],risks:["Integration with existing systems","Staff training requirements","Regulatory changes"],template:"Build an intelligent medical inventory management system that optimizes supply chain operations.\n\nKey Requirements:\n- Real-time tracking of medical supplies and pharmaceuticals\n- Automated reorder points and purchase order generation\n- Expiration date monitoring and alerts\n- Barcode/RFID scanning for inventory updates\n- Regulatory compliance reporting (FDA, DEA)\n- Integration with supplier systems and ERP\n\nTechnical Specifications:\n- Microservices architecture for scalability\n- Real-time dashboard with inventory analytics\n- Mobile app for warehouse staff\n- API integrations with supplier systems\n- Automated compliance reporting\n- Role-based access control for different user types\n\nSuccess Criteria:\n- 25% reduction in inventory carrying costs\n- 99% inventory accuracy\n- Zero expired medication incidents\n- Full regulatory compliance maintained"},{id:"hospital-management-system",name:"Hospital Management System",industry:"Healthcare & Medical",description:"Comprehensive hospital management system for operations and patient care",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","Redis","Docker","Elasticsearch"],features:["Patient management","Staff scheduling","Billing system","Inventory tracking"],businessGoals:["Streamline operations","Improve patient care","Reduce operational costs"],targetAudience:"Hospital administrators, doctors, and nursing staff",timeline:"12-18 months",budget:"$300K - $1M",successMetrics:["30% efficiency improvement","Patient satisfaction >4.5","Cost reduction 20%"],risks:["System integration complexity","Staff training","Data migration challenges"],template:"Create a comprehensive hospital management system that integrates all aspects of hospital operations.\n\nKey Requirements:\n- Patient admission, discharge, and transfer (ADT) management\n- Electronic medical records (EMR) integration\n- Staff scheduling and resource allocation\n- Billing and insurance claim processing\n- Pharmacy and inventory management\n- Laboratory and radiology information systems\n\nTechnical Specifications:\n- Modular architecture with microservices\n- Real-time data synchronization across departments\n- Integration with medical devices and equipment\n- Advanced reporting and analytics dashboard\n- Mobile applications for staff\n- Disaster recovery and backup systems\n\nSuccess Criteria:\n- 99.9% system availability\n- 30% reduction in administrative tasks\n- Full integration with existing medical systems\n- HIPAA compliance and security certification"},{id:"mental-health-app",name:"Mental Health Support App",industry:"Healthcare & Medical",description:"Mobile mental health platform with therapy sessions and wellness tracking",projectType:"mobile-application",platform:"mobile",complexity:"intermediate",technologies:["React Native","Node.js","MongoDB","AWS","WebRTC"],features:["Therapy sessions","Mood tracking","Meditation guides","Crisis support"],businessGoals:["Improve mental health access","Reduce therapy costs","Provide 24/7 support"],targetAudience:"Individuals seeking mental health support",timeline:"6-10 months",budget:"$150K - $500K",successMetrics:["User engagement >80%","Therapy completion >70%","Crisis response <5min"],risks:["Regulatory compliance","Crisis management","User safety concerns"],template:"Develop a comprehensive mental health support application with professional therapy integration.\n\nKey Requirements:\n- Secure video therapy sessions with licensed professionals\n- Daily mood and wellness tracking with insights\n- Guided meditation and mindfulness exercises\n- Crisis intervention and emergency support\n- Peer support communities and forums\n- Integration with wearable devices for health monitoring\n\nTechnical Specifications:\n- End-to-end encryption for all communications\n- HIPAA-compliant data storage and processing\n- AI-powered mood analysis and recommendations\n- Real-time crisis detection and alert systems\n- Offline functionality for core features\n- Accessibility features for diverse user needs\n\nSuccess Criteria:\n- 80% user retention after 3 months\n- 24/7 crisis support availability\n- Integration with 5+ major therapy providers\n- 4.5+ app store rating"},{id:"lms-platform",name:"Learning Management System",industry:"Education & E-learning",description:"Comprehensive LMS for educational institutions with advanced analytics",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Django","PostgreSQL","Redis","AWS","TensorFlow"],features:["Course management","Student tracking","Assessment tools","Analytics dashboard"],businessGoals:["Improve learning outcomes","Increase student engagement","Reduce administrative burden"],targetAudience:"Educational institutions, teachers, and students",timeline:"8-12 months",budget:"$200K - $700K",successMetrics:["90% course completion","85% student satisfaction","40% admin time reduction"],risks:["User adoption challenges","Content migration complexity","Scalability issues"],template:"Build a comprehensive learning management system that enhances educational delivery and outcomes.\n\nKey Requirements:\n- Course creation and content management tools\n- Student enrollment and progress tracking\n- Interactive assessments and grading systems\n- Discussion forums and collaboration tools\n- Advanced analytics and reporting\n- Integration with existing educational systems\n\nTechnical Specifications:\n- Scalable cloud architecture supporting 10,000+ concurrent users\n- Mobile-responsive design for all devices\n- AI-powered learning analytics and recommendations\n- Video streaming and content delivery network (CDN)\n- Single sign-on (SSO) integration\n- Accessibility compliance (WCAG 2.1 AA)\n\nSuccess Criteria:\n- Support for 50,000+ students\n- 99.9% platform uptime\n- <3 second page load times\n- Full FERPA compliance for student data"},{id:"online-tutoring-platform",name:"Online Tutoring Platform",industry:"Education & E-learning",description:"AI-powered tutoring platform connecting students with qualified tutors",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["Vue.js","Node.js","MongoDB","WebRTC","Stripe","Socket.io"],features:["Tutor matching","Video sessions","Payment processing","Progress tracking"],businessGoals:["Improve learning outcomes","Increase tutor utilization","Expand market reach"],targetAudience:"Students, parents, and tutors",timeline:"6-9 months",budget:"$150K - $400K",successMetrics:["90% session completion","Tutor utilization >75%","Student improvement 25%"],risks:["Quality control","Payment disputes","Tutor availability"],template:"Create an intelligent tutoring platform that matches students with the best tutors for their needs.\n\nKey Requirements:\n- AI-powered tutor-student matching algorithm\n- High-quality video conferencing for tutoring sessions\n- Integrated whiteboard and screen sharing tools\n- Secure payment processing and tutor payouts\n- Session recording and review capabilities\n- Progress tracking and performance analytics\n\nTechnical Specifications:\n- Real-time video communication with WebRTC\n- Machine learning for tutor recommendation engine\n- Secure payment processing with escrow system\n- Mobile-responsive design for all devices\n- Integration with calendar and scheduling systems\n- Multi-language support for global reach\n\nSuccess Criteria:\n- 90% successful tutor-student matches\n- Average session rating >4.5\n- 75% student retention after 3 months\n- Platform supports 1,000+ concurrent sessions"},{id:"property-management-platform",name:"Property Management Platform",industry:"Real Estate",description:"Comprehensive property management system for landlords and property managers",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","Stripe","AWS","Twilio"],features:["Property listings","Tenant management","Rent collection","Maintenance tracking"],businessGoals:["Streamline operations","Improve tenant satisfaction","Increase rental income"],targetAudience:"Property managers, landlords, and tenants",timeline:"6-10 months",budget:"$150K - $500K",successMetrics:["95% rent collection rate","Tenant satisfaction >4.0","30% time savings"],risks:["Regulatory compliance","Payment processing issues","Data security"],template:"Build a comprehensive property management platform that automates rental operations.\n\nKey Requirements:\n- Property portfolio management and listings\n- Tenant screening and application processing\n- Automated rent collection and payment processing\n- Maintenance request tracking and vendor management\n- Financial reporting and accounting integration\n- Communication tools for landlord-tenant interactions\n\nTechnical Specifications:\n- Multi-tenant architecture for property managers\n- Secure payment processing with ACH and credit cards\n- Mobile applications for tenants and maintenance staff\n- Integration with accounting software (QuickBooks, Xero)\n- Document management and e-signature capabilities\n- Automated late payment notifications and collections\n\nSuccess Criteria:\n- 95% on-time rent collection rate\n- 50% reduction in administrative tasks\n- 99.5% payment processing uptime\n- Full compliance with local rental regulations"},{id:"real-estate-marketplace",name:"Real Estate Marketplace",industry:"Real Estate",description:"AI-powered real estate marketplace with virtual tours and market analytics",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["Next.js","Python","PostgreSQL","Elasticsearch","AWS","TensorFlow"],features:["Property search","Virtual tours","Market analytics","Agent matching"],businessGoals:["Increase property sales","Improve user experience","Expand market share"],targetAudience:"Home buyers, sellers, and real estate agents",timeline:"10-15 months",budget:"$300K - $1M",successMetrics:["1M+ property views/month","Conversion rate >3%","Agent satisfaction >4.5"],risks:["Market competition","Data accuracy","Technology adoption"],template:"Create an innovative real estate marketplace with AI-powered features and immersive experiences.\n\nKey Requirements:\n- Advanced property search with AI-powered recommendations\n- 360-degree virtual tours and augmented reality features\n- Real-time market analytics and price predictions\n- Agent-buyer matching and communication tools\n- Mortgage calculator and financing options\n- Neighborhood insights and demographic data\n\nTechnical Specifications:\n- Machine learning for property valuation and recommendations\n- High-performance search with Elasticsearch\n- CDN for fast image and video delivery\n- Integration with MLS (Multiple Listing Service) data\n- Mobile-first responsive design\n- Real-time chat and video calling capabilities\n\nSuccess Criteria:\n- 1 million monthly active users\n- 3% buyer-to-sale conversion rate\n- <2 second property search response time\n- Integration with 50+ MLS systems nationwide"},{id:"multiplayer-game-platform",name:"Multiplayer Gaming Platform",industry:"Gaming & Entertainment",description:"Real-time multiplayer gaming platform with social features and tournaments",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","Socket.io","Redis","MongoDB","WebRTC"],features:["Real-time gameplay","Tournament system","Social features","Leaderboards"],businessGoals:["Increase player engagement","Monetize through tournaments","Build gaming community"],targetAudience:"Gamers and esports enthusiasts",timeline:"8-12 months",budget:"$200K - $800K",successMetrics:["100K+ active players","Average session >30min","Tournament participation >20%"],risks:["Server scalability","Cheating prevention","Player retention"],template:"Develop a high-performance multiplayer gaming platform with competitive features.\n\nKey Requirements:\n- Real-time multiplayer game engine with low latency\n- Tournament and league management system\n- Player profiles and social networking features\n- In-game chat and voice communication\n- Anti-cheat detection and prevention systems\n- Spectator mode and live streaming integration\n\nTechnical Specifications:\n- WebSocket-based real-time communication\n- Distributed server architecture for global reach\n- Redis for session management and caching\n- Machine learning for cheat detection\n- CDN for game asset delivery\n- Mobile-responsive design for cross-platform play\n\nSuccess Criteria:\n- <50ms latency for real-time gameplay\n- Support for 10,000+ concurrent players\n- 99.9% server uptime during peak hours\n- Zero tolerance for cheating with 99% detection rate"},{id:"streaming-platform",name:"Video Streaming Platform",industry:"Gaming & Entertainment",description:"Live streaming platform for content creators with monetization features",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","WebRTC","AWS","Redis","Elasticsearch"],features:["Live streaming","Chat system","Monetization tools","Content discovery"],businessGoals:["Attract content creators","Increase viewer engagement","Generate revenue"],targetAudience:"Content creators and viewers",timeline:"10-15 months",budget:"$400K - $1.5M",successMetrics:["1M+ monthly viewers","Creator retention >80%","Revenue growth 50%/year"],risks:["Content moderation","Bandwidth costs","Competition from major platforms"],template:"Build a comprehensive live streaming platform that empowers content creators.\n\nKey Requirements:\n- High-quality live video streaming with adaptive bitrate\n- Real-time chat and interaction features\n- Creator monetization tools (subscriptions, donations, ads)\n- Content discovery and recommendation engine\n- Mobile streaming applications for creators\n- Advanced analytics and creator dashboard\n\nTechnical Specifications:\n- WebRTC and HLS for video streaming\n- Global CDN for low-latency delivery\n- Real-time chat with moderation tools\n- Machine learning for content recommendations\n- Payment processing for creator monetization\n- Scalable architecture supporting millions of viewers\n\nSuccess Criteria:\n- Support for 100,000+ concurrent viewers per stream\n- <3 second stream start time globally\n- 99.9% streaming uptime\n- Creator payout processing within 24 hours"},{id:"task-management-app",name:"TaskMaster Pro",industry:"Productivity & Collaboration",description:"Comprehensive task management application for teams and individuals",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["Next.js","Tailwind CSS","Supabase","NextAuth.js","Vercel"],features:["Task assignment","Deadline tracking","Progress visualization","Team collaboration"],businessGoals:["Improve team productivity","Enhance project visibility","Reduce missed deadlines"],targetAudience:"Project managers, teams, and individual professionals",timeline:"4-6 months",budget:"$80K - $250K",successMetrics:["90% task completion rate","Team productivity increase 25%","User adoption >80%"],risks:["User adoption challenges","Integration complexity","Competition from established tools"],template:"Build a comprehensive task management application that helps teams organize, track, and collaborate on projects.\n\nKey Requirements:\n- User authentication and team management\n- Task creation, assignment, and tracking\n- Project dashboard with progress visualization\n- Real-time collaboration and comments\n- File attachments and document sharing\n- Email notifications and reminders\n- Time tracking and reporting\n- Integration with calendar apps\n\nTechnical Specifications:\n- Modern React-based frontend with Next.js\n- Real-time updates using WebSocket connections\n- Role-based permissions and workspace management\n- Mobile-responsive design for all devices\n- Integration with popular productivity tools\n- Automated backup and data recovery\n\nSuccess Criteria:\n- Support for teams of up to 500 members\n- 99.5% uptime for critical features\n- <2 second page load times\n- Integration with 10+ popular productivity apps"},{id:"fitness-tracking-app",name:"FitTracker Mobile",industry:"Health & Fitness",description:"Mobile fitness tracking app with wearable integration and social features",projectType:"mobile-application",platform:"mobile",complexity:"advanced",technologies:["React Native","Firebase","Firebase Auth","Jest","HealthKit"],features:["Workout tracking","Nutrition logging","Progress photos","Social challenges"],businessGoals:["Improve user health outcomes","Increase app engagement","Build fitness community"],targetAudience:"Fitness enthusiasts and health-conscious individuals",timeline:"8-12 months",budget:"$200K - $600K",successMetrics:["Daily active users >50K","Workout completion >80%","User retention >70%"],risks:["Wearable integration complexity","Health data privacy","User motivation"],template:"Create a comprehensive fitness tracking mobile app with advanced features and social integration.\n\nKey Requirements:\n- User profiles and personalized goal setting\n- Comprehensive workout tracking and exercise library\n- Nutrition logging with calorie and macro counting\n- Progress photos and body measurements tracking\n- Social features including challenges and leaderboards\n- Integration with popular fitness wearables and health apps\n- Offline workout mode with data synchronization\n- Push notifications for motivation and reminders\n\nTechnical Specifications:\n- Cross-platform mobile development with React Native\n- Integration with HealthKit (iOS) and Google Fit (Android)\n- Real-time data synchronization with cloud backend\n- Machine learning for personalized recommendations\n- Social networking features with privacy controls\n- Offline-first architecture with background sync\n\nSuccess Criteria:\n- Integration with 10+ major fitness wearables\n- 80% workout completion rate\n- 4.5+ app store rating\n- Support for offline usage in 90% of features"},{id:"data-visualization-platform",name:"DataViz Dashboard",industry:"Business Intelligence & Analytics",description:"Interactive data visualization platform with advanced analytics capabilities",projectType:"data-analysis",platform:"web",complexity:"advanced",technologies:["React","Python/FastAPI","PostgreSQL","Material-UI","AWS","D3.js"],features:["Data import","Interactive charts","Real-time processing","Collaborative sharing"],businessGoals:["Enable data-driven decisions","Improve analytics accessibility","Reduce reporting time"],targetAudience:"Data analysts, business users, and executives",timeline:"10-14 months",budget:"$300K - $900K",successMetrics:["Processing 1M+ rows","Report generation <30sec","User adoption >85%"],risks:["Data security","Performance with large datasets","User training requirements"],template:"Develop an advanced data visualization platform that transforms raw data into actionable insights.\n\nKey Requirements:\n- Support for multiple data sources (CSV, JSON, databases, APIs)\n- Interactive chart and graph creation with drag-and-drop interface\n- Real-time data processing and streaming capabilities\n- Collaborative dashboard sharing and permissions\n- Advanced statistical analysis and machine learning integration\n- Custom visualization components and templates\n- Automated report generation and scheduling\n- RESTful API for data integration and embedding\n\nTechnical Specifications:\n- High-performance backend with Python/FastAPI\n- Scalable data processing with Apache Spark\n- Interactive visualizations using D3.js and custom components\n- Real-time data streaming with WebSocket connections\n- Cloud-native architecture with auto-scaling\n- Advanced caching for improved performance\n\nSuccess Criteria:\n- Process datasets with millions of rows in real-time\n- Generate complex reports in under 30 seconds\n- Support for 1,000+ concurrent users\n- 99.9% data accuracy and integrity"},{id:"hospital-management-system",name:"Hospital Management System",industry:"Healthcare & Medical",description:"Comprehensive HIPAA-compliant hospital management platform with patient records and scheduling",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","AWS","HL7 FHIR","Redis"],features:["Patient records","Appointment scheduling","Billing system","Inventory management","Staff management"],businessGoals:["Improve patient care","Reduce administrative costs","Ensure HIPAA compliance","Streamline operations"],targetAudience:"Hospitals, clinics, and healthcare administrators",timeline:"12-18 months",budget:"$400K - $1.2M",successMetrics:["HIPAA compliance certification","50% reduction in paperwork","99.9% uptime"],risks:["Data breaches","Regulatory compliance","Staff training requirements"],template:"Build a comprehensive hospital management system with full HIPAA compliance and integrated patient care workflows.\n\nKey Requirements:\n- Electronic Health Records (EHR) with HL7 FHIR compliance\n- Patient appointment scheduling and management\n- Billing and insurance claim processing\n- Medical inventory and pharmacy management\n- Staff scheduling and role-based access control\n- Integration with medical devices and lab systems\n- Telemedicine capabilities for remote consultations\n- Automated reporting and analytics dashboard\n\nTechnical Specifications:\n- HIPAA-compliant cloud infrastructure\n- End-to-end encryption for all patient data\n- Role-based access control with audit trails\n- Integration with existing hospital systems\n- Real-time notifications and alerts\n- Mobile-responsive design for tablets and smartphones\n- Backup and disaster recovery systems\n- API integrations with insurance providers\n\nSuccess Criteria:\n- Full HIPAA compliance certification\n- 99.9% system uptime during critical hours\n- 50% reduction in administrative paperwork\n- Integration with 95% of existing hospital systems"},{id:"mental-health-platform",name:"Mental Health Support Platform",industry:"Healthcare & Medical",description:"Digital mental health platform with therapy matching, progress tracking, and crisis intervention",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","Stripe","Twilio","TensorFlow"],features:["Therapist matching","Video sessions","Progress tracking","Crisis intervention","Resource library"],businessGoals:["Improve mental health access","Reduce therapy wait times","Provide 24/7 support"],targetAudience:"Individuals seeking mental health support and licensed therapists",timeline:"8-12 months",budget:"$200K - $600K",successMetrics:["90% user satisfaction","70% therapy completion rate","24/7 crisis response"],risks:["Privacy concerns","Therapist availability","Crisis management protocols"],template:"Create a comprehensive mental health platform that connects users with licensed therapists and provides ongoing support.\n\nKey Requirements:\n- AI-powered therapist matching based on specialties and preferences\n- Secure video conferencing for therapy sessions\n- Progress tracking with mood journals and assessments\n- 24/7 crisis intervention with emergency protocols\n- Comprehensive resource library with self-help tools\n- Insurance integration and billing management\n- Mobile app for on-the-go access\n- Community support groups and forums\n\nTechnical Specifications:\n- HIPAA-compliant video conferencing\n- Encrypted messaging and file sharing\n- AI algorithms for therapist-client matching\n- Real-time crisis detection and alert systems\n- Integration with electronic health records\n- Payment processing with insurance claims\n- Multi-platform mobile and web applications\n- Advanced analytics for treatment outcomes\n\nSuccess Criteria:\n- 90% user satisfaction rating\n- 70% therapy session completion rate\n- Sub-5 minute crisis response time\n- Integration with major insurance providers"},{id:"medical-imaging-ai",name:"AI Medical Imaging Platform",industry:"Healthcare & Medical",description:"AI-powered medical imaging analysis platform for radiology and diagnostic imaging",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["Python","TensorFlow","React","FastAPI","PostgreSQL","DICOM"],features:["Image analysis","AI diagnostics","Report generation","DICOM integration","Radiologist workflow"],businessGoals:["Improve diagnostic accuracy","Reduce analysis time","Support radiologists"],targetAudience:"Radiologists, hospitals, and diagnostic imaging centers",timeline:"15-24 months",budget:"$800K - $2.5M",successMetrics:["95% diagnostic accuracy","60% faster analysis","FDA approval"],risks:["Regulatory approval","AI model accuracy","Integration complexity"],template:"Develop an AI-powered medical imaging platform that assists radiologists in diagnostic analysis and reporting.\n\nKey Requirements:\n- Advanced AI models for medical image analysis (X-ray, CT, MRI, ultrasound)\n- DICOM standard compliance for medical imaging\n- Automated report generation with confidence scores\n- Integration with existing radiology workflows (PACS/RIS)\n- Real-time collaboration tools for radiologists\n- Quality assurance and peer review systems\n- Mobile access for emergency consultations\n- Comprehensive audit trails and version control\n\nTechnical Specifications:\n- Deep learning models trained on medical datasets\n- GPU-accelerated image processing infrastructure\n- DICOM viewer with advanced visualization tools\n- RESTful APIs for PACS/RIS integration\n- Cloud-based storage with HIPAA compliance\n- Real-time image streaming and processing\n- Advanced security with role-based access\n- Scalable architecture for high-volume processing\n\nSuccess Criteria:\n- 95% diagnostic accuracy compared to expert radiologists\n- 60% reduction in image analysis time\n- FDA 510(k) clearance for clinical use\n- Integration with 90% of major PACS systems"},{id:"pharmacy-management",name:"Pharmacy Management System",industry:"Healthcare & Medical",description:"Complete pharmacy management solution with inventory, prescriptions, and insurance processing",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","Stripe","Twilio","AWS"],features:["Prescription management","Inventory tracking","Insurance processing","Patient profiles","Drug interaction alerts"],businessGoals:["Streamline pharmacy operations","Reduce medication errors","Improve customer service"],targetAudience:"Pharmacies, pharmacists, and pharmacy technicians",timeline:"6-10 months",budget:"$150K - $500K",successMetrics:["99% prescription accuracy","50% faster processing","95% insurance approval"],risks:["Regulatory compliance","Drug database accuracy","Insurance integration"],template:"Build a comprehensive pharmacy management system that streamlines prescription processing and inventory management.\n\nKey Requirements:\n- Electronic prescription processing and verification\n- Real-time inventory management with automatic reordering\n- Insurance claim processing and prior authorization\n- Patient profile management with medication history\n- Drug interaction and allergy checking\n- Automated refill reminders and notifications\n- Point-of-sale integration with payment processing\n- Regulatory compliance reporting (DEA, state boards)\n\nTechnical Specifications:\n- Integration with electronic health records (EHR)\n- Real-time drug database updates (First Databank, Lexicomp)\n- Secure prescription transmission (SCRIPT standard)\n- Barcode scanning for medication verification\n- Cloud-based backup and disaster recovery\n- Mobile app for prescription management\n- Advanced reporting and analytics dashboard\n- Multi-location support for pharmacy chains\n\nSuccess Criteria:\n- 99% prescription accuracy with error checking\n- 50% reduction in prescription processing time\n- 95% insurance claim approval rate\n- Full compliance with pharmacy regulations"},{id:"fitness-wellness-app",name:"Fitness & Wellness Tracking App",industry:"Healthcare & Medical",description:"Comprehensive fitness and wellness platform with personalized coaching and health monitoring",projectType:"mobile-application",platform:"mobile",complexity:"intermediate",technologies:["React Native","Node.js","MongoDB","AWS","HealthKit","Google Fit"],features:["Workout tracking","Nutrition logging","Health monitoring","Personal coaching","Social features"],businessGoals:["Promote healthy lifestyles","Increase user engagement","Provide personalized guidance"],targetAudience:"Fitness enthusiasts, health-conscious individuals, and personal trainers",timeline:"6-9 months",budget:"$100K - $400K",successMetrics:["80% daily active users","70% goal completion","4.5+ app rating"],risks:["User retention","Data accuracy","Competition from established apps"],template:"Create a comprehensive fitness and wellness platform that motivates users to achieve their health goals.\n\nKey Requirements:\n- Comprehensive workout tracking with exercise library\n- Nutrition logging with barcode scanning and meal planning\n- Health metrics monitoring (weight, heart rate, sleep, steps)\n- AI-powered personal coaching and recommendations\n- Social features with challenges and community support\n- Integration with wearable devices and health apps\n- Progress tracking with detailed analytics and insights\n- Customizable workout plans and nutrition programs\n\nTechnical Specifications:\n- Cross-platform mobile development (iOS/Android)\n- Integration with HealthKit (iOS) and Google Fit (Android)\n- Real-time synchronization across devices\n- Machine learning for personalized recommendations\n- Social networking features with privacy controls\n- Push notifications for motivation and reminders\n- Offline mode for workout tracking\n- Advanced analytics and reporting dashboard\n\nSuccess Criteria:\n- 80% daily active user retention\n- 70% user goal completion rate\n- 4.5+ average app store rating\n- Integration with 95% of popular fitness wearables"},{id:"cryptocurrency-exchange",name:"Cryptocurrency Exchange Platform",industry:"Financial Services & Fintech",description:"Secure cryptocurrency trading platform with advanced order matching and wallet management",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","Redis","WebSocket","Blockchain APIs"],features:["Crypto trading","Wallet management","Order matching","Security features","Market analysis"],businessGoals:["Enable crypto trading","Ensure security","Provide liquidity","Comply with regulations"],targetAudience:"Cryptocurrency traders and investors",timeline:"12-18 months",budget:"$600K - $2M",successMetrics:["$1M+ daily volume","99.9% uptime","Zero security breaches"],risks:["Regulatory changes","Security threats","Market volatility"],template:"Build a secure and scalable cryptocurrency exchange platform with advanced trading features.\n\nKey Requirements:\n- Multi-cryptocurrency support (Bitcoin, Ethereum, altcoins)\n- Advanced order matching engine with high throughput\n- Secure wallet management with cold storage integration\n- Real-time market data and charting tools\n- KYC/AML compliance and identity verification\n- Two-factor authentication and security measures\n- API for algorithmic trading and third-party integrations\n- Liquidity management and market making tools\n\nTechnical Specifications:\n- High-performance order matching engine (100k+ orders/sec)\n- Multi-signature wallet security with hardware security modules\n- Real-time WebSocket connections for market data\n- Microservices architecture for scalability\n- Advanced monitoring and alerting systems\n- Compliance reporting and audit trails\n- DDoS protection and security hardening\n- Multi-region deployment for global access\n\nSuccess Criteria:\n- Process $1M+ in daily trading volume\n- 99.9% platform uptime during market hours\n- Zero security breaches or fund losses\n- Full regulatory compliance in target jurisdictions"},{id:"robo-advisor-platform",name:"Robo-Advisor Investment Platform",industry:"Financial Services & Fintech",description:"AI-powered investment advisory platform with automated portfolio management and rebalancing",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Python","TensorFlow","PostgreSQL","AWS","Alpaca API"],features:["Portfolio management","Risk assessment","Automated rebalancing","Tax optimization","Goal tracking"],businessGoals:["Democratize investing","Reduce management fees","Provide personalized advice"],targetAudience:"Individual investors and financial advisors",timeline:"10-15 months",budget:"$400K - $1.2M",successMetrics:["$100M+ assets under management","8%+ annual returns","0.5% management fee"],risks:["Market volatility","Regulatory compliance","Algorithm performance"],template:"Create an AI-powered robo-advisor platform that provides automated investment management and financial planning.\n\nKey Requirements:\n- AI-driven portfolio construction and optimization\n- Automated rebalancing based on market conditions\n- Risk tolerance assessment and goal-based investing\n- Tax-loss harvesting and optimization strategies\n- Integration with brokerage accounts and custodians\n- Comprehensive financial planning tools\n- Real-time performance tracking and reporting\n- Educational content and investment insights\n\nTechnical Specifications:\n- Machine learning algorithms for portfolio optimization\n- Real-time market data integration and analysis\n- Automated trading execution with best execution\n- Advanced risk management and compliance monitoring\n- Secure account aggregation and data synchronization\n- Mobile-first responsive design\n- Comprehensive API for third-party integrations\n- Advanced analytics and performance attribution\n\nSuccess Criteria:\n- Manage $100M+ in assets under management\n- Achieve 8%+ average annual returns net of fees\n- Maintain 0.5% or lower management fee structure\n- 95% client satisfaction and retention rate"},{id:"peer-to-peer-lending",name:"Peer-to-Peer Lending Platform",industry:"Financial Services & Fintech",description:"P2P lending marketplace connecting borrowers with investors for personal and business loans",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","Stripe","Plaid","TensorFlow"],features:["Loan marketplace","Credit scoring","Automated investing","Risk assessment","Payment processing"],businessGoals:["Connect borrowers and lenders","Reduce lending costs","Improve access to credit"],targetAudience:"Individual borrowers, investors, and small businesses",timeline:"12-16 months",budget:"$500K - $1.5M",successMetrics:["$50M+ loan origination","5% default rate","12% investor returns"],risks:["Credit risk","Regulatory compliance","Economic downturns"],template:"Build a comprehensive peer-to-peer lending platform that efficiently matches borrowers with investors.\n\nKey Requirements:\n- Borrower application and verification system\n- AI-powered credit scoring and risk assessment\n- Investor dashboard with automated investing options\n- Loan marketplace with filtering and search capabilities\n- Integrated payment processing and loan servicing\n- Regulatory compliance and reporting tools\n- Mobile applications for borrowers and investors\n- Advanced analytics and performance tracking\n\nTechnical Specifications:\n- Machine learning models for credit risk assessment\n- Integration with credit bureaus and financial data providers\n- Automated loan servicing and payment processing\n- Real-time loan performance monitoring\n- Secure document upload and verification\n- Advanced fraud detection and prevention\n- Comprehensive reporting and compliance tools\n- Scalable architecture for high transaction volume\n\nSuccess Criteria:\n- Originate $50M+ in loans annually\n- Maintain sub-5% default rate across loan portfolio\n- Achieve 12%+ average annual returns for investors\n- Full compliance with lending regulations"},{id:"expense-management-app",name:"Corporate Expense Management",industry:"Financial Services & Fintech",description:"AI-powered expense management platform with receipt scanning and automated approval workflows",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","AWS","OCR API","Stripe"],features:["Receipt scanning","Expense tracking","Approval workflows","Reimbursement processing","Analytics"],businessGoals:["Streamline expense reporting","Reduce processing time","Improve compliance"],targetAudience:"Businesses, employees, and finance teams",timeline:"6-10 months",budget:"$200K - $600K",successMetrics:["80% faster processing","95% receipt accuracy","90% user adoption"],risks:["OCR accuracy","Integration complexity","User adoption"],template:"Create an intelligent expense management platform that automates expense reporting and approval processes.\n\nKey Requirements:\n- AI-powered receipt scanning and data extraction\n- Mobile app for expense capture and submission\n- Customizable approval workflows and policies\n- Integration with accounting systems (QuickBooks, SAP, etc.)\n- Real-time expense tracking and budget monitoring\n- Automated mileage tracking and calculation\n- Corporate credit card integration and reconciliation\n- Comprehensive reporting and analytics dashboard\n\nTechnical Specifications:\n- OCR technology for receipt data extraction\n- Machine learning for expense categorization\n- Real-time synchronization across devices\n- Integration with major accounting platforms\n- Automated policy compliance checking\n- Advanced reporting with custom dashboards\n- Mobile-first responsive design\n- Secure document storage and retrieval\n\nSuccess Criteria:\n- 80% reduction in expense processing time\n- 95% accuracy in receipt data extraction\n- 90% employee adoption within 6 months\n- Integration with 95% of popular accounting systems"},{id:"insurance-claims-platform",name:"Digital Insurance Claims Platform",industry:"Financial Services & Fintech",description:"AI-powered insurance claims processing platform with automated assessment and fraud detection",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Python","TensorFlow","PostgreSQL","AWS","Computer Vision"],features:["Claims processing","Damage assessment","Fraud detection","Customer portal","Agent dashboard"],businessGoals:["Accelerate claims processing","Reduce fraud","Improve customer satisfaction"],targetAudience:"Insurance companies, claims adjusters, and policyholders",timeline:"12-18 months",budget:"$600K - $1.8M",successMetrics:["70% faster processing","90% fraud detection","95% customer satisfaction"],risks:["AI accuracy","Regulatory compliance","Integration complexity"],template:"Develop an AI-powered insurance claims platform that automates assessment and accelerates processing.\n\nKey Requirements:\n- AI-powered damage assessment using computer vision\n- Automated fraud detection and risk scoring\n- Customer self-service portal for claim submission\n- Claims adjuster dashboard with workflow management\n- Integration with existing insurance systems\n- Real-time claim status tracking and notifications\n- Mobile app for photo capture and documentation\n- Comprehensive reporting and analytics tools\n\nTechnical Specifications:\n- Computer vision models for damage assessment\n- Machine learning algorithms for fraud detection\n- Real-time image processing and analysis\n- Integration with core insurance systems\n- Automated workflow orchestration\n- Advanced security and data protection\n- Scalable cloud infrastructure\n- Mobile-optimized user interfaces\n\nSuccess Criteria:\n- 70% reduction in claims processing time\n- 90% accuracy in fraud detection\n- 95% customer satisfaction rating\n- Integration with major insurance carriers"},{id:"marketplace-platform",name:"Multi-Vendor Marketplace",industry:"E-commerce & Retail",description:"Comprehensive multi-vendor marketplace with seller management and advanced analytics",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","Stripe","AWS","Elasticsearch"],features:["Vendor management","Product catalog","Order processing","Payment gateway","Analytics dashboard"],businessGoals:["Create marketplace ecosystem","Generate commission revenue","Scale vendor network"],targetAudience:"Online sellers, buyers, and marketplace operators",timeline:"10-15 months",budget:"$400K - $1.2M",successMetrics:["1000+ active vendors","$10M+ GMV","95% uptime"],risks:["Vendor quality control","Payment disputes","Competition"],template:"Build a comprehensive multi-vendor marketplace platform that connects sellers with buyers globally.\n\nKey Requirements:\n- Vendor onboarding and management system\n- Advanced product catalog with search and filtering\n- Integrated payment processing with split payments\n- Order management and fulfillment tracking\n- Review and rating system for vendors and products\n- Commission management and payout automation\n- Mobile-responsive design with PWA capabilities\n- Advanced analytics and reporting dashboard\n\nTechnical Specifications:\n- Microservices architecture for scalability\n- Elasticsearch for advanced product search\n- Real-time inventory management across vendors\n- Automated commission calculation and distribution\n- Multi-currency and multi-language support\n- Advanced fraud detection and prevention\n- CDN integration for fast global content delivery\n- Comprehensive API for third-party integrations\n\nSuccess Criteria:\n- Onboard 1000+ active vendors within first year\n- Achieve $10M+ gross merchandise value (GMV)\n- Maintain 95% platform uptime\n- Process 100,000+ orders monthly"},{id:"fashion-ecommerce",name:"Fashion E-commerce Platform",industry:"E-commerce & Retail",description:"AI-powered fashion e-commerce with virtual try-on and personalized recommendations",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","TensorFlow","PostgreSQL","AWS","AR.js"],features:["Virtual try-on","Style recommendations","Size matching","Social shopping","Inventory management"],businessGoals:["Reduce returns","Increase conversions","Enhance shopping experience"],targetAudience:"Fashion-conscious consumers and clothing retailers",timeline:"12-16 months",budget:"$500K - $1.5M",successMetrics:["30% reduction in returns","25% increase in conversion","4.5+ user rating"],risks:["AR technology adoption","Size accuracy","Fashion trend changes"],template:"Create an innovative fashion e-commerce platform with AI-powered features and virtual try-on capabilities.\n\nKey Requirements:\n- AI-powered virtual try-on using augmented reality\n- Personalized style recommendations based on preferences\n- Advanced size matching and fit prediction\n- Social shopping features with style sharing\n- Comprehensive inventory management system\n- Integration with fashion brands and suppliers\n- Mobile-first design with AR capabilities\n- Advanced search with visual similarity matching\n\nTechnical Specifications:\n- Computer vision for virtual try-on and fit analysis\n- Machine learning for personalized recommendations\n- Augmented reality integration for mobile devices\n- Real-time inventory synchronization\n- Advanced image processing and optimization\n- Social media integration for style sharing\n- Progressive web app for mobile experience\n- Analytics dashboard for fashion insights\n\nSuccess Criteria:\n- 30% reduction in return rates through better fit\n- 25% increase in conversion rates\n- 4.5+ average user rating in app stores\n- Integration with 100+ fashion brands"},{id:"grocery-delivery-app",name:"Grocery Delivery Platform",industry:"E-commerce & Retail",description:"On-demand grocery delivery platform with real-time tracking and inventory management",projectType:"mobile-application",platform:"mobile",complexity:"intermediate",technologies:["React Native","Node.js","PostgreSQL","Stripe","Google Maps","Firebase"],features:["Product browsing","Real-time tracking","Delivery scheduling","Payment processing","Inventory sync"],businessGoals:["Provide convenient shopping","Optimize delivery routes","Increase customer retention"],targetAudience:"Busy consumers, families, and grocery stores",timeline:"8-12 months",budget:"$250K - $750K",successMetrics:["30-minute delivery","95% on-time delivery","4.8+ app rating"],risks:["Delivery logistics","Inventory accuracy","Driver availability"],template:"Build a comprehensive grocery delivery platform that provides fast and reliable service to customers.\n\nKey Requirements:\n- Intuitive product browsing with categories and search\n- Real-time inventory synchronization with stores\n- Flexible delivery scheduling and time slots\n- Live order tracking with GPS integration\n- Multiple payment options and secure processing\n- Driver management and route optimization\n- Customer support and order management\n- Loyalty programs and promotional campaigns\n\nTechnical Specifications:\n- Cross-platform mobile development (iOS/Android)\n- Real-time GPS tracking and route optimization\n- Integration with grocery store POS systems\n- Automated inventory management and updates\n- Push notifications for order status updates\n- Advanced analytics for demand forecasting\n- Driver app with navigation and order management\n- Admin dashboard for operations management\n\nSuccess Criteria:\n- Achieve 30-minute average delivery time\n- Maintain 95% on-time delivery rate\n- 4.8+ average app store rating\n- Process 10,000+ orders monthly"},{id:"subscription-box-platform",name:"Subscription Box Service",industry:"E-commerce & Retail",description:"Customizable subscription box platform with curation algorithms and customer preferences",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","Stripe","AWS","Machine Learning"],features:["Subscription management","Product curation","Customer preferences","Billing automation","Analytics"],businessGoals:["Build recurring revenue","Increase customer lifetime value","Personalize experiences"],targetAudience:"Subscription box enthusiasts and niche product consumers",timeline:"6-10 months",budget:"$200K - $600K",successMetrics:["90% retention rate","$100 average LTV","95% satisfaction"],risks:["Customer churn","Inventory management","Shipping costs"],template:"Create a personalized subscription box platform that delivers curated products based on customer preferences.\n\nKey Requirements:\n- Flexible subscription management with pause/skip options\n- AI-powered product curation based on preferences\n- Customer preference profiling and feedback system\n- Automated billing and payment processing\n- Inventory management with supplier integration\n- Shipping and logistics management\n- Customer portal for subscription customization\n- Analytics dashboard for business insights\n\nTechnical Specifications:\n- Machine learning algorithms for product recommendations\n- Automated subscription billing and dunning management\n- Integration with shipping carriers and tracking\n- Customer feedback and rating system\n- Inventory forecasting and procurement automation\n- Mobile-responsive customer portal\n- Advanced analytics and cohort analysis\n- Integration with e-commerce platforms\n\nSuccess Criteria:\n- Achieve 90% monthly customer retention rate\n- $100+ average customer lifetime value\n- 95% customer satisfaction rating\n- Process 50,000+ subscription boxes monthly"},{id:"b2b-wholesale-platform",name:"B2B Wholesale Marketplace",industry:"E-commerce & Retail",description:"B2B wholesale platform connecting manufacturers with retailers and distributors",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","Stripe","AWS","ERP Integration"],features:["Bulk ordering","Price negotiation","Credit management","Logistics coordination","Analytics"],businessGoals:["Connect B2B buyers and sellers","Streamline wholesale processes","Increase trade volume"],targetAudience:"Manufacturers, wholesalers, retailers, and distributors",timeline:"12-18 months",budget:"$500K - $1.5M",successMetrics:["$50M+ trade volume","500+ active buyers","99% order accuracy"],risks:["Credit risk","Logistics complexity","Market competition"],template:"Build a comprehensive B2B wholesale marketplace that facilitates large-scale trade between businesses.\n\nKey Requirements:\n- Advanced product catalog with bulk pricing tiers\n- Quote request and negotiation system\n- Credit management and payment terms\n- Bulk order processing and fulfillment\n- Logistics coordination and shipping management\n- Supplier verification and quality assurance\n- Integration with ERP and accounting systems\n- Advanced analytics and market insights\n\nTechnical Specifications:\n- Enterprise-grade security and compliance\n- Integration with major ERP systems (SAP, Oracle)\n- Advanced search and filtering for B2B products\n- Automated credit checking and approval workflows\n- Real-time inventory management across suppliers\n- Comprehensive reporting and analytics dashboard\n- API integrations for third-party logistics\n- Multi-currency and international trade support\n\nSuccess Criteria:\n- Facilitate $50M+ in annual trade volume\n- Onboard 500+ active business buyers\n- Achieve 99% order accuracy and fulfillment\n- Process 10,000+ B2B transactions monthly"},{id:"online-learning-platform",name:"Online Learning Platform",industry:"Education & EdTech",description:"Comprehensive online learning platform with interactive courses and progress tracking",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","AWS","WebRTC","TensorFlow"],features:["Course creation","Video streaming","Interactive assessments","Progress tracking","Certification"],businessGoals:["Democratize education","Scale learning delivery","Improve learning outcomes"],targetAudience:"Students, educators, and educational institutions",timeline:"10-15 months",budget:"$400K - $1.2M",successMetrics:["100K+ active learners","85% course completion","4.7+ rating"],risks:["Content quality","Technology adoption","Competition"],template:"Create a comprehensive online learning platform that delivers engaging educational experiences at scale.\n\nKey Requirements:\n- Intuitive course creation tools for educators\n- High-quality video streaming with adaptive bitrate\n- Interactive assessments and quizzes with instant feedback\n- Comprehensive progress tracking and analytics\n- Certification and badge system for achievements\n- Discussion forums and peer collaboration tools\n- Mobile-responsive design with offline capabilities\n- Integration with existing educational systems (LTI)\n\nTechnical Specifications:\n- Scalable video delivery with CDN integration\n- Real-time collaboration tools for group projects\n- AI-powered content recommendations\n- Advanced analytics for learning insights\n- Secure payment processing for course purchases\n- Multi-language support and accessibility features\n- API integrations with educational tools\n- Comprehensive admin dashboard for institutions\n\nSuccess Criteria:\n- Onboard 100,000+ active learners\n- Achieve 85% average course completion rate\n- Maintain 4.7+ average course rating\n- Support 1,000+ concurrent video streams"},{id:"student-information-system",name:"Student Information System",industry:"Education & EdTech",description:"Comprehensive SIS for K-12 schools with gradebook, attendance, and parent communication",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","AWS","Twilio","Chart.js"],features:["Student records","Gradebook","Attendance tracking","Parent portal","Reporting"],businessGoals:["Streamline school administration","Improve parent engagement","Enhance student outcomes"],targetAudience:"K-12 schools, teachers, students, and parents",timeline:"12-18 months",budget:"$300K - $900K",successMetrics:["99% data accuracy","90% parent engagement","50% admin time savings"],risks:["Data privacy","System integration","User training"],template:"Build a comprehensive student information system that manages all aspects of K-12 school operations.\n\nKey Requirements:\n- Complete student record management with academic history\n- Digital gradebook with standards-based grading\n- Automated attendance tracking with notifications\n- Parent portal with real-time access to student progress\n- Comprehensive reporting and analytics dashboard\n- Integration with state reporting systems\n- Mobile app for teachers and parents\n- Secure communication tools between stakeholders\n\nTechnical Specifications:\n- FERPA-compliant data security and privacy\n- Integration with existing school systems (HR, Finance)\n- Real-time synchronization across all modules\n- Advanced reporting with custom dashboard creation\n- Automated notifications via email and SMS\n- Role-based access control for different user types\n- Backup and disaster recovery systems\n- API integrations with educational tools\n\nSuccess Criteria:\n- Achieve 99% data accuracy across all records\n- 90% parent engagement through portal usage\n- 50% reduction in administrative processing time\n- Full compliance with educational data regulations"},{id:"language-learning-app",name:"AI Language Learning App",industry:"Education & EdTech",description:"AI-powered language learning app with speech recognition and personalized curriculum",projectType:"mobile-application",platform:"mobile",complexity:"advanced",technologies:["React Native","Python","TensorFlow","PostgreSQL","AWS","Speech API"],features:["Speech recognition","Adaptive learning","Gamification","Progress tracking","Cultural content"],businessGoals:["Make language learning accessible","Improve learning efficiency","Increase user engagement"],targetAudience:"Language learners of all ages and proficiency levels",timeline:"12-16 months",budget:"$500K - $1.5M",successMetrics:["1M+ downloads","70% retention rate","B2+ proficiency achievement"],risks:["Speech recognition accuracy","Content localization","User motivation"],template:"Develop an AI-powered language learning app that adapts to individual learning styles and pace.\n\nKey Requirements:\n- Advanced speech recognition for pronunciation practice\n- AI-driven adaptive learning curriculum\n- Gamification elements with achievements and streaks\n- Comprehensive progress tracking and analytics\n- Cultural context and real-world conversation practice\n- Offline mode for learning without internet\n- Social features for language exchange\n- Integration with language proficiency standards (CEFR)\n\nTechnical Specifications:\n- Machine learning models for personalized learning paths\n- Advanced speech processing and pronunciation analysis\n- Real-time progress adaptation based on performance\n- Gamification engine with rewards and challenges\n- Cross-platform mobile development (iOS/Android)\n- Offline content synchronization and storage\n- Social networking features for peer interaction\n- Analytics dashboard for learning insights\n\nSuccess Criteria:\n- Achieve 1M+ app downloads within first year\n- Maintain 70% user retention after 30 days\n- Help users achieve B2+ proficiency level\n- Support 20+ languages with native speaker quality"},{id:"virtual-classroom-platform",name:"Virtual Classroom Platform",industry:"Education & EdTech",description:"Interactive virtual classroom with real-time collaboration and engagement tools",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","WebRTC","Socket.io","PostgreSQL","AWS"],features:["Video conferencing","Screen sharing","Interactive whiteboard","Breakout rooms","Recording"],businessGoals:["Enable remote learning","Increase engagement","Reduce technology barriers"],targetAudience:"Educators, students, and educational institutions",timeline:"8-12 months",budget:"$300K - $900K",successMetrics:["500+ concurrent users","95% uptime","4.5+ user satisfaction"],risks:["Bandwidth limitations","Technology adoption","Security concerns"],template:"Create an interactive virtual classroom platform that replicates and enhances in-person learning experiences.\n\nKey Requirements:\n- High-quality video conferencing with screen sharing\n- Interactive whiteboard with real-time collaboration\n- Breakout rooms for small group activities\n- Session recording and playback capabilities\n- Chat and messaging with moderation tools\n- Attendance tracking and engagement analytics\n- Integration with learning management systems\n- Mobile support for tablets and smartphones\n\nTechnical Specifications:\n- WebRTC for peer-to-peer video communication\n- Real-time collaboration using WebSocket connections\n- Scalable architecture supporting 500+ concurrent users\n- Advanced audio/video processing and optimization\n- Cloud recording with automatic transcription\n- Comprehensive security and privacy controls\n- API integrations with popular LMS platforms\n- Responsive design for multiple device types\n\nSuccess Criteria:\n- Support 500+ concurrent users per session\n- Maintain 95% platform uptime during peak hours\n- Achieve 4.5+ user satisfaction rating\n- Process 10,000+ virtual classroom sessions monthly"},{id:"skill-assessment-platform",name:"Skills Assessment Platform",industry:"Education & EdTech",description:"AI-powered skills assessment platform with adaptive testing and competency mapping",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Python","TensorFlow","PostgreSQL","AWS","Proctoring API"],features:["Adaptive testing","Skill mapping","Proctoring","Analytics","Certification"],businessGoals:["Validate skills accurately","Reduce assessment time","Provide actionable insights"],targetAudience:"Educational institutions, employers, and certification bodies",timeline:"10-14 months",budget:"$400K - $1.2M",successMetrics:["95% assessment accuracy","50% time reduction","90% user satisfaction"],risks:["Cheating prevention","Algorithm bias","Technical complexity"],template:"Build an AI-powered skills assessment platform that accurately measures competencies and provides actionable insights.\n\nKey Requirements:\n- Adaptive testing algorithms that adjust difficulty in real-time\n- Comprehensive skill mapping and competency frameworks\n- Advanced proctoring with AI-powered monitoring\n- Detailed analytics and performance insights\n- Automated certification and badge generation\n- Integration with HR systems and job platforms\n- Multi-format questions (multiple choice, coding, simulation)\n- Accessibility features for diverse learners\n\nTechnical Specifications:\n- Machine learning models for adaptive question selection\n- Computer vision for proctoring and identity verification\n- Advanced analytics engine for skill gap analysis\n- Secure test delivery with anti-cheating measures\n- Real-time performance monitoring and alerts\n- Integration APIs for third-party systems\n- Comprehensive reporting and dashboard tools\n- Multi-language support and localization\n\nSuccess Criteria:\n- Achieve 95% assessment accuracy compared to expert evaluation\n- Reduce assessment time by 50% through adaptive testing\n- Maintain 90% user satisfaction rating\n- Process 100,000+ assessments annually"},{id:"real-estate-marketplace",name:"Real Estate Marketplace",industry:"Real Estate & PropTech",description:"Comprehensive real estate marketplace with virtual tours and AI-powered property matching",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","AWS","Google Maps","Three.js"],features:["Property listings","Virtual tours","Search filters","Agent profiles","Market analytics"],businessGoals:["Connect buyers and sellers","Streamline property discovery","Provide market insights"],targetAudience:"Home buyers, sellers, real estate agents, and investors",timeline:"10-15 months",budget:"$400K - $1.2M",successMetrics:["100K+ property listings","1M+ monthly visitors","15% conversion rate"],risks:["Data accuracy","Market competition","Technology adoption"],template:"Create a comprehensive real estate marketplace that revolutionizes property discovery and transactions.\n\nKey Requirements:\n- Advanced property search with AI-powered matching\n- Immersive virtual tours and 360-degree photography\n- Comprehensive property details with market analytics\n- Agent profiles with ratings and transaction history\n- Mortgage calculator and financing options\n- Neighborhood insights and demographic data\n- Mobile app for property viewing and notifications\n- Integration with MLS and real estate databases\n\nTechnical Specifications:\n- AI algorithms for property recommendation and matching\n- 3D virtual tour technology with WebGL/Three.js\n- Advanced mapping with Google Maps integration\n- Real-time property data synchronization\n- Image optimization and CDN for fast loading\n- Advanced search with filters and sorting options\n- Mobile-responsive design with native app features\n- Analytics dashboard for market trends and insights\n\nSuccess Criteria:\n- List 100,000+ active properties across major markets\n- Achieve 1M+ monthly unique visitors\n- Maintain 15% lead-to-sale conversion rate\n- Support virtual tours for 80% of listings"},{id:"property-investment-platform",name:"Property Investment Platform",industry:"Real Estate & PropTech",description:"Real estate investment platform with crowdfunding and portfolio management tools",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","Stripe","AWS","TensorFlow"],features:["Investment opportunities","Portfolio tracking","Due diligence","Returns calculation","Investor dashboard"],businessGoals:["Democratize real estate investing","Provide passive income opportunities","Reduce investment barriers"],targetAudience:"Individual investors, accredited investors, and real estate sponsors",timeline:"12-18 months",budget:"$500K - $1.5M",successMetrics:["$100M+ invested","12% average returns","95% investor satisfaction"],risks:["Regulatory compliance","Market volatility","Due diligence accuracy"],template:"Build a comprehensive real estate investment platform that enables fractional ownership and passive investing.\n\nKey Requirements:\n- Curated investment opportunities with detailed analysis\n- Fractional ownership and crowdfunding capabilities\n- Comprehensive due diligence and property evaluation\n- Real-time portfolio tracking and performance analytics\n- Automated distribution of rental income and profits\n- Investor education and market insights\n- Regulatory compliance and investor accreditation\n- Mobile app for investment monitoring\n\nTechnical Specifications:\n- SEC-compliant investment processing and documentation\n- AI-powered property valuation and risk assessment\n- Automated distribution and tax reporting systems\n- Real-time portfolio performance tracking\n- Integration with property management systems\n- Advanced analytics for investment insights\n- Secure document storage and e-signature capabilities\n- Comprehensive investor dashboard and reporting\n\nSuccess Criteria:\n- Facilitate $100M+ in real estate investments\n- Achieve 12% average annual returns for investors\n- Maintain 95% investor satisfaction rating\n- Process 1,000+ investment transactions annually"},{id:"smart-building-management",name:"Smart Building Management System",industry:"Real Estate & PropTech",description:"IoT-enabled building management platform with energy optimization and predictive maintenance",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","AWS IoT","TensorFlow","MQTT"],features:["IoT monitoring","Energy optimization","Predictive maintenance","Tenant portal","Analytics dashboard"],businessGoals:["Reduce operating costs","Improve tenant satisfaction","Optimize energy usage"],targetAudience:"Property managers, building owners, and commercial tenants",timeline:"12-16 months",budget:"$600K - $1.8M",successMetrics:["30% energy savings","50% maintenance cost reduction","95% tenant satisfaction"],risks:["IoT integration complexity","Data security","Hardware compatibility"],template:"Develop a smart building management system that optimizes operations through IoT and AI technologies.\n\nKey Requirements:\n- Comprehensive IoT sensor integration for monitoring\n- AI-powered energy optimization and demand management\n- Predictive maintenance with equipment health monitoring\n- Tenant portal for service requests and building information\n- Real-time analytics dashboard for building performance\n- Integration with existing building automation systems\n- Mobile app for facility managers and maintenance teams\n- Advanced reporting and compliance tools\n\nTechnical Specifications:\n- IoT device management with MQTT protocol\n- Machine learning models for predictive analytics\n- Real-time data processing and alerting systems\n- Integration with HVAC, lighting, and security systems\n- Cloud-based architecture with edge computing\n- Advanced visualization and dashboard tools\n- API integrations with third-party building systems\n- Comprehensive security and access control\n\nSuccess Criteria:\n- Achieve 30% reduction in energy consumption\n- Reduce maintenance costs by 50% through predictive analytics\n- Maintain 95% tenant satisfaction rating\n- Monitor 10,000+ IoT devices across multiple buildings"},{id:"rental-management-platform",name:"Rental Property Management",industry:"Real Estate & PropTech",description:"Comprehensive rental property management platform with tenant screening and maintenance tracking",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","Stripe","Twilio","AWS"],features:["Property listings","Tenant screening","Rent collection","Maintenance requests","Financial reporting"],businessGoals:["Streamline property management","Improve tenant relations","Maximize rental income"],targetAudience:"Property managers, landlords, and tenants",timeline:"8-12 months",budget:"$250K - $750K",successMetrics:["95% rent collection","24-hour maintenance response","90% tenant retention"],risks:["Tenant screening accuracy","Payment processing issues","Maintenance coordination"],template:"Create a comprehensive rental property management platform that automates operations and improves tenant experiences.\n\nKey Requirements:\n- Online property listings with virtual tours\n- Automated tenant screening with credit and background checks\n- Digital lease signing and document management\n- Automated rent collection with late fee processing\n- Maintenance request system with vendor coordination\n- Financial reporting and expense tracking\n- Tenant portal for payments and communication\n- Mobile app for property managers and tenants\n\nTechnical Specifications:\n- Integration with credit reporting agencies\n- Automated payment processing with ACH and credit cards\n- Document management with e-signature capabilities\n- Real-time communication tools for tenants and managers\n- Advanced reporting and analytics dashboard\n- Integration with accounting systems (QuickBooks, etc.)\n- Mobile-responsive design with native app features\n- Comprehensive security and data protection\n\nSuccess Criteria:\n- Achieve 95% on-time rent collection rate\n- Respond to maintenance requests within 24 hours\n- Maintain 90% tenant retention rate\n- Manage 10,000+ rental units across multiple properties"},{id:"construction-project-management",name:"Construction Project Management",industry:"Real Estate & PropTech",description:"Digital construction management platform with project tracking and collaboration tools",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","AWS","AutoCAD API","Drone API"],features:["Project planning","Progress tracking","Document management","Team collaboration","Budget monitoring"],businessGoals:["Improve project efficiency","Reduce construction delays","Enhance collaboration"],targetAudience:"Construction companies, project managers, and contractors",timeline:"10-14 months",budget:"$400K - $1.2M",successMetrics:["20% faster completion","15% cost savings","95% on-time delivery"],risks:["Project complexity","Team adoption","Integration challenges"],template:"Build a comprehensive construction project management platform that streamlines workflows and improves collaboration.\n\nKey Requirements:\n- Comprehensive project planning with Gantt charts and timelines\n- Real-time progress tracking with photo documentation\n- Document management with version control and approvals\n- Team collaboration tools with role-based access\n- Budget monitoring and cost tracking with alerts\n- Integration with CAD software and building plans\n- Mobile app for field workers and site managers\n- Reporting and analytics for project insights\n\nTechnical Specifications:\n- Integration with AutoCAD and BIM software\n- Real-time collaboration with WebSocket connections\n- Document versioning and approval workflows\n- Mobile-first design for field use\n- Advanced project analytics and reporting\n- Integration with accounting and ERP systems\n- Drone integration for aerial progress monitoring\n- Comprehensive security and access control\n\nSuccess Criteria:\n- Achieve 20% faster project completion times\n- Reduce project costs by 15% through better planning\n- Maintain 95% on-time project delivery rate\n- Manage 500+ concurrent construction projects"},{id:"smart-factory-platform",name:"Smart Factory Management Platform",industry:"Manufacturing & IoT",description:"IoT-enabled smart factory platform with predictive maintenance and production optimization",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","AWS IoT","TensorFlow","InfluxDB"],features:["Production monitoring","Predictive maintenance","Quality control","Inventory management","Analytics dashboard"],businessGoals:["Increase production efficiency","Reduce downtime","Improve product quality"],targetAudience:"Manufacturing companies, plant managers, and operations teams",timeline:"12-18 months",budget:"$600K - $2M",successMetrics:["25% efficiency increase","40% downtime reduction","99.5% quality rate"],risks:["IoT integration complexity","Legacy system compatibility","Data security"],template:"Build a comprehensive smart factory platform that leverages IoT and AI to optimize manufacturing operations.\n\nKey Requirements:\n- Real-time production monitoring with IoT sensors\n- AI-powered predictive maintenance for equipment\n- Automated quality control with computer vision\n- Intelligent inventory management and supply chain optimization\n- Energy consumption monitoring and optimization\n- Worker safety monitoring and alert systems\n- Integration with existing ERP and MES systems\n- Advanced analytics dashboard for operational insights\n\nTechnical Specifications:\n- IoT device management with industrial protocols (OPC-UA, Modbus)\n- Machine learning models for predictive analytics\n- Time-series database for sensor data storage\n- Real-time data processing and alerting systems\n- Computer vision for quality inspection\n- Edge computing for low-latency processing\n- Comprehensive security for industrial networks\n- API integrations with manufacturing systems\n\nSuccess Criteria:\n- Achieve 25% increase in production efficiency\n- Reduce unplanned downtime by 40%\n- Maintain 99.5% product quality rate\n- Monitor 10,000+ IoT devices across production lines"},{id:"supply-chain-optimization",name:"Supply Chain Optimization Platform",industry:"Manufacturing & IoT",description:"AI-powered supply chain platform with demand forecasting and logistics optimization",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Python","TensorFlow","PostgreSQL","AWS","Blockchain"],features:["Demand forecasting","Inventory optimization","Supplier management","Logistics tracking","Risk assessment"],businessGoals:["Optimize inventory levels","Reduce supply chain costs","Improve delivery times"],targetAudience:"Supply chain managers, procurement teams, and logistics coordinators",timeline:"10-15 months",budget:"$500K - $1.5M",successMetrics:["30% inventory reduction","20% cost savings","95% on-time delivery"],risks:["Demand volatility","Supplier reliability","Integration complexity"],template:"Create an AI-powered supply chain optimization platform that enhances efficiency and reduces costs.\n\nKey Requirements:\n- Advanced demand forecasting using machine learning\n- Intelligent inventory optimization with safety stock calculations\n- Comprehensive supplier management and performance tracking\n- Real-time logistics tracking and route optimization\n- Risk assessment and mitigation strategies\n- Blockchain integration for supply chain transparency\n- Integration with ERP and procurement systems\n- Advanced analytics and reporting dashboard\n\nTechnical Specifications:\n- Machine learning models for demand prediction\n- Optimization algorithms for inventory and logistics\n- Real-time tracking with GPS and IoT integration\n- Blockchain for supply chain traceability\n- API integrations with suppliers and logistics providers\n- Advanced analytics and visualization tools\n- Mobile app for field operations and tracking\n- Comprehensive security and data protection\n\nSuccess Criteria:\n- Reduce inventory holding costs by 30%\n- Achieve 20% overall supply chain cost savings\n- Maintain 95% on-time delivery performance\n- Process 100,000+ supply chain transactions monthly"},{id:"industrial-iot-platform",name:"Industrial IoT Management Platform",industry:"Manufacturing & IoT",description:"Comprehensive IIoT platform for device management, data analytics, and remote monitoring",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","InfluxDB","AWS IoT","TensorFlow","MQTT"],features:["Device management","Data visualization","Remote monitoring","Predictive analytics","Alert systems"],businessGoals:["Enable digital transformation","Improve operational visibility","Reduce maintenance costs"],targetAudience:"Industrial companies, IoT engineers, and operations managers",timeline:"12-16 months",budget:"$500K - $1.5M",successMetrics:["50K+ connected devices","99.9% uptime","60% maintenance savings"],risks:["Device compatibility","Network reliability","Data security"],template:"Develop a comprehensive Industrial IoT platform that connects, monitors, and optimizes industrial operations.\n\nKey Requirements:\n- Scalable device management for thousands of IoT sensors\n- Real-time data visualization with customizable dashboards\n- Remote monitoring and control capabilities\n- Predictive analytics for equipment health and performance\n- Automated alert and notification systems\n- Edge computing for low-latency processing\n- Integration with existing industrial systems\n- Comprehensive security and access control\n\nTechnical Specifications:\n- Support for multiple IoT protocols (MQTT, CoAP, OPC-UA)\n- Time-series database for efficient data storage\n- Real-time data streaming and processing\n- Machine learning models for predictive maintenance\n- Edge computing deployment for critical applications\n- Advanced visualization with 3D plant models\n- API integrations for third-party systems\n- Enterprise-grade security and compliance\n\nSuccess Criteria:\n- Connect and manage 50,000+ IoT devices\n- Achieve 99.9% platform uptime and reliability\n- Reduce maintenance costs by 60% through predictive analytics\n- Process 1M+ sensor readings per minute"},{id:"quality-management-system",name:"Digital Quality Management System",industry:"Manufacturing & IoT",description:"Comprehensive QMS with automated inspections, compliance tracking, and corrective actions",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","AWS","Computer Vision","OCR"],features:["Quality inspections","Compliance tracking","Corrective actions","Document control","Audit management"],businessGoals:["Ensure product quality","Maintain compliance","Reduce defects"],targetAudience:"Quality managers, inspectors, and compliance teams",timeline:"8-12 months",budget:"$300K - $900K",successMetrics:["99% compliance rate","50% defect reduction","90% audit success"],risks:["Regulatory changes","Process complexity","User adoption"],template:"Build a comprehensive digital quality management system that ensures compliance and continuous improvement.\n\nKey Requirements:\n- Digital quality inspection workflows with mobile support\n- Automated compliance tracking and reporting\n- Corrective and preventive action (CAPA) management\n- Document control with version management\n- Audit management and preparation tools\n- Statistical process control (SPC) with real-time monitoring\n- Integration with manufacturing execution systems\n- Training management and competency tracking\n\nTechnical Specifications:\n- Computer vision for automated quality inspections\n- OCR technology for document digitization\n- Real-time statistical analysis and control charts\n- Workflow automation for quality processes\n- Integration with ERP and manufacturing systems\n- Mobile app for field inspections and data collection\n- Advanced reporting and analytics dashboard\n- Compliance templates for industry standards (ISO, FDA)\n\nSuccess Criteria:\n- Achieve 99% regulatory compliance rate\n- Reduce product defects by 50%\n- Pass 90% of external audits on first attempt\n- Process 10,000+ quality inspections monthly"},{id:"asset-tracking-system",name:"Industrial Asset Tracking System",industry:"Manufacturing & IoT",description:"RFID/IoT-enabled asset tracking platform with maintenance scheduling and lifecycle management",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","AWS","RFID","GPS"],features:["Asset tracking","Maintenance scheduling","Lifecycle management","Location monitoring","Reporting"],businessGoals:["Improve asset utilization","Reduce asset loss","Optimize maintenance"],targetAudience:"Asset managers, maintenance teams, and operations staff",timeline:"6-10 months",budget:"$200K - $600K",successMetrics:["99% asset visibility","30% utilization increase","25% maintenance savings"],risks:["RFID implementation","Data accuracy","System integration"],template:"Create a comprehensive asset tracking system that provides real-time visibility and optimizes asset management.\n\nKey Requirements:\n- Real-time asset tracking with RFID and GPS technology\n- Comprehensive asset database with specifications and history\n- Automated maintenance scheduling based on usage and time\n- Asset lifecycle management from procurement to disposal\n- Location monitoring and geofencing capabilities\n- Mobile app for asset scanning and updates\n- Integration with ERP and maintenance systems\n- Advanced reporting and analytics dashboard\n\nTechnical Specifications:\n- RFID reader integration for automated tracking\n- GPS tracking for mobile and outdoor assets\n- Barcode and QR code scanning capabilities\n- Real-time location updates and alerts\n- Integration with CMMS and ERP systems\n- Mobile app for field asset management\n- Advanced analytics for asset optimization\n- Comprehensive security and access control\n\nSuccess Criteria:\n- Achieve 99% asset visibility and tracking accuracy\n- Increase asset utilization by 30%\n- Reduce maintenance costs by 25%\n- Track 100,000+ assets across multiple facilities"},{id:"streaming-platform",name:"Video Streaming Platform",industry:"Media & Entertainment",description:"Scalable video streaming platform with content management and personalized recommendations",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","AWS","CDN","TensorFlow","FFmpeg"],features:["Video streaming","Content management","User profiles","Recommendations","Analytics"],businessGoals:["Deliver high-quality streaming","Increase user engagement","Monetize content"],targetAudience:"Content creators, viewers, and media companies",timeline:"12-18 months",budget:"$600K - $2M",successMetrics:["1M+ concurrent streams","80% user retention","4K streaming quality"],risks:["Bandwidth costs","Content licensing","Competition"],template:"Build a scalable video streaming platform that delivers high-quality content with personalized experiences.\n\nKey Requirements:\n- Adaptive bitrate streaming for optimal quality\n- Comprehensive content management system\n- User profiles with viewing history and preferences\n- AI-powered content recommendations\n- Multi-device support (web, mobile, TV, gaming consoles)\n- Live streaming capabilities with real-time chat\n- Content protection and DRM integration\n- Advanced analytics and viewer insights\n\nTechnical Specifications:\n- CDN integration for global content delivery\n- Video transcoding and optimization pipeline\n- Machine learning for personalized recommendations\n- Real-time streaming with low latency\n- Scalable architecture supporting millions of users\n- Advanced video player with adaptive streaming\n- Content protection with digital rights management\n- Comprehensive analytics and reporting dashboard\n\nSuccess Criteria:\n- Support 1M+ concurrent video streams\n- Achieve 80% user retention after 30 days\n- Deliver 4K streaming quality with minimal buffering\n- Process 100TB+ of video content monthly"},{id:"music-streaming-app",name:"Music Streaming Application",industry:"Media & Entertainment",description:"AI-powered music streaming platform with social features and artist collaboration tools",projectType:"mobile-application",platform:"mobile",complexity:"advanced",technologies:["React Native","Node.js","PostgreSQL","AWS","TensorFlow","Spotify API"],features:["Music streaming","Playlist creation","Social sharing","Artist profiles","Discovery"],businessGoals:["Build music community","Support artists","Increase user engagement"],targetAudience:"Music lovers, artists, and content creators",timeline:"10-15 months",budget:"$400K - $1.2M",successMetrics:["10M+ songs catalog","5M+ active users","90% user satisfaction"],risks:["Music licensing","Artist acquisition","Platform competition"],template:"Create an innovative music streaming platform that connects artists with fans and builds music communities.\n\nKey Requirements:\n- High-quality audio streaming with offline capabilities\n- AI-powered music discovery and recommendations\n- Social features with playlist sharing and collaboration\n- Artist profiles with direct fan engagement\n- Podcast and audio content support\n- Live streaming for concerts and events\n- Music creation tools and collaboration features\n- Advanced search and music discovery\n\nTechnical Specifications:\n- High-quality audio streaming with lossless options\n- Machine learning for music recommendation algorithms\n- Real-time social features and messaging\n- Integration with music distribution platforms\n- Cross-platform mobile development (iOS/Android)\n- Offline music storage and synchronization\n- Advanced audio processing and equalization\n- Comprehensive analytics for artists and labels\n\nSuccess Criteria:\n- Build catalog of 10M+ licensed songs\n- Achieve 5M+ monthly active users\n- Maintain 90% user satisfaction rating\n- Support 100,000+ independent artists"},{id:"content-creation-platform",name:"Content Creation Platform",industry:"Media & Entertainment",description:"All-in-one content creation platform with editing tools and collaboration features",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","WebGL","FFmpeg","AWS","WebRTC"],features:["Video editing","Audio editing","Collaboration tools","Asset library","Publishing"],businessGoals:["Democratize content creation","Enable collaboration","Streamline workflows"],targetAudience:"Content creators, video editors, and creative teams",timeline:"12-16 months",budget:"$500K - $1.5M",successMetrics:["1M+ projects created","500K+ active creators","4.8+ user rating"],risks:["Performance optimization","Browser compatibility","Feature complexity"],template:"Build a comprehensive content creation platform that empowers creators with professional-grade tools.\n\nKey Requirements:\n- Browser-based video and audio editing with timeline interface\n- Real-time collaboration with multiple editors\n- Comprehensive asset library with stock media\n- AI-powered editing assistance and automation\n- Multi-format export and publishing options\n- Cloud storage with version control\n- Template library for quick content creation\n- Integration with social media platforms\n\nTechnical Specifications:\n- WebGL-based video rendering and effects\n- Real-time collaboration using WebRTC\n- Cloud-based media processing and storage\n- AI algorithms for automated editing suggestions\n- Progressive web app for offline editing\n- Advanced timeline interface with precision controls\n- Multi-format media support and conversion\n- Comprehensive project management and sharing\n\nSuccess Criteria:\n- Enable creation of 1M+ video projects\n- Onboard 500,000+ active content creators\n- Achieve 4.8+ average user rating\n- Process 10PB+ of media content annually"},{id:"podcast-platform",name:"Podcast Hosting Platform",industry:"Media & Entertainment",description:"Complete podcast hosting and distribution platform with analytics and monetization tools",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","AWS","Stripe","RSS"],features:["Podcast hosting","Distribution","Analytics","Monetization","Audience engagement"],businessGoals:["Support podcast creators","Enable monetization","Grow podcast ecosystem"],targetAudience:"Podcast creators, listeners, and media companies",timeline:"8-12 months",budget:"$250K - $750K",successMetrics:["100K+ podcasts hosted","10M+ downloads monthly","95% uptime"],risks:["Content moderation","Bandwidth costs","Platform competition"],template:"Create a comprehensive podcast platform that supports creators from recording to monetization.\n\nKey Requirements:\n- Easy podcast upload and hosting with unlimited storage\n- Automatic distribution to major podcast platforms\n- Comprehensive analytics with listener demographics\n- Monetization tools including ads and subscriptions\n- Audience engagement features with comments and ratings\n- Recording and editing tools for content creation\n- RSS feed management and customization\n- Mobile app for podcast management and listening\n\nTechnical Specifications:\n- Scalable audio hosting with global CDN\n- Automated distribution to Apple Podcasts, Spotify, etc.\n- Advanced analytics with real-time listener tracking\n- Payment processing for subscriptions and donations\n- Audio processing and optimization pipeline\n- RSS feed generation and management\n- Mobile-responsive design with native app features\n- Comprehensive creator dashboard and tools\n\nSuccess Criteria:\n- Host 100,000+ active podcasts\n- Deliver 10M+ podcast downloads monthly\n- Maintain 95% platform uptime\n- Generate $1M+ in creator revenue annually"},{id:"live-streaming-platform",name:"Live Streaming Platform",industry:"Media & Entertainment",description:"Interactive live streaming platform with real-time chat and monetization features",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","WebRTC","Socket.io","AWS","Stripe"],features:["Live streaming","Real-time chat","Virtual gifts","Subscriptions","Analytics"],businessGoals:["Enable live content creation","Build creator economy","Increase engagement"],targetAudience:"Live streamers, content creators, and viewers",timeline:"10-14 months",budget:"$400K - $1.2M",successMetrics:["100K+ concurrent viewers","50K+ active streamers","$5M+ creator earnings"],risks:["Streaming quality","Content moderation","Monetization balance"],template:"Build an interactive live streaming platform that empowers creators and engages audiences in real-time.\n\nKey Requirements:\n- High-quality live video streaming with low latency\n- Real-time chat with moderation tools\n- Virtual gifts and tipping system for monetization\n- Subscription and membership features\n- Stream recording and highlight creation\n- Multi-platform streaming (simultaneous broadcast)\n- Creator dashboard with analytics and earnings\n- Mobile app for streaming and viewing\n\nTechnical Specifications:\n- WebRTC for low-latency live streaming\n- Real-time messaging with Socket.io\n- Scalable architecture supporting 100K+ concurrent viewers\n- Payment processing for virtual gifts and subscriptions\n- Content delivery network for global streaming\n- Advanced moderation tools and AI content filtering\n- Mobile streaming with camera and screen capture\n- Comprehensive analytics and revenue tracking\n\nSuccess Criteria:\n- Support 100,000+ concurrent viewers during peak times\n- Onboard 50,000+ active streamers\n- Generate $5M+ in creator earnings annually\n- Achieve sub-3 second streaming latency"},{id:"hotel-booking-platform",name:"Hotel Booking Platform",industry:"Travel & Hospitality",description:"Comprehensive hotel booking platform with real-time availability and dynamic pricing",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","Stripe","Google Maps","Redis"],features:["Hotel search","Real-time booking","Dynamic pricing","Reviews","Mobile app"],businessGoals:["Increase bookings","Optimize pricing","Improve guest experience"],targetAudience:"Travelers, hotels, and hospitality businesses",timeline:"10-15 months",budget:"$400K - $1.2M",successMetrics:["1M+ bookings annually","95% booking accuracy","4.5+ user rating"],risks:["Inventory management","Payment processing","Competition"],template:"Create a comprehensive hotel booking platform that connects travelers with accommodations worldwide.\n\nKey Requirements:\n- Advanced hotel search with filters and map integration\n- Real-time availability and instant booking confirmation\n- Dynamic pricing based on demand and seasonality\n- Comprehensive hotel profiles with photos and amenities\n- Guest review and rating system\n- Multi-currency and multi-language support\n- Mobile app for booking management and check-in\n- Integration with hotel management systems\n\nTechnical Specifications:\n- Real-time inventory management with Redis caching\n- Payment processing with multiple gateways\n- Advanced search with Elasticsearch\n- Map integration with Google Maps API\n- Mobile-responsive design with PWA capabilities\n- Integration with hotel PMS and channel managers\n- Advanced analytics and revenue optimization\n- Comprehensive security and fraud prevention\n\nSuccess Criteria:\n- Process 1M+ hotel bookings annually\n- Achieve 95% booking accuracy and confirmation\n- Maintain 4.5+ average user rating\n- Partner with 100,000+ hotels globally"},{id:"travel-planning-app",name:"AI Travel Planning App",industry:"Travel & Hospitality",description:"AI-powered travel planning app with personalized itineraries and local recommendations",projectType:"mobile-application",platform:"mobile",complexity:"advanced",technologies:["React Native","Python","TensorFlow","PostgreSQL","Google Maps","AWS"],features:["Itinerary planning","Local recommendations","Budget tracking","Social sharing","Offline maps"],businessGoals:["Personalize travel experiences","Increase user engagement","Monetize recommendations"],targetAudience:"Travelers, tourists, and travel enthusiasts",timeline:"8-12 months",budget:"$300K - $900K",successMetrics:["5M+ app downloads","80% trip completion","4.7+ app rating"],risks:["Data accuracy","Local content quality","User adoption"],template:"Build an AI-powered travel planning app that creates personalized itineraries and enhances travel experiences.\n\nKey Requirements:\n- AI-powered itinerary generation based on preferences\n- Local recommendations for restaurants, attractions, and activities\n- Budget tracking and expense management\n- Social features for trip sharing and collaboration\n- Offline maps and navigation capabilities\n- Real-time travel updates and notifications\n- Integration with booking platforms and services\n- Photo sharing and travel journal features\n\nTechnical Specifications:\n- Machine learning for personalized recommendations\n- Integration with travel APIs (flights, hotels, activities)\n- Offline map storage and GPS navigation\n- Real-time data synchronization across devices\n- Social networking features with privacy controls\n- Advanced analytics for travel insights\n- Cross-platform mobile development (iOS/Android)\n- Comprehensive travel database and content management\n\nSuccess Criteria:\n- Achieve 5M+ app downloads within first year\n- 80% of planned trips completed using the app\n- Maintain 4.7+ average app store rating\n- Generate 1M+ personalized itineraries"},{id:"restaurant-management-system",name:"Restaurant Management System",industry:"Food & Restaurant",description:"Complete restaurant management platform with POS, inventory, and staff scheduling",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","Stripe","Twilio","AWS"],features:["POS system","Inventory management","Staff scheduling","Customer management","Analytics"],businessGoals:["Streamline operations","Reduce costs","Improve customer service"],targetAudience:"Restaurant owners, managers, and staff",timeline:"8-12 months",budget:"$250K - $750K",successMetrics:["30% cost reduction","95% order accuracy","90% staff satisfaction"],risks:["Hardware integration","Staff training","System reliability"],template:"Create a comprehensive restaurant management system that optimizes operations and enhances customer experiences.\n\nKey Requirements:\n- Point-of-sale system with order management\n- Real-time inventory tracking and automatic reordering\n- Staff scheduling and time tracking\n- Customer relationship management with loyalty programs\n- Table reservation and waitlist management\n- Kitchen display system for order coordination\n- Financial reporting and analytics dashboard\n- Mobile app for staff and customer interactions\n\nTechnical Specifications:\n- Integration with payment processors and hardware\n- Real-time order synchronization across devices\n- Inventory management with supplier integration\n- Staff scheduling with labor cost optimization\n- Customer data management with privacy compliance\n- Kitchen workflow optimization and timing\n- Advanced reporting and business intelligence\n- Mobile-responsive design with tablet support\n\nSuccess Criteria:\n- Reduce operational costs by 30%\n- Achieve 95% order accuracy and customer satisfaction\n- Improve staff satisfaction to 90%\n- Process 100,000+ orders monthly"},{id:"food-delivery-platform",name:"Food Delivery Platform",industry:"Food & Restaurant",description:"Multi-restaurant food delivery platform with real-time tracking and driver management",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","Stripe","Google Maps","Socket.io"],features:["Restaurant listings","Order management","Real-time tracking","Driver coordination","Payment processing"],businessGoals:["Connect restaurants with customers","Optimize delivery routes","Increase order volume"],targetAudience:"Restaurants, customers, and delivery drivers",timeline:"10-15 months",budget:"$400K - $1.2M",successMetrics:["30-minute delivery","95% on-time delivery","1M+ orders monthly"],risks:["Driver availability","Delivery logistics","Restaurant partnerships"],template:"Build a comprehensive food delivery platform that connects restaurants, customers, and drivers efficiently.\n\nKey Requirements:\n- Restaurant onboarding and menu management\n- Customer app with search, ordering, and payment\n- Real-time order tracking with GPS integration\n- Driver app with route optimization and earnings tracking\n- Restaurant dashboard for order management\n- Dynamic pricing and delivery fee calculation\n- Customer support and dispute resolution\n- Analytics dashboard for all stakeholders\n\nTechnical Specifications:\n- Real-time order processing and status updates\n- GPS tracking and route optimization algorithms\n- Payment processing with split payments to restaurants\n- Push notifications for order status updates\n- Machine learning for delivery time estimation\n- Integration with restaurant POS systems\n- Advanced analytics and reporting dashboard\n- Scalable architecture for high order volume\n\nSuccess Criteria:\n- Achieve 30-minute average delivery time\n- Maintain 95% on-time delivery rate\n- Process 1M+ food orders monthly\n- Partner with 10,000+ restaurants"},{id:"recipe-sharing-platform",name:"Recipe Sharing Community",industry:"Food & Restaurant",description:"Social recipe sharing platform with meal planning and grocery integration",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","AWS","Stripe","Computer Vision"],features:["Recipe sharing","Meal planning","Grocery lists","Social features","Nutrition tracking"],businessGoals:["Build cooking community","Monetize through partnerships","Promote healthy eating"],targetAudience:"Home cooks, food enthusiasts, and health-conscious individuals",timeline:"6-10 months",budget:"$200K - $600K",successMetrics:["1M+ registered users","100K+ recipes shared","4.6+ user rating"],risks:["Content quality","User engagement","Monetization strategy"],template:"Create a vibrant recipe sharing community that helps people discover, plan, and cook delicious meals.\n\nKey Requirements:\n- Recipe creation and sharing with photo uploads\n- Advanced search and filtering by ingredients, diet, cuisine\n- Meal planning calendar with automated grocery lists\n- Social features with following, likes, and comments\n- Nutrition tracking and dietary restriction support\n- Integration with grocery delivery services\n- Video recipe tutorials and cooking tips\n- Personal recipe collections and favorites\n\nTechnical Specifications:\n- Image recognition for recipe ingredient detection\n- Advanced search with ingredient-based filtering\n- Social networking features with user profiles\n- Integration with grocery APIs for shopping lists\n- Nutrition calculation and dietary analysis\n- Video streaming for cooking tutorials\n- Mobile-responsive design with PWA capabilities\n- Content moderation and quality control systems\n\nSuccess Criteria:\n- Build community of 1M+ registered users\n- Facilitate sharing of 100,000+ unique recipes\n- Achieve 4.6+ average user rating\n- Generate 10M+ monthly recipe views"},{id:"fleet-management-system",name:"Fleet Management System",industry:"Logistics & Supply Chain",description:"Comprehensive fleet management platform with GPS tracking and maintenance scheduling",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","AWS","GPS API","IoT"],features:["Vehicle tracking","Route optimization","Maintenance scheduling","Driver management","Fuel monitoring"],businessGoals:["Optimize fleet operations","Reduce fuel costs","Improve safety"],targetAudience:"Fleet managers, logistics companies, and transportation businesses",timeline:"10-14 months",budget:"$400K - $1.2M",successMetrics:["25% fuel savings","30% route optimization","99% vehicle uptime"],risks:["GPS accuracy","Driver adoption","Hardware integration"],template:"Build a comprehensive fleet management system that optimizes vehicle operations and reduces costs.\n\nKey Requirements:\n- Real-time GPS tracking and vehicle monitoring\n- Route optimization and traffic-aware navigation\n- Preventive maintenance scheduling and alerts\n- Driver behavior monitoring and safety scoring\n- Fuel consumption tracking and cost analysis\n- Electronic logging device (ELD) compliance\n- Mobile app for drivers and field operations\n- Advanced analytics and reporting dashboard\n\nTechnical Specifications:\n- Integration with GPS and telematics devices\n- Real-time data processing and alerts\n- Machine learning for route optimization\n- IoT integration for vehicle diagnostics\n- Compliance reporting for transportation regulations\n- Mobile app with offline capabilities\n- Advanced analytics and predictive maintenance\n- Comprehensive security and data protection\n\nSuccess Criteria:\n- Achieve 25% reduction in fuel costs\n- Improve route efficiency by 30%\n- Maintain 99% vehicle uptime through predictive maintenance\n- Manage 10,000+ vehicles across multiple fleets"},{id:"warehouse-management-system",name:"Warehouse Management System",industry:"Logistics & Supply Chain",description:"Advanced WMS with automated inventory tracking and order fulfillment optimization",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","AWS","RFID","Barcode"],features:["Inventory tracking","Order fulfillment","Warehouse optimization","Staff management","Reporting"],businessGoals:["Optimize warehouse operations","Reduce fulfillment time","Improve accuracy"],targetAudience:"Warehouse managers, logistics coordinators, and fulfillment centers",timeline:"8-12 months",budget:"$300K - $900K",successMetrics:["99.5% inventory accuracy","50% faster fulfillment","30% space optimization"],risks:["System integration","Staff training","Inventory complexity"],template:"Create an advanced warehouse management system that automates operations and optimizes fulfillment processes.\n\nKey Requirements:\n- Real-time inventory tracking with RFID and barcode scanning\n- Automated order picking and fulfillment workflows\n- Warehouse layout optimization and slotting\n- Staff task management and productivity tracking\n- Integration with ERP and e-commerce platforms\n- Returns processing and quality control\n- Mobile devices for warehouse operations\n- Advanced reporting and analytics dashboard\n\nTechnical Specifications:\n- Integration with barcode and RFID systems\n- Real-time inventory synchronization\n- Automated workflow orchestration\n- Mobile app for warehouse staff\n- Integration with shipping carriers and systems\n- Advanced analytics for warehouse optimization\n- API integrations with e-commerce platforms\n- Comprehensive security and access control\n\nSuccess Criteria:\n- Achieve 99.5% inventory accuracy\n- Reduce order fulfillment time by 50%\n- Optimize warehouse space utilization by 30%\n- Process 1M+ orders annually"},{id:"smart-grid-management",name:"Smart Grid Management Platform",industry:"Energy & Utilities",description:"IoT-enabled smart grid platform with energy optimization and demand response",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","InfluxDB","AWS IoT","TensorFlow","SCADA"],features:["Grid monitoring","Energy optimization","Demand response","Outage management","Analytics"],businessGoals:["Optimize energy distribution","Reduce outages","Enable renewable integration"],targetAudience:"Utility companies, grid operators, and energy managers",timeline:"15-24 months",budget:"$800K - $2.5M",successMetrics:["20% efficiency gain","50% outage reduction","99.9% grid reliability"],risks:["Critical infrastructure security","Regulatory compliance","System complexity"],template:"Develop a smart grid management platform that optimizes energy distribution and integrates renewable sources.\n\nKey Requirements:\n- Real-time grid monitoring with IoT sensors and smart meters\n- AI-powered energy demand forecasting and optimization\n- Automated demand response and load balancing\n- Outage detection and restoration management\n- Renewable energy integration and storage management\n- Customer energy usage analytics and billing\n- Cybersecurity and critical infrastructure protection\n- Regulatory compliance and reporting tools\n\nTechnical Specifications:\n- Integration with SCADA and grid control systems\n- Time-series database for energy data storage\n- Machine learning for demand prediction and optimization\n- Real-time data processing and alerting\n- Cybersecurity frameworks for critical infrastructure\n- Advanced visualization and control dashboards\n- API integrations with energy markets and systems\n- Comprehensive backup and disaster recovery\n\nSuccess Criteria:\n- Achieve 20% improvement in grid efficiency\n- Reduce power outages by 50%\n- Maintain 99.9% grid reliability\n- Integrate 50% renewable energy sources"},{id:"renewable-energy-platform",name:"Renewable Energy Management",industry:"Energy & Utilities",description:"Comprehensive platform for managing solar, wind, and other renewable energy assets",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Python","InfluxDB","AWS","TensorFlow","Weather API"],features:["Asset monitoring","Performance optimization","Predictive maintenance","Energy trading","Analytics"],businessGoals:["Maximize energy production","Reduce maintenance costs","Optimize trading"],targetAudience:"Renewable energy companies, asset managers, and energy traders",timeline:"12-18 months",budget:"$500K - $1.5M",successMetrics:["95% asset uptime","15% production increase","30% maintenance savings"],risks:["Weather dependency","Equipment reliability","Market volatility"],template:"Build a comprehensive renewable energy management platform that maximizes production and optimizes operations.\n\nKey Requirements:\n- Real-time monitoring of solar, wind, and other renewable assets\n- Weather-based production forecasting and optimization\n- Predictive maintenance for renewable energy equipment\n- Energy trading and market participation tools\n- Performance analytics and benchmarking\n- Integration with grid systems and energy markets\n- Mobile app for field technicians and asset managers\n- Environmental impact tracking and reporting\n\nTechnical Specifications:\n- Integration with renewable energy equipment and inverters\n- Weather data integration for production forecasting\n- Machine learning for performance optimization\n- Time-series database for energy production data\n- Trading algorithms for energy market participation\n- Advanced analytics and visualization tools\n- Mobile app for remote monitoring and maintenance\n- Comprehensive reporting and compliance tools\n\nSuccess Criteria:\n- Maintain 95% renewable asset uptime\n- Increase energy production by 15% through optimization\n- Reduce maintenance costs by 30%\n- Manage 1GW+ of renewable energy capacity"},{id:"precision-farming-platform",name:"Precision Farming Platform",industry:"Agriculture & AgTech",description:"IoT-enabled precision agriculture platform with crop monitoring and yield optimization",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Python","InfluxDB","AWS IoT","TensorFlow","Drone API"],features:["Crop monitoring","Soil analysis","Weather integration","Yield prediction","Equipment tracking"],businessGoals:["Increase crop yields","Reduce resource usage","Optimize farming operations"],targetAudience:"Farmers, agricultural consultants, and agribusiness companies",timeline:"12-16 months",budget:"$400K - $1.2M",successMetrics:["20% yield increase","30% water savings","25% cost reduction"],risks:["Weather dependency","Technology adoption","Data accuracy"],template:"Create a precision farming platform that leverages IoT and AI to optimize agricultural operations and increase yields.\n\nKey Requirements:\n- IoT sensor networks for soil moisture, temperature, and nutrient monitoring\n- Drone integration for aerial crop monitoring and analysis\n- Weather data integration and microclimate monitoring\n- AI-powered yield prediction and crop health analysis\n- Irrigation and fertilizer optimization recommendations\n- Equipment tracking and maintenance scheduling\n- Mobile app for field operations and data collection\n- Integration with farm management systems\n\nTechnical Specifications:\n- IoT device management with agricultural sensors\n- Computer vision for crop health analysis from drone imagery\n- Machine learning for yield prediction and optimization\n- Weather API integration for localized forecasting\n- Time-series database for agricultural data storage\n- Mobile app with offline capabilities for field use\n- Advanced analytics and reporting dashboard\n- Integration with agricultural equipment and systems\n\nSuccess Criteria:\n- Achieve 20% increase in crop yields\n- Reduce water usage by 30% through precision irrigation\n- Lower farming costs by 25%\n- Monitor 100,000+ acres of farmland"},{id:"livestock-management-system",name:"Livestock Management System",industry:"Agriculture & AgTech",description:"Comprehensive livestock tracking and health monitoring platform with RFID and IoT sensors",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","AWS","RFID","IoT"],features:["Animal tracking","Health monitoring","Breeding management","Feed optimization","Veterinary records"],businessGoals:["Improve animal health","Optimize breeding programs","Reduce veterinary costs"],targetAudience:"Livestock farmers, ranchers, and veterinarians",timeline:"8-12 months",budget:"$250K - $750K",successMetrics:["95% animal health tracking","20% breeding efficiency","30% vet cost reduction"],risks:["Animal welfare concerns","Technology durability","Data privacy"],template:"Build a comprehensive livestock management system that monitors animal health and optimizes farm operations.\n\nKey Requirements:\n- RFID tagging and tracking for individual animal identification\n- Health monitoring with wearable sensors and alerts\n- Breeding program management with genetic tracking\n- Feed optimization and nutrition management\n- Veterinary records and treatment history\n- Milk production tracking for dairy operations\n- Mobile app for field operations and animal care\n- Integration with veterinary and feed supplier systems\n\nTechnical Specifications:\n- RFID reader integration for animal identification\n- IoT sensors for health and activity monitoring\n- Real-time alerts for health issues and breeding cycles\n- Genetic database for breeding optimization\n- Mobile app with barcode scanning capabilities\n- Integration with veterinary management systems\n- Advanced analytics for herd performance\n- Comprehensive reporting and compliance tools\n\nSuccess Criteria:\n- Track health status of 95% of livestock\n- Improve breeding efficiency by 20%\n- Reduce veterinary costs by 30%\n- Manage 50,000+ head of livestock"},{id:"sports-analytics-platform",name:"Sports Analytics Platform",industry:"Sports & Fitness",description:"Advanced sports analytics platform with player performance tracking and game analysis",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Python","TensorFlow","PostgreSQL","AWS","Computer Vision"],features:["Performance tracking","Game analysis","Player statistics","Video analysis","Predictive modeling"],businessGoals:["Improve team performance","Optimize player development","Gain competitive advantage"],targetAudience:"Sports teams, coaches, and performance analysts",timeline:"10-15 months",budget:"$400K - $1.2M",successMetrics:["15% performance improvement","90% prediction accuracy","100% coach adoption"],risks:["Data accuracy","Technology complexity","Coach acceptance"],template:"Develop an advanced sports analytics platform that provides deep insights into player and team performance.\n\nKey Requirements:\n- Real-time player performance tracking with wearable sensors\n- Video analysis with computer vision for game breakdown\n- Advanced statistics and performance metrics\n- Predictive modeling for injury prevention and performance\n- Game strategy analysis and opponent scouting\n- Player development tracking and recommendations\n- Mobile app for coaches and players\n- Integration with existing sports management systems\n\nTechnical Specifications:\n- Computer vision for automated video analysis\n- Machine learning for performance prediction and optimization\n- Real-time data processing from wearable devices\n- Advanced statistical analysis and visualization\n- Video streaming and annotation tools\n- Mobile app with real-time performance monitoring\n- Integration with sports equipment and tracking systems\n- Comprehensive reporting and dashboard tools\n\nSuccess Criteria:\n- Achieve 15% improvement in team performance metrics\n- Maintain 90% accuracy in performance predictions\n- Achieve 100% adoption by coaching staff\n- Analyze 1,000+ hours of game footage monthly"},{id:"fitness-coaching-app",name:"AI Fitness Coaching App",industry:"Sports & Fitness",description:"AI-powered personal fitness coaching app with workout generation and progress tracking",projectType:"mobile-application",platform:"mobile",complexity:"advanced",technologies:["React Native","Python","TensorFlow","PostgreSQL","AWS","HealthKit"],features:["Workout generation","Form analysis","Progress tracking","Nutrition guidance","Social features"],businessGoals:["Personalize fitness experiences","Improve user engagement","Reduce trainer costs"],targetAudience:"Fitness enthusiasts, personal trainers, and gym members",timeline:"8-12 months",budget:"$300K - $900K",successMetrics:["1M+ app downloads","80% user retention","90% goal achievement"],risks:["AI accuracy","User motivation","Competition"],template:"Create an AI-powered fitness coaching app that provides personalized workouts and real-time form feedback.\n\nKey Requirements:\n- AI-generated personalized workout plans based on goals and fitness level\n- Computer vision for exercise form analysis and correction\n- Progress tracking with detailed analytics and insights\n- Nutrition guidance and meal planning integration\n- Social features with challenges and community support\n- Integration with wearable devices and health apps\n- Video exercise library with professional demonstrations\n- Personal trainer marketplace and virtual coaching\n\nTechnical Specifications:\n- Machine learning for workout personalization\n- Computer vision for real-time form analysis\n- Integration with HealthKit (iOS) and Google Fit (Android)\n- Real-time video processing and feedback\n- Social networking features with privacy controls\n- Advanced analytics for fitness progress tracking\n- Cross-platform mobile development (iOS/Android)\n- Comprehensive exercise database and content management\n\nSuccess Criteria:\n- Achieve 1M+ app downloads within first year\n- Maintain 80% user retention after 30 days\n- Help 90% of users achieve their fitness goals\n- Process 10M+ workout sessions annually"},{id:"esports-tournament-platform",name:"Esports Tournament Platform",industry:"Gaming & Interactive Media",description:"Comprehensive esports tournament platform with live streaming and prize management",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","WebRTC","Stripe","Socket.io"],features:["Tournament management","Live streaming","Player registration","Prize distribution","Analytics"],businessGoals:["Grow esports ecosystem","Monetize tournaments","Engage gaming community"],targetAudience:"Esports players, tournament organizers, and gaming enthusiasts",timeline:"10-14 months",budget:"$400K - $1.2M",successMetrics:["1000+ tournaments hosted","100K+ registered players","$1M+ prize pool"],risks:["Technical complexity","Player acquisition","Monetization challenges"],template:"Build a comprehensive esports tournament platform that connects players, organizers, and audiences globally.\n\nKey Requirements:\n- Tournament creation and management tools for organizers\n- Player registration and team formation systems\n- Live streaming integration with chat and commentary\n- Automated bracket generation and match scheduling\n- Prize pool management and distribution\n- Anti-cheat integration and fair play monitoring\n- Mobile app for players and spectators\n- Analytics dashboard for tournament insights\n\nTechnical Specifications:\n- Real-time tournament bracket updates\n- Live streaming with low-latency video delivery\n- Payment processing for entry fees and prize distribution\n- Integration with popular gaming platforms and APIs\n- Real-time chat and social features\n- Advanced analytics for player and tournament performance\n- Mobile-responsive design with native app features\n- Comprehensive security and anti-fraud measures\n\nSuccess Criteria:\n- Host 1,000+ tournaments annually\n- Register 100,000+ active players\n- Distribute $1M+ in tournament prizes\n- Stream 10,000+ hours of live esports content"},{id:"volunteer-management-platform",name:"Volunteer Management Platform",industry:"Non-profit & Social Impact",description:"Comprehensive volunteer management platform with scheduling and impact tracking",projectType:"web-application",platform:"web",complexity:"intermediate",technologies:["React","Node.js","PostgreSQL","Stripe","Twilio","AWS"],features:["Volunteer registration","Event scheduling","Impact tracking","Communication tools","Reporting"],businessGoals:["Increase volunteer engagement","Streamline operations","Measure social impact"],targetAudience:"Non-profit organizations, volunteers, and community groups",timeline:"6-10 months",budget:"$200K - $600K",successMetrics:["10K+ active volunteers","90% event attendance","50% admin time savings"],risks:["Volunteer retention","Technology adoption","Funding constraints"],template:"Create a volunteer management platform that helps non-profits organize, engage, and track volunteer activities.\n\nKey Requirements:\n- Volunteer registration and profile management\n- Event creation and scheduling with automated notifications\n- Skill-based volunteer matching for optimal placement\n- Impact tracking and measurement tools\n- Communication tools for volunteer coordination\n- Training module management and certification tracking\n- Mobile app for volunteers and coordinators\n- Integration with fundraising and CRM systems\n\nTechnical Specifications:\n- User management with role-based access control\n- Automated email and SMS notifications\n- Calendar integration for event scheduling\n- Mobile-responsive design with PWA capabilities\n- Integration with popular CRM and fundraising platforms\n- Advanced reporting and analytics dashboard\n- Volunteer hour tracking and verification\n- Comprehensive security and data protection\n\nSuccess Criteria:\n- Engage 10,000+ active volunteers\n- Achieve 90% volunteer event attendance rate\n- Reduce administrative time by 50%\n- Track 1M+ volunteer hours annually"},{id:"donation-crowdfunding-platform",name:"Donation & Crowdfunding Platform",industry:"Non-profit & Social Impact",description:"Social impact crowdfunding platform with transparent donation tracking and impact reporting",projectType:"web-application",platform:"web",complexity:"advanced",technologies:["React","Node.js","PostgreSQL","Stripe","Blockchain","AWS"],features:["Campaign creation","Donation processing","Impact tracking","Social sharing","Transparency tools"],businessGoals:["Enable social impact funding","Increase donation transparency","Build donor trust"],targetAudience:"Non-profits, social entrepreneurs, and donors",timeline:"8-12 months",budget:"$300K - $900K",successMetrics:["$10M+ raised","95% donor satisfaction","1000+ successful campaigns"],risks:["Fraud prevention","Regulatory compliance","Platform trust"],template:"Build a transparent crowdfunding platform that connects social impact projects with donors worldwide.\n\nKey Requirements:\n- Campaign creation tools with multimedia content support\n- Secure donation processing with multiple payment methods\n- Blockchain-based transparency for donation tracking\n- Impact reporting and progress updates\n- Social sharing and viral campaign features\n- Donor management and engagement tools\n- Mobile app for campaign management and donations\n- Integration with social media and marketing platforms\n\nTechnical Specifications:\n- Secure payment processing with fraud prevention\n- Blockchain integration for donation transparency\n- Social media integration for campaign promotion\n- Advanced analytics for campaign performance\n- Mobile-responsive design with native app features\n- Integration with email marketing and CRM systems\n- Comprehensive reporting and impact measurement\n- Multi-currency and international payment support\n\nSuccess Criteria:\n- Facilitate $10M+ in donations annually\n- Achieve 95% donor satisfaction rating\n- Launch 1,000+ successful fundraising campaigns\n- Maintain 99.9% payment processing reliability"}];function C(e){let{isOpen:t,onClose:n,onLoadTemplate:s}=e,[r,o]=(0,i.useState)("all"),[c,l]=(0,i.useState)("all"),[d,m]=(0,i.useState)(""),[p,g]=(0,i.useState)(null),u=Array.from(new Set(N.map(e=>e.industry))),h=N.filter(e=>{let t="all"===r||e.industry===r,n="all"===c||e.complexity===c,a=""===d||e.name.toLowerCase().includes(d.toLowerCase())||e.description.toLowerCase().includes(d.toLowerCase())||e.industry.toLowerCase().includes(d.toLowerCase());return t&&n&&a}),y=e=>{s({projectType:e.projectType,projectName:e.name,projectIdea:"".concat(e.description,"\n\nBusiness Goals:\n").concat(e.businessGoals.map(e=>"• ".concat(e)).join("\n"),"\n\nTarget Audience: ").concat(e.targetAudience,"\n\nKey Features:\n").concat(e.features.map(e=>"• ".concat(e)).join("\n")),platform:e.platform,complexity:e.complexity,technologies:e.technologies.slice(0,4).map(t=>({category:"frontend",name:t,description:"".concat(t," for ").concat(e.industry.toLowerCase()," solutions")})),additionalRequirements:"Timeline: ".concat(e.timeline,"\nBudget: ").concat(e.budget,"\n\nSuccess Metrics:\n").concat(e.successMetrics.map(e=>"• ".concat(e)).join("\n"),"\n\nRisk Considerations:\n").concat(e.risks.map(e=>"• ".concat(e)).join("\n"))}),n()},x=e=>{switch(e){case"basic":return"bg-green-600 text-green-200";case"intermediate":return"bg-yellow-600 text-yellow-200";case"advanced":return"bg-red-600 text-red-200";default:return"bg-gray-600 text-gray-200"}};return t?(0,a.jsx)("div",{className:"fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-2 sm:p-4",children:(0,a.jsxs)("div",{className:"bg-black border border-white/20 rounded-xl shadow-2xl p-4 sm:p-6 max-w-7xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-hidden",style:{boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.8)"},children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4 sm:mb-6",children:[(0,a.jsx)("h3",{className:"text-lg sm:text-xl font-semibold text-white",children:"Industry Templates"}),(0,a.jsx)("button",{onClick:n,className:"bg-gray-800 text-white px-3 py-1 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm flex-shrink-0",children:"Close"})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Industry Templates"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Choose from comprehensive industry-specific project templates with detailed specifications and best practices."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-4 sm:mb-6",children:[(0,a.jsx)("input",{type:"text",value:d,onChange:e=>m(e.target.value),placeholder:"Search templates...",className:"px-3 py-2 rounded-md bg-gray-900 text-white placeholder-gray-400 border border-white/10 focus:border-white/30 focus:outline-none text-sm sm:text-base"}),(0,a.jsxs)("select",{value:r,onChange:e=>o(e.target.value),className:"px-3 py-2 rounded-md bg-gray-900 text-white border border-white/10 focus:border-white/30 focus:outline-none text-sm sm:text-base",children:[(0,a.jsx)("option",{value:"all",children:"All Industries"}),u.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]}),(0,a.jsxs)("select",{value:c,onChange:e=>l(e.target.value),className:"px-3 py-2 rounded-md bg-gray-900 text-white border border-white/10 focus:border-white/30 focus:outline-none text-sm sm:text-base sm:col-span-2 lg:col-span-1",children:[(0,a.jsx)("option",{value:"all",children:"All Complexity Levels"}),["basic","intermediate","advanced"].map(e=>(0,a.jsx)("option",{value:e,className:"capitalize",children:e},e))]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 max-h-[55vh] sm:max-h-[65vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"space-y-3 sm:space-y-4 ".concat(p?"hidden lg:block":""),children:[h.map(e=>(0,a.jsxs)("div",{className:"bg-gray-900 border border-white/10 rounded-lg p-3 sm:p-4 transition-all duration-300 cursor-pointer ".concat((null==p?void 0:p.id)===e.id?"ring-2 ring-white bg-gray-800":"hover:bg-gray-800"),onClick:()=>g(e),children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("h4",{className:"text-white font-medium text-sm sm:text-base pr-2 flex-1 min-w-0",children:e.name}),(0,a.jsx)("span",{className:"px-2 py-1 rounded text-xs capitalize flex-shrink-0 ".concat(x(e.complexity)),children:e.complexity})]}),(0,a.jsx)("p",{className:"text-gray-300 text-xs sm:text-sm mb-2",children:e.industry}),(0,a.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm mb-3 line-clamp-2",children:e.description}),(0,a.jsxs)("div",{className:"flex justify-between items-center text-xs text-gray-500",children:[(0,a.jsx)("span",{className:"truncate pr-2",children:e.timeline}),(0,a.jsx)("span",{className:"truncate",children:e.budget})]})]},e.id)),0===h.length&&(0,a.jsxs)("div",{className:"text-center text-gray-400 py-12",children:[(0,a.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDD0D"}),(0,a.jsx)("h4",{className:"text-lg font-semibold mb-2",children:"No Templates Found"}),(0,a.jsx)("p",{className:"text-sm",children:"Try adjusting your filters or search query"})]})]}),(0,a.jsx)("div",{className:"bg-gray-900 border border-white/10 rounded-lg p-3 sm:p-4 ".concat(p?"lg:col-span-1":"hidden lg:block"," ").concat(p?"flex flex-col":""),children:p?(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsxs)("button",{onClick:()=>g(null),className:"lg:hidden flex items-center space-x-2 text-gray-400 hover:text-white transition-colors mb-4 flex-shrink-0",children:[(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),(0,a.jsx)("span",{className:"text-sm",children:"Back to Templates"})]}),(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-3 sm:space-y-4 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-medium text-base sm:text-lg mb-2",children:p.name}),(0,a.jsx)("p",{className:"text-gray-300 text-xs sm:text-sm mb-2",children:p.industry}),(0,a.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm",children:p.description})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium mb-2 text-sm sm:text-base",children:"Key Features"}),(0,a.jsx)("div",{className:"space-y-1",children:p.features.map((e,t)=>(0,a.jsxs)("div",{className:"text-gray-400 text-xs sm:text-sm",children:["• ",e]},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium mb-2 text-sm sm:text-base",children:"Technologies"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1 sm:gap-2",children:p.technologies.map((e,t)=>(0,a.jsx)("span",{className:"bg-gray-700 text-gray-200 text-xs px-2 py-1 rounded",children:e},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium mb-2 text-sm sm:text-base",children:"Business Goals"}),(0,a.jsx)("div",{className:"space-y-1",children:p.businessGoals.map((e,t)=>(0,a.jsxs)("div",{className:"text-gray-400 text-xs sm:text-sm",children:["• ",e]},t))})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"p-2 sm:p-3 rounded-lg bg-white/5 border border-white/10",children:[(0,a.jsx)("span",{className:"text-gray-400 text-xs sm:text-sm",children:"Timeline:"}),(0,a.jsx)("div",{className:"text-white text-sm sm:text-base font-medium",children:p.timeline})]}),(0,a.jsxs)("div",{className:"p-2 sm:p-3 rounded-lg bg-white/5 border border-white/10",children:[(0,a.jsx)("span",{className:"text-gray-400 text-xs sm:text-sm",children:"Budget:"}),(0,a.jsx)("div",{className:"text-white text-sm sm:text-base font-medium",children:p.budget})]})]})]}),(0,a.jsx)("div",{className:"flex-shrink-0 pt-3 border-t border-white/10",children:(0,a.jsx)("button",{onClick:()=>y(p),className:"w-full bg-white text-black py-3 px-4 rounded-lg hover:bg-gray-200 transition-all duration-300 font-semibold text-sm sm:text-base shadow-lg hover:shadow-xl transform hover:scale-[1.02]",children:"Use This Template"})})]}):(0,a.jsxs)("div",{className:"text-center text-gray-400 py-8 sm:py-12",children:[(0,a.jsx)("div",{className:"text-3xl sm:text-4xl mb-3 sm:mb-4",children:"\uD83D\uDCCB"}),(0,a.jsx)("h4",{className:"text-base sm:text-lg font-semibold mb-2",children:"Select a Template"}),(0,a.jsx)("p",{className:"text-xs sm:text-sm",children:"Choose a template from the list to see detailed information"})]})})]})]})}):null}let I=()=>"undefined"!=typeof crypto&&crypto.randomUUID?crypto.randomUUID():"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)});function P(){var e;let[t,n]=(0,i.useState)({projectName:"",projectIdea:"",projectType:"web-application",platform:"web",technologies:[],complexity:"intermediate",features:[],additionalRequirements:""}),[o,d]=(0,i.useState)(""),[p,g]=(0,i.useState)(!1),[u,h]=(0,i.useState)(0),[x,f]=(0,i.useState)({}),[v,w]=(0,i.useState)({}),[j,S]=(0,i.useState)([]),[N,P]=(0,i.useState)(!1),[M,T]=(0,i.useState)([]),[R,D]=(0,i.useState)(!1),[E,z]=(0,i.useState)(!1),[L,F]=(0,i.useState)(null),[G,K]=(0,i.useState)(!1),[q,B]=(0,i.useState)(!1),[W,O]=(0,i.useState)({model:"deepseek/deepseek-chat-v3-0324:free",optimizationLevel:"enhanced",includeExamples:!0,includeConstraints:!0,includeMetrics:!1,targetAudience:"developer"}),[$,H]=(0,i.useState)(null),U=(0,i.useCallback)(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info";F({message:e,type:t}),setTimeout(()=>F(null),3e3)},[]),Q=(e,t)=>{var n;return(null==(n=({"web-application":["TaskMaster Pro","DataViz Dashboard","EcoTracker","ShopSmart","ConnectHub"],"mobile-application":["FitTrack Mobile","LocalEats App","StudyBuddy","WeatherWise","PhotoShare"],"desktop-application":["CodeEditor Pro","MediaManager","TaskPlanner","FileSync","NoteTaker"],"api-backend":["UserAuth API","Payment Gateway","Analytics Service","Notification Hub","Data Processor"],"data-analysis":["Sales Analytics","Customer Insights","Market Research Tool","Performance Dashboard","Trend Analyzer"],"machine-learning":["Recommendation Engine","Image Classifier","Sentiment Analyzer","Fraud Detector","Chatbot AI"],"devops-infrastructure":["CI/CD Pipeline","Monitoring Stack","Container Platform","Auto Scaler","Log Aggregator"],"chrome-extension":["Productivity Booster","Tab Manager","Password Helper","Web Scraper","Time Tracker"],"cli-tool":["File Processor","Code Generator","Deployment Tool","Data Migrator","System Monitor"],"library-package":["UI Components","Utility Library","API Client","Data Validator","Chart Library"]})[t])?void 0:n.filter(t=>t.toLowerCase().includes(e.toLowerCase())))||[]};(0,i.useEffect)(()=>{let e=localStorage.getItem("promptGenerator_draft");if(e)try{let t=JSON.parse(e);n(t)}catch(e){console.error("Failed to load saved data:",e)}let t=localStorage.getItem("promptHistory");if(t)try{let e=JSON.parse(t);S(e.map(e=>({...e,timestamp:new Date(e.timestamp)})))}catch(e){console.error("Failed to load history:",e)}},[]);let V=(0,i.useCallback)((e,t)=>{let n=[{id:I(),timestamp:new Date,projectInput:e,generatedPrompt:t,isFavorite:!1},...j.slice(0,19)];S(n),localStorage.setItem("promptHistory",JSON.stringify(n))},[j]),J=e=>{let t=j.map(t=>t.id===e?{...t,isFavorite:!t.isFavorite}:t);S(t),localStorage.setItem("promptHistory",JSON.stringify(t))},_=e=>{n(e.projectInput),d(e.generatedPrompt),P(!1)},Y=(0,i.useCallback)((e,t)=>{let n={...x};switch(e){case"projectName":t.trim()?t.length<3?n.projectName="Project name must be at least 3 characters":t.length>50?n.projectName="Project name must be less than 50 characters":delete n.projectName:n.projectName="Project name is required";break;case"projectIdea":t.trim()?t.length<20?n.projectIdea="Please provide more details (minimum 20 characters)":t.length>1e3?n.projectIdea="Project concept must be less than 1000 characters":delete n.projectIdea:n.projectIdea="Project concept is required"}return f(n),0===Object.keys(n).length},[x]),Z=(0,i.useCallback)(()=>{let e=Y("projectName",t.projectName),n=Y("projectIdea",t.projectIdea);return e&&n},[t.projectName,t.projectIdea,Y]),X=(e,t)=>{n(n=>({...n,[e]:t})),w(t=>({...t,[e]:!0})),("projectName"===e||"projectIdea"===e)&&Y(e,t)},ee=(0,i.useCallback)(async()=>{if(!Z())return void w({projectName:!0,projectIdea:!0});g(!0),h(0);let e=setInterval(()=>{h(e=>Math.min(e+10,90))},200);try{var n;let e=c.getModelRecommendation(t),a={...W};a.model&&"auto"!==a.model||(a.model=e);let i=await c.generatePrompt(t,a);h(100),d(i.prompt),H(i.metadata),V(t,i.prompt);let s=(null==(n=Object.values(r).find(e=>e.id===a.model))?void 0:n.name)||"AI";U("Prompt generated with ".concat(s),"success")}catch(t){console.error("Error generating prompt:",t);let e=t instanceof Error?t.message:"Unknown error occurred";f({general:"Error generating prompt: ".concat(e)}),U("Failed to generate prompt","error")}finally{clearInterval(e),g(!1),setTimeout(()=>h(0),1e3)}},[t,Z,U,W,V]);(0,i.useEffect)(()=>{let e=e=>{if(e.ctrlKey||e.metaKey)switch(e.key){case"Enter":e.preventDefault(),p||ee();break;case"s":e.preventDefault();break;case"k":var t;e.preventDefault(),null==(t=document.querySelector('input[type="text"]'))||t.focus();break;case"h":e.preventDefault(),P(!0);break;case"t":case"b":e.preventDefault(),B(!0);break;case",":case"a":e.preventDefault(),K(!0);break;case"Escape":e.preventDefault(),P(!1),B(!1),K(!1),B(!1)}"Escape"===e.key&&(P(!1),B(!1),K(!1),B(!1))};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[p,ee]);let et={copyToClipboard:async e=>{try{await navigator.clipboard.writeText(e),U("Copied to clipboard!","success")}catch(e){U("Failed to copy to clipboard","error")}},downloadAsFile:(e,t)=>{try{let n=new Blob([e],{type:"text/plain"}),a=URL.createObjectURL(n),i=document.createElement("a");i.href=a,i.download="".concat(t,".txt"),document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(a),U("File downloaded successfully!","success")}catch(e){U("Failed to download file","error")}},shareViaAPI:async e=>{if(navigator.share)try{await navigator.share({title:"AI Generated Prompt for Augment Agent",text:e}),U("Shared successfully!","success")}catch(e){e instanceof Error&&"AbortError"!==e.name&&U("Failed to share","error")}else U("Sharing not supported on this device","info")}},en=async()=>{await et.copyToClipboard(o)},ea=e=>{n(t=>({...t,technologies:t.technologies.some(t=>t.name===e.name)?t.technologies.filter(t=>t.name!==e.name):[...t.technologies,e]}))};return(0,a.jsxs)("div",{className:"min-h-screen bg-black py-4 sm:py-8 px-2 sm:px-4 relative",children:[(0,a.jsx)(l,{}),(0,a.jsxs)("div",{className:"max-w-6xl mx-auto relative z-10",children:[(0,a.jsxs)("header",{className:"text-center mb-4 sm:mb-8 px-2",children:[(0,a.jsx)(b,{sequences:[{text:"AI Prompt Generator",deleteAfter:!0,pauseAfter:1200},{text:"Professional Prompts",deleteAfter:!0,pauseAfter:1200},{text:"DeepSeek V3 Powered",deleteAfter:!0,pauseAfter:1200},{text:"Augment Agent Ready",deleteAfter:!0,pauseAfter:2e3}],typingSpeed:60,autoLoop:!0,loopDelay:3e3,className:"mb-4"}),(0,a.jsx)(k,{text:"AI-Powered Prompt Generation with DeepSeek V3 for Augment Agent",className:"text-sm sm:text-base lg:text-lg"})]}),(0,a.jsxs)("div",{className:"space-y-4 sm:space-y-8",children:[(0,a.jsxs)("div",{className:"rounded-xl shadow-2xl p-3 sm:p-4 lg:p-6 transition-all duration-300 ease-in-out max-w-2xl mx-auto",style:{boxShadow:"0 20px 40px -12px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center mb-3 space-y-2 sm:space-y-0",children:[(0,a.jsx)(k,{text:"Project Details",className:"text-lg sm:text-xl font-semibold"}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2",children:[(0,a.jsx)("button",{onClick:()=>{n({projectName:"",projectIdea:"",projectType:"web-application",platform:"web",technologies:[],complexity:"intermediate",features:[],additionalRequirements:""}),d(""),f({}),w({}),U("Form cleared","info")},className:"bg-black/5 text-white px-3 py-2 sm:px-2 sm:py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg min-h-[44px] sm:min-h-auto",style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:"Clear"}),(0,a.jsxs)("button",{onClick:()=>P(!0),className:"bg-black/5 text-white px-3 py-2 sm:px-2 sm:py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg min-h-[44px] sm:min-h-auto",style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:["History (",j.length,")"]}),(0,a.jsx)("button",{onClick:()=>B(!0),className:"bg-gray-800 text-white px-3 py-2 sm:px-2 sm:py-1 rounded-md hover:bg-gray-700 transition-all duration-300 text-xs hover:shadow-lg min-h-[44px] sm:min-h-auto",style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:"Industry Templates"}),(0,a.jsx)("button",{onClick:()=>K(!0),className:"bg-black/5 text-white px-3 py-2 sm:px-2 sm:py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg min-h-[44px] sm:min-h-auto",style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:"AI Settings & Integrations"})]})]}),x.general&&(0,a.jsx)("div",{className:"mb-3 p-2 bg-gray-800 border border-gray-600 rounded-md",children:(0,a.jsx)("p",{className:"text-gray-200 text-xs",children:x.general})}),x.success&&(0,a.jsx)("div",{className:"mb-3 p-2 bg-gray-800 border border-gray-600 rounded-md",children:(0,a.jsx)("p",{className:"text-gray-200 text-xs",children:x.success})}),(0,a.jsxs)("div",{className:"mb-3 sm:mb-2 relative",children:[(0,a.jsx)("label",{className:"block text-sm sm:text-xs font-medium mb-2 sm:mb-1",children:(0,a.jsx)(k,{text:"Project Name *",className:"text-sm sm:text-xs"})}),(0,a.jsx)("input",{type:"text",value:t.projectName,onChange:e=>{X("projectName",e.target.value);let n=Q(e.target.value,t.projectType);T(n),D(e.target.value.length>0&&n.length>0)},onBlur:()=>{w(e=>({...e,projectName:!0})),setTimeout(()=>D(!1),200)},onFocus:()=>{let e=Q(t.projectName,t.projectType);T(e),D(t.projectName.length>0&&e.length>0)},className:"w-full px-3 py-3 sm:px-2 sm:py-1.5 rounded-md focus:outline-none focus:ring-1 text-base sm:text-sm text-white bg-black/5 backdrop-blur-md placeholder-gray-400 transition-all duration-300 hover:bg-black/10 min-h-[44px] sm:min-h-auto ".concat(x.projectName&&v.projectName?"ring-red-400 border-red-400":"focus:ring-white/20"),style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},placeholder:"e.g., TaskMaster Pro"}),R&&M.length>0&&(0,a.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-black/20 backdrop-blur-xl rounded-md shadow-lg border border-white/10 max-h-40 overflow-y-auto",children:M.map((e,t)=>(0,a.jsx)("button",{type:"button",onClick:()=>{X("projectName",e),D(!1)},className:"w-full text-left px-3 py-2 text-sm text-white hover:bg-black/20 transition-all duration-200 first:rounded-t-md last:rounded-b-md",children:e},t))}),x.projectName&&v.projectName&&(0,a.jsx)("p",{className:"text-gray-300 text-xs mt-1",children:x.projectName}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[t.projectName.length,"/50 characters"]})]}),(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsx)("label",{className:"block text-xs font-medium mb-1",children:(0,a.jsx)(k,{text:"Project Concept *",className:"text-xs"})}),(0,a.jsx)("textarea",{value:t.projectIdea,onChange:e=>X("projectIdea",e.target.value),onBlur:()=>w(e=>({...e,projectIdea:!0})),className:"w-full px-2 py-1.5 rounded-md focus:outline-none focus:ring-1 h-16 text-sm text-white bg-black/5 backdrop-blur-md placeholder-gray-400 transition-all duration-300 hover:bg-black/10 ".concat(x.projectIdea&&v.projectIdea?"ring-red-400 border-red-400":"focus:ring-white/20"),style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},placeholder:"Describe your project idea, goals, and target users..."}),x.projectIdea&&v.projectIdea&&(0,a.jsx)("p",{className:"text-gray-300 text-xs mt-1",children:x.projectIdea}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1 space-y-1",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{children:[t.projectIdea.length,"/1000 characters"]}),(0,a.jsx)("span",{className:"".concat(t.projectIdea.length<50?"text-gray-400":t.projectIdea.length<100?"text-gray-300":"text-white"),children:t.projectIdea.length<50?"Too short":t.projectIdea.length<100?"Good start":"Great detail!"})]}),t.projectIdea.length<100&&(0,a.jsx)("div",{className:"text-gray-300 text-xs",children:"\uD83D\uDCA1 Tip: Include target users, main features, and business goals for better AI prompts"})]})]}),(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsx)("label",{className:"block text-xs font-medium mb-1",children:(0,a.jsx)(k,{text:"Project Type",className:"text-xs"})}),(0,a.jsx)(m,{value:t.projectType,onChange:e=>X("projectType",e),options:[{value:"web-application",label:"Web Application",description:"Frontend web apps, SPAs, and websites"},{value:"mobile-application",label:"Mobile Application",description:"iOS, Android, and cross-platform apps"},{value:"desktop-application",label:"Desktop Application",description:"Native desktop software and tools"},{value:"api-backend",label:"API/Backend Service",description:"REST APIs, GraphQL, and backend services"},{value:"data-analysis",label:"Data Analysis Tool",description:"Analytics, reporting, and data processing"},{value:"machine-learning",label:"Machine Learning Project",description:"AI/ML models and applications"},{value:"devops-infrastructure",label:"DevOps Infrastructure",description:"CI/CD, deployment, and automation"},{value:"chrome-extension",label:"Chrome Extension",description:"Browser extensions and add-ons"},{value:"cli-tool",label:"CLI Tool",description:"Command-line utilities and scripts"},{value:"library-package",label:"Library/Package",description:"Reusable libraries and npm packages"}],placeholder:"Select project type"})]}),(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsx)("label",{className:"block text-xs font-medium mb-1",children:(0,a.jsx)(k,{text:"Target Platform",className:"text-xs"})}),(0,a.jsx)(m,{value:t.platform,onChange:e=>X("platform",e),options:[{value:"web",label:"Web",description:"Browser-based applications"},{value:"mobile",label:"Mobile",description:"iOS and Android platforms"},{value:"desktop",label:"Desktop",description:"Windows, macOS, and Linux"},{value:"server",label:"Server",description:"Backend and cloud services"},{value:"cross-platform",label:"Cross-Platform",description:"Multiple platforms"}],placeholder:"Select target platform"})]}),(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsx)("label",{className:"block text-xs font-medium mb-1",children:(0,a.jsx)(k,{text:"Complexity Level",className:"text-xs"})}),(0,a.jsx)("div",{className:"flex space-x-2",children:["simple","intermediate","advanced"].map(e=>(0,a.jsxs)("label",{className:"group relative flex-1 cursor-pointer transition-all duration-300 ".concat(t.complexity===e?"transform scale-[1.02]":""),children:[(0,a.jsx)("input",{type:"radio",value:e,checked:t.complexity===e,onChange:e=>X("complexity",e.target.value),className:"sr-only"}),(0,a.jsx)("div",{className:"\n                      px-4 py-3 rounded-lg text-center text-sm font-medium transition-all duration-300 border backdrop-blur-xl\n                      ".concat(t.complexity===e?"bg-gradient-to-br from-white/20 to-white/10 border-white/30 text-white shadow-lg ring-2 ring-white/20":"bg-gradient-to-br from-gray-800/90 to-gray-900/90 border-white/10 text-gray-300 hover:border-white/20 hover:from-gray-700/90 hover:to-gray-800/90 hover:text-white","\n                    "),style:{boxShadow:t.complexity===e?"0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1)":"0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:(0,a.jsx)("span",{className:"capitalize",children:e})})]},e))})]}),(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsx)("label",{className:"block text-xs font-medium mb-1",children:(0,a.jsx)(k,{text:"Technology Stack (Optional)",className:"text-xs"})}),(0,a.jsx)("div",{className:"space-y-2",children:Object.entries(s).map(e=>{let[n,i]=e;return(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-white mb-1 capitalize text-xs",children:n.replace("-"," ")}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:i.map(e=>(0,a.jsx)("button",{type:"button",onClick:()=>ea(e),className:"px-2 py-0.5 text-xs rounded-full transition-all duration-300 ease-in-out ".concat(t.technologies.some(t=>t.name===e.name)?"bg-white/10 text-white backdrop-blur-md shadow-lg hover:bg-white/15":"bg-black/5 text-white backdrop-blur-md hover:bg-black/10 hover:shadow-lg"),style:{boxShadow:t.technologies.some(t=>t.name===e.name)?"0 2px 8px rgba(255, 255, 255, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.05)":"0 1px 4px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:e.name},e.name))})]},n)})})]}),(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsx)("label",{className:"block text-xs font-medium mb-1",children:(0,a.jsx)(k,{text:"Additional Requirements (Optional)",className:"text-xs"})}),(0,a.jsx)("textarea",{value:t.additionalRequirements,onChange:e=>X("additionalRequirements",e.target.value),className:"w-full px-2 py-1.5 rounded-md focus:outline-none focus:ring-1 focus:ring-white/20 h-12 text-sm text-white bg-black/5 backdrop-blur-md placeholder-gray-400 transition-all duration-300 hover:bg-black/10",style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},placeholder:"Any specific requirements, constraints, or preferences..."})]}),(0,a.jsx)("div",{className:"space-y-1.5",children:(0,a.jsx)("button",{onClick:ee,disabled:p||!t.projectName||!t.projectIdea,className:"w-full bg-black/10 text-white py-2 px-4 rounded-lg hover:bg-black/20 disabled:bg-gray-600/10 disabled:cursor-not-allowed transition-all duration-300 font-semibold text-sm backdrop-blur-md hover:shadow-xl",style:{boxShadow:"0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:p?(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsx)(A,{texts:["Analyzing requirements...","Structuring prompt...","Optimizing for AI...","Finalizing output...","Almost ready..."],interval:1800,className:"text-lg"}),(0,a.jsx)("div",{className:"w-full bg-black/20 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-white to-gray-300 h-2 rounded-full transition-all duration-500 ease-out",style:{width:"".concat(u,"%")}})}),(0,a.jsxs)("span",{className:"text-xs text-gray-400",children:[u,"%"]})]}):"Generate AI Prompt"})})]}),(0,a.jsxs)("div",{className:"rounded-xl shadow-2xl p-3 transition-all duration-300 ease-in-out max-w-2xl mx-auto",style:{boxShadow:"0 20px 40px -12px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(k,{text:"Generated Prompt",className:"text-xl font-semibold"}),o&&(0,a.jsxs)("div",{className:"text-xs text-gray-400 mt-1 space-y-1",children:[(0,a.jsxs)("div",{children:[o.split(" ").length," words • ",Math.ceil(o.split(" ").length/200)," min read"]}),$&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{children:["Generated with ",(null==(e=Object.values(r).find(e=>e.id===$.model))?void 0:e.name)||"AI"]}),$.tokensUsed&&(0,a.jsxs)("span",{children:["• ",$.tokensUsed," tokens"]}),(0,a.jsx)("span",{className:"px-1.5 py-0.5 rounded text-xs ".concat("expert"===$.optimizationLevel?"bg-gray-700 text-gray-200":"enhanced"===$.optimizationLevel?"bg-gray-600 text-gray-200":"bg-gray-500 text-gray-200"),children:$.optimizationLevel})]})]})]}),o&&(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:en,className:"bg-black/5 text-white px-2 py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg",style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:"Copy"}),(0,a.jsx)("button",{onClick:()=>et.downloadAsFile(o,"augment-prompt-".concat(Date.now())),className:"bg-black/5 text-white px-2 py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg",style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:"Download"}),"undefined"!=typeof navigator&&"share"in navigator&&(0,a.jsx)("button",{onClick:()=>et.shareViaAPI(o),className:"bg-black/5 text-white px-2 py-1 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg",style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:"Share"})]})]}),o?(0,a.jsx)("div",{className:"bg-black/5 backdrop-blur-md rounded-lg p-2 max-h-60 overflow-y-auto transition-all duration-300 hover:bg-black/10",style:{boxShadow:"0 4px 16px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:(0,a.jsx)("pre",{className:"whitespace-pre-wrap text-xs text-white font-mono",children:o})}):(0,a.jsxs)("div",{className:"bg-black/3 backdrop-blur-lg rounded-lg p-4 text-center transition-all duration-300 hover:bg-black/8",style:{boxShadow:"0 4px 16px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"AI-Powered Prompt Generation"}),(0,a.jsx)("p",{className:"text-gray-400 mb-2 text-sm",children:"Fill in your project details and let DeepSeek V3 create the perfect Augment Agent prompt for you."}),(0,a.jsx)("div",{className:"text-xs text-white font-medium",children:"Personalized • Optimized • Professional"})]})]})]}),(0,a.jsx)("div",{className:"mt-8 sm:mt-16 text-center",children:(0,a.jsxs)("button",{onClick:()=>z(!E),className:"bg-black/5 backdrop-blur-xl text-white px-6 py-3 sm:px-4 sm:py-2 rounded-xl hover:bg-black/10 transition-all duration-300 ease-in-out min-h-[44px] sm:min-h-auto text-base sm:text-sm font-medium",style:{boxShadow:"0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:["\uD83D\uDCD6 ",E?"Hide":"Show"," Complete User Guide"]})}),(0,a.jsx)("div",{className:"mt-4 sm:mt-8 rounded-2xl shadow-2xl transition-all duration-500 ease-in-out overflow-hidden ".concat(E?"max-h-none opacity-100 p-4 sm:p-6":"max-h-0 opacity-0 p-0"),style:{boxShadow:E?"0 25px 50px -12px rgba(0, 0, 0, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.05)":"none"},children:E&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4 sm:mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-white",children:"Complete User Guide"}),(0,a.jsx)("button",{onClick:()=>z(!1),className:"bg-black/5 text-white px-3 py-2 rounded-md hover:bg-black/15 transition-all duration-300 text-xs backdrop-blur-md hover:shadow-lg min-h-[44px] sm:min-h-auto",style:{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)"},children:"✕ Close"})]}),(0,a.jsx)("p",{className:"text-center text-gray-400 mb-4 sm:mb-6 text-sm sm:text-lg",children:"Learn how to use each feature effectively to generate the best AI-powered prompts for your projects"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out",style:{boxShadow:"0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-3",children:"Project Details"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Project Name"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Enter a descriptive name for your project. This helps the AI understand the scope and purpose. Examples: “TaskMaster Pro”, “E-commerce Platform”, “Mobile Banking App”"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Project Concept"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Describe your project idea in detail. Include the main purpose, target users, key features, and goals. The more specific you are, the better the AI can tailor the prompt to your needs."})]})]})]}),(0,a.jsxs)("div",{className:"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out",style:{boxShadow:"0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-3",children:"Project Types"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Web Application"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Choose this for websites, web apps, or browser-based applications. Includes both frontend and backend development."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Mobile Application"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Select for iOS, Android, or cross-platform mobile apps. Covers native and hybrid development approaches."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Desktop Application"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"For Windows, macOS, or Linux desktop software. Includes cross-platform and native desktop solutions."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"API/Backend Service"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Choose for server-side applications, REST APIs, microservices, or backend systems without a user interface."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Data Analysis Tool"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"For data processing, analytics, visualization, or machine learning applications and tools."})]})]})]}),(0,a.jsxs)("div",{className:"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out",style:{boxShadow:"0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-3",children:"Platform Options"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Web"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Browser-based applications accessible via web browsers. Includes responsive design for all devices."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Mobile"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Applications designed specifically for smartphones and tablets, with mobile-optimized interfaces."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Desktop"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Native applications that run directly on desktop operating systems like Windows, macOS, or Linux."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Server"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Backend services, APIs, and server-side applications that run on servers or cloud infrastructure."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Cloud"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Cloud-native applications designed to leverage cloud services and distributed computing resources."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Cross-platform"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Applications that work across multiple platforms using frameworks like Electron, Flutter, or React Native."})]})]})]}),(0,a.jsxs)("div",{className:"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out",style:{boxShadow:"0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-3",children:"Complexity Levels"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Simple"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Basic functionality with minimal features. Quick to develop, perfect for MVPs, prototypes, or learning projects. Focuses on core features only."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Intermediate"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Moderate complexity with additional features, user authentication, database integration, and proper error handling. Suitable for most business applications."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Advanced"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Enterprise-level complexity with scalability, performance optimization, advanced security, microservices, and comprehensive testing strategies."})]})]})]}),(0,a.jsxs)("div",{className:"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out",style:{boxShadow:"0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-3",children:"Technology Stack Guide"}),(0,a.jsx)("p",{className:"text-gray-400 mb-4",children:"A technology stack is the combination of programming languages, frameworks, libraries, and tools used to build your application. Selecting the right technologies is crucial for project success."}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Frontend Technologies"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Frontend development creates the user interface and user experience. Choose technologies based on your project needs:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"React:"})," Popular, component-based, large ecosystem"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Vue.js:"})," Progressive, easy to learn, flexible"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Angular:"})," Full framework, TypeScript-based, enterprise-ready"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Next.js:"})," React framework with SSR and static generation"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Svelte:"})," Compile-time optimized, smaller bundle sizes"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"TypeScript:"})," Adds type safety to JavaScript"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Backend Technologies"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Backend development handles server-side logic, databases, and APIs:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Node.js:"})," JavaScript runtime, fast development"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Python/Django:"})," Rapid development, batteries included"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Python/FastAPI:"})," Modern, fast, automatic API docs"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Go:"})," High performance, excellent concurrency"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Rust:"})," Memory safety, extreme performance"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Java/Spring:"})," Enterprise-grade, mature ecosystem"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Database Options"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Choose databases based on your data structure and scalability needs:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"PostgreSQL:"})," Advanced relational database, JSON support"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"MongoDB:"})," NoSQL document database, flexible schema"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"SQLite:"})," Lightweight, embedded, perfect for small apps"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Redis:"})," In-memory store, caching, real-time features"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Supabase:"})," Open-source Firebase alternative"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Firebase:"})," Google's platform, real-time features"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Styling Technologies"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Choose styling approaches based on your project needs and team preferences:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Tailwind CSS:"})," Utility-first CSS framework"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"CSS Modules:"})," Scoped CSS with local class names"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Styled Components:"})," CSS-in-JS with component styling"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"SCSS/Sass:"})," CSS preprocessor with variables and mixins"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Material-UI:"})," React components with Material Design"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Chakra UI:"})," Simple, modular React component library"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Mobile Frameworks"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Cross-platform and native mobile development frameworks:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"React Native:"})," Cross-platform with React"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Flutter:"})," Google's UI toolkit for mobile"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Swift:"})," Native iOS development language"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Kotlin:"})," Modern Android development language"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Expo:"})," Platform for React Native development"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Desktop Frameworks"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Cross-platform and native desktop application frameworks:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Electron:"})," Web technologies for desktop apps"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Tauri:"})," Rust-based lightweight desktop framework"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Qt:"})," Cross-platform C++ application framework"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"WPF:"})," Windows Presentation Foundation for .NET"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"JavaFX:"})," Java platform for desktop applications"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Deployment Platforms"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Modern deployment and hosting solutions for applications:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Vercel:"})," Frontend deployment with edge functions"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Netlify:"})," JAMstack deployment and hosting"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"AWS:"})," Amazon Web Services cloud platform"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Google Cloud:"})," Google's cloud computing services"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Docker:"})," Containerization platform"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Kubernetes:"})," Container orchestration system"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Testing Frameworks"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Testing tools and frameworks for quality assurance:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Jest:"})," JavaScript testing framework"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Vitest:"})," Fast unit testing framework"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Cypress:"})," End-to-end testing framework"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Playwright:"})," Cross-browser automation testing"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"React Testing Library:"})," React component testing"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Authentication Solutions"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"User authentication and authorization services:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Auth0:"})," Identity platform as a service"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Firebase Auth:"})," Google's authentication service"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"NextAuth.js:"})," Authentication for Next.js"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Supabase Auth:"})," Open-source authentication"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"JWT:"})," JSON Web Tokens for stateless auth"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"State Management"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Tools for managing application state across components and user sessions:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Redux Toolkit:"})," Modern Redux with simplified API"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Zustand:"})," Lightweight state management solution"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Recoil:"})," Experimental state management for React"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"MobX:"})," Reactive state management through observables"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Context API:"})," React's built-in state management"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Valtio:"})," Proxy-based state management"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"API Tools"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Tools for API development, data fetching, and client-server communication:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"GraphQL:"})," Query language for APIs"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Apollo Client:"})," Comprehensive GraphQL client"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"React Query:"})," Data fetching and caching library"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"SWR:"})," Data fetching with caching and revalidation"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Axios:"})," Promise-based HTTP client"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"tRPC:"})," End-to-end typesafe APIs"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Monitoring & Analytics"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Tools for application monitoring, error tracking, and performance analysis:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Sentry:"})," Error tracking and performance monitoring"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"LogRocket:"})," Session replay and logging"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"New Relic:"})," Application performance monitoring"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Datadog:"})," Infrastructure and application monitoring"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Prometheus:"})," Open-source monitoring and alerting"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Grafana:"})," Analytics and monitoring dashboards"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"CI/CD & DevOps"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Continuous integration and deployment tools for automated workflows:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"GitHub Actions:"})," CI/CD platform integrated with GitHub"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"GitLab CI:"})," Built-in CI/CD for GitLab"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Jenkins:"})," Open-source automation server"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"CircleCI:"})," Cloud-based CI/CD platform"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Azure DevOps:"})," Microsoft's DevOps platform"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Travis CI:"})," Hosted CI service for GitHub projects"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Version Control"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Version control systems and repository hosting platforms:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Git:"})," Distributed version control system"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"GitHub:"})," Git hosting with collaboration features"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"GitLab:"})," DevOps platform with Git repository"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Bitbucket:"})," Git solution for teams"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Azure Repos:"})," Git repositories in Azure DevOps"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Package Managers"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Tools for managing project dependencies and packages:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"npm:"})," Node.js package manager"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Yarn:"})," Fast, reliable package manager"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"pnpm:"})," Efficient package manager with shared dependencies"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Bun:"})," Fast all-in-one JavaScript runtime and package manager"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Build Tools"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Tools for bundling, compiling, and optimizing application code:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Webpack:"})," Module bundler for JavaScript applications"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Vite:"})," Fast build tool for modern web projects"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Rollup:"})," Module bundler for JavaScript libraries"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Parcel:"})," Zero-configuration build tool"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"esbuild:"})," Extremely fast JavaScript bundler"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Turbopack:"})," Incremental bundler optimized for JavaScript and TypeScript"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Code Quality"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Tools for maintaining code quality, formatting, and automated code review:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"ESLint:"})," JavaScript linting utility"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Prettier:"})," Code formatter"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Husky:"})," Git hooks for code quality"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"lint-staged:"})," Run linters on staged files"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"SonarQube:"})," Code quality and security analysis"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"CodeClimate:"})," Automated code review and quality analytics"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Documentation"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Tools for creating and maintaining project documentation:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-400",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Storybook:"})," Tool for building UI components in isolation"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Docusaurus:"})," Documentation website generator"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"GitBook:"})," Documentation platform"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Notion:"})," All-in-one workspace for documentation"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"Confluence:"})," Team collaboration and documentation"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-white",children:"JSDoc:"})," API documentation generator for JavaScript"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:"Additional Requirements"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Use this field to specify any special requirements, constraints, integrations, performance needs, security requirements, or specific features not covered in the main form. Be as detailed as possible to help the AI generate the most relevant prompt."})]})]})]}),(0,a.jsxs)("div",{className:"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out",style:{boxShadow:"0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-3",children:"Tips for Best Results"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)("span",{className:"text-white font-bold",children:"1."}),(0,a.jsxs)("p",{className:"text-gray-400 text-sm",children:[(0,a.jsx)("strong",{className:"text-white",children:"Be Specific:"})," The more detailed your project description, the better the AI can tailor the prompt to your exact needs."]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)("span",{className:"text-white font-bold",children:"2."}),(0,a.jsxs)("p",{className:"text-gray-400 text-sm",children:[(0,a.jsx)("strong",{className:"text-white",children:"Choose Appropriate Complexity:"})," Match the complexity level to your timeline, budget, and technical requirements."]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)("span",{className:"text-white font-bold",children:"3."}),(0,a.jsxs)("p",{className:"text-gray-400 text-sm",children:[(0,a.jsx)("strong",{className:"text-white",children:"Select Relevant Technologies:"})," If you're unsure about technologies, leave them blank and let the AI suggest appropriate options."]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)("span",{className:"text-white font-bold",children:"4."}),(0,a.jsxs)("p",{className:"text-gray-400 text-sm",children:[(0,a.jsx)("strong",{className:"text-white",children:"Use Templates:"}),' Click "Project Templates" to explore examples and see how to structure your project information effectively.']})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)("span",{className:"text-white font-bold",children:"5."}),(0,a.jsxs)("p",{className:"text-gray-400 text-sm",children:[(0,a.jsx)("strong",{className:"text-white",children:"Include Context:"})," Mention your target audience, business goals, and any existing systems you need to integrate with."]})]})]})]}),(0,a.jsxs)("div",{className:"rounded-xl p-4 bg-black/5 backdrop-blur-lg hover:bg-black/10 transition-all duration-300 ease-in-out",style:{boxShadow:"0 8px 32px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)"},children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-3",children:"Keyboard Shortcuts"}),(0,a.jsx)("p",{className:"text-gray-400 mb-4 text-sm",children:"Use these keyboard shortcuts to work more efficiently:"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Generate Prompt"}),(0,a.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + Enter"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Focus Project Name"}),(0,a.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + K"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Open Templates"}),(0,a.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + T"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"AI Settings"}),(0,a.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + ,"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Open History"}),(0,a.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + H"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"AI Settings & Integrations"}),(0,a.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + A"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Team Dashboard"}),(0,a.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + M"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Share Prompt"}),(0,a.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + S"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Integrations"}),(0,a.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + I"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Live Collaboration"}),(0,a.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + R"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Industry Templates"}),(0,a.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Ctrl + B"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Close Modal"}),(0,a.jsx)("kbd",{className:"bg-black/20 text-white px-2 py-1 rounded text-xs",children:"Escape"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Auto-save"}),(0,a.jsx)("span",{className:"text-gray-300 text-xs",children:"Automatic"})]})]})]})]})]})]})}),N&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-black border border-white/20 rounded-xl shadow-2xl p-6 max-w-4xl w-full max-h-[80vh] overflow-hidden",style:{boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.8)"},children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-white",children:"Prompt History"}),(0,a.jsx)("button",{onClick:()=>P(!1),className:"bg-gray-800 text-white px-3 py-1 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm",children:"Close"})]}),(0,a.jsx)("div",{className:"overflow-y-auto max-h-[60vh] space-y-3",children:0===j.length?(0,a.jsx)("p",{className:"text-gray-400 text-center py-8",children:"No prompts generated yet"}):j.map(e=>(0,a.jsxs)("div",{className:"bg-gray-900 border border-white/10 rounded-lg p-4 hover:bg-gray-800 transition-all duration-300",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-white font-medium",children:e.projectInput.projectName}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:e.timestamp.toLocaleString()})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>J(e.id),className:"px-2 py-1 rounded text-xs transition-all duration-300 ".concat(e.isFavorite?"bg-white text-black":"bg-gray-700 text-gray-400 hover:bg-gray-600"),children:e.isFavorite?"★":"☆"}),(0,a.jsx)("button",{onClick:()=>_(e),className:"bg-gray-700 text-white px-2 py-1 rounded text-xs hover:bg-gray-600 transition-all duration-300",children:"Load"})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-300 mb-2 line-clamp-2",children:e.projectInput.projectIdea}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-1 mb-2",children:[(0,a.jsx)("span",{className:"bg-gray-700 text-xs px-2 py-1 rounded text-gray-300",children:e.projectInput.projectType}),(0,a.jsx)("span",{className:"bg-gray-700 text-xs px-2 py-1 rounded text-gray-300",children:e.projectInput.platform}),(0,a.jsx)("span",{className:"bg-gray-700 text-xs px-2 py-1 rounded text-gray-300",children:e.projectInput.complexity})]})]},e.id))})]})}),(0,a.jsx)(y,{isOpen:G,onClose:()=>K(!1),settings:W,onSettingsChange:O,recommendedModel:c.getModelRecommendation(t),projectInput:t,generatedPrompt:o}),(0,a.jsx)(C,{isOpen:q,onClose:()=>B(!1),onLoadTemplate:e=>{n(t=>({...t,...e})),U("Loaded advanced template: ".concat(e.projectName),"success")}}),L&&(0,a.jsx)("div",{className:"fixed top-4 right-4 z-50 px-4 py-2 rounded-lg transition-all duration-300 animate-slide-up ".concat("success"===L.type?"bg-white border border-gray-300 text-black":"error"===L.type?"bg-gray-900 border border-gray-600 text-white":"bg-gray-800 border border-gray-600 text-white"),children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:L.message}),(0,a.jsx)("button",{onClick:()=>F(null),className:"text-xs opacity-70 hover:opacity-100 transition-opacity",children:"✕"})]})})]})]})}},9406:(e,t,n)=>{Promise.resolve().then(n.bind(n,4843)),Promise.resolve().then(n.bind(n,5782))}},e=>{var t=t=>e(e.s=t);e.O(0,[668,441,684,358],()=>t(9406)),_N_E=e.O()}]);