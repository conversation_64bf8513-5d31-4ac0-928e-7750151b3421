import { Technology, TechnologyStack } from './types';

export const TECHNOLOGIES: Record<string, Technology[]> = {
  frontend: [
    { category: 'frontend', name: 'React', description: 'Popular JavaScript library for building user interfaces' },
    { category: 'frontend', name: 'Next.js', description: 'React framework with SSR and static generation' },
    { category: 'frontend', name: 'Vue.js', description: 'Progressive JavaScript framework' },
    { category: 'frontend', name: 'Angular', description: 'TypeScript-based web application framework' },
    { category: 'frontend', name: '<PERSON><PERSON><PERSON>', description: 'Compile-time optimized framework' },
    { category: 'frontend', name: 'Vanilla JavaScript', description: 'Pure JavaScript without frameworks' },
    { category: 'frontend', name: 'TypeScript', description: 'Typed superset of JavaScript' },
  ],
  
  backend: [
    { category: 'backend', name: 'Node.js', description: 'JavaScript runtime for server-side development' },
    { category: 'backend', name: 'Express.js', description: 'Fast, unopinionated web framework for Node.js' },
    { category: 'backend', name: 'Python/Django', description: 'High-level Python web framework' },
    { category: 'backend', name: 'Python/Flask', description: 'Lightweight Python web framework' },
    { category: 'backend', name: 'Python/FastAPI', description: 'Modern, fast Python API framework' },
    { category: 'backend', name: 'Go', description: 'Efficient, compiled programming language' },
    { category: 'backend', name: 'Rust', description: 'Systems programming language focused on safety' },
    { category: 'backend', name: 'Java/Spring Boot', description: 'Enterprise Java framework' },
    { category: 'backend', name: 'C#/.NET', description: 'Microsoft\'s cross-platform framework' },
  ],
  
  database: [
    { category: 'database', name: 'PostgreSQL', description: 'Advanced open-source relational database' },
    { category: 'database', name: 'MongoDB', description: 'NoSQL document database' },
    { category: 'database', name: 'SQLite', description: 'Lightweight embedded database' },
    { category: 'database', name: 'MySQL', description: 'Popular open-source relational database' },
    { category: 'database', name: 'Redis', description: 'In-memory data structure store' },
    { category: 'database', name: 'Supabase', description: 'Open-source Firebase alternative' },
    { category: 'database', name: 'Firebase', description: 'Google\'s mobile and web development platform' },
  ],
  
  styling: [
    { category: 'styling', name: 'Tailwind CSS', description: 'Utility-first CSS framework' },
    { category: 'styling', name: 'CSS Modules', description: 'Localized CSS' },
    { category: 'styling', name: 'Styled Components', description: 'CSS-in-JS library' },
    { category: 'styling', name: 'SCSS/Sass', description: 'CSS preprocessor' },
    { category: 'styling', name: 'Material-UI', description: 'React components implementing Material Design' },
    { category: 'styling', name: 'Chakra UI', description: 'Simple, modular and accessible component library' },
  ],
  
  'mobile-framework': [
    { category: 'mobile-framework', name: 'React Native', description: 'Cross-platform mobile development with React' },
    { category: 'mobile-framework', name: 'Flutter', description: 'Google\'s UI toolkit for mobile, web, and desktop' },
    { category: 'mobile-framework', name: 'Swift', description: 'Native iOS development language' },
    { category: 'mobile-framework', name: 'Kotlin', description: 'Modern programming language for Android' },
    { category: 'mobile-framework', name: 'Expo', description: 'Platform for universal React applications' },
  ],
  
  'desktop-framework': [
    { category: 'desktop-framework', name: 'Electron', description: 'Build desktop apps with web technologies' },
    { category: 'desktop-framework', name: 'Tauri', description: 'Rust-based desktop app framework' },
    { category: 'desktop-framework', name: 'Qt', description: 'Cross-platform application framework' },
    { category: 'desktop-framework', name: 'WPF', description: 'Windows Presentation Foundation' },
    { category: 'desktop-framework', name: 'JavaFX', description: 'Java platform for desktop applications' },
  ],
  
  deployment: [
    { category: 'deployment', name: 'Vercel', description: 'Platform for frontend frameworks and static sites' },
    { category: 'deployment', name: 'Netlify', description: 'Platform for modern web projects' },
    { category: 'deployment', name: 'AWS', description: 'Amazon Web Services cloud platform' },
    { category: 'deployment', name: 'Google Cloud', description: 'Google\'s cloud computing services' },
    { category: 'deployment', name: 'Docker', description: 'Containerization platform' },
    { category: 'deployment', name: 'Kubernetes', description: 'Container orchestration platform' },
  ],
  
  testing: [
    { category: 'testing', name: 'Jest', description: 'JavaScript testing framework' },
    { category: 'testing', name: 'Vitest', description: 'Fast unit test framework' },
    { category: 'testing', name: 'Cypress', description: 'End-to-end testing framework' },
    { category: 'testing', name: 'Playwright', description: 'Cross-browser automation library' },
    { category: 'testing', name: 'React Testing Library', description: 'Testing utilities for React components' },
  ],
  
  authentication: [
    { category: 'authentication', name: 'Auth0', description: 'Identity platform for developers' },
    { category: 'authentication', name: 'Firebase Auth', description: 'Google\'s authentication service' },
    { category: 'authentication', name: 'NextAuth.js', description: 'Authentication for Next.js' },
    { category: 'authentication', name: 'Supabase Auth', description: 'Open-source authentication' },
    { category: 'authentication', name: 'JWT', description: 'JSON Web Tokens for stateless authentication' },
  ],

  'state-management': [
    { category: 'state-management', name: 'Redux Toolkit', description: 'Modern Redux with simplified API' },
    { category: 'state-management', name: 'Zustand', description: 'Lightweight state management solution' },
    { category: 'state-management', name: 'Recoil', description: 'Experimental state management for React' },
    { category: 'state-management', name: 'MobX', description: 'Reactive state management through observables' },
    { category: 'state-management', name: 'Context API', description: 'React\'s built-in state management' },
    { category: 'state-management', name: 'Valtio', description: 'Proxy-based state management' },
  ],

  'api-tools': [
    { category: 'api-tools', name: 'GraphQL', description: 'Query language for APIs' },
    { category: 'api-tools', name: 'Apollo Client', description: 'Comprehensive GraphQL client' },
    { category: 'api-tools', name: 'React Query', description: 'Data fetching and caching library' },
    { category: 'api-tools', name: 'SWR', description: 'Data fetching with caching and revalidation' },
    { category: 'api-tools', name: 'Axios', description: 'Promise-based HTTP client' },
    { category: 'api-tools', name: 'Fetch API', description: 'Native browser API for HTTP requests' },
    { category: 'api-tools', name: 'tRPC', description: 'End-to-end typesafe APIs' },
  ],

  monitoring: [
    { category: 'monitoring', name: 'Sentry', description: 'Error tracking and performance monitoring' },
    { category: 'monitoring', name: 'LogRocket', description: 'Session replay and logging' },
    { category: 'monitoring', name: 'New Relic', description: 'Application performance monitoring' },
    { category: 'monitoring', name: 'Datadog', description: 'Infrastructure and application monitoring' },
    { category: 'monitoring', name: 'Prometheus', description: 'Open-source monitoring and alerting' },
    { category: 'monitoring', name: 'Grafana', description: 'Analytics and monitoring dashboards' },
  ],

  'ci-cd': [
    { category: 'ci-cd', name: 'GitHub Actions', description: 'CI/CD platform integrated with GitHub' },
    { category: 'ci-cd', name: 'GitLab CI', description: 'Built-in CI/CD for GitLab' },
    { category: 'ci-cd', name: 'Jenkins', description: 'Open-source automation server' },
    { category: 'ci-cd', name: 'CircleCI', description: 'Cloud-based CI/CD platform' },
    { category: 'ci-cd', name: 'Azure DevOps', description: 'Microsoft\'s DevOps platform' },
    { category: 'ci-cd', name: 'Travis CI', description: 'Hosted CI service for GitHub projects' },
  ],

  'version-control': [
    { category: 'version-control', name: 'Git', description: 'Distributed version control system' },
    { category: 'version-control', name: 'GitHub', description: 'Git hosting with collaboration features' },
    { category: 'version-control', name: 'GitLab', description: 'DevOps platform with Git repository' },
    { category: 'version-control', name: 'Bitbucket', description: 'Git solution for teams' },
    { category: 'version-control', name: 'Azure Repos', description: 'Git repositories in Azure DevOps' },
  ],

  'package-managers': [
    { category: 'package-managers', name: 'npm', description: 'Node.js package manager' },
    { category: 'package-managers', name: 'Yarn', description: 'Fast, reliable package manager' },
    { category: 'package-managers', name: 'pnpm', description: 'Efficient package manager with shared dependencies' },
    { category: 'package-managers', name: 'Bun', description: 'Fast all-in-one JavaScript runtime and package manager' },
  ],

  'build-tools': [
    { category: 'build-tools', name: 'Webpack', description: 'Module bundler for JavaScript applications' },
    { category: 'build-tools', name: 'Vite', description: 'Fast build tool for modern web projects' },
    { category: 'build-tools', name: 'Rollup', description: 'Module bundler for JavaScript libraries' },
    { category: 'build-tools', name: 'Parcel', description: 'Zero-configuration build tool' },
    { category: 'build-tools', name: 'esbuild', description: 'Extremely fast JavaScript bundler' },
    { category: 'build-tools', name: 'Turbopack', description: 'Incremental bundler optimized for JavaScript and TypeScript' },
  ],

  'code-quality': [
    { category: 'code-quality', name: 'ESLint', description: 'JavaScript linting utility' },
    { category: 'code-quality', name: 'Prettier', description: 'Code formatter' },
    { category: 'code-quality', name: 'Husky', description: 'Git hooks for code quality' },
    { category: 'code-quality', name: 'lint-staged', description: 'Run linters on staged files' },
    { category: 'code-quality', name: 'SonarQube', description: 'Code quality and security analysis' },
    { category: 'code-quality', name: 'CodeClimate', description: 'Automated code review and quality analytics' },
  ],

  'documentation': [
    { category: 'documentation', name: 'Storybook', description: 'Tool for building UI components in isolation' },
    { category: 'documentation', name: 'Docusaurus', description: 'Documentation website generator' },
    { category: 'documentation', name: 'GitBook', description: 'Documentation platform' },
    { category: 'documentation', name: 'Notion', description: 'All-in-one workspace for documentation' },
    { category: 'documentation', name: 'Confluence', description: 'Team collaboration and documentation' },
    { category: 'documentation', name: 'JSDoc', description: 'API documentation generator for JavaScript' },
  ],
};

export const getDefaultTechStack = (projectType: string): TechnologyStack => {
  const stacks: Record<string, TechnologyStack> = {
    'web-application': {
      frontend: [TECHNOLOGIES.frontend[1]], // Next.js
      styling: [TECHNOLOGIES.styling[0]], // Tailwind CSS
      database: [TECHNOLOGIES.database[5]], // Supabase
      deployment: [TECHNOLOGIES.deployment[0]], // Vercel
      testing: [TECHNOLOGIES.testing[0]], // Jest
    },
    'mobile-application': {
      'mobile-framework': [TECHNOLOGIES['mobile-framework'][0]], // React Native
      database: [TECHNOLOGIES.database[6]], // Firebase
      testing: [TECHNOLOGIES.testing[0]], // Jest
    },
    'api-backend': {
      backend: [TECHNOLOGIES.backend[0]], // Node.js
      database: [TECHNOLOGIES.database[0]], // PostgreSQL
      deployment: [TECHNOLOGIES.deployment[2]], // AWS
      testing: [TECHNOLOGIES.testing[0]], // Jest
    },
  };
  
  return stacks[projectType] || {};
};
