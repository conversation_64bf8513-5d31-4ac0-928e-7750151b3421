const t={};class e{constructor(){this.subscriptions=[]}add(t){var e,s;return e=this.subscriptions,s=t,-1===e.indexOf(s)&&e.push(s),()=>function(t,e){const s=t.indexOf(e);s>-1&&t.splice(s,1)}(this.subscriptions,t)}notify(t,e,s){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,s);else for(let n=0;n<i;n++){const i=this.subscriptions[n];i&&i(t,e,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const s=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],i={value:null,addProjectionMetrics:null};function n(e,n){let r=!1,a=!0;const o={delta:0,timestamp:0,isProcessing:!1},c=()=>r=!0,h=s.reduce((t,e)=>(t[e]=function(t,e){let s=new Set,n=new Set,r=!1,a=!1;const o=new WeakSet;let c={delta:0,timestamp:0,isProcessing:!1},h=0;function d(e){o.has(e)&&(p.schedule(e),t()),h++,e(c)}const p={schedule:(t,e=!1,i=!1)=>{const a=i&&r?s:n;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{c=t,r?a=!0:(r=!0,[s,n]=[n,s],s.forEach(d),e&&i.value&&i.value.frameloop[e].push(h),h=0,s.clear(),r=!1,a&&(a=!1,p.process(t)))}};return p}(c,n?e:void 0),t),{}),{setup:d,read:p,resolveKeyframes:u,preUpdate:l,update:v,preRender:m,render:f,postRender:g}=h,y=()=>{const s=t.useManualTiming?o.timestamp:performance.now();r=!1,t.useManualTiming||(o.delta=a?1e3/60:Math.max(Math.min(s-o.timestamp,40),1)),o.timestamp=s,o.isProcessing=!0,d.process(o),p.process(o),u.process(o),l.process(o),v.process(o),m.process(o),f.process(o),g.process(o),o.isProcessing=!1,r&&n&&(a=!1,e(y))};return{schedule:s.reduce((t,s)=>{const i=h[s];return t[s]=(t,s=!1,n=!1)=>(r||(r=!0,a=!0,o.isProcessing||e(y)),i.schedule(t,s,n)),t},{}),cancel:t=>{for(let e=0;e<s.length;e++)h[s[e]].cancel(t)},state:o,steps:h}}const{schedule:r,cancel:a,state:o,steps:c}=n("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:t=>t,!0);let h;function d(){h=void 0}const p={now:()=>(void 0===h&&p.set(o.isProcessing||t.useManualTiming?o.timestamp:performance.now()),h),set:t=>{h=t,queueMicrotask(d)}},u={current:void 0};class l{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const s=p.now();if(this.updatedAt!==s&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=p.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,s){this.events[t]||(this.events[t]=new e);const i=this.events[t].add(s);return"change"===t?()=>{i(),r.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,s){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-s}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return u.current&&u.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=p.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return s=parseFloat(this.current)-parseFloat(this.prevFrameValue),(i=e)?s*(1e3/i):0;var s,i}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function v(t,e){return new l(t,e)}export{l as MotionValue,u as collectMotionValues,v as motionValue};
