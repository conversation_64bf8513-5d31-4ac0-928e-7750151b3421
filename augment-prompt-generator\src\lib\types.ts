// Types for the prompt generator

export interface ProjectInput {
  projectName: string;
  projectIdea: string;
  projectType: ProjectType;
  platform: Platform;
  technologies: Technology[];
  complexity: Complexity;
  features?: string[];
  additionalRequirements?: string;
}

export interface GeneratedPrompt {
  title: string;
  content: string;
  sections: {
    projectDescription: string;
    technicalRequirements: string;
    technologyStack: string;
    implementationApproach: string;
    testingRequirements: string;
    augmentSpecificInstructions: string;
  };
}

export type ProjectType = 
  | 'web-application'
  | 'mobile-application'
  | 'desktop-application'
  | 'api-backend'
  | 'data-analysis'
  | 'machine-learning'
  | 'devops-infrastructure'
  | 'chrome-extension'
  | 'cli-tool'
  | 'library-package';

export type Platform = 
  | 'web'
  | 'mobile'
  | 'desktop'
  | 'server'
  | 'cloud'
  | 'cross-platform';

export type Technology = {
  category: TechnologyCategory;
  name: string;
  description?: string;
};

export type TechnologyCategory =
  | 'frontend'
  | 'backend'
  | 'database'
  | 'deployment'
  | 'testing'
  | 'styling'
  | 'state-management'
  | 'authentication'
  | 'api'
  | 'mobile-framework'
  | 'desktop-framework'
  | 'api-tools'
  | 'monitoring'
  | 'ci-cd'
  | 'version-control'
  | 'package-managers'
  | 'build-tools'
  | 'code-quality'
  | 'documentation';

export type Complexity = 'simple' | 'intermediate' | 'advanced';

export interface ProjectTemplate {
  type: ProjectType;
  name: string;
  description: string;
  defaultTechnologies: Technology[];
  commonFeatures: string[];
  promptTemplate: string;
}

export type TechnologyStack = {
  [key in TechnologyCategory]?: Technology[];
};
