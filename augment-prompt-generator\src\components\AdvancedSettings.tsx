import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { AI_MODELS, PromptOptimizationOptions } from '@/lib/ai-service';
import { apiIntegrationsService, APIIntegration } from '@/lib/api-integrations';

import { ProjectInput } from '@/lib/types';
import CustomSelect from './CustomSelect';

interface AdvancedSettingsProps {
  isOpen: boolean;
  onClose: () => void;
  settings: PromptOptimizationOptions;
  onSettingsChange: (settings: PromptOptimizationOptions) => void;
  recommendedModel?: string;
  projectInput?: ProjectInput;
  generatedPrompt?: string;
}

export default function AdvancedSettings({
  isOpen,
  onClose,
  settings,
  onSettingsChange,
  recommendedModel,
  projectInput,
  generatedPrompt
}: AdvancedSettingsProps) {
  const [localSettings, setLocalSettings] = useState<PromptOptimizationOptions>(settings);
  const [activeTab, setActiveTab] = useState<'ai' | 'integrations'>('ai');

  // Integration states
  const [integrations, setIntegrations] = useState<APIIntegration[]>([]);
  const [selectedIntegration, setSelectedIntegration] = useState<APIIntegration | null>(null);
  const [isConfiguring, setIsConfiguring] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [testResults, setTestResults] = useState<Record<string, { success: boolean; data?: unknown; error?: string }>>({});
  const [configForm, setConfigForm] = useState<Record<string, string>>({});



  useEffect(() => {
    if (isOpen && activeTab === 'integrations') {
      loadIntegrationData();
    }

  }, [isOpen, activeTab]);

  const loadIntegrationData = () => {
    setIntegrations(apiIntegrationsService.getIntegrations());
  };



  const handleEnableIntegration = async (integration: APIIntegration) => {
    setSelectedIntegration(integration);
    setIsConfiguring(true);
    setConfigForm({});
  };

  const handleSaveConfig = async () => {
    if (!selectedIntegration) return;

    const success = apiIntegrationsService.enableIntegration(selectedIntegration.id, configForm);
    if (success) {
      loadIntegrationData();
      setIsConfiguring(false);
      setSelectedIntegration(null);
      setConfigForm({});
    }
  };

  const handleDisableIntegration = (id: string) => {
    apiIntegrationsService.disableIntegration(id);
    loadIntegrationData();
  };

  const handleTestIntegration = async (integration: APIIntegration) => {
    setIsTesting(true);
    const result = await apiIntegrationsService.testIntegration(integration.id);
    setTestResults({ ...testResults, [integration.id]: result });
    setIsTesting(false);
  };

  const handleSendToIntegration = async (integration: APIIntegration) => {
    if (!projectInput || !generatedPrompt) return;

    setIsSending(true);
    const result = await apiIntegrationsService.sendToIntegration(
      integration.id,
      projectInput,
      generatedPrompt
    );
    setTestResults({ ...testResults, [`${integration.id}_send`]: result });
    setIsSending(false);
  };

  const modelOptions = Object.entries(AI_MODELS).map(([, model]) => ({
    value: model.id,
    label: model.name,
    description: `${model.provider} • ${model.description}`
  }));

  const optimizationOptions = [
    { value: 'basic', label: 'Basic', description: 'Clear, structured prompt with essentials' },
    { value: 'enhanced', label: 'Enhanced', description: 'Comprehensive with detailed specifications' },
    { value: 'expert', label: 'Expert', description: 'Advanced architectural considerations' }
  ];

  const audienceOptions = [
    { value: 'developer', label: 'Developer', description: 'Technical implementation focus' },
    { value: 'business', label: 'Business', description: 'Business value and outcomes' },
    { value: 'technical', label: 'Technical', description: 'System requirements and integration' },
    { value: 'general', label: 'General', description: 'Balanced technical and business' }
  ];

  const handleSave = () => {
    onSettingsChange(localSettings);
    onClose();
  };

  const handleReset = () => {
    const defaultSettings: PromptOptimizationOptions = {
      model: recommendedModel || 'deepseek/deepseek-chat-v3-0324:free',
      optimizationLevel: 'enhanced',
      includeExamples: true,
      includeConstraints: true,
      includeMetrics: false,
      targetAudience: 'developer'
    };
    setLocalSettings(defaultSettings);
  };



  if (!isOpen) return null;

  const selectedModel = Object.values(AI_MODELS).find(m => m.id === localSettings.model);

  return (
    <>
      {/* Main Modal */}
      <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-2 sm:p-4">
        <div className="bg-black border border-white/20 rounded-xl shadow-2xl p-4 sm:p-6 w-full max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-y-auto" style={{
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8)'
        }}>
        <div className="flex justify-between items-center mb-4 sm:mb-6">
          <h3 className="text-lg sm:text-xl font-semibold text-white">AI Settings & Integrations</h3>
          <button
            onClick={onClose}
            className="bg-gray-800 text-white px-3 py-1 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm flex-shrink-0"
          >
            Close
          </button>
        </div>

        {/* Tabs - AI Settings, Integrations */}
        <div className="flex gap-2 sm:gap-3 mb-4 sm:mb-6 overflow-x-auto">
          <button
            onClick={() => setActiveTab('ai')}
            className={`px-3 sm:px-4 py-2 rounded-md transition-all duration-300 whitespace-nowrap text-sm sm:text-base flex-shrink-0 ${
              activeTab === 'ai'
                ? 'bg-white text-black'
                : 'bg-gray-800 text-gray-400 hover:bg-gray-700'
            }`}
          >
            🤖 AI Settings
          </button>
          <button
            onClick={() => setActiveTab('integrations')}
            className={`px-3 sm:px-4 py-2 rounded-md transition-all duration-300 whitespace-nowrap text-sm sm:text-base flex-shrink-0 ${
              activeTab === 'integrations'
                ? 'bg-white text-black'
                : 'bg-gray-800 text-gray-400 hover:bg-gray-700'
            }`}
          >
            🔗 Integrations
          </button>
        </div>

        {activeTab === 'ai' && (
          <div className="space-y-4 sm:space-y-6">
            {/* AI Model Selection */}
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                AI Model
                {recommendedModel && localSettings.model === recommendedModel && (
                  <span className="ml-2 text-xs bg-white text-black px-2 py-1 rounded">Recommended</span>
                )}
              </label>
              <CustomSelect
                value={localSettings.model}
                onChange={(value) => setLocalSettings(prev => ({ ...prev, model: value }))}
                options={modelOptions}
                placeholder="Select AI model"
              />
              {selectedModel && (
                <div className="mt-2 p-3 bg-gray-900 border border-white/10 rounded-lg">
                  <div className="text-sm text-white font-medium mb-1">{selectedModel.name}</div>
                  <div className="text-xs text-gray-400 mb-2">{selectedModel.description}</div>
                  <div className="flex flex-wrap gap-1 mb-2">
                    {selectedModel.strengths.map((strength, index) => (
                      <span key={index} className="bg-gray-700 text-gray-200 text-xs px-2 py-1 rounded">
                        {strength}
                      </span>
                    ))}
                  </div>
                  <div className="text-xs text-gray-500">
                    Max tokens: {selectedModel.maxTokens.toLocaleString()} •
                    Cost: ${selectedModel.costPer1kTokens}/1k tokens
                  </div>
                </div>
              )}
            </div>

          {/* Optimization Level */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Optimization Level</label>
            <CustomSelect
              value={localSettings.optimizationLevel}
              onChange={(value) => setLocalSettings(prev => ({ ...prev, optimizationLevel: value as 'basic' | 'enhanced' | 'expert' }))}
              options={optimizationOptions}
              placeholder="Select optimization level"
            />
          </div>

          {/* Target Audience */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Target Audience</label>
            <CustomSelect
              value={localSettings.targetAudience}
              onChange={(value) => setLocalSettings(prev => ({ ...prev, targetAudience: value as 'developer' | 'business' | 'technical' | 'general' }))}
              options={audienceOptions}
              placeholder="Select target audience"
            />
          </div>

          {/* Additional Options */}
          <div>
            <label className="block text-sm font-medium text-white mb-3">Additional Options</label>
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={localSettings.includeExamples}
                  onChange={(e) => setLocalSettings(prev => ({ ...prev, includeExamples: e.target.checked }))}
                  className="mr-3 accent-white"
                />
                <div>
                  <span className="text-white text-sm font-medium">Include Examples</span>
                  <p className="text-xs text-gray-400">Add relevant code examples and snippets</p>
                </div>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={localSettings.includeConstraints}
                  onChange={(e) => setLocalSettings(prev => ({ ...prev, includeConstraints: e.target.checked }))}
                  className="mr-3 accent-white"
                />
                <div>
                  <span className="text-white text-sm font-medium">Include Constraints</span>
                  <p className="text-xs text-gray-400">Specify technical limitations and requirements</p>
                </div>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={localSettings.includeMetrics}
                  onChange={(e) => setLocalSettings(prev => ({ ...prev, includeMetrics: e.target.checked }))}
                  className="mr-3 accent-white"
                />
                <div>
                  <span className="text-white text-sm font-medium">Include Success Metrics</span>
                  <p className="text-xs text-gray-400">Add KPIs and measurable outcomes</p>
                </div>
              </label>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row justify-between pt-4 sm:pt-6 border-t border-white/10 space-y-3 sm:space-y-0">
            <button
              onClick={handleReset}
              className="group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-medium text-white bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl border border-white/10 transition-all duration-300 hover:border-white/20 hover:from-gray-700/90 hover:to-gray-800/90 focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-black"
              style={{
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
              }}
            >
              <span className="flex items-center justify-center space-x-2">
                <svg className="w-4 h-4 transition-transform duration-300 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span>Reset to Defaults</span>
              </span>
            </button>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <button
                onClick={onClose}
                className="group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-medium text-gray-300 bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl border border-white/10 transition-all duration-300 hover:border-white/20 hover:from-gray-700/90 hover:to-gray-800/90 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-black"
                style={{
                  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
                }}
              >
                <span className="flex items-center justify-center space-x-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  <span>Cancel</span>
                </span>
              </button>
              <button
                onClick={handleSave}
                className="group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-semibold text-black bg-gradient-to-br from-white to-gray-100 backdrop-blur-xl border border-white/20 transition-all duration-300 hover:from-gray-100 hover:to-gray-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-black transform hover:scale-[1.02]"
                style={{
                  boxShadow: '0 6px 20px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
                }}
              >
                <span className="flex items-center justify-center space-x-2">
                  <svg className="w-4 h-4 transition-transform duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Save Settings</span>
                </span>
              </button>
            </div>
          </div>
          </div>
        )}

        {activeTab === 'integrations' && (
          <div className="space-y-4 sm:space-y-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              {integrations.map((integration) => (
                <div
                  key={integration.id}
                  className={`p-3 sm:p-4 rounded-lg border transition-all duration-300 ${
                    integration.isEnabled
                      ? 'bg-gray-800 border-white'
                      : 'bg-gray-900 border-white/10 hover:border-white/20'
                  }`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-start space-x-2 sm:space-x-3 flex-1 min-w-0">
                      <div className="text-xl sm:text-2xl flex-shrink-0">{integration.icon}</div>
                      <div className="min-w-0 flex-1">
                        <h4 className="text-white font-medium text-sm sm:text-base truncate">{integration.name}</h4>
                        <p className="text-gray-400 text-xs sm:text-sm line-clamp-2">{integration.description}</p>
                      </div>
                    </div>
                    <div className={`w-3 h-3 rounded-full ${
                      integration.isEnabled ? 'bg-white' : 'bg-gray-500'
                    }`} />
                  </div>

                  <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                    {!integration.isEnabled ? (
                      <button
                        onClick={() => handleEnableIntegration(integration)}
                        className="flex-1 bg-gray-800 text-white py-2 px-3 rounded-md hover:bg-gray-700 transition-all duration-300 text-xs sm:text-sm"
                      >
                        Enable
                      </button>
                    ) : (
                      <>
                        <button
                          onClick={() => handleTestIntegration(integration)}
                          disabled={isTesting}
                          className="flex-1 bg-gray-700 text-white py-2 px-3 rounded-md hover:bg-gray-600 transition-all duration-300 text-xs sm:text-sm disabled:opacity-50"
                        >
                          {isTesting ? 'Testing...' : 'Test'}
                        </button>
                        {projectInput && generatedPrompt && (
                          <button
                            onClick={() => handleSendToIntegration(integration)}
                            disabled={isSending}
                            className="flex-1 bg-gray-800 text-white py-2 px-3 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm disabled:opacity-50"
                          >
                            {isSending ? 'Sending...' : 'Send'}
                          </button>
                        )}
                        <button
                          onClick={() => handleDisableIntegration(integration.id)}
                          className="bg-gray-800 text-white py-2 px-3 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm"
                        >
                          Disable
                        </button>
                      </>
                    )}
                  </div>

                  {testResults[integration.id] && (
                    <div className="mt-3 p-2 bg-gray-800 border border-white/10 rounded text-xs">
                      <div className={`font-medium ${
                        testResults[integration.id].success ? 'text-white' : 'text-gray-300'
                      }`}>
                        {testResults[integration.id].success ? 'Success' : 'Failed'}
                      </div>
                      <div className="text-gray-400 mt-1">
                        {testResults[integration.id].error || 'Connection successful'}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>


          </div>
        )}


        </div>
      </div>

      {/* Configuration Modal - Rendered using Portal for proper positioning */}
      {isConfiguring && selectedIntegration && createPortal(
        <div
          className="fixed inset-0 bg-black/90 flex items-center justify-center p-4"
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 10000,
            margin: 0,
            padding: '1rem'
          }}
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setIsConfiguring(false);
            }
          }}
        >
          <div
            className="bg-black border border-white/20 rounded-xl shadow-2xl p-6 w-full max-w-md mx-auto my-auto"
            style={{
              maxHeight: '90vh',
              overflow: 'auto',
              position: 'relative',
              transform: 'none'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <h3 className="text-lg font-semibold text-white mb-4">
              Configure {selectedIntegration.name}
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  API Token
                </label>
                <input
                  type="password"
                  value={configForm.token || ''}
                  onChange={(e) => setConfigForm({ ...configForm, token: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-900 border border-white/10 rounded-md text-white placeholder-gray-400 focus:border-white/30 focus:outline-none"
                  placeholder="Enter your API token"
                />
              </div>
            </div>

            <div className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 mt-4 sm:mt-6">
              <button
                onClick={() => setIsConfiguring(false)}
                className="group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-medium text-gray-300 bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl border border-white/10 transition-all duration-300 hover:border-white/20 hover:from-gray-700/90 hover:to-gray-800/90 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-black"
                style={{
                  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
                }}
              >
                <span className="flex items-center justify-center space-x-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  <span>Cancel</span>
                </span>
              </button>
              <button
                onClick={handleSaveConfig}
                className="group w-full sm:w-auto px-4 sm:px-5 py-2.5 sm:py-3 rounded-lg text-sm font-semibold text-black bg-gradient-to-br from-white to-gray-100 backdrop-blur-xl border border-white/20 transition-all duration-300 hover:from-gray-100 hover:to-gray-200 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-black transform hover:scale-[1.02]"
                style={{
                  boxShadow: '0 6px 20px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
                }}
              >
                <span className="flex items-center justify-center space-x-2">
                  <svg className="w-4 h-4 transition-transform duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Save</span>
                </span>
              </button>
            </div>
          </div>
        </div>,
        document.body
      )}
    </>
  );
}
