import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { AI_MODELS, PromptOptimizationOptions } from '@/lib/ai-service';
import { apiIntegrationsService, APIIntegration } from '@/lib/api-integrations';

import { ProjectInput } from '@/lib/types';
import CustomSelect from './CustomSelect';

interface AdvancedSettingsProps {
  isOpen: boolean;
  onClose: () => void;
  settings: PromptOptimizationOptions;
  onSettingsChange: (settings: PromptOptimizationOptions) => void;
  recommendedModel?: string;
  projectInput?: ProjectInput;
  generatedPrompt?: string;
}

export default function AdvancedSettings({
  isOpen,
  onClose,
  settings,
  onSettingsChange,
  recommendedModel,
  projectInput,
  generatedPrompt
}: AdvancedSettingsProps) {
  const [localSettings, setLocalSettings] = useState<PromptOptimizationOptions>(settings);
  const [activeTab, setActiveTab] = useState<'ai' | 'integrations'>('ai');

  // Integration states
  const [integrations, setIntegrations] = useState<APIIntegration[]>([]);
  const [selectedIntegration, setSelectedIntegration] = useState<APIIntegration | null>(null);
  const [isConfiguring, setIsConfiguring] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [testResults, setTestResults] = useState<Record<string, { success: boolean; data?: unknown; error?: string }>>({});
  const [configForm, setConfigForm] = useState<Record<string, string>>({});



  useEffect(() => {
    if (isOpen && activeTab === 'integrations') {
      loadIntegrationData();
    }

  }, [isOpen, activeTab]);

  const loadIntegrationData = () => {
    setIntegrations(apiIntegrationsService.getIntegrations());
  };



  const handleEnableIntegration = async (integration: APIIntegration) => {
    setSelectedIntegration(integration);
    setIsConfiguring(true);
    setConfigForm({});
  };

  const handleSaveConfig = async () => {
    if (!selectedIntegration) return;

    const success = apiIntegrationsService.enableIntegration(selectedIntegration.id, configForm);
    if (success) {
      loadIntegrationData();
      setIsConfiguring(false);
      setSelectedIntegration(null);
      setConfigForm({});
    }
  };

  const handleDisableIntegration = (id: string) => {
    apiIntegrationsService.disableIntegration(id);
    loadIntegrationData();
  };

  const handleTestIntegration = async (integration: APIIntegration) => {
    setIsTesting(true);
    const result = await apiIntegrationsService.testIntegration(integration.id);
    setTestResults({ ...testResults, [integration.id]: result });
    setIsTesting(false);
  };

  const handleSendToIntegration = async (integration: APIIntegration) => {
    if (!projectInput || !generatedPrompt) return;

    setIsSending(true);
    const result = await apiIntegrationsService.sendToIntegration(
      integration.id,
      projectInput,
      generatedPrompt
    );
    setTestResults({ ...testResults, [`${integration.id}_send`]: result });
    setIsSending(false);
  };

  const modelOptions = Object.entries(AI_MODELS).map(([, model]) => ({
    value: model.id,
    label: model.name,
    description: `${model.provider} • ${model.description}`
  }));

  const optimizationOptions = [
    { value: 'basic', label: 'Basic', description: 'Clear, structured prompt with essentials' },
    { value: 'enhanced', label: 'Enhanced', description: 'Comprehensive with detailed specifications' },
    { value: 'expert', label: 'Expert', description: 'Advanced architectural considerations' }
  ];

  const audienceOptions = [
    { value: 'developer', label: 'Developer', description: 'Technical implementation focus' },
    { value: 'business', label: 'Business', description: 'Business value and outcomes' },
    { value: 'technical', label: 'Technical', description: 'System requirements and integration' },
    { value: 'general', label: 'General', description: 'Balanced technical and business' }
  ];

  const handleSave = () => {
    onSettingsChange(localSettings);
    onClose();
  };

  const handleReset = () => {
    const defaultSettings: PromptOptimizationOptions = {
      model: recommendedModel || 'deepseek/deepseek-chat-v3-0324:free',
      optimizationLevel: 'enhanced',
      includeExamples: true,
      includeConstraints: true,
      includeMetrics: false,
      targetAudience: 'developer'
    };
    setLocalSettings(defaultSettings);
  };



  if (!isOpen) return null;

  const selectedModel = Object.values(AI_MODELS).find(m => m.id === localSettings.model);

  return (
    <>
      {/* Main Modal */}
      <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
        <div className="bg-black border border-white/20 rounded-xl shadow-2xl p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto" style={{
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8)'
        }}>
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-white">AI Settings & Integrations</h3>
          <button
            onClick={onClose}
            className="bg-gray-800 text-white px-3 py-1 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm"
          >
            Close
          </button>
        </div>

        {/* Tabs - AI Settings, Integrations */}
        <div className="flex flex-wrap gap-3 mb-6 overflow-x-auto">
          <button
            onClick={() => setActiveTab('ai')}
            className={`px-3 py-2 rounded-md transition-all duration-300 whitespace-nowrap ${
              activeTab === 'ai'
                ? 'bg-white text-black'
                : 'bg-gray-800 text-gray-400 hover:bg-gray-700'
            }`}
          >
            🤖 AI Settings
          </button>
          <button
            onClick={() => setActiveTab('integrations')}
            className={`px-3 py-2 rounded-md transition-all duration-300 whitespace-nowrap ${
              activeTab === 'integrations'
                ? 'bg-white text-black'
                : 'bg-gray-800 text-gray-400 hover:bg-gray-700'
            }`}
          >
            🔗 Integrations
          </button>
        </div>

        {activeTab === 'ai' && (
          <div className="space-y-6">
            {/* AI Model Selection */}
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                AI Model
                {recommendedModel && localSettings.model === recommendedModel && (
                  <span className="ml-2 text-xs bg-white text-black px-2 py-1 rounded">Recommended</span>
                )}
              </label>
              <CustomSelect
                value={localSettings.model}
                onChange={(value) => setLocalSettings(prev => ({ ...prev, model: value }))}
                options={modelOptions}
                placeholder="Select AI model"
              />
              {selectedModel && (
                <div className="mt-2 p-3 bg-gray-900 border border-white/10 rounded-lg">
                  <div className="text-sm text-white font-medium mb-1">{selectedModel.name}</div>
                  <div className="text-xs text-gray-400 mb-2">{selectedModel.description}</div>
                  <div className="flex flex-wrap gap-1 mb-2">
                    {selectedModel.strengths.map((strength, index) => (
                      <span key={index} className="bg-gray-700 text-gray-200 text-xs px-2 py-1 rounded">
                        {strength}
                      </span>
                    ))}
                  </div>
                  <div className="text-xs text-gray-500">
                    Max tokens: {selectedModel.maxTokens.toLocaleString()} •
                    Cost: ${selectedModel.costPer1kTokens}/1k tokens
                  </div>
                </div>
              )}
            </div>

          {/* Optimization Level */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Optimization Level</label>
            <CustomSelect
              value={localSettings.optimizationLevel}
              onChange={(value) => setLocalSettings(prev => ({ ...prev, optimizationLevel: value as 'basic' | 'enhanced' | 'expert' }))}
              options={optimizationOptions}
              placeholder="Select optimization level"
            />
          </div>

          {/* Target Audience */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Target Audience</label>
            <CustomSelect
              value={localSettings.targetAudience}
              onChange={(value) => setLocalSettings(prev => ({ ...prev, targetAudience: value as 'developer' | 'business' | 'technical' | 'general' }))}
              options={audienceOptions}
              placeholder="Select target audience"
            />
          </div>

          {/* Additional Options */}
          <div>
            <label className="block text-sm font-medium text-white mb-3">Additional Options</label>
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={localSettings.includeExamples}
                  onChange={(e) => setLocalSettings(prev => ({ ...prev, includeExamples: e.target.checked }))}
                  className="mr-3 accent-white"
                />
                <div>
                  <span className="text-white text-sm font-medium">Include Examples</span>
                  <p className="text-xs text-gray-400">Add relevant code examples and snippets</p>
                </div>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={localSettings.includeConstraints}
                  onChange={(e) => setLocalSettings(prev => ({ ...prev, includeConstraints: e.target.checked }))}
                  className="mr-3 accent-white"
                />
                <div>
                  <span className="text-white text-sm font-medium">Include Constraints</span>
                  <p className="text-xs text-gray-400">Specify technical limitations and requirements</p>
                </div>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={localSettings.includeMetrics}
                  onChange={(e) => setLocalSettings(prev => ({ ...prev, includeMetrics: e.target.checked }))}
                  className="mr-3 accent-white"
                />
                <div>
                  <span className="text-white text-sm font-medium">Include Success Metrics</span>
                  <p className="text-xs text-gray-400">Add KPIs and measurable outcomes</p>
                </div>
              </label>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between pt-4 border-t border-white/10">
            <button
              onClick={handleReset}
              className="bg-gray-800 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm"
            >
              Reset to Defaults
            </button>
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="bg-gray-800 text-gray-400 px-4 py-2 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="bg-white text-black px-4 py-2 rounded-md hover:bg-gray-200 transition-all duration-300 text-sm font-medium"
              >
                Save Settings
              </button>
            </div>
          </div>
          </div>
        )}

        {activeTab === 'integrations' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {integrations.map((integration) => (
                <div
                  key={integration.id}
                  className={`p-4 rounded-lg border transition-all duration-300 ${
                    integration.isEnabled
                      ? 'bg-gray-800 border-white'
                      : 'bg-gray-900 border-white/10 hover:border-white/20'
                  }`}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="text-2xl">{integration.icon}</div>
                      <div>
                        <h4 className="text-white font-medium">{integration.name}</h4>
                        <p className="text-gray-400 text-sm">{integration.description}</p>
                      </div>
                    </div>
                    <div className={`w-3 h-3 rounded-full ${
                      integration.isEnabled ? 'bg-white' : 'bg-gray-500'
                    }`} />
                  </div>

                  <div className="flex space-x-2">
                    {!integration.isEnabled ? (
                      <button
                        onClick={() => handleEnableIntegration(integration)}
                        className="flex-1 bg-gray-800 text-white py-2 px-3 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm"
                      >
                        Enable
                      </button>
                    ) : (
                      <>
                        <button
                          onClick={() => handleTestIntegration(integration)}
                          disabled={isTesting}
                          className="flex-1 bg-gray-700 text-white py-2 px-3 rounded-md hover:bg-gray-600 transition-all duration-300 text-sm disabled:opacity-50"
                        >
                          {isTesting ? 'Testing...' : 'Test'}
                        </button>
                        {projectInput && generatedPrompt && (
                          <button
                            onClick={() => handleSendToIntegration(integration)}
                            disabled={isSending}
                            className="flex-1 bg-gray-800 text-white py-2 px-3 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm disabled:opacity-50"
                          >
                            {isSending ? 'Sending...' : 'Send'}
                          </button>
                        )}
                        <button
                          onClick={() => handleDisableIntegration(integration.id)}
                          className="bg-gray-800 text-white py-2 px-3 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm"
                        >
                          Disable
                        </button>
                      </>
                    )}
                  </div>

                  {testResults[integration.id] && (
                    <div className="mt-3 p-2 bg-gray-800 border border-white/10 rounded text-xs">
                      <div className={`font-medium ${
                        testResults[integration.id].success ? 'text-white' : 'text-gray-300'
                      }`}>
                        {testResults[integration.id].success ? 'Success' : 'Failed'}
                      </div>
                      <div className="text-gray-400 mt-1">
                        {testResults[integration.id].error || 'Connection successful'}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>


          </div>
        )}


        </div>
      </div>

      {/* Configuration Modal - Rendered using Portal for proper positioning */}
      {isConfiguring && selectedIntegration && createPortal(
        <div
          className="fixed inset-0 bg-black/90 flex items-center justify-center p-4"
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 10000,
            margin: 0,
            padding: '1rem'
          }}
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setIsConfiguring(false);
            }
          }}
        >
          <div
            className="bg-black border border-white/20 rounded-xl shadow-2xl p-6 w-full max-w-md mx-auto my-auto"
            style={{
              maxHeight: '90vh',
              overflow: 'auto',
              position: 'relative',
              transform: 'none'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <h3 className="text-lg font-semibold text-white mb-4">
              Configure {selectedIntegration.name}
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  API Token
                </label>
                <input
                  type="password"
                  value={configForm.token || ''}
                  onChange={(e) => setConfigForm({ ...configForm, token: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-900 border border-white/10 rounded-md text-white placeholder-gray-400 focus:border-white/30 focus:outline-none"
                  placeholder="Enter your API token"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setIsConfiguring(false)}
                className="bg-gray-800 text-gray-400 px-4 py-2 rounded-md hover:bg-gray-700 transition-all duration-300 text-sm"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveConfig}
                className="bg-white text-black px-4 py-2 rounded-md hover:bg-gray-200 transition-all duration-300 text-sm"
              >
                Save
              </button>
            </div>
          </div>
        </div>,
        document.body
      )}
    </>
  );
}
