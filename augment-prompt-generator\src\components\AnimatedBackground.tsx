'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';

interface Star {
  x: number;
  y: number;
  vx: number;
  vy: number;
  radius: number;
  opacity: number;
  twinkleSpeed: number;
  twinklePhase: number;
}

interface MousePosition {
  x: number;
  y: number;
}

const AnimatedBackground: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | null>(null);
  const starsRef = useRef<Star[]>([]);
  const mouseRef = useRef<MousePosition>({ x: 0, y: 0 });
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  // Initialize stars
  const initStars = (width: number, height: number) => {
    const stars: Star[] = [];
    const numStars = Math.floor((width * height) / 6000); // More stars for richer effect

    for (let i = 0; i < numStars; i++) {
      stars.push({
        x: Math.random() * width,
        y: Math.random() * height,
        vx: (Math.random() - 0.5) * 0.8,
        vy: (Math.random() - 0.5) * 0.8,
        radius: Math.random() * 3 + 0.3, // Varied sizes
        opacity: Math.random() * 0.9 + 0.1,
        twinkleSpeed: Math.random() * 0.03 + 0.005,
        twinklePhase: Math.random() * Math.PI * 2,
      });
    }
    starsRef.current = stars;
  };

  // Update canvas dimensions
  const updateDimensions = useCallback(() => {
    if (canvasRef.current) {
      const { innerWidth, innerHeight } = window;
      setDimensions({ width: innerWidth, height: innerHeight });
      canvasRef.current.width = innerWidth;
      canvasRef.current.height = innerHeight;
      initStars(innerWidth, innerHeight);
    }
  }, []);

  // Mouse move handler
  const handleMouseMove = (event: MouseEvent) => {
    mouseRef.current = {
      x: event.clientX,
      y: event.clientY,
    };
  };

  // Animation loop
  const animate = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const { width, height } = dimensions;
    const mouse = mouseRef.current;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Update and draw stars
    starsRef.current.forEach((star) => {
      // Mouse interaction - attraction and repulsion effect
      const dx = mouse.x - star.x;
      const dy = mouse.y - star.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      const maxDistance = 200;
      const repulsionDistance = 80;

      if (distance < maxDistance && distance > 0) {
        const force = (maxDistance - distance) / maxDistance;
        if (distance < repulsionDistance) {
          // Repulsion effect for close stars
          star.vx -= (dx / distance) * force * 0.003;
          star.vy -= (dy / distance) * force * 0.003;
        } else {
          // Attraction effect for distant stars
          star.vx += (dx / distance) * force * 0.0015;
          star.vy += (dy / distance) * force * 0.0015;
        }
      }

      // Update position
      star.x += star.vx;
      star.y += star.vy;

      // Boundary wrapping
      if (star.x < 0) star.x = width;
      if (star.x > width) star.x = 0;
      if (star.y < 0) star.y = height;
      if (star.y > height) star.y = 0;

      // Damping
      star.vx *= 0.99;
      star.vy *= 0.99;

      // Twinkle effect
      star.twinklePhase += star.twinkleSpeed;
      const twinkle = Math.sin(star.twinklePhase) * 0.3 + 0.7;

      // Draw star with enhanced effects
      ctx.beginPath();
      ctx.arc(star.x, star.y, star.radius, 0, Math.PI * 2);
      ctx.fillStyle = `rgba(255, 255, 255, ${star.opacity * twinkle})`;
      ctx.fill();

      // Draw multiple glow layers for larger stars
      if (star.radius > 1.5) {
        // Outer glow
        ctx.beginPath();
        ctx.arc(star.x, star.y, star.radius * 3, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(255, 255, 255, ${star.opacity * twinkle * 0.05})`;
        ctx.fill();

        // Middle glow
        ctx.beginPath();
        ctx.arc(star.x, star.y, star.radius * 2, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(255, 255, 255, ${star.opacity * twinkle * 0.1})`;
        ctx.fill();

        // Inner bright core
        ctx.beginPath();
        ctx.arc(star.x, star.y, star.radius * 0.5, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(255, 255, 255, ${Math.min(1, star.opacity * twinkle * 1.5)})`;
        ctx.fill();
      }

      // Special effect for very large stars (create cross pattern)
      if (star.radius > 2.5) {
        const crossLength = star.radius * 4;
        const crossOpacity = star.opacity * twinkle * 0.3;

        ctx.beginPath();
        ctx.moveTo(star.x - crossLength, star.y);
        ctx.lineTo(star.x + crossLength, star.y);
        ctx.moveTo(star.x, star.y - crossLength);
        ctx.lineTo(star.x, star.y + crossLength);
        ctx.strokeStyle = `rgba(255, 255, 255, ${crossOpacity})`;
        ctx.lineWidth = 0.5;
        ctx.stroke();
      }
    });

    // Draw connections between nearby stars
    starsRef.current.forEach((star, i) => {
      starsRef.current.slice(i + 1).forEach((otherStar) => {
        const dx = star.x - otherStar.x;
        const dy = star.y - otherStar.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 120) {
          const opacity = (120 - distance) / 120 * 0.15;
          ctx.beginPath();
          ctx.moveTo(star.x, star.y);
          ctx.lineTo(otherStar.x, otherStar.y);
          ctx.strokeStyle = `rgba(255, 255, 255, ${opacity})`;
          ctx.lineWidth = 0.8;
          ctx.stroke();
        }
      });
    });

    // Draw mouse connections to nearby stars
    if (mouse.x > 0 && mouse.y > 0) {
      starsRef.current.forEach((star) => {
        const dx = mouse.x - star.x;
        const dy = mouse.y - star.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 150) {
          const opacity = (150 - distance) / 150 * 0.3;
          ctx.beginPath();
          ctx.moveTo(mouse.x, mouse.y);
          ctx.lineTo(star.x, star.y);
          ctx.strokeStyle = `rgba(255, 255, 255, ${opacity})`;
          ctx.lineWidth = 1;
          ctx.stroke();
        }
      });
    }

    // Enhanced mouse cursor effect with pulsing animation
    if (mouse.x > 0 && mouse.y > 0) {
      const time = Date.now() * 0.005;
      const pulse = Math.sin(time) * 0.3 + 0.7;

      // Outer ring
      ctx.beginPath();
      ctx.arc(mouse.x, mouse.y, 60 * pulse, 0, Math.PI * 2);
      ctx.strokeStyle = `rgba(255, 255, 255, ${0.1 * pulse})`;
      ctx.lineWidth = 2;
      ctx.stroke();

      // Middle ring
      ctx.beginPath();
      ctx.arc(mouse.x, mouse.y, 35 * pulse, 0, Math.PI * 2);
      ctx.strokeStyle = `rgba(255, 255, 255, ${0.2 * pulse})`;
      ctx.lineWidth = 1.5;
      ctx.stroke();

      // Inner ring
      ctx.beginPath();
      ctx.arc(mouse.x, mouse.y, 15 * pulse, 0, Math.PI * 2);
      ctx.strokeStyle = `rgba(255, 255, 255, ${0.3 * pulse})`;
      ctx.lineWidth = 1;
      ctx.stroke();

      // Center dot
      ctx.beginPath();
      ctx.arc(mouse.x, mouse.y, 3, 0, Math.PI * 2);
      ctx.fillStyle = `rgba(255, 255, 255, ${0.8 * pulse})`;
      ctx.fill();
    }

    animationRef.current = requestAnimationFrame(animate);
  }, [dimensions]);

  useEffect(() => {
    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    window.addEventListener('mousemove', handleMouseMove);

    // Start animation
    animate();

    return () => {
      window.removeEventListener('resize', updateDimensions);
      window.removeEventListener('mousemove', handleMouseMove);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [dimensions.width, dimensions.height, animate, updateDimensions]);

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-0"
      style={{
        background: `
          radial-gradient(circle at 20% 80%, rgba(30, 30, 30, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(40, 40, 40, 0.2) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(20, 20, 20, 0.4) 0%, transparent 50%),
          linear-gradient(135deg, #000000 0%, #0a0a0a 25%, #111111 50%, #0a0a0a 75%, #000000 100%)
        `,
      }}
    />
  );
};

export default AnimatedBackground;
