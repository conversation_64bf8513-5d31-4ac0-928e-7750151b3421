'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';

interface Star {
  x: number;
  y: number;
  vx: number;
  vy: number;
  radius: number;
  opacity: number;
  twinkleSpeed: number;
  twinklePhase: number;
}

interface MousePosition {
  x: number;
  y: number;
}

const AnimatedBackground: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | null>(null);
  const starsRef = useRef<Star[]>([]);
  const mouseRef = useRef<MousePosition>({ x: 0, y: 0 });
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const lastFrameTimeRef = useRef<number>(0);
  const lastMouseUpdateRef = useRef<number>(0);
  const targetFPS = 30; // Limit to 30 FPS for better performance
  const frameInterval = 1000 / targetFPS;
  const mouseUpdateInterval = 50; // Update mouse position every 50ms

  // Initialize stars with performance optimization
  const initStars = (width: number, height: number) => {
    const stars: Star[] = [];
    // Significantly reduce star count for better performance
    const numStars = Math.min(Math.floor((width * height) / 15000), 80); // Max 80 stars

    for (let i = 0; i < numStars; i++) {
      stars.push({
        x: Math.random() * width,
        y: Math.random() * height,
        vx: (Math.random() - 0.5) * 0.2, // Much slower movement
        vy: (Math.random() - 0.5) * 0.2,
        radius: Math.random() * 2 + 0.5, // Smaller stars
        opacity: Math.random() * 0.6 + 0.2,
        twinkleSpeed: Math.random() * 0.01 + 0.002, // Slower twinkling
        twinklePhase: Math.random() * Math.PI * 2,
      });
    }
    starsRef.current = stars;
  };

  // Update canvas dimensions
  const updateDimensions = useCallback(() => {
    if (canvasRef.current) {
      const { innerWidth, innerHeight } = window;
      setDimensions({ width: innerWidth, height: innerHeight });
      canvasRef.current.width = innerWidth;
      canvasRef.current.height = innerHeight;
      initStars(innerWidth, innerHeight);
    }
  }, []);

  // Mouse move handler
  const handleMouseMove = (event: MouseEvent) => {
    mouseRef.current = {
      x: event.clientX,
      y: event.clientY,
    };
  };

  // Animation loop with frame rate limiting
  const animate = useCallback((currentTime: number = 0) => {
    // Frame rate limiting
    if (currentTime - lastFrameTimeRef.current < frameInterval) {
      animationRef.current = requestAnimationFrame(animate);
      return;
    }
    lastFrameTimeRef.current = currentTime;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const { width, height } = dimensions;
    const mouse = mouseRef.current;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Update and draw stars
    starsRef.current.forEach((star) => {
      // Reduced mouse interaction for better performance
      const dx = mouse.x - star.x;
      const dy = mouse.y - star.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      const maxDistance = 150; // Reduced interaction range
      const repulsionDistance = 60;

      if (distance < maxDistance && distance > 0) {
        const force = (maxDistance - distance) / maxDistance;
        if (distance < repulsionDistance) {
          // Much gentler repulsion effect
          star.vx -= (dx / distance) * force * 0.001;
          star.vy -= (dy / distance) * force * 0.001;
        } else {
          // Much gentler attraction effect
          star.vx += (dx / distance) * force * 0.0005;
          star.vy += (dy / distance) * force * 0.0005;
        }
      }

      // Update position
      star.x += star.vx;
      star.y += star.vy;

      // Boundary wrapping
      if (star.x < 0) star.x = width;
      if (star.x > width) star.x = 0;
      if (star.y < 0) star.y = height;
      if (star.y > height) star.y = 0;

      // Stronger damping for smoother movement
      star.vx *= 0.95;
      star.vy *= 0.95;

      // Simplified twinkle effect
      star.twinklePhase += star.twinkleSpeed;
      const twinkle = Math.sin(star.twinklePhase) * 0.2 + 0.8;

      // Draw star with minimal effects for performance
      ctx.beginPath();
      ctx.arc(star.x, star.y, star.radius, 0, Math.PI * 2);
      ctx.fillStyle = `rgba(255, 255, 255, ${star.opacity * twinkle})`;
      ctx.fill();

      // Only add subtle glow for larger stars to reduce performance impact
      if (star.radius > 1.8) {
        ctx.beginPath();
        ctx.arc(star.x, star.y, star.radius * 1.5, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(255, 255, 255, ${star.opacity * twinkle * 0.1})`;
        ctx.fill();

        // Inner bright core
        ctx.beginPath();
        ctx.arc(star.x, star.y, star.radius * 0.5, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(255, 255, 255, ${Math.min(1, star.opacity * twinkle * 1.5)})`;
        ctx.fill();
      }
    });

    animationRef.current = requestAnimationFrame(animate);
  }, [dimensions, frameInterval]);

  useEffect(() => {
    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    window.addEventListener('mousemove', handleMouseMove);

    // Start animation
    animate();

    return () => {
      window.removeEventListener('resize', updateDimensions);
      window.removeEventListener('mousemove', handleMouseMove);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [dimensions.width, dimensions.height, animate, updateDimensions]);

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-0"
      style={{
        background: `
          radial-gradient(circle at 20% 80%, rgba(30, 30, 30, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(40, 40, 40, 0.2) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(20, 20, 20, 0.4) 0%, transparent 50%),
          linear-gradient(135deg, #000000 0%, #0a0a0a 25%, #111111 50%, #0a0a0a 75%, #000000 100%)
        `,
      }}
    />
  );
};

export default AnimatedBackground;
