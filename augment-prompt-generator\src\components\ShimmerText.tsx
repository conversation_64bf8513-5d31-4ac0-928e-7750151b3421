"use client";

/**
 * @author: @dorian_baffier
 * @description: Shimmer Text - Safari iOS Compatible Version
 * @version: 1.1.0
 * @date: 2025-06-26
 * @license: MIT
 * @website: https://kokonutui.com
 * @github: https://github.com/kokonut-labs/kokonutui
 */

import { cn } from "@/lib/utils";
import { motion } from "motion/react";
import { useEffect, useState } from "react";

interface ShimmerTextProps {
    text: string;
    className?: string;
}

export default function ShimmerText({
    text = "Text Shimmer",
    className,
}: ShimmerTextProps) {
    const [isSafari, setIsSafari] = useState(false);

    useEffect(() => {
        // Detect Safari browser for fallback
        const userAgent = navigator.userAgent.toLowerCase();
        const isSafariBrowser = userAgent.includes('safari') && !userAgent.includes('chrome');
        setIsSafari(isSafariBrowser);
    }, []);

    // Safari fallback - simple white text with opacity animation
    if (isSafari) {
        return (
            <motion.span
                className={cn("text-white font-medium", className)}
                initial={{ opacity: 0.7 }}
                animate={{ opacity: [0.7, 1, 0.7] }}
                transition={{
                    duration: 2.5,
                    ease: "easeInOut",
                    repeat: Number.POSITIVE_INFINITY,
                }}
            >
                {text}
            </motion.span>
        );
    }

    // Standard shimmer effect for other browsers
    return (
        <motion.span
            className={cn(
                "relative font-medium text-white",
                className
            )}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            style={{
                background: 'linear-gradient(90deg, #ffffff 0%, #d1d5db 50%, #ffffff 100%)',
                backgroundSize: '200% 100%',
                WebkitBackgroundClip: 'text',
                backgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                color: 'transparent'
            }}
        >
            <motion.span
                animate={{
                    backgroundPosition: ['200% center', '-200% center'],
                }}
                transition={{
                    duration: 2.5,
                    ease: "linear",
                    repeat: Number.POSITIVE_INFINITY,
                }}
                style={{
                    background: 'inherit',
                    backgroundSize: 'inherit',
                    WebkitBackgroundClip: 'inherit',
                    backgroundClip: 'inherit',
                    WebkitTextFillColor: 'inherit',
                    color: 'inherit',
                }}
            >
                {text}
            </motion.span>
            {/* Fallback text for Safari iOS */}
            <span
                className="absolute inset-0 text-white opacity-0"
                style={{
                    opacity: isSafari ? 1 : 0,
                    WebkitTextFillColor: 'white',
                    color: 'white'
                }}
            >
                {text}
            </span>
        </motion.span>
    );
}
