/* Universal Modal Backdrop-Blur with Safari iOS Compatibility */

/*
 * This file provides a unified modal styling approach that works consistently
 * across all browsers and devices, including Safari iOS. Instead of using
 * @supports queries (which don't work reliably for Safari iOS backdrop-filter issues),
 * we use a layered approach with both backdrop-filter AND solid backgrounds.
 *
 * The solid background ensures readability on Safari iOS where backdrop-filter
 * may not render properly, while the backdrop-filter enhances the effect on
 * supported browsers without interfering with the solid background.
 */

/* Universal Modal Overlay - Works on all platforms */
.modal-overlay-universal {
  /* Solid background for guaranteed readability on all platforms */
  background: rgba(0, 0, 0, 0.75) !important;
  /* Backdrop-filter for enhanced effect on supported browsers */
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}

/* Universal Modal Content - Works on all platforms */
.modal-content-universal {
  /* Solid background for guaranteed readability on all platforms */
  background: rgba(0, 0, 0, 0.85) !important;
  /* Backdrop-filter for enhanced effect on supported browsers */
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  /* Enhanced border for better definition */
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
}

/* Configuration Modal Overlay - Higher opacity for settings */
.config-modal-overlay-universal {
  /* Solid background for guaranteed readability */
  background: rgba(0, 0, 0, 0.9) !important;
  /* Backdrop-filter for enhanced effect */
  backdrop-filter: blur(6px) !important;
  -webkit-backdrop-filter: blur(6px) !important;
}

/* Configuration Modal Content - Higher opacity for settings */
.config-modal-content-universal {
  /* Solid background for guaranteed readability */
  background: rgba(0, 0, 0, 0.9) !important;
  /* Backdrop-filter for enhanced effect */
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  /* Enhanced border for better definition */
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Universal Text Enhancement - Always applied for better readability */
.modal-text-enhanced {
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.modal-text-secondary-enhanced {
  color: #e5e5e5;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.modal-text-muted-enhanced {
  color: #d1d1d1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

/* Universal Button Enhancement - Always applied for better visibility */
.modal-button-enhanced {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  transition: all 0.3s ease;
}

.modal-button-enhanced:hover {
  background: rgba(255, 255, 255, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.modal-button-primary-enhanced {
  background: rgba(59, 130, 246, 0.8);
  border: 1px solid rgba(59, 130, 246, 1);
  color: #ffffff;
  transition: all 0.3s ease;
}

.modal-button-primary-enhanced:hover {
  background: rgba(59, 130, 246, 0.9);
}

/* Universal Input Enhancement - Always applied for better visibility */
.modal-input-enhanced {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
}

.modal-input-enhanced::placeholder {
  color: #b3b3b3;
}

.modal-input-enhanced:focus {
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.5);
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* Universal Select Enhancement - Always applied for better visibility */
.modal-select-enhanced {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
}

.modal-select-enhanced:focus {
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.5);
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* Universal Card Enhancement - Always applied for better visibility */
.modal-card-enhanced {
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.modal-card-enhanced:hover {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
}
