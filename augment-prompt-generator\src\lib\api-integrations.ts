import { ProjectInput } from './types';

export interface IntegrationConfig {
  apiKey?: string;
  token?: string;
  username?: string;
  password?: string;
  endpoint?: string;
  workspace?: string;
  organization?: string;
  [key: string]: string | number | boolean | undefined;
}

export interface APIIntegration {
  id: string;
  name: string;
  description: string;
  category: 'development' | 'design' | 'productivity' | 'ai' | 'communication' | 'cloud' | 'analytics';
  icon: string;
  baseUrl: string;
  authType: 'none' | 'api_key' | 'oauth' | 'bearer';
  isEnabled: boolean;
  config: IntegrationConfig;
}

export interface WebhookConfig {
  id: string;
  name: string;
  url: string;
  events: string[];
  isActive: boolean;
  secret?: string;
  headers: Record<string, string>;
}

export interface APIResponse {
  success: boolean;
  data?: Record<string, unknown>;
  error?: string;
  statusCode?: number;
}

export const AVAILABLE_INTEGRATIONS: APIIntegration[] = [
  // Development Tools
  {
    id: 'github',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Create repositories and issues from prompts',
    category: 'development',
    icon: '🐙',
    baseUrl: 'https://api.github.com',
    authType: 'api_key',
    isEnabled: false,
    config: {}
  },
  {
    id: 'gitlab',
    name: 'GitLab',
    description: 'Create projects and merge requests',
    category: 'development',
    icon: '🦊',
    baseUrl: 'https://gitlab.com/api/v4',
    authType: 'api_key',
    isEnabled: false,
    config: {}
  },
  {
    id: 'linear',
    name: 'Linear',
    description: 'Create issues and projects',
    category: 'development',
    icon: '📋',
    baseUrl: 'https://api.linear.app/graphql',
    authType: 'api_key',
    isEnabled: false,
    config: {}
  },
  {
    id: 'github-actions',
    name: 'GitHub Actions',
    description: 'Trigger CI/CD workflows',
    category: 'development',
    icon: '⚡',
    baseUrl: 'https://api.github.com',
    authType: 'api_key',
    isEnabled: false,
    config: {}
  },
  {
    id: 'gitlab-ci',
    name: 'GitLab CI/CD',
    description: 'Manage CI/CD pipelines',
    category: 'development',
    icon: '🔄',
    baseUrl: 'https://gitlab.com/api/v4',
    authType: 'api_key',
    isEnabled: false,
    config: {}
  },

  // Project Management
  {
    id: 'jira',
    name: 'Jira',
    description: 'Create tickets and manage projects',
    category: 'productivity',
    icon: '🎯',
    baseUrl: 'https://api.atlassian.com',
    authType: 'bearer',
    isEnabled: false,
    config: {}
  },
  {
    id: 'asana',
    name: 'Asana',
    description: 'Create tasks and manage workflows',
    category: 'productivity',
    icon: '📊',
    baseUrl: 'https://app.asana.com/api/1.0',
    authType: 'bearer',
    isEnabled: false,
    config: {}
  },
  {
    id: 'monday',
    name: 'Monday.com',
    description: 'Create boards and manage projects',
    category: 'productivity',
    icon: '📅',
    baseUrl: 'https://api.monday.com/v2',
    authType: 'api_key',
    isEnabled: false,
    config: {}
  },
  {
    id: 'trello',
    name: 'Trello',
    description: 'Create cards and manage boards',
    category: 'productivity',
    icon: '📋',
    baseUrl: 'https://api.trello.com/1',
    authType: 'api_key',
    isEnabled: false,
    config: {}
  },
  {
    id: 'notion',
    name: 'Notion',
    description: 'Save prompts as Notion pages',
    category: 'productivity',
    icon: '📝',
    baseUrl: 'https://api.notion.com/v1',
    authType: 'bearer',
    isEnabled: false,
    config: {}
  },

  // Communication Platforms
  {
    id: 'slack',
    name: 'Slack',
    description: 'Share prompts in Slack channels',
    category: 'communication',
    icon: '💬',
    baseUrl: 'https://slack.com/api',
    authType: 'bearer',
    isEnabled: false,
    config: {}
  },
  {
    id: 'discord',
    name: 'Discord',
    description: 'Share prompts in Discord servers',
    category: 'communication',
    icon: '🎮',
    baseUrl: 'https://discord.com/api/v10',
    authType: 'bearer',
    isEnabled: false,
    config: {}
  },
  {
    id: 'microsoft-teams',
    name: 'Microsoft Teams',
    description: 'Share prompts in Teams channels',
    category: 'communication',
    icon: '👥',
    baseUrl: 'https://graph.microsoft.com/v1.0',
    authType: 'bearer',
    isEnabled: false,
    config: {}
  },
  {
    id: 'telegram',
    name: 'Telegram',
    description: 'Send prompts via Telegram bot',
    category: 'communication',
    icon: '✈️',
    baseUrl: 'https://api.telegram.org',
    authType: 'api_key',
    isEnabled: false,
    config: {}
  },

  // Cloud Services
  {
    id: 'aws',
    name: 'Amazon Web Services',
    description: 'Deploy to AWS services',
    category: 'cloud',
    icon: '☁️',
    baseUrl: 'https://aws.amazon.com',
    authType: 'api_key',
    isEnabled: false,
    config: {}
  },
  {
    id: 'google-cloud',
    name: 'Google Cloud Platform',
    description: 'Deploy to GCP services',
    category: 'cloud',
    icon: '🌩️',
    baseUrl: 'https://cloud.google.com',
    authType: 'oauth',
    isEnabled: false,
    config: {}
  },
  {
    id: 'azure',
    name: 'Microsoft Azure',
    description: 'Deploy to Azure services',
    category: 'cloud',
    icon: '🔷',
    baseUrl: 'https://management.azure.com',
    authType: 'bearer',
    isEnabled: false,
    config: {}
  },
  {
    id: 'vercel',
    name: 'Vercel',
    description: 'Deploy web applications',
    category: 'cloud',
    icon: '▲',
    baseUrl: 'https://api.vercel.com',
    authType: 'bearer',
    isEnabled: false,
    config: {}
  },
  {
    id: 'netlify',
    name: 'Netlify',
    description: 'Deploy static sites',
    category: 'cloud',
    icon: '🌐',
    baseUrl: 'https://api.netlify.com/api/v1',
    authType: 'bearer',
    isEnabled: false,
    config: {}
  },

  // Design Tools
  {
    id: 'figma',
    name: 'Figma',
    description: 'Create design briefs from prompts',
    category: 'design',
    icon: '🎨',
    baseUrl: 'https://api.figma.com/v1',
    authType: 'api_key',
    isEnabled: false,
    config: {}
  },
  {
    id: 'sketch',
    name: 'Sketch',
    description: 'Create design documents',
    category: 'design',
    icon: '💎',
    baseUrl: 'https://api.sketch.com/v1',
    authType: 'bearer',
    isEnabled: false,
    config: {}
  },

  // Analytics & Monitoring
  {
    id: 'datadog',
    name: 'Datadog',
    description: 'Set up monitoring and alerts',
    category: 'analytics',
    icon: '📊',
    baseUrl: 'https://api.datadoghq.com/api/v1',
    authType: 'api_key',
    isEnabled: false,
    config: {}
  },
  {
    id: 'new-relic',
    name: 'New Relic',
    description: 'Application performance monitoring',
    category: 'analytics',
    icon: '📈',
    baseUrl: 'https://api.newrelic.com/v2',
    authType: 'api_key',
    isEnabled: false,
    config: {}
  },
  {
    id: 'sentry',
    name: 'Sentry',
    description: 'Error tracking and monitoring',
    category: 'analytics',
    icon: '🚨',
    baseUrl: 'https://sentry.io/api/0',
    authType: 'bearer',
    isEnabled: false,
    config: {}
  },
  {
    id: 'google-analytics',
    name: 'Google Analytics',
    description: 'Web analytics and reporting',
    category: 'analytics',
    icon: '📊',
    baseUrl: 'https://analyticsreporting.googleapis.com/v4',
    authType: 'oauth',
    isEnabled: false,
    config: {}
  },

  // AI Services
  {
    id: 'openai',
    name: 'OpenAI',
    description: 'Send prompts to ChatGPT and GPT models',
    category: 'ai',
    icon: '🤖',
    baseUrl: 'https://api.openai.com/v1',
    authType: 'bearer',
    isEnabled: false,
    config: {}
  },
  {
    id: 'anthropic',
    name: 'Anthropic Claude',
    description: 'Send prompts to Claude Sonnet 4',
    category: 'ai',
    icon: '🧠',
    baseUrl: 'https://api.anthropic.com/v1',
    authType: 'api_key',
    isEnabled: false,
    config: {}
  },
  {
    id: 'google-gemini',
    name: 'Google Gemini',
    description: 'Send prompts to Gemini Pro models',
    category: 'ai',
    icon: '💎',
    baseUrl: 'https://generativelanguage.googleapis.com/v1',
    authType: 'api_key',
    isEnabled: false,
    config: {}
  },
  {
    id: 'cursor',
    name: 'Cursor IDE',
    description: 'Send prompts to Cursor AI assistant',
    category: 'ai',
    icon: '⚡',
    baseUrl: 'https://api.cursor.sh/v1',
    authType: 'bearer',
    isEnabled: false,
    config: {}
  },
  {
    id: 'deepseek',
    name: 'DeepSeek',
    description: 'Send prompts to DeepSeek V3 models',
    category: 'ai',
    icon: '🔍',
    baseUrl: 'https://api.deepseek.com/v1',
    authType: 'bearer',
    isEnabled: false,
    config: {}
  },
  {
    id: 'perplexity',
    name: 'Perplexity AI',
    description: 'Send prompts to Perplexity models',
    category: 'ai',
    icon: '🔮',
    baseUrl: 'https://api.perplexity.ai',
    authType: 'bearer',
    isEnabled: false,
    config: {}
  },
  {
    id: 'cohere',
    name: 'Cohere',
    description: 'Send prompts to Cohere Command models',
    category: 'ai',
    icon: '🌟',
    baseUrl: 'https://api.cohere.ai/v1',
    authType: 'bearer',
    isEnabled: false,
    config: {}
  },
  {
    id: 'huggingface',
    name: 'Hugging Face',
    description: 'Access open-source AI models',
    category: 'ai',
    icon: '🤗',
    baseUrl: 'https://api-inference.huggingface.co',
    authType: 'bearer',
    isEnabled: false,
    config: {}
  }
];

class APIIntegrationsService {
  private integrations: APIIntegration[] = [];
  private webhooks: WebhookConfig[] = [];
  private storageKey = 'promptGenerator_integrations';
  private webhooksKey = 'promptGenerator_webhooks';

  constructor() {
    this.loadIntegrations();
    this.loadWebhooks();
  }

  private loadIntegrations(): void {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        this.integrations = JSON.parse(stored);
      } else {
        this.integrations = [...AVAILABLE_INTEGRATIONS];
      }
    } catch (error) {
      console.error('Failed to load integrations:', error);
      this.integrations = [...AVAILABLE_INTEGRATIONS];
    }
  }

  private loadWebhooks(): void {
    try {
      const stored = localStorage.getItem(this.webhooksKey);
      if (stored) {
        this.webhooks = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to load webhooks:', error);
    }
  }

  private saveIntegrations(): void {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.integrations));
    } catch (error) {
      console.error('Failed to save integrations:', error);
    }
  }

  private saveWebhooks(): void {
    try {
      localStorage.setItem(this.webhooksKey, JSON.stringify(this.webhooks));
    } catch (error) {
      console.error('Failed to save webhooks:', error);
    }
  }

  getIntegrations(): APIIntegration[] {
    return this.integrations;
  }

  getEnabledIntegrations(): APIIntegration[] {
    return this.integrations.filter(integration => integration.isEnabled);
  }

  enableIntegration(id: string, config: IntegrationConfig): boolean {
    const integration = this.integrations.find(i => i.id === id);
    if (!integration) return false;

    integration.isEnabled = true;
    integration.config = { ...integration.config, ...config };
    this.saveIntegrations();
    return true;
  }

  disableIntegration(id: string): boolean {
    const integration = this.integrations.find(i => i.id === id);
    if (!integration) return false;

    integration.isEnabled = false;
    integration.config = {};
    this.saveIntegrations();
    return true;
  }

  async testIntegration(id: string): Promise<APIResponse> {
    const integration = this.integrations.find(i => i.id === id);
    if (!integration || !integration.isEnabled) {
      return { success: false, error: 'Integration not found or not enabled' };
    }

    try {
      // Mock API test - in real implementation, this would make actual API calls
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate different responses based on integration
      const mockResponses: Record<string, APIResponse> = {
        github: { success: true, data: { user: 'demo-user', repos: 42 } },
        gitlab: { success: true, data: { user: 'demo-user', projects: 28 } },
        notion: { success: true, data: { workspace: 'Demo Workspace' } },
        slack: { success: true, data: { team: 'Demo Team', channels: 15 } },
        figma: { success: true, data: { teams: 3, projects: 12 } },
        linear: { success: true, data: { workspace: 'Demo Workspace', issues: 28 } },
        discord: { success: true, data: { guild: 'Demo Server', channels: 8 } },
        jira: { success: true, data: { projects: 12, issues: 156 } },
        asana: { success: true, data: { workspaces: 3, tasks: 89 } },
        monday: { success: true, data: { boards: 8, items: 234 } },
        trello: { success: true, data: { boards: 15, cards: 67 } },
        'microsoft-teams': { success: true, data: { teams: 5, channels: 23 } },
        telegram: { success: true, data: { bot: 'active', chats: 12 } },
        aws: { success: true, data: { regions: 3, services: 45 } },
        'google-cloud': { success: true, data: { projects: 2, services: 32 } },
        azure: { success: true, data: { subscriptions: 1, resources: 28 } },
        vercel: { success: true, data: { projects: 8, deployments: 156 } },
        netlify: { success: true, data: { sites: 12, builds: 89 } },
        sketch: { success: true, data: { documents: 15, symbols: 234 } },
        datadog: { success: true, data: { dashboards: 8, monitors: 45 } },
        'new-relic': { success: true, data: { applications: 6, alerts: 23 } },
        sentry: { success: true, data: { projects: 4, errors: 12 } },
        'google-analytics': { success: true, data: { properties: 3, reports: 67 } },
        openai: { success: true, data: { models: 8, usage: 'active' } },
        anthropic: { success: true, data: { models: 3, status: 'connected' } },
        'google-gemini': { success: true, data: { models: 5, quota: 'available' } },
        cursor: { success: true, data: { workspace: 'connected', features: 12 } },
        deepseek: { success: true, data: { models: 4, status: 'active' } },
        perplexity: { success: true, data: { models: 3, credits: 1000 } },
        cohere: { success: true, data: { models: 6, usage: 'normal' } },
        huggingface: { success: true, data: { models: 50, status: 'connected' } }
      };

      return mockResponses[id] || { success: true, data: { status: 'connected' } };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return { success: false, error: errorMessage };
    }
  }

  async sendToIntegration(
    integrationId: string,
    projectInput: ProjectInput,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _generatedPrompt: string, // Reserved for future use
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _action: string = 'create' // Reserved for future use
  ): Promise<APIResponse> {
    const integration = this.integrations.find(i => i.id === integrationId);
    if (!integration || !integration.isEnabled) {
      return { success: false, error: 'Integration not found or not enabled' };
    }

    try {
      // Mock API call - in real implementation, this would make actual API calls
      await new Promise(resolve => setTimeout(resolve, 1500));

      const mockResponses: Record<string, APIResponse> = {
        github: {
          success: true,
          data: {
            repository: `${projectInput.projectName.toLowerCase().replace(/\s+/g, '-')}`,
            url: `https://github.com/demo-user/${projectInput.projectName.toLowerCase().replace(/\s+/g, '-')}`,
            action: 'Repository created'
          }
        },
        notion: {
          success: true,
          data: {
            page: projectInput.projectName,
            url: 'https://notion.so/demo-page',
            action: 'Page created'
          }
        },
        slack: {
          success: true,
          data: {
            channel: '#general',
            message: 'Prompt shared successfully',
            action: 'Message sent'
          }
        },
        figma: {
          success: true,
          data: {
            file: `${projectInput.projectName} Design Brief`,
            url: 'https://figma.com/file/demo',
            action: 'Design file created'
          }
        },
        linear: {
          success: true,
          data: {
            issue: `${projectInput.projectName} - Implementation`,
            url: 'https://linear.app/demo/issue/DEMO-123',
            action: 'Issue created'
          }
        },
        discord: {
          success: true,
          data: {
            channel: '#development',
            message: 'Prompt shared in Discord',
            action: 'Message sent'
          }
        },
        gitlab: {
          success: true,
          data: {
            project: `${projectInput.projectName.toLowerCase().replace(/\s+/g, '-')}`,
            url: `https://gitlab.com/demo-user/${projectInput.projectName.toLowerCase().replace(/\s+/g, '-')}`,
            action: 'Project created'
          }
        },
        jira: {
          success: true,
          data: {
            ticket: `${projectInput.projectName} - Epic`,
            url: 'https://demo.atlassian.net/browse/PROJ-123',
            action: 'Epic created'
          }
        },
        asana: {
          success: true,
          data: {
            task: `${projectInput.projectName} - Project`,
            url: 'https://app.asana.com/0/123456/789012',
            action: 'Task created'
          }
        },
        monday: {
          success: true,
          data: {
            item: `${projectInput.projectName} - Board Item`,
            url: 'https://demo.monday.com/boards/123456',
            action: 'Item created'
          }
        },
        openai: {
          success: true,
          data: {
            response: 'Prompt sent to ChatGPT',
            model: 'gpt-4',
            action: 'Prompt processed'
          }
        },
        anthropic: {
          success: true,
          data: {
            response: 'Prompt sent to Claude Sonnet 4',
            model: 'claude-3-5-sonnet',
            action: 'Prompt processed'
          }
        },
        'google-gemini': {
          success: true,
          data: {
            response: 'Prompt sent to Gemini Pro',
            model: 'gemini-pro',
            action: 'Prompt processed'
          }
        },
        cursor: {
          success: true,
          data: {
            response: 'Prompt sent to Cursor AI',
            workspace: 'active',
            action: 'Prompt processed'
          }
        },
        vercel: {
          success: true,
          data: {
            deployment: `${projectInput.projectName.toLowerCase().replace(/\s+/g, '-')}`,
            url: `https://${projectInput.projectName.toLowerCase().replace(/\s+/g, '-')}.vercel.app`,
            action: 'Deployment created'
          }
        },
        aws: {
          success: true,
          data: {
            stack: `${projectInput.projectName}-stack`,
            region: 'us-east-1',
            action: 'CloudFormation stack created'
          }
        }
      };

      return mockResponses[integrationId] || { success: true, data: { action: 'Completed' } };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return { success: false, error: errorMessage };
    }
  }

  // Webhook management
  addWebhook(config: Omit<WebhookConfig, 'id'>): WebhookConfig {
    const webhook: WebhookConfig = {
      ...config,
      id: 'webhook_' + Math.random().toString(36).substring(2, 11)
    };
    
    this.webhooks.push(webhook);
    this.saveWebhooks();
    return webhook;
  }

  updateWebhook(id: string, updates: Partial<WebhookConfig>): boolean {
    const index = this.webhooks.findIndex(w => w.id === id);
    if (index === -1) return false;

    this.webhooks[index] = { ...this.webhooks[index], ...updates };
    this.saveWebhooks();
    return true;
  }

  deleteWebhook(id: string): boolean {
    const index = this.webhooks.findIndex(w => w.id === id);
    if (index === -1) return false;

    this.webhooks.splice(index, 1);
    this.saveWebhooks();
    return true;
  }

  getWebhooks(): WebhookConfig[] {
    return this.webhooks;
  }

  async triggerWebhook(
    webhookId: string,
    event: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _data: Record<string, unknown> // Reserved for future use
  ): Promise<APIResponse> {
    const webhook = this.webhooks.find(w => w.id === webhookId);
    if (!webhook || !webhook.isActive || !webhook.events.includes(event)) {
      return { success: false, error: 'Webhook not found, inactive, or event not supported' };
    }

    try {
      // Mock webhook call
      await new Promise(resolve => setTimeout(resolve, 800));
      return { success: true, data: { webhook: webhook.name, event, delivered: true } };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return { success: false, error: errorMessage };
    }
  }

  exportIntegrationData(): string {
    return JSON.stringify({
      integrations: this.integrations,
      webhooks: this.webhooks,
      exportedAt: new Date()
    }, null, 2);
  }

  clearData(): void {
    this.integrations = [...AVAILABLE_INTEGRATIONS];
    this.webhooks = [];
    localStorage.removeItem(this.storageKey);
    localStorage.removeItem(this.webhooksKey);
  }
}

export const apiIntegrationsService = new APIIntegrationsService();
