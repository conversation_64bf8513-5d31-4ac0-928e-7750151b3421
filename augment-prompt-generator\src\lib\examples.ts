import { ProjectInput } from './types';

export const EXAMPLE_PROJECTS: ProjectInput[] = [
  {
    projectName: 'TaskMaster Pro',
    projectIdea: 'A comprehensive task management application that helps teams organize, track, and collaborate on projects. Features include task assignment, deadline tracking, progress visualization, team collaboration tools, and integration with popular productivity apps.',
    projectType: 'web-application',
    platform: 'web',
    technologies: [
      { category: 'frontend', name: 'Next.js' },
      { category: 'styling', name: 'Tailwind CSS' },
      { category: 'database', name: 'Supabase' },
      { category: 'authentication', name: 'NextAuth.js' },
      { category: 'deployment', name: 'Vercel' }
    ],
    complexity: 'intermediate',
    features: [
      'User authentication and team management',
      'Task creation, assignment, and tracking',
      'Project dashboard with progress visualization',
      'Real-time collaboration and comments',
      'File attachments and document sharing',
      'Email notifications and reminders',
      'Time tracking and reporting',
      'Integration with calendar apps'
    ],
    additionalRequirements: 'The application should support both individual users and teams, with role-based permissions and the ability to create multiple workspaces.'
  },
  
  {
    projectName: 'FitTracker Mobile',
    projectIdea: 'A mobile fitness tracking app that helps users monitor their workouts, nutrition, and health goals. Includes workout planning, progress tracking, social features, and integration with wearable devices.',
    projectType: 'mobile-application',
    platform: 'mobile',
    technologies: [
      { category: 'mobile-framework', name: 'React Native' },
      { category: 'database', name: 'Firebase' },
      { category: 'authentication', name: 'Firebase Auth' },
      { category: 'testing', name: 'Jest' }
    ],
    complexity: 'advanced',
    features: [
      'User profiles and goal setting',
      'Workout tracking and exercise library',
      'Nutrition logging and calorie counting',
      'Progress photos and measurements',
      'Social features and challenges',
      'Wearable device integration',
      'Offline workout mode',
      'Push notifications for reminders'
    ],
    additionalRequirements: 'The app should work offline for core features and sync data when connected. Include integration with popular fitness wearables and health apps.'
  },

  {
    projectName: 'DataViz Dashboard',
    projectIdea: 'An interactive data visualization platform that allows users to upload datasets, perform analysis, and create beautiful, shareable dashboards. Supports multiple data formats and provides advanced analytics capabilities.',
    projectType: 'data-analysis',
    platform: 'web',
    technologies: [
      { category: 'frontend', name: 'React' },
      { category: 'backend', name: 'Python/FastAPI' },
      { category: 'database', name: 'PostgreSQL' },
      { category: 'styling', name: 'Material-UI' },
      { category: 'deployment', name: 'AWS' }
    ],
    complexity: 'advanced',
    features: [
      'Data import from multiple sources',
      'Interactive chart and graph creation',
      'Real-time data processing',
      'Collaborative dashboard sharing',
      'Advanced statistical analysis',
      'Custom visualization components',
      'Automated report generation',
      'API for data integration'
    ],
    additionalRequirements: 'Support for large datasets (millions of rows), real-time data streaming, and machine learning model integration for predictive analytics.'
  },

  {
    projectName: 'Invoice Generator API',
    projectIdea: 'A RESTful API service for generating, managing, and tracking invoices for small businesses. Includes customer management, payment tracking, tax calculations, and integration with accounting software.',
    projectType: 'api-backend',
    platform: 'server',
    technologies: [
      { category: 'backend', name: 'Node.js' },
      { category: 'backend', name: 'Express.js' },
      { category: 'database', name: 'PostgreSQL' },
      { category: 'authentication', name: 'JWT' },
      { category: 'deployment', name: 'AWS' }
    ],
    complexity: 'intermediate',
    features: [
      'Customer and business management',
      'Invoice creation and customization',
      'Payment tracking and reminders',
      'Tax calculation and compliance',
      'PDF generation and email delivery',
      'Recurring invoice automation',
      'Financial reporting and analytics',
      'Third-party integrations'
    ],
    additionalRequirements: 'The API should be highly scalable, support multiple currencies and tax systems, and include comprehensive documentation with rate limiting.'
  },

  {
    projectName: 'CodeEditor Desktop',
    projectIdea: 'A lightweight, cross-platform code editor with modern features like syntax highlighting, intelligent autocomplete, integrated terminal, and plugin support. Designed for developers who want a fast, customizable coding environment.',
    projectType: 'desktop-application',
    platform: 'cross-platform',
    technologies: [
      { category: 'desktop-framework', name: 'Electron' },
      { category: 'frontend', name: 'React' },
      { category: 'styling', name: 'CSS Modules' },
      { category: 'testing', name: 'Jest' }
    ],
    complexity: 'advanced',
    features: [
      'Multi-language syntax highlighting',
      'Intelligent code completion',
      'Integrated terminal and file explorer',
      'Plugin system and marketplace',
      'Git integration and version control',
      'Multiple themes and customization',
      'Split pane and tab management',
      'Search and replace across files'
    ],
    additionalRequirements: 'The editor should be performant with large files, support language servers for advanced features, and include a robust plugin API for extensibility.'
  }
];

export const getRandomExample = (): ProjectInput => {
  const randomIndex = Math.floor(Math.random() * EXAMPLE_PROJECTS.length);
  return EXAMPLE_PROJECTS[randomIndex];
};
