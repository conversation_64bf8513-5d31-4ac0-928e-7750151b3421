import React, { useState, useRef, useEffect, useId } from 'react';

// Global dropdown manager to ensure only one dropdown is open at a time
class DropdownManager {
  private static instance: DropdownManager;
  private openDropdownId: string | null = null;
  private callbacks: Map<string, () => void> = new Map();

  static getInstance(): DropdownManager {
    if (!DropdownManager.instance) {
      DropdownManager.instance = new DropdownManager();
    }
    return DropdownManager.instance;
  }

  register(id: string, closeCallback: () => void) {
    this.callbacks.set(id, closeCallback);
  }

  unregister(id: string) {
    this.callbacks.delete(id);
  }

  openDropdown(id: string) {
    if (this.openDropdownId && this.openDropdownId !== id) {
      const closeCallback = this.callbacks.get(this.openDropdownId);
      if (closeCallback) {
        closeCallback();
      }
    }
    this.openDropdownId = id;
  }

  closeDropdown(id: string) {
    if (this.openDropdownId === id) {
      this.openDropdownId = null;
    }
  }
}

interface Option {
  value: string;
  label: string;
  description?: string;
}

interface CustomSelectProps {
  value: string;
  onChange: (value: string) => void;
  options: Option[];
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export default function CustomSelect({
  value,
  onChange,
  options,
  placeholder = "Select an option",
  className = "",
  disabled = false
}: CustomSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const selectRef = useRef<HTMLDivElement>(null);
  const dropdownId = useId();
  const dropdownManager = DropdownManager.getInstance();

  const selectedOption = options.find(option => option.value === value);

  const closeDropdown = () => {
    setIsOpen(false);
    dropdownManager.closeDropdown(dropdownId);
  };

  const openDropdown = () => {
    dropdownManager.openDropdown(dropdownId);
    setIsOpen(true);
  };

  useEffect(() => {
    // Register with dropdown manager
    dropdownManager.register(dropdownId, closeDropdown);

    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        closeDropdown();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      dropdownManager.unregister(dropdownId);
    };
  }, [dropdownId]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'Escape':
          closeDropdown();
          break;
        case 'ArrowDown':
          event.preventDefault();
          setHighlightedIndex(prev => 
            prev < options.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          event.preventDefault();
          setHighlightedIndex(prev => 
            prev > 0 ? prev - 1 : options.length - 1
          );
          break;
        case 'Enter':
          event.preventDefault();
          if (highlightedIndex >= 0) {
            onChange(options[highlightedIndex].value);
            closeDropdown();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, highlightedIndex, options, onChange]);

  return (
    <div ref={selectRef} className={`relative ${className}`}>
      <button
        type="button"
        onClick={() => !disabled && (isOpen ? closeDropdown() : openDropdown())}
        disabled={disabled}
        className={`group w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-black text-sm text-white bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl border border-white/10 transition-all duration-300 hover:border-white/20 hover:shadow-lg text-left flex items-center justify-between ${
          disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:from-gray-700/90 hover:to-gray-800/90'
        } ${isOpen ? 'ring-2 ring-white/20 border-white/25 shadow-xl' : ''}`}
        style={{
          boxShadow: isOpen
            ? '0 8px 32px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
            : '0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
        }}
      >
        <span className={`font-medium ${selectedOption ? 'text-white' : 'text-gray-400'} transition-colors duration-200`}>
          {selectedOption ? selectedOption.label : placeholder}
        </span>
        <svg
          className={`w-5 h-5 transition-all duration-300 text-gray-300 group-hover:text-white ${isOpen ? 'rotate-180 text-white' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          strokeWidth={2.5}
        >
          <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div
          className="absolute z-[9999] w-full mt-2 bg-gradient-to-b from-gray-900/95 to-black/95 backdrop-blur-2xl rounded-xl shadow-2xl border border-white/15 max-h-64 overflow-y-auto animate-fade-in"
          style={{
            boxShadow: '0 32px 64px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
            backgroundColor: 'rgba(0, 0, 0, 0.92)'
          }}
        >
          <div className="p-1">
            {options.map((option, index) => (
              <button
                key={option.value}
                type="button"
                onClick={() => {
                  onChange(option.value);
                  closeDropdown();
                }}
                onMouseEnter={() => setHighlightedIndex(index)}
                className={`group w-full text-left px-4 py-3 text-sm transition-all duration-200 rounded-lg mb-1 last:mb-0 ${
                  index === highlightedIndex || option.value === value
                    ? 'bg-gradient-to-r from-white/20 to-white/15 text-white shadow-lg border border-white/20'
                    : 'text-gray-200 hover:bg-gradient-to-r hover:from-white/10 hover:to-white/5 hover:text-white border border-transparent hover:border-white/10'
                }`}
                style={{
                  boxShadow: (index === highlightedIndex || option.value === value)
                    ? '0 4px 12px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
                    : 'none'
                }}
              >
                <div className="font-semibold text-sm leading-tight">{option.label}</div>
                {option.description && (
                  <div className="text-xs text-gray-300 mt-1.5 leading-relaxed opacity-90 group-hover:opacity-100 transition-opacity duration-200">
                    {option.description}
                  </div>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
