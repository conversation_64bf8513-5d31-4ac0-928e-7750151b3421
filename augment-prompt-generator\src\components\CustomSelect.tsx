import React, { useState, useRef, useEffect, useId } from 'react';

// Global dropdown manager to ensure only one dropdown is open at a time
class DropdownManager {
  private static instance: DropdownManager;
  private openDropdownId: string | null = null;
  private callbacks: Map<string, () => void> = new Map();

  static getInstance(): DropdownManager {
    if (!DropdownManager.instance) {
      DropdownManager.instance = new DropdownManager();
    }
    return DropdownManager.instance;
  }

  register(id: string, closeCallback: () => void) {
    this.callbacks.set(id, closeCallback);
  }

  unregister(id: string) {
    this.callbacks.delete(id);
  }

  openDropdown(id: string) {
    if (this.openDropdownId && this.openDropdownId !== id) {
      const closeCallback = this.callbacks.get(this.openDropdownId);
      if (closeCallback) {
        closeCallback();
      }
    }
    this.openDropdownId = id;
  }

  closeDropdown(id: string) {
    if (this.openDropdownId === id) {
      this.openDropdownId = null;
    }
  }
}

interface Option {
  value: string;
  label: string;
  description?: string;
}

interface CustomSelectProps {
  value: string;
  onChange: (value: string) => void;
  options: Option[];
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export default function CustomSelect({
  value,
  onChange,
  options,
  placeholder = "Select an option",
  className = "",
  disabled = false
}: CustomSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const selectRef = useRef<HTMLDivElement>(null);
  const dropdownId = useId();
  const dropdownManager = DropdownManager.getInstance();

  const selectedOption = options.find(option => option.value === value);

  const closeDropdown = () => {
    setIsOpen(false);
    dropdownManager.closeDropdown(dropdownId);
  };

  const openDropdown = () => {
    dropdownManager.openDropdown(dropdownId);
    setIsOpen(true);
  };

  useEffect(() => {
    // Register with dropdown manager
    dropdownManager.register(dropdownId, closeDropdown);

    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        closeDropdown();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      dropdownManager.unregister(dropdownId);
    };
  }, [dropdownId]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'Escape':
          closeDropdown();
          break;
        case 'ArrowDown':
          event.preventDefault();
          setHighlightedIndex(prev => 
            prev < options.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          event.preventDefault();
          setHighlightedIndex(prev => 
            prev > 0 ? prev - 1 : options.length - 1
          );
          break;
        case 'Enter':
          event.preventDefault();
          if (highlightedIndex >= 0) {
            onChange(options[highlightedIndex].value);
            closeDropdown();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, highlightedIndex, options, onChange]);

  return (
    <div ref={selectRef} className={`relative ${className}`}>
      <button
        type="button"
        onClick={() => !disabled && (isOpen ? closeDropdown() : openDropdown())}
        disabled={disabled}
        className={`w-full px-2 py-1.5 rounded-md focus:outline-none focus:ring-1 focus:ring-white/20 text-sm text-white bg-black/5 backdrop-blur-md transition-all duration-300 hover:bg-black/10 text-left flex items-center justify-between ${
          disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
        }`}
        style={{
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.03)'
        }}
      >
        <span className={selectedOption ? 'text-white' : 'text-gray-400'}>
          {selectedOption ? selectedOption.label : placeholder}
        </span>
        <svg
          className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div
          className="absolute z-[9999] w-full mt-1 modal-content-universal rounded-md shadow-lg max-h-60 overflow-y-auto"
          style={{
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
          }}
        >
          {options.map((option, index) => (
            <button
              key={option.value}
              type="button"
              onClick={() => {
                onChange(option.value);
                closeDropdown();
              }}
              onMouseEnter={() => setHighlightedIndex(index)}
              className={`w-full text-left px-3 py-2 text-sm transition-all duration-200 first:rounded-t-md last:rounded-b-md ${
                index === highlightedIndex || option.value === value
                  ? 'bg-white/20 text-white shadow-sm'
                  : 'text-gray-100 hover:bg-white/10 hover:text-white'
              }`}
            >
              <div className="font-semibold">{option.label}</div>
              {option.description && (
                <div className="text-xs text-gray-200 mt-1 opacity-90">{option.description}</div>
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
